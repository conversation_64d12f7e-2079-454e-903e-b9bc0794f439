'use client';

import { useState, useEffect } from 'react';

interface RobotSettings {
  enabled: boolean;
  maxRiskPerTrade: number;
  maxDailyRisk: number;
  minConfidence: number;
  allowedPairs: string[];
  tradingHours: {
    start: string;
    end: string;
  };
  maxOpenTrades: number;
}

interface RobotStats {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  totalProfit: number;
  winRate: number;
  profitFactor: number;
  currentDrawdown: number;
}

export default function AutoTradingRobot() {
  const [robotSettings, setRobotSettings] = useState<RobotSettings>({
    enabled: false,
    maxRiskPerTrade: 2,
    maxDailyRisk: 6,
    minConfidence: 80,
    allowedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],
    tradingHours: {
      start: '08:00',
      end: '18:00'
    },
    maxOpenTrades: 3
  });

  const [robotStats, setRobotStats] = useState<RobotStats>({
    totalTrades: 47,
    winningTrades: 34,
    losingTrades: 13,
    totalProfit: 2850.50,
    winRate: 72.3,
    profitFactor: 1.85,
    currentDrawdown: 3.2
  });

  const [isRunning, setIsRunning] = useState(false);
  const [lastAction, setLastAction] = useState('');

  // Simulate robot activity
  useEffect(() => {
    if (robotSettings.enabled && isRunning) {
      const interval = setInterval(() => {
        const actions = [
          'تحليل إشارة EUR/USD...',
          'فحص مستويات الدعم والمقاومة...',
          'تقييم مخاطر الصفقة...',
          'مراقبة Order Blocks...',
          'تحليل Fair Value Gaps...',
          'فحص تدفق الأموال الذكية...',
          'تنفيذ صفقة شراء EUR/USD',
          'إغلاق صفقة بربح +45 نقطة',
          'وضع وقف خسارة متحرك...',
          'مراقبة الجلسة الآسيوية...'
        ];
        
        const randomAction = actions[Math.floor(Math.random() * actions.length)];
        setLastAction(randomAction);
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [robotSettings.enabled, isRunning]);

  const toggleRobot = () => {
    setRobotSettings(prev => ({ ...prev, enabled: !prev.enabled }));
    setIsRunning(!isRunning);
    if (!robotSettings.enabled) {
      setLastAction('تم تشغيل الروبوت - بدء المراقبة...');
    } else {
      setLastAction('تم إيقاف الروبوت');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            🤖 روبوت التداول الآلي
            <span className={`mr-2 text-xs px-2 py-1 rounded-full ${
              robotSettings.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {robotSettings.enabled ? 'نشط' : 'متوقف'}
            </span>
          </h3>
          <button
            onClick={toggleRobot}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              robotSettings.enabled
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {robotSettings.enabled ? '⏹️ إيقاف الروبوت' : '▶️ تشغيل الروبوت'}
          </button>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Robot Settings */}
          <div>
            <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
              ⚙️ إعدادات الروبوت
            </h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الحد الأقصى للمخاطرة لكل صفقة (%)
                </label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  step="0.5"
                  value={robotSettings.maxRiskPerTrade}
                  onChange={(e) => setRobotSettings(prev => ({
                    ...prev,
                    maxRiskPerTrade: parseFloat(e.target.value)
                  }))}
                  className="w-full"
                />
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {robotSettings.maxRiskPerTrade}%
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الحد الأدنى لمستوى الثقة (%)
                </label>
                <input
                  type="range"
                  min="60"
                  max="95"
                  step="5"
                  value={robotSettings.minConfidence}
                  onChange={(e) => setRobotSettings(prev => ({
                    ...prev,
                    minConfidence: parseInt(e.target.value)
                  }))}
                  className="w-full"
                />
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {robotSettings.minConfidence}%
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الحد الأقصى للصفقات المفتوحة
                </label>
                <select
                  value={robotSettings.maxOpenTrades}
                  onChange={(e) => setRobotSettings(prev => ({
                    ...prev,
                    maxOpenTrades: parseInt(e.target.value)
                  }))}
                  className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value={1}>1 صفقة</option>
                  <option value={2}>2 صفقة</option>
                  <option value={3}>3 صفقات</option>
                  <option value={5}>5 صفقات</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ساعات التداول
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="time"
                    value={robotSettings.tradingHours.start}
                    onChange={(e) => setRobotSettings(prev => ({
                      ...prev,
                      tradingHours: { ...prev.tradingHours, start: e.target.value }
                    }))}
                    className="p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600"
                  />
                  <input
                    type="time"
                    value={robotSettings.tradingHours.end}
                    onChange={(e) => setRobotSettings(prev => ({
                      ...prev,
                      tradingHours: { ...prev.tradingHours, end: e.target.value }
                    }))}
                    className="p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Robot Stats */}
          <div>
            <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
              📊 إحصائيات الأداء
            </h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{robotStats.totalTrades}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الصفقات</div>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{robotStats.winRate.toFixed(1)}%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">معدل النجاح</div>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">${robotStats.totalProfit.toFixed(2)}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">إجمالي الربح</div>
              </div>
              
              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{robotStats.profitFactor}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">عامل الربح</div>
              </div>
            </div>

            {/* Robot Activity */}
            <div className="mt-6">
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                🔄 النشاط الحالي
              </h5>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <div className="flex items-center space-x-2 space-x-reverse">
                  {robotSettings.enabled && (
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  )}
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {lastAction || 'الروبوت في وضع الانتظار...'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Warning */}
        <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start">
            <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
              <span className="text-white text-sm">⚠</span>
            </div>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                تحذير مهم - الروبوت التجريبي
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                هذا روبوت تجريبي للأغراض التعليمية فقط. لا يتم تنفيذ صفقات حقيقية. 
                اختبر جميع الإعدادات بعناية قبل استخدام أي نظام تداول آلي حقيقي.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
