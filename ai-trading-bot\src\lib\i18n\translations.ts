// Multi-language Support System
export type SupportedLanguage = 'ar' | 'en' | 'fr';

export interface Translation {
  // Navigation & UI
  dashboard: string;
  signals: string;
  analysis: string;
  riskManagement: string;
  settings: string;
  
  // Trading Terms
  buy: string;
  sell: string;
  entry: string;
  stopLoss: string;
  takeProfit: string;
  riskReward: string;
  confidence: string;
  
  // Market Terms
  bullish: string;
  bearish: string;
  neutral: string;
  trend: string;
  momentum: string;
  volatility: string;
  liquidity: string;
  session: string;
  
  // ICT Terms
  orderBlocks: string;
  fairValueGap: string;
  changeOfCharacter: string;
  breakOfStructure: string;
  smartMoney: string;
  accumulation: string;
  distribution: string;
  
  // Risk Management
  accountBalance: string;
  dailyRisk: string;
  winRate: string;
  profitFactor: string;
  maxDrawdown: string;
  
  // Notifications
  newSignal: string;
  riskAlert: string;
  marketUpdate: string;
  systemStatus: string;
  
  // Time & Sessions
  londonSession: string;
  newYorkSession: string;
  asianSession: string;
  overlap: string;
  
  // Status
  active: string;
  inactive: string;
  connected: string;
  disconnected: string;
  online: string;
  offline: string;
}

export const translations: Record<SupportedLanguage, Translation> = {
  // Arabic (العربية)
  ar: {
    // Navigation & UI
    dashboard: 'لوحة التحكم',
    signals: 'الإشارات',
    analysis: 'التحليل',
    riskManagement: 'إدارة المخاطر',
    settings: 'الإعدادات',
    
    // Trading Terms
    buy: 'شراء',
    sell: 'بيع',
    entry: 'الدخول',
    stopLoss: 'وقف الخسارة',
    takeProfit: 'جني الأرباح',
    riskReward: 'نسبة المخاطرة/العائد',
    confidence: 'الثقة',
    
    // Market Terms
    bullish: 'صاعد',
    bearish: 'هابط',
    neutral: 'محايد',
    trend: 'الاتجاه',
    momentum: 'الزخم',
    volatility: 'التقلب',
    liquidity: 'السيولة',
    session: 'الجلسة',
    
    // ICT Terms
    orderBlocks: 'مناطق الطلبات',
    fairValueGap: 'فجوة القيمة العادلة',
    changeOfCharacter: 'تغيير الطبيعة',
    breakOfStructure: 'كسر الهيكل',
    smartMoney: 'الأموال الذكية',
    accumulation: 'التجميع',
    distribution: 'التوزيع',
    
    // Risk Management
    accountBalance: 'رصيد الحساب',
    dailyRisk: 'المخاطرة اليومية',
    winRate: 'معدل النجاح',
    profitFactor: 'عامل الربح',
    maxDrawdown: 'أقصى تراجع',
    
    // Notifications
    newSignal: 'إشارة جديدة',
    riskAlert: 'تنبيه مخاطر',
    marketUpdate: 'تحديث السوق',
    systemStatus: 'حالة النظام',
    
    // Time & Sessions
    londonSession: 'جلسة لندن',
    newYorkSession: 'جلسة نيويورك',
    asianSession: 'الجلسة الآسيوية',
    overlap: 'التداخل',
    
    // Status
    active: 'نشط',
    inactive: 'غير نشط',
    connected: 'متصل',
    disconnected: 'منقطع',
    online: 'متصل',
    offline: 'غير متصل'
  },

  // English
  en: {
    // Navigation & UI
    dashboard: 'Dashboard',
    signals: 'Signals',
    analysis: 'Analysis',
    riskManagement: 'Risk Management',
    settings: 'Settings',
    
    // Trading Terms
    buy: 'Buy',
    sell: 'Sell',
    entry: 'Entry',
    stopLoss: 'Stop Loss',
    takeProfit: 'Take Profit',
    riskReward: 'Risk/Reward',
    confidence: 'Confidence',
    
    // Market Terms
    bullish: 'Bullish',
    bearish: 'Bearish',
    neutral: 'Neutral',
    trend: 'Trend',
    momentum: 'Momentum',
    volatility: 'Volatility',
    liquidity: 'Liquidity',
    session: 'Session',
    
    // ICT Terms
    orderBlocks: 'Order Blocks',
    fairValueGap: 'Fair Value Gap',
    changeOfCharacter: 'Change of Character',
    breakOfStructure: 'Break of Structure',
    smartMoney: 'Smart Money',
    accumulation: 'Accumulation',
    distribution: 'Distribution',
    
    // Risk Management
    accountBalance: 'Account Balance',
    dailyRisk: 'Daily Risk',
    winRate: 'Win Rate',
    profitFactor: 'Profit Factor',
    maxDrawdown: 'Max Drawdown',
    
    // Notifications
    newSignal: 'New Signal',
    riskAlert: 'Risk Alert',
    marketUpdate: 'Market Update',
    systemStatus: 'System Status',
    
    // Time & Sessions
    londonSession: 'London Session',
    newYorkSession: 'New York Session',
    asianSession: 'Asian Session',
    overlap: 'Overlap',
    
    // Status
    active: 'Active',
    inactive: 'Inactive',
    connected: 'Connected',
    disconnected: 'Disconnected',
    online: 'Online',
    offline: 'Offline'
  },

  // French (Français)
  fr: {
    // Navigation & UI
    dashboard: 'Tableau de Bord',
    signals: 'Signaux',
    analysis: 'Analyse',
    riskManagement: 'Gestion des Risques',
    settings: 'Paramètres',
    
    // Trading Terms
    buy: 'Acheter',
    sell: 'Vendre',
    entry: 'Entrée',
    stopLoss: 'Stop Loss',
    takeProfit: 'Take Profit',
    riskReward: 'Risque/Récompense',
    confidence: 'Confiance',
    
    // Market Terms
    bullish: 'Haussier',
    bearish: 'Baissier',
    neutral: 'Neutre',
    trend: 'Tendance',
    momentum: 'Momentum',
    volatility: 'Volatilité',
    liquidity: 'Liquidité',
    session: 'Session',
    
    // ICT Terms
    orderBlocks: 'Blocs d\'Ordres',
    fairValueGap: 'Écart de Valeur Équitable',
    changeOfCharacter: 'Changement de Caractère',
    breakOfStructure: 'Rupture de Structure',
    smartMoney: 'Argent Intelligent',
    accumulation: 'Accumulation',
    distribution: 'Distribution',
    
    // Risk Management
    accountBalance: 'Solde du Compte',
    dailyRisk: 'Risque Quotidien',
    winRate: 'Taux de Réussite',
    profitFactor: 'Facteur de Profit',
    maxDrawdown: 'Drawdown Maximum',
    
    // Notifications
    newSignal: 'Nouveau Signal',
    riskAlert: 'Alerte de Risque',
    marketUpdate: 'Mise à Jour du Marché',
    systemStatus: 'État du Système',
    
    // Time & Sessions
    londonSession: 'Session de Londres',
    newYorkSession: 'Session de New York',
    asianSession: 'Session Asiatique',
    overlap: 'Chevauchement',
    
    // Status
    active: 'Actif',
    inactive: 'Inactif',
    connected: 'Connecté',
    disconnected: 'Déconnecté',
    online: 'En Ligne',
    offline: 'Hors Ligne'
  }
};

export class I18nService {
  private currentLanguage: SupportedLanguage = 'ar'; // Default to Arabic
  private translations = translations;

  // Set current language
  setLanguage(language: SupportedLanguage): void {
    this.currentLanguage = language;
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('trading-bot-language', language);
    }
  }

  // Get current language
  getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  // Initialize language from localStorage
  initializeLanguage(): void {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('trading-bot-language') as SupportedLanguage;
      if (savedLanguage && this.translations[savedLanguage]) {
        this.currentLanguage = savedLanguage;
      }
    }
  }

  // Get translation for a key
  t(key: keyof Translation): string {
    return this.translations[this.currentLanguage][key] || key;
  }

  // Get all translations for current language
  getTranslations(): Translation {
    return this.translations[this.currentLanguage];
  }

  // Check if language is RTL
  isRTL(): boolean {
    return this.currentLanguage === 'ar';
  }

  // Get language direction
  getDirection(): 'ltr' | 'rtl' {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  // Get language name
  getLanguageName(language?: SupportedLanguage): string {
    const lang = language || this.currentLanguage;
    const names = {
      ar: 'العربية',
      en: 'English',
      fr: 'Français'
    };
    return names[lang];
  }

  // Get all supported languages
  getSupportedLanguages(): Array<{ code: SupportedLanguage; name: string; nativeName: string }> {
    return [
      { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'fr', name: 'French', nativeName: 'Français' }
    ];
  }

  // Format number based on language
  formatNumber(number: number, decimals: number = 2): string {
    const locale = {
      ar: 'ar-SA',
      en: 'en-US',
      fr: 'fr-FR'
    }[this.currentLanguage];

    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(number);
  }

  // Format currency based on language
  formatCurrency(amount: number, currency: string = 'USD'): string {
    const locale = {
      ar: 'ar-SA',
      en: 'en-US',
      fr: 'fr-FR'
    }[this.currentLanguage];

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  // Format date based on language
  formatDate(date: Date): string {
    const locale = {
      ar: 'ar-SA',
      en: 'en-US',
      fr: 'fr-FR'
    }[this.currentLanguage];

    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }
}

// Create singleton instance
export const i18n = new I18nService();
