// Risk Management and Trade Management System
import { TechnicalIndicators, CandleData } from '../technical-analysis/indicators';

export interface TradeSignal {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  entry: number;
  stopLoss: number;
  takeProfit1: number;
  takeProfit2: number;
  takeProfit3: number;
  riskRewardRatio: number;
  confidence: number;
  timestamp: number;
  reasoning: string[];
  indicators: Partial<TechnicalIndicators>;
}

export interface RiskParameters {
  maxRiskPerTrade: number; // Percentage of account
  maxDailyRisk: number; // Percentage of account
  maxOpenTrades: number;
  minRiskRewardRatio: number;
  accountBalance: number;
}

export interface TradeEntry {
  price: number;
  stopLoss: number;
  takeProfit: number[];
  positionSize: number;
  riskAmount: number;
}

export class RiskManagementEngine {
  private riskParams: RiskParameters;
  private activeTrades: TradeSignal[] = [];

  constructor(riskParams: RiskParameters) {
    this.riskParams = riskParams;
  }

  // Calculate optimal position size based on risk
  calculatePositionSize(entry: number, stopLoss: number, riskPercentage: number): number {
    const riskAmount = this.riskParams.accountBalance * (riskPercentage / 100);
    const riskPerUnit = Math.abs(entry - stopLoss);
    
    if (riskPerUnit === 0) return 0;
    
    return riskAmount / riskPerUnit;
  }

  // Calculate risk-reward ratio
  calculateRiskRewardRatio(entry: number, stopLoss: number, takeProfit: number): number {
    const risk = Math.abs(entry - stopLoss);
    const reward = Math.abs(takeProfit - entry);
    
    if (risk === 0) return 0;
    
    return reward / risk;
  }

  // Generate trade signal based on technical analysis
  generateTradeSignal(
    symbol: string,
    currentPrice: number,
    indicators: TechnicalIndicators,
    candleData: CandleData[]
  ): TradeSignal | null {
    const analysis = this.analyzeMarketConditions(indicators, candleData);
    
    if (!analysis.shouldTrade) {
      return null;
    }

    const signal = this.createTradeSignal(
      symbol,
      currentPrice,
      analysis,
      indicators
    );

    // Validate signal against risk parameters
    if (this.validateSignal(signal)) {
      return signal;
    }

    return null;
  }

  private analyzeMarketConditions(indicators: TechnicalIndicators, candleData: CandleData[]) {
    const reasoning: string[] = [];
    let bullishSignals = 0;
    let bearishSignals = 0;
    let confidence = 0;

    // RSI Analysis
    if (indicators.rsi < 30) {
      bullishSignals++;
      reasoning.push('RSI oversold (< 30)');
    } else if (indicators.rsi > 70) {
      bearishSignals++;
      reasoning.push('RSI overbought (> 70)');
    }

    // MACD Analysis
    if (indicators.macd.MACD > indicators.macd.signal && indicators.macd.histogram > 0) {
      bullishSignals++;
      reasoning.push('MACD bullish crossover');
    } else if (indicators.macd.MACD < indicators.macd.signal && indicators.macd.histogram < 0) {
      bearishSignals++;
      reasoning.push('MACD bearish crossover');
    }

    // EMA Analysis
    const currentPrice = candleData[candleData.length - 1].close;
    const ema = indicators.ema[indicators.ema.length - 1];
    
    if (currentPrice > ema) {
      bullishSignals++;
      reasoning.push('Price above EMA');
    } else {
      bearishSignals++;
      reasoning.push('Price below EMA');
    }

    // Supertrend Analysis
    if (indicators.supertrend.trend === 'up') {
      bullishSignals++;
      reasoning.push('Supertrend bullish');
    } else {
      bearishSignals++;
      reasoning.push('Supertrend bearish');
    }

    // VWAP Analysis
    if (currentPrice > indicators.vwap) {
      bullishSignals++;
      reasoning.push('Price above VWAP');
    } else {
      bearishSignals++;
      reasoning.push('Price below VWAP');
    }

    // Volume Profile Analysis
    const poc = indicators.volumeProfile.poc;
    if (currentPrice > poc) {
      bullishSignals++;
      reasoning.push('Price above POC');
    } else {
      bearishSignals++;
      reasoning.push('Price below POC');
    }

    // Fair Value Gap Analysis
    const activeFVGs = indicators.fvg.filter(gap => !gap.filled);
    if (activeFVGs.length > 0) {
      const nearestFVG = activeFVGs.reduce((nearest, gap) => 
        Math.abs(gap.price - currentPrice) < Math.abs(nearest.price - currentPrice) ? gap : nearest
      );
      
      if (nearestFVG.type === 'bullish' && currentPrice < nearestFVG.price) {
        bullishSignals++;
        reasoning.push('Approaching bullish FVG');
      } else if (nearestFVG.type === 'bearish' && currentPrice > nearestFVG.price) {
        bearishSignals++;
        reasoning.push('Approaching bearish FVG');
      }
    }

    // Change of Character Analysis
    const recentCHoCH = indicators.choch.slice(-3);
    if (recentCHoCH.length > 0) {
      const latestCHoCH = recentCHoCH[recentCHoCH.length - 1];
      if (latestCHoCH.type === 'bullish') {
        bullishSignals++;
        reasoning.push('Recent bullish CHoCH');
      } else {
        bearishSignals++;
        reasoning.push('Recent bearish CHoCH');
      }
    }

    // Break of Structure Analysis
    const recentBOS = indicators.bos.slice(-3);
    if (recentBOS.length > 0) {
      const latestBOS = recentBOS[recentBOS.length - 1];
      if (latestBOS.type === 'bullish') {
        bullishSignals++;
        reasoning.push('Recent bullish BOS');
      } else {
        bearishSignals++;
        reasoning.push('Recent bearish BOS');
      }
    }

    // Support/Resistance Analysis
    const nearestSupport = indicators.supportResistance
      .filter(level => level.type === 'support' && level.price < currentPrice)
      .sort((a, b) => Math.abs(a.price - currentPrice) - Math.abs(b.price - currentPrice))[0];
    
    const nearestResistance = indicators.supportResistance
      .filter(level => level.type === 'resistance' && level.price > currentPrice)
      .sort((a, b) => Math.abs(a.price - currentPrice) - Math.abs(b.price - currentPrice))[0];

    // Calculate confidence based on signal strength
    const totalSignals = bullishSignals + bearishSignals;
    if (totalSignals > 0) {
      if (bullishSignals > bearishSignals) {
        confidence = (bullishSignals / totalSignals) * 100;
      } else {
        confidence = (bearishSignals / totalSignals) * 100;
      }
    }

    const shouldTrade = Math.abs(bullishSignals - bearishSignals) >= 3 && confidence >= 60;
    const direction = bullishSignals > bearishSignals ? 'BUY' : 'SELL';

    return {
      shouldTrade,
      direction: direction as 'BUY' | 'SELL',
      confidence,
      reasoning,
      nearestSupport,
      nearestResistance,
      bullishSignals,
      bearishSignals
    };
  }

  private createTradeSignal(
    symbol: string,
    currentPrice: number,
    analysis: any,
    indicators: TechnicalIndicators
  ): TradeSignal {
    const { direction, confidence, reasoning, nearestSupport, nearestResistance } = analysis;
    
    let entry = currentPrice;
    let stopLoss: number;
    let takeProfit1: number;
    let takeProfit2: number;
    let takeProfit3: number;

    if (direction === 'BUY') {
      // For buy signals
      stopLoss = nearestSupport ? nearestSupport.price * 0.999 : currentPrice * 0.98; // 2% below or below support
      
      const riskDistance = entry - stopLoss;
      takeProfit1 = entry + (riskDistance * 1.5); // 1.5:1 RR
      takeProfit2 = entry + (riskDistance * 2.5); // 2.5:1 RR
      takeProfit3 = entry + (riskDistance * 4.0); // 4:1 RR
      
      // Adjust TPs based on resistance levels
      if (nearestResistance && takeProfit1 > nearestResistance.price) {
        takeProfit1 = nearestResistance.price * 0.999;
        takeProfit2 = nearestResistance.price * 1.005;
        takeProfit3 = nearestResistance.price * 1.015;
      }
    } else {
      // For sell signals
      stopLoss = nearestResistance ? nearestResistance.price * 1.001 : currentPrice * 1.02; // 2% above or above resistance
      
      const riskDistance = stopLoss - entry;
      takeProfit1 = entry - (riskDistance * 1.5); // 1.5:1 RR
      takeProfit2 = entry - (riskDistance * 2.5); // 2.5:1 RR
      takeProfit3 = entry - (riskDistance * 4.0); // 4:1 RR
      
      // Adjust TPs based on support levels
      if (nearestSupport && takeProfit1 < nearestSupport.price) {
        takeProfit1 = nearestSupport.price * 1.001;
        takeProfit2 = nearestSupport.price * 0.995;
        takeProfit3 = nearestSupport.price * 0.985;
      }
    }

    const riskRewardRatio = this.calculateRiskRewardRatio(entry, stopLoss, takeProfit1);

    return {
      id: this.generateSignalId(),
      symbol,
      type: direction,
      entry,
      stopLoss,
      takeProfit1,
      takeProfit2,
      takeProfit3,
      riskRewardRatio,
      confidence,
      timestamp: Date.now(),
      reasoning,
      indicators
    };
  }

  private validateSignal(signal: TradeSignal): boolean {
    // Check minimum risk-reward ratio
    if (signal.riskRewardRatio < this.riskParams.minRiskRewardRatio) {
      return false;
    }

    // Check maximum open trades
    if (this.activeTrades.length >= this.riskParams.maxOpenTrades) {
      return false;
    }

    // Check confidence threshold
    if (signal.confidence < 60) {
      return false;
    }

    // Check if we already have a trade on this symbol
    const existingTrade = this.activeTrades.find(trade => trade.symbol === signal.symbol);
    if (existingTrade) {
      return false;
    }

    return true;
  }

  private generateSignalId(): string {
    return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Calculate optimal entry points based on market structure
  calculateOptimalEntry(
    signal: TradeSignal,
    indicators: TechnicalIndicators,
    currentPrice: number
  ): TradeEntry {
    const positionSize = this.calculatePositionSize(
      signal.entry,
      signal.stopLoss,
      this.riskParams.maxRiskPerTrade
    );

    const riskAmount = Math.abs(signal.entry - signal.stopLoss) * positionSize;

    return {
      price: signal.entry,
      stopLoss: signal.stopLoss,
      takeProfit: [signal.takeProfit1, signal.takeProfit2, signal.takeProfit3],
      positionSize,
      riskAmount
    };
  }

  // Update risk parameters
  updateRiskParameters(newParams: Partial<RiskParameters>): void {
    this.riskParams = { ...this.riskParams, ...newParams };
  }

  // Add active trade
  addActiveTrade(signal: TradeSignal): void {
    this.activeTrades.push(signal);
  }

  // Remove active trade
  removeActiveTrade(signalId: string): void {
    this.activeTrades = this.activeTrades.filter(trade => trade.id !== signalId);
  }

  // Get active trades
  getActiveTrades(): TradeSignal[] {
    return [...this.activeTrades];
  }

  // Calculate total risk exposure
  getTotalRiskExposure(): number {
    return this.activeTrades.reduce((total, trade) => {
      const riskAmount = Math.abs(trade.entry - trade.stopLoss);
      return total + riskAmount;
    }, 0);
  }

  // Check if new trade would exceed daily risk limit
  canTakeNewTrade(potentialRisk: number): boolean {
    const currentRisk = this.getTotalRiskExposure();
    const maxDailyRiskAmount = this.riskParams.accountBalance * (this.riskParams.maxDailyRisk / 100);
    
    return (currentRisk + potentialRisk) <= maxDailyRiskAmount;
  }
}
