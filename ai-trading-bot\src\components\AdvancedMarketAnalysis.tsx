'use client';

import { useState, useEffect } from 'react';
import TimeframeAnalysis from './TimeframeAnalysis';

interface MarketCondition {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  buyPressure: number;
  sellPressure: number;
  trend: {
    direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    strength: number;
    timeframes: {
      '1m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      '5m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      '15m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      '1h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      '4h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      '1d': 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    };
  };
  technicalAnalysis: {
    rsi: number;
    macd: number;
    ema: number;
    support: number;
    resistance: number;
    recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
    confidence: number;
  };
  flag: string;
}

interface AIRecommendation {
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  entry: number;
  stopLoss: number;
  takeProfit1: number;
  takeProfit2: number;
  takeProfit3: number;
  riskReward: number;
  confidence: number;
  timeframe: string;
  reasoning: string[];
  accuracy: number;
}

export default function AdvancedMarketAnalysis() {
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');
  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']);
  const [marketConditions, setMarketConditions] = useState<MarketCondition[]>([]);
  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];
  const allPairs = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
    'EURGBP', 'EURJPY', 'GBPJPY', 'EURCHF', 'GBPCHF', 'AUDCHF', 'CADJPY',
    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD', 'USDJPY'
  ];

  // محاكاة الاتصال بـ TradingView
  useEffect(() => {
    const connectToTradingView = async () => {
      setIsConnected(true);
      // محاكاة تحميل البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      generateMarketData();
    };

    connectToTradingView();
    
    // تحديث البيانات كل 5 ثوانٍ
    const interval = setInterval(() => {
      generateMarketData();
      setLastUpdate(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, [selectedTimeframe, selectedPairs]);

  const generateMarketData = () => {
    const conditions: MarketCondition[] = selectedPairs.map(symbol => {
      const basePrice = getBasePrice(symbol);
      const change = (Math.random() - 0.5) * basePrice * 0.02;
      const buyPressure = Math.random() * 100;
      const sellPressure = 100 - buyPressure;
      
      return {
        symbol,
        name: getPairName(symbol),
        price: basePrice + change,
        change,
        changePercent: (change / basePrice) * 100,
        volume: Math.floor(Math.random() * 2000000) + 500000,
        buyPressure,
        sellPressure,
        trend: {
          direction: buyPressure > 60 ? 'BULLISH' : buyPressure < 40 ? 'BEARISH' : 'NEUTRAL',
          strength: Math.abs(buyPressure - 50) * 2,
          timeframes: {
            '1m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
            '5m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
            '15m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
            '1h': buyPressure > 50 ? 'BULLISH' : 'BEARISH',
            '4h': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
            '1d': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',
          }
        },
        technicalAnalysis: {
          rsi: Math.random() * 100,
          macd: (Math.random() - 0.5) * 0.01,
          ema: basePrice + (Math.random() - 0.5) * basePrice * 0.01,
          support: basePrice - Math.random() * basePrice * 0.02,
          resistance: basePrice + Math.random() * basePrice * 0.02,
          recommendation: getRecommendation(buyPressure),
          confidence: 70 + Math.random() * 25
        },
        flag: getPairFlag(symbol)
      };
    });

    setMarketConditions(conditions);
    generateAIRecommendations(conditions);
  };

  const generateAIRecommendations = (conditions: MarketCondition[]) => {
    const recommendations: AIRecommendation[] = conditions
      .filter(condition => condition.technicalAnalysis.confidence > 80)
      .slice(0, 3)
      .map(condition => {
        const action = condition.buyPressure > 65 ? 'BUY' : condition.buyPressure < 35 ? 'SELL' : 'HOLD';
        const entry = condition.price;
        const stopLoss = action === 'BUY' 
          ? entry - (entry * 0.015) 
          : entry + (entry * 0.015);
        const tp1 = action === 'BUY' 
          ? entry + (entry * 0.02) 
          : entry - (entry * 0.02);
        const tp2 = action === 'BUY' 
          ? entry + (entry * 0.035) 
          : entry - (entry * 0.035);
        const tp3 = action === 'BUY' 
          ? entry + (entry * 0.05) 
          : entry - (entry * 0.05);

        return {
          symbol: condition.symbol,
          action,
          entry,
          stopLoss,
          takeProfit1: tp1,
          takeProfit2: tp2,
          takeProfit3: tp3,
          riskReward: Math.abs(tp1 - entry) / Math.abs(entry - stopLoss),
          confidence: condition.technicalAnalysis.confidence,
          timeframe: selectedTimeframe,
          reasoning: generateReasoning(condition, action),
          accuracy: 85 + Math.random() * 10
        };
      });

    setAIRecommendations(recommendations);
  };

  const generateReasoning = (condition: MarketCondition, action: string): string[] => {
    const reasons = [];
    
    if (action === 'BUY') {
      reasons.push(`ضغط الشراء قوي: ${condition.buyPressure.toFixed(1)}%`);
      reasons.push(`RSI يظهر زخم صاعد: ${condition.technicalAnalysis.rsi.toFixed(1)}`);
      reasons.push(`السعر فوق EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);
      reasons.push(`كسر مستوى المقاومة: ${condition.technicalAnalysis.resistance.toFixed(5)}`);
      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);
    } else if (action === 'SELL') {
      reasons.push(`ضغط البيع قوي: ${condition.sellPressure.toFixed(1)}%`);
      reasons.push(`RSI يظهر زخم هابط: ${condition.technicalAnalysis.rsi.toFixed(1)}`);
      reasons.push(`السعر تحت EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);
      reasons.push(`كسر مستوى الدعم: ${condition.technicalAnalysis.support.toFixed(5)}`);
      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);
    }

    return reasons;
  };

  const getBasePrice = (symbol: string): number => {
    const prices: { [key: string]: number } = {
      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,
      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,
      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,
      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00
    };
    return prices[symbol] || 1.0000;
  };

  const getPairName = (symbol: string): string => {
    const names: { [key: string]: string } = {
      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',
      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',
      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',
      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',
      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',
      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',
      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',
      'ETHUSD': 'إيثريوم/دولار أمريكي'
    };
    return names[symbol] || symbol;
  };

  const getPairFlag = (symbol: string): string => {
    const flags: { [key: string]: string } = {
      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',
      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',
      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',
      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'
    };
    return flags[symbol] || '💱';
  };

  const getRecommendation = (buyPressure: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {
    if (buyPressure > 80) return 'STRONG_BUY';
    if (buyPressure > 60) return 'BUY';
    if (buyPressure > 40) return 'NEUTRAL';
    if (buyPressure > 20) return 'SELL';
    return 'STRONG_SELL';
  };

  const getRecommendationColor = (rec: string): string => {
    switch (rec) {
      case 'STRONG_BUY': return 'text-green-600 bg-green-100';
      case 'BUY': return 'text-green-500 bg-green-50';
      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';
      case 'SELL': return 'text-red-500 bg-red-50';
      case 'STRONG_SELL': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRecommendationText = (rec: string): string => {
    switch (rec) {
      case 'STRONG_BUY': return 'شراء قوي';
      case 'BUY': return 'شراء';
      case 'NEUTRAL': return 'محايد';
      case 'SELL': return 'بيع';
      case 'STRONG_SELL': return 'بيع قوي';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            📊 تحليل السوق المتقدم
            <span className={`mr-3 w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
            <span className="text-sm font-normal text-gray-600 dark:text-gray-400">
              {isConnected ? 'متصل بـ TradingView' : 'غير متصل'}
            </span>
          </h2>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
          </div>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Timeframe Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الإطار الزمني:
            </label>
            <div className="flex flex-wrap gap-2">
              {timeframes.map(tf => (
                <button
                  key={tf}
                  onClick={() => setSelectedTimeframe(tf)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedTimeframe === tf
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                >
                  {tf}
                </button>
              ))}
            </div>
          </div>

          {/* Enhanced Pair Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              أزواج العملات المختارة ({selectedPairs.length}):
            </label>

            {/* Quick Selection Buttons */}
            <div className="flex flex-wrap gap-2 mb-3">
              <button
                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}
                className="px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700"
              >
                العملات الرئيسية
              </button>
              <button
                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700"
              >
                السلع
              </button>
              <button
                onClick={() => setSelectedPairs(['BTCUSD', 'ETHUSD'])}
                className="px-3 py-1 bg-purple-600 text-white rounded text-xs font-medium hover:bg-purple-700"
              >
                العملات الرقمية
              </button>
              <button
                onClick={() => setSelectedPairs(allPairs)}
                className="px-3 py-1 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700"
              >
                الكل ({allPairs.length})
              </button>
              <button
                onClick={() => setSelectedPairs([])}
                className="px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700"
              >
                مسح الكل
              </button>
            </div>

            {/* Individual Pair Selection */}
            <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2">
              {allPairs.map(pair => (
                <button
                  key={pair}
                  onClick={() => {
                    if (selectedPairs.includes(pair)) {
                      setSelectedPairs(selectedPairs.filter(p => p !== pair));
                    } else {
                      setSelectedPairs([...selectedPairs, pair]);
                    }
                  }}
                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${
                    selectedPairs.includes(pair)
                      ? 'bg-green-600 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                  title={getPairName(pair)}
                >
                  {pair}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Market Conditions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {marketConditions.map(condition => (
          <div key={condition.symbol} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-lg">{condition.flag}</span>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {condition.symbol}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {condition.name}
                  </p>
                </div>
              </div>
              <div className={`px-2 py-1 rounded text-xs font-medium ${getRecommendationColor(condition.technicalAnalysis.recommendation)}`}>
                {getRecommendationText(condition.technicalAnalysis.recommendation)}
              </div>
            </div>

            {/* Price and Change */}
            <div className="mb-4">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {condition.price.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}
              </div>
              <div className={`text-sm font-medium ${condition.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {condition.changePercent >= 0 ? '+' : ''}{condition.changePercent.toFixed(2)}%
              </div>
            </div>

            {/* Buy/Sell Pressure */}
            <div className="mb-4">
              <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                <span>ضغط الشراء: {condition.buyPressure.toFixed(1)}%</span>
                <span>ضغط البيع: {condition.sellPressure.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${condition.buyPressure}%` }}
                ></div>
              </div>
            </div>

            {/* Multi-timeframe Trend */}
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">الاتجاه متعدد الإطارات:</h4>
              <div className="grid grid-cols-3 gap-1 text-xs">
                {Object.entries(condition.trend.timeframes).map(([tf, trend]) => (
                  <div key={tf} className={`text-center py-1 rounded ${
                    trend === 'BULLISH' ? 'bg-green-100 text-green-800' :
                    trend === 'BEARISH' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    <div className="font-medium">{tf}</div>
                    <div>{trend === 'BULLISH' ? '↗' : trend === 'BEARISH' ? '↘' : '→'}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Technical Indicators */}
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">RSI:</span>
                <span className={`font-medium ${
                  condition.technicalAnalysis.rsi > 70 ? 'text-red-600' :
                  condition.technicalAnalysis.rsi < 30 ? 'text-green-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {condition.technicalAnalysis.rsi.toFixed(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الدعم:</span>
                <span className="font-medium text-green-600">
                  {condition.technicalAnalysis.support.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">المقاومة:</span>
                <span className="font-medium text-red-600">
                  {condition.technicalAnalysis.resistance.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الثقة:</span>
                <span className="font-medium text-blue-600">
                  {condition.technicalAnalysis.confidence.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* AI Recommendations */}
      {aiRecommendations.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            🤖 توصيات الذكاء الاصطناعي المربحة
            <span className="mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
              دقة عالية
            </span>
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {aiRecommendations.map((rec, index) => (
              <div key={index} className={`border-2 rounded-xl p-6 ${
                rec.action === 'BUY' 
                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'
                  : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'
              }`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-lg">{getPairFlag(rec.symbol)}</span>
                    <div>
                      <h4 className="text-lg font-bold text-gray-900">
                        {rec.action} {rec.symbol}
                      </h4>
                      <p className="text-xs text-gray-600">{rec.timeframe}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      {rec.confidence.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">دقة: {rec.accuracy.toFixed(1)}%</div>
                  </div>
                </div>

                {/* Price Levels */}
                <div className="grid grid-cols-2 gap-2 mb-4 text-sm">
                  <div className="bg-white rounded p-2 text-center">
                    <div className="text-xs text-gray-600">الدخول</div>
                    <div className="font-bold">{rec.entry.toFixed(5)}</div>
                  </div>
                  <div className="bg-white rounded p-2 text-center">
                    <div className="text-xs text-gray-600">وقف الخسارة</div>
                    <div className="font-bold text-red-600">{rec.stopLoss.toFixed(5)}</div>
                  </div>
                  <div className="bg-white rounded p-2 text-center">
                    <div className="text-xs text-gray-600">هدف 1</div>
                    <div className="font-bold text-green-600">{rec.takeProfit1.toFixed(5)}</div>
                  </div>
                  <div className="bg-white rounded p-2 text-center">
                    <div className="text-xs text-gray-600">R/R</div>
                    <div className="font-bold text-blue-600">{rec.riskReward.toFixed(2)}:1</div>
                  </div>
                </div>

                {/* AI Reasoning */}
                <div className="bg-white rounded p-3">
                  <h5 className="text-sm font-medium text-gray-900 mb-2">🧠 تحليل الذكاء الاصطناعي:</h5>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {rec.reasoning.slice(0, 3).map((reason, i) => (
                      <li key={i} className="flex items-start">
                        <span className="mr-1 text-blue-500">▶</span>
                        <span>{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}


    </div>
  );
}
