{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\n\nexport const metadata: Metadata = {\n  title: \"AI Trading Bot - Professional Trading System\",\n  description: \"Advanced AI-powered trading bot with technical analysis, pattern recognition, and risk management\",\n  icons: {\n    icon: [\n      { url: '/icon.svg', type: 'image/svg+xml' },\n      { url: '/favicon.ico', sizes: '32x32' }\n    ],\n    apple: '/icon.svg',\n    shortcut: '/favicon.ico'\n  },\n  keywords: ['AI Trading', 'Forex', 'Technical Analysis', 'TradingView', 'Professional Trading', 'ICT', 'Smart Money'],\n  authors: [{ name: 'AI Trading Bot Team' }],\n  creator: 'AI Trading Bot',\n  publisher: 'AI Trading Bot',\n  robots: 'index, follow',\n  openGraph: {\n    title: 'AI Trading Bot - Professional Trading System',\n    description: 'Advanced AI-powered trading bot with TradingView integration',\n    type: 'website',\n    images: ['/logo.svg']\n  }\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className=\"antialiased\">\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAAa,MAAM;YAAgB;YAC1C;gBAAE,KAAK;gBAAgB,OAAO;YAAQ;SACvC;QACD,OAAO;QACP,UAAU;IACZ;IACA,UAAU;QAAC;QAAc;QAAS;QAAsB;QAAe;QAAwB;QAAO;KAAc;IACpH,SAAS;QAAC;YAAE,MAAM;QAAsB;KAAE;IAC1C,SAAS;IACT,WAAW;IACX,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;YAAC;SAAY;IACvB;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACb;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}