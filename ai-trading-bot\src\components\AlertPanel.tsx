'use client';

import React, { useState } from 'react';
import { AlertSystem } from '@/lib/notifications/alert-system';
import { TradeSignal } from '@/lib/trading/risk-management';
import { Bell, Mail, MessageSquare, Settings, Check, X, Clock } from 'lucide-react';

interface AlertPanelProps {
  alertSystem: AlertSystem;
  signals: TradeSignal[];
}

const AlertPanel: React.FC<AlertPanelProps> = ({
  alertSystem,
  signals
}) => {
  const [telegramEnabled, setTelegramEnabled] = useState(false);
  const [emailEnabled, setEmailEnabled] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [emailAddress, setEmailAddress] = useState('');

  // Mock alert history for demo
  const alertHistory = [
    {
      id: '1',
      type: 'TRADE_SIGNAL',
      title: '🚨 BUY Signal - EURUSD',
      message: 'Strong bullish signal detected with 85% confidence',
      timestamp: Date.now() - 300000, // 5 minutes ago
      status: 'sent',
      channels: ['telegram']
    },
    {
      id: '2',
      type: 'MARKET_UPDATE',
      title: '📊 Market Update - GBPUSD',
      message: 'Market sentiment changed to bearish',
      timestamp: Date.now() - 900000, // 15 minutes ago
      status: 'sent',
      channels: ['email']
    },
    {
      id: '3',
      type: 'RISK_WARNING',
      title: '⚠️ Risk Warning',
      message: 'Daily risk limit approaching 80%',
      timestamp: Date.now() - 1800000, // 30 minutes ago
      status: 'sent',
      channels: ['telegram', 'email']
    }
  ];

  const alertStats = alertSystem.getAlertStats();

  const handleTestAlert = async (channel: 'telegram' | 'email') => {
    try {
      if (channel === 'telegram') {
        await alertSystem.sendSystemStatus('ONLINE', 'Test alert from AI Trading Bot');
      } else {
        await alertSystem.sendSystemStatus('ONLINE', 'Test email alert from AI Trading Bot');
      }
      alert(`Test ${channel} alert sent successfully!`);
    } catch (error) {
      alert(`Failed to send test ${channel} alert: ${error}`);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'TRADE_SIGNAL':
        return <Bell className="w-4 h-4 text-blue-500" />;
      case 'MARKET_UPDATE':
        return <MessageSquare className="w-4 h-4 text-green-500" />;
      case 'RISK_WARNING':
        return <X className="w-4 h-4 text-red-500" />;
      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <X className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ago`;
    }
    return `${minutes}m ago`;
  };

  return (
    <div className="space-y-6">
      {/* Alert Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <Bell className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Queue Length</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alertStats.queueLength}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <Clock className="w-8 h-8 text-orange-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Countdowns</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alertStats.activeCountdowns}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <Check className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Alerts Sent Today</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alertHistory.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Alert Configuration */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Alert Configuration
          </h3>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Telegram Configuration */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <MessageSquare className="w-5 h-5 text-blue-500 mr-2" />
                <h4 className="text-md font-medium text-gray-900 dark:text-white">
                  Telegram Notifications
                </h4>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={telegramEnabled}
                  onChange={(e) => setTelegramEnabled(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {telegramEnabled && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bot Token
                  </label>
                  <input
                    type="password"
                    value={telegramBotToken}
                    onChange={(e) => setTelegramBotToken(e.target.value)}
                    placeholder="Enter your Telegram bot token"
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Chat ID
                  </label>
                  <input
                    type="text"
                    value={telegramChatId}
                    onChange={(e) => setTelegramChatId(e.target.value)}
                    placeholder="Enter your Telegram chat ID"
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm"
                  />
                </div>
                <button
                  onClick={() => handleTestAlert('telegram')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                >
                  Send Test Alert
                </button>
              </div>
            )}
          </div>

          {/* Email Configuration */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Mail className="w-5 h-5 text-green-500 mr-2" />
                <h4 className="text-md font-medium text-gray-900 dark:text-white">
                  Email Notifications
                </h4>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={emailEnabled}
                  onChange={(e) => setEmailEnabled(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {emailEnabled && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={emailAddress}
                    onChange={(e) => setEmailAddress(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm"
                  />
                </div>
                <button
                  onClick={() => handleTestAlert('email')}
                  className="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                >
                  Send Test Email
                </button>
              </div>
            )}
          </div>

          {/* Alert Types */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
              Alert Types
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">Trade Signals</span>
                <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">Market Updates</span>
                <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">Risk Warnings</span>
                <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">System Status</span>
                <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">Countdown Alerts</span>
                <input type="checkbox" defaultChecked className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Alert History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Alerts
          </h3>
        </div>
        
        <div className="p-6">
          {alertHistory.length > 0 ? (
            <div className="space-y-4">
              {alertHistory.map(alert => (
                <div key={alert.id} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-shrink-0">
                    {getAlertIcon(alert.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {alert.title}
                      </h4>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(alert.status)}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTime(alert.timestamp)}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {alert.message}
                    </p>
                    
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">Sent via:</span>
                      {alert.channels.map(channel => (
                        <span key={channel} className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-1 rounded">
                          {channel}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                No alerts sent yet. Configure your notification settings above.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Demo Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-start">
          <Bell className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
              Demo Mode Notice
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              This is a demo version. To enable real notifications, you need to configure your Telegram bot token and email settings.
              All alerts shown here are simulated for demonstration purposes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertPanel;
