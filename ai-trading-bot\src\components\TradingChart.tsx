'use client';

import React, { useEffect, useRef } from 'react';
import { TradingViewProvider } from '@/lib/data-providers/tradingview';
import { TechnicalIndicators } from '@/lib/technical-analysis/indicators';
import { MarketState } from '@/lib/market-analysis/market-state';
import { AIDecision } from '@/lib/ai/pattern-recognition';

interface TradingChartProps {
  symbol: string;
  tradingProvider: TradingViewProvider;
  indicators?: TechnicalIndicators;
  marketState?: MarketState;
  aiDecision?: AIDecision;
}

const TradingChart: React.FC<TradingChartProps> = ({
  symbol,
  tradingProvider,
  indicators,
  marketState,
  aiDecision
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartContainerRef.current) {
      // In a real implementation, you would integrate with TradingView's charting library
      // For now, we'll create a placeholder chart
      renderChart();
    }
  }, [symbol, indicators]);

  const renderChart = () => {
    if (!chartContainerRef.current) return;

    const chartData = tradingProvider.getChartData(symbol);
    if (!chartData) return;

    // Clear previous chart
    chartContainerRef.current.innerHTML = '';

    // Create a simple SVG chart as placeholder
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '400');
    svg.setAttribute('viewBox', '0 0 800 400');
    svg.style.background = '#1f2937';

    // Generate sample candlestick data
    const candles = chartData.data.slice(-50); // Last 50 candles
    const maxPrice = Math.max(...candles.map(c => c.high));
    const minPrice = Math.min(...candles.map(c => c.low));
    const priceRange = maxPrice - minPrice;
    const candleWidth = 800 / candles.length;

    candles.forEach((candle, index) => {
      const x = index * candleWidth + candleWidth / 2;
      const openY = 350 - ((candle.open - minPrice) / priceRange) * 300;
      const closeY = 350 - ((candle.close - minPrice) / priceRange) * 300;
      const highY = 350 - ((candle.high - minPrice) / priceRange) * 300;
      const lowY = 350 - ((candle.low - minPrice) / priceRange) * 300;

      // High-Low line
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', x.toString());
      line.setAttribute('y1', highY.toString());
      line.setAttribute('x2', x.toString());
      line.setAttribute('y2', lowY.toString());
      line.setAttribute('stroke', '#6b7280');
      line.setAttribute('stroke-width', '1');
      svg.appendChild(line);

      // Candle body
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      const bodyHeight = Math.abs(closeY - openY);
      const bodyY = Math.min(openY, closeY);
      
      rect.setAttribute('x', (x - candleWidth * 0.3).toString());
      rect.setAttribute('y', bodyY.toString());
      rect.setAttribute('width', (candleWidth * 0.6).toString());
      rect.setAttribute('height', bodyHeight.toString());
      rect.setAttribute('fill', candle.close > candle.open ? '#10b981' : '#ef4444');
      svg.appendChild(rect);
    });

    chartContainerRef.current.appendChild(svg);
  };

  return (
    <div className="space-y-6">
      {/* Chart Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {symbol} Chart
          </h2>
          <div className="flex items-center space-x-4">
            {marketState && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
                <span className={`text-sm font-medium px-2 py-1 rounded ${
                  marketState.trend === 'BULLISH' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                  marketState.trend === 'BEARISH' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {marketState.trend}
                </span>
              </div>
            )}
            
            {aiDecision && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">AI Signal:</span>
                <span className={`text-sm font-medium px-2 py-1 rounded ${
                  aiDecision.action === 'BUY' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                  aiDecision.action === 'SELL' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {aiDecision.action} ({aiDecision.confidence.toFixed(1)}%)
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Chart Container */}
        <div 
          ref={chartContainerRef}
          className="w-full h-96 bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
        />

        {/* Chart Controls */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Timeframe:</span>
            <select className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm">
              <option>1m</option>
              <option>5m</option>
              <option>15m</option>
              <option>1h</option>
              <option>4h</option>
              <option>1d</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1 text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded">
              Indicators
            </button>
            <button className="px-3 py-1 text-sm bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 rounded">
              Patterns
            </button>
          </div>
        </div>
      </div>

      {/* Technical Indicators Panel */}
      {indicators && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">RSI</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {indicators.rsi.toFixed(1)}
            </div>
            <div className={`text-sm ${
              indicators.rsi > 70 ? 'text-red-600' :
              indicators.rsi < 30 ? 'text-green-600' : 'text-gray-600'
            }`}>
              {indicators.rsi > 70 ? 'Overbought' :
               indicators.rsi < 30 ? 'Oversold' : 'Neutral'}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">MACD</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {indicators.macd.MACD.toFixed(4)}
            </div>
            <div className={`text-sm ${
              indicators.macd.MACD > indicators.macd.signal ? 'text-green-600' : 'text-red-600'
            }`}>
              {indicators.macd.MACD > indicators.macd.signal ? 'Bullish' : 'Bearish'}
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">VWAP</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {indicators.vwap.toFixed(5)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Volume Weighted
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Supertrend</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {indicators.supertrend.value.toFixed(5)}
            </div>
            <div className={`text-sm ${
              indicators.supertrend.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {indicators.supertrend.trend === 'up' ? 'Uptrend' : 'Downtrend'}
            </div>
          </div>
        </div>
      )}

      {/* Pattern Recognition */}
      {aiDecision && aiDecision.patterns.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Detected Patterns
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {aiDecision.patterns.slice(0, 4).map((pattern, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {pattern.name}
                  </h4>
                  <span className={`text-sm px-2 py-1 rounded ${
                    pattern.type === 'BULLISH' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                    pattern.type === 'BEARISH' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                    'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {pattern.type}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {pattern.description}
                </p>
                <div className="text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Confidence: </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {pattern.confidence.toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingChart;
