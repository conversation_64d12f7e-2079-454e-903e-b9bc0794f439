'use client';

import React, { useState, useEffect } from 'react';

interface RiskMetrics {
  accountBalance: number;
  dailyRiskLimit: number;
  currentDailyLoss: number;
  maxPositionSize: number;
  correlationRisk: number;
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
}

interface PositionSizing {
  pair: string;
  riskAmount: number;
  stopLossDistance: number;
  recommendedLotSize: number;
  maxLotSize: number;
  riskRewardRatio: number;
}

const AdvancedRiskManagement: React.FC = () => {
  const [accountBalance, setAccountBalance] = useState<number>(10000);
  const [riskPercentage, setRiskPercentage] = useState<number>(1);
  const [dailyRiskLimit, setDailyRiskLimit] = useState<number>(5);
  const [currentDailyPnL, setCurrentDailyPnL] = useState<number>(0);
  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');
  const [stopLossDistance, setStopLossDistance] = useState<number>(20);
  const [targetRR, setTargetRR] = useState<number>(2);

  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({
    accountBalance: 10000,
    dailyRiskLimit: 500,
    currentDailyLoss: 0,
    maxPositionSize: 100,
    correlationRisk: 0,
    winRate: 65,
    profitFactor: 1.8,
    sharpeRatio: 1.2,
    maxDrawdown: 8.5
  });

  const forexPairs = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
    'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURJPY', 'GBPAUD', 'AUDCAD'
  ];

  const calculatePositionSize = (): PositionSizing => {
    const riskAmount = (accountBalance * riskPercentage) / 100;
    const pipValue = selectedPair.includes('JPY') ? 0.01 : 0.0001;
    const stopLossPips = stopLossDistance;
    
    // Standard lot calculation
    const recommendedLotSize = riskAmount / (stopLossPips * pipValue * 100000);
    const maxLotSize = (accountBalance * 2) / 100000; // Max 2% of account as position size
    
    return {
      pair: selectedPair,
      riskAmount,
      stopLossDistance: stopLossPips,
      recommendedLotSize: Math.min(recommendedLotSize, maxLotSize),
      maxLotSize,
      riskRewardRatio: targetRR
    };
  };

  const calculateKellyCriterion = (): number => {
    const winRate = riskMetrics.winRate / 100;
    const lossRate = 1 - winRate;
    const avgWin = targetRR; // Assuming average win is based on R:R ratio
    const avgLoss = 1;
    
    const kelly = (winRate * avgWin - lossRate * avgLoss) / avgWin;
    return Math.max(0, Math.min(kelly * 100, 25)); // Cap at 25% for safety
  };

  const getTradingStatus = (): { status: string; color: string; message: string } => {
    const dailyLossPercentage = (Math.abs(currentDailyPnL) / accountBalance) * 100;
    
    if (currentDailyPnL < 0 && dailyLossPercentage >= dailyRiskLimit) {
      return {
        status: 'توقف عن التداول',
        color: 'text-red-600 bg-red-100',
        message: 'تم الوصول لحد المخاطرة اليومية. توقف عن التداول اليوم.'
      };
    } else if (currentDailyPnL < 0 && dailyLossPercentage >= dailyRiskLimit * 0.7) {
      return {
        status: 'مخاطرة عالية',
        color: 'text-orange-600 bg-orange-100',
        message: 'اقتراب من حد المخاطرة اليومية. قلل أحجام المراكز.'
      };
    } else if (currentDailyPnL > 0) {
      return {
        status: 'مربح',
        color: 'text-green-600 bg-green-100',
        message: 'أداء جيد اليوم. حافظ على الانضباط.'
      };
    } else {
      return {
        status: 'طبيعي',
        color: 'text-blue-600 bg-blue-100',
        message: 'جاهز للتداول. اتبع خطتك.'
      };
    }
  };

  const positionSizing = calculatePositionSize();
  const kellyPercentage = calculateKellyCriterion();
  const tradingStatus = getTradingStatus();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          🛡️ نظام إدارة المخاطر المتقدم
        </h2>
        <div className={`px-4 py-2 rounded-lg font-bold ${tradingStatus.color}`}>
          {tradingStatus.status}
        </div>
      </div>

      {/* Account Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-600 dark:text-blue-400">رصيد الحساب</h3>
          <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
            ${accountBalance.toLocaleString()}
          </p>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <h3 className="text-sm font-medium text-green-600 dark:text-green-400">الربح والخسارة اليومية</h3>
          <p className={`text-2xl font-bold ${currentDailyPnL >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>
            ${currentDailyPnL >= 0 ? '+' : ''}{currentDailyPnL.toFixed(2)}
          </p>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
          <h3 className="text-sm font-medium text-purple-600 dark:text-purple-400">معدل الفوز</h3>
          <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
            {riskMetrics.winRate}%
          </p>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
          <h3 className="text-sm font-medium text-orange-600 dark:text-orange-400">عامل الربح</h3>
          <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
            {riskMetrics.profitFactor}
          </p>
        </div>
      </div>

      {/* Risk Settings */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">معايير المخاطر</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              رصيد الحساب ($)
            </label>
            <input
              type="number"
              value={accountBalance}
              onChange={(e) => setAccountBalance(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المخاطرة لكل صفقة (%)
            </label>
            <input
              type="number"
              value={riskPercentage}
              onChange={(e) => setRiskPercentage(Number(e.target.value))}
              min="0.1"
              max="5"
              step="0.1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              حد المخاطرة اليومية (%)
            </label>
            <input
              type="number"
              value={dailyRiskLimit}
              onChange={(e) => setDailyRiskLimit(Number(e.target.value))}
              min="1"
              max="10"
              step="0.5"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الربح والخسارة اليومية الحالية ($)
            </label>
            <input
              type="number"
              value={currentDailyPnL}
              onChange={(e) => setCurrentDailyPnL(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">حجم المركز</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              زوج العملات
            </label>
            <select
              value={selectedPair}
              onChange={(e) => setSelectedPair(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {forexPairs.map(pair => (
                <option key={pair} value={pair}>{pair}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              مسافة وقف الخسارة (نقطة)
            </label>
            <input
              type="number"
              value={stopLossDistance}
              onChange={(e) => setStopLossDistance(Number(e.target.value))}
              min="5"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نسبة المخاطرة:العائد المستهدفة
            </label>
            <input
              type="number"
              value={targetRR}
              onChange={(e) => setTargetRR(Number(e.target.value))}
              min="1"
              max="5"
              step="0.1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </div>

      {/* Position Sizing Results */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          📊 حاسبة حجم المركز
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">مبلغ المخاطرة</p>
            <p className="text-xl font-bold text-blue-600">${positionSizing.riskAmount.toFixed(2)}</p>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">حجم اللوت الموصى به</p>
            <p className="text-xl font-bold text-green-600">{positionSizing.recommendedLotSize.toFixed(2)}</p>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">معيار كيلي</p>
            <p className="text-xl font-bold text-purple-600">{kellyPercentage.toFixed(1)}%</p>
          </div>
        </div>
      </div>

      {/* Trading Status Alert */}
      <div className={`rounded-lg p-4 ${tradingStatus.color}`}>
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <span className="text-2xl">
              {tradingStatus.status === 'STOP TRADING' ? '🛑' : 
               tradingStatus.status === 'HIGH RISK' ? '⚠️' : 
               tradingStatus.status === 'PROFITABLE' ? '✅' : '🟢'}
            </span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium">
              {tradingStatus.status}
            </h3>
            <p className="text-sm">
              {tradingStatus.message}
            </p>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-600 dark:text-gray-400">نسبة شارب</p>
          <p className="text-lg font-bold text-gray-900 dark:text-white">{riskMetrics.sharpeRatio}</p>
        </div>

        <div className="text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-600 dark:text-gray-400">أقصى انخفاض</p>
          <p className="text-lg font-bold text-red-600">{riskMetrics.maxDrawdown}%</p>
        </div>

        <div className="text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-600 dark:text-gray-400">المخاطرة المستخدمة يومياً</p>
          <p className="text-lg font-bold text-orange-600">
            {((Math.abs(currentDailyPnL) / accountBalance) * 100).toFixed(1)}%
          </p>
        </div>

        <div className="text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-600 dark:text-gray-400">المخاطرة المتبقية</p>
          <p className="text-lg font-bold text-green-600">
            {Math.max(0, dailyRiskLimit - ((Math.abs(currentDailyPnL) / accountBalance) * 100)).toFixed(1)}%
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdvancedRiskManagement;
