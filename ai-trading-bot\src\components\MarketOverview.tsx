'use client';

import React from 'react';
import { RealTimeQuote } from '@/lib/data-providers/tradingview';
import { MarketState } from '@/lib/market-analysis/market-state';
import { AIDecision } from '@/lib/ai/pattern-recognition';
import { TradeSignal } from '@/lib/trading/risk-management';
import { TrendingUp, TrendingDown, Minus, Activity, Clock, AlertTriangle } from 'lucide-react';

interface MarketOverviewProps {
  symbols: string[];
  quotes: Map<string, RealTimeQuote>;
  marketStates: Map<string, MarketState>;
  aiDecisions: Map<string, AIDecision>;
  signals: TradeSignal[];
}

const MarketOverview: React.FC<MarketOverviewProps> = ({
  symbols,
  quotes,
  marketStates,
  aiDecisions,
  signals
}) => {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'BULLISH':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'BEARISH':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'BULLISH':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'BEARISH':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getMomentumColor = (momentum: string) => {
    if (momentum.includes('STRONG_BULLISH')) return 'text-green-700 bg-green-100 dark:bg-green-900/30';
    if (momentum.includes('WEAK_BULLISH')) return 'text-green-600 bg-green-50 dark:bg-green-900/20';
    if (momentum.includes('STRONG_BEARISH')) return 'text-red-700 bg-red-100 dark:bg-red-900/30';
    if (momentum.includes('WEAK_BEARISH')) return 'text-red-600 bg-red-50 dark:bg-red-900/20';
    return 'text-gray-600 bg-gray-50 dark:bg-gray-800';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const recentSignals = signals.slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Market Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {symbols.map(symbol => {
          const quote = quotes.get(symbol);
          const marketState = marketStates.get(symbol);
          const aiDecision = aiDecisions.get(symbol);

          return (
            <div key={symbol} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {symbol}
                </h3>
                {marketState && getTrendIcon(marketState.trend)}
              </div>

              {quote && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-gray-900 dark:text-white">
                      {quote.price.toFixed(symbol.includes('JPY') ? 3 : 5)}
                    </span>
                    <span className={`text-sm font-medium ${
                      quote.change >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {quote.change >= 0 ? '+' : ''}{quote.changePercent.toFixed(2)}%
                    </span>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Bid: {quote.bid.toFixed(symbol.includes('JPY') ? 3 : 5)} | 
                    Ask: {quote.ask.toFixed(symbol.includes('JPY') ? 3 : 5)}
                  </div>

                  {marketState && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">Trend:</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${getTrendColor(marketState.trend)}`}>
                          {marketState.trend}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">Momentum:</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${getMomentumColor(marketState.momentum)}`}>
                          {marketState.momentum.replace('_', ' ')}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">Volatility:</span>
                        <span className="text-xs text-gray-900 dark:text-white">
                          {marketState.volatility}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">Session:</span>
                        <span className="text-xs text-gray-900 dark:text-white">
                          {marketState.session.name}
                        </span>
                      </div>
                    </div>
                  )}

                  {aiDecision && (
                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">AI Signal:</span>
                        <span className={`text-xs font-medium ${
                          aiDecision.action === 'BUY' ? 'text-green-600' :
                          aiDecision.action === 'SELL' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {aiDecision.action}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600 dark:text-gray-400">Confidence:</span>
                        <span className={`text-xs font-medium ${getConfidenceColor(aiDecision.confidence)}`}>
                          {aiDecision.confidence.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Market Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Trading Session Info */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-4">
            <Clock className="w-5 h-5 text-blue-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Trading Sessions
            </h3>
          </div>
          
          <div className="space-y-3">
            {['ASIAN', 'LONDON', 'NEW_YORK'].map(session => {
              const isActive = symbols.some(symbol => {
                const state = marketStates.get(symbol);
                return state?.session.name === session;
              });
              
              return (
                <div key={session} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {session.replace('_', ' ')}
                  </span>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      isActive ? 'bg-green-500' : 'bg-gray-300'
                    }`}></div>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {isActive ? 'Active' : 'Closed'}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Market Sentiment */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-4">
            <Activity className="w-5 h-5 text-purple-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Market Sentiment
            </h3>
          </div>
          
          <div className="space-y-3">
            {symbols.map(symbol => {
              const marketState = marketStates.get(symbol);
              if (!marketState) return null;
              
              const sentimentScore = marketState.confidence;
              const sentimentColor = sentimentScore >= 70 ? 'bg-green-500' :
                                   sentimentScore >= 50 ? 'bg-yellow-500' : 'bg-red-500';
              
              return (
                <div key={symbol}>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {symbol}
                    </span>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {sentimentScore.toFixed(0)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${sentimentColor}`}
                      style={{ width: `${sentimentScore}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Risk Overview */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="w-5 h-5 text-orange-500 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Risk Overview
            </h3>
          </div>
          
          <div className="space-y-3">
            {symbols.map(symbol => {
              const marketState = marketStates.get(symbol);
              if (!marketState) return null;
              
              const riskColor = marketState.riskLevel === 'HIGH' ? 'text-red-600' :
                               marketState.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-green-600';
              
              return (
                <div key={symbol} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {symbol}
                  </span>
                  <span className={`text-sm font-medium ${riskColor}`}>
                    {marketState.riskLevel}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Recent Signals */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Trade Signals
          </h3>
        </div>
        
        <div className="p-6">
          {recentSignals.length > 0 ? (
            <div className="space-y-4">
              {recentSignals.map(signal => (
                <div key={signal.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full ${
                      signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {signal.type} {signal.symbol}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Entry: {signal.entry} | SL: {signal.stopLoss} | TP: {signal.takeProfit1}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {signal.confidence.toFixed(1)}% confidence
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      R/R: {signal.riskRewardRatio.toFixed(2)}:1
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                No recent signals. The AI is analyzing market conditions...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketOverview;
