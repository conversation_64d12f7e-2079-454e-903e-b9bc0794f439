// Market State Analysis System
import { TechnicalIndicators, CandleData } from '../technical-analysis/indicators';

export interface MarketState {
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  strength: number; // 0-100
  volatility: 'LOW' | 'MEDIUM' | 'HIGH';
  liquidity: 'LOW' | 'MEDIUM' | 'HIGH';
  momentum: 'STRONG_BULLISH' | 'WEAK_BULLISH' | 'NEUTRAL' | 'WEAK_BEARISH' | 'STRONG_BEARISH';
  session: TradingSession;
  marketPhase: 'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  confidence: number; // 0-100
}

export interface TradingSession {
  name: 'ASIAN' | 'LONDON' | 'NEW_YORK' | 'OVERLAP_LONDON_NY' | 'OVERLAP_ASIAN_LONDON';
  isActive: boolean;
  timeRemaining: number; // minutes
  volatilityExpected: 'LOW' | 'MEDIUM' | 'HIGH';
  liquidityExpected: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface MarketStructure {
  higherHighs: boolean;
  higherLows: boolean;
  lowerHighs: boolean;
  lowerLows: boolean;
  keyLevels: {
    support: number[];
    resistance: number[];
  };
  trendLines: {
    support: TrendLine[];
    resistance: TrendLine[];
  };
}

export interface TrendLine {
  startPrice: number;
  endPrice: number;
  startTime: number;
  endTime: number;
  slope: number;
  strength: number;
  touches: number;
}

export class MarketStateAnalyzer {
  private timeZone: string;

  constructor(timeZone: string = 'UTC') {
    this.timeZone = timeZone;
  }

  // Analyze complete market state
  analyzeMarketState(
    candleData: CandleData[],
    indicators: TechnicalIndicators,
    symbol: string
  ): MarketState {
    const trend = this.analyzeTrend(candleData, indicators);
    const volatility = this.analyzeVolatility(candleData);
    const liquidity = this.analyzeLiquidity(candleData, indicators);
    const momentum = this.analyzeMomentum(indicators, candleData);
    const session = this.getCurrentTradingSession(symbol);
    const marketPhase = this.analyzeMarketPhase(candleData, indicators);
    const riskLevel = this.calculateRiskLevel(volatility, liquidity, trend);
    const confidence = this.calculateConfidence(trend, indicators, candleData);

    return {
      trend: trend.direction,
      strength: trend.strength,
      volatility,
      liquidity,
      momentum,
      session,
      marketPhase,
      riskLevel,
      confidence
    };
  }

  // Analyze market trend
  private analyzeTrend(candleData: CandleData[], indicators: TechnicalIndicators) {
    const recentCandles = candleData.slice(-50); // Last 50 candles
    const structure = this.analyzeMarketStructure(recentCandles);
    
    let bullishSignals = 0;
    let bearishSignals = 0;
    let strength = 0;

    // EMA trend analysis
    const ema = indicators.ema;
    if (ema.length >= 2) {
      const emaSlope = ema[ema.length - 1] - ema[ema.length - 2];
      if (emaSlope > 0) bullishSignals++;
      else bearishSignals++;
    }

    // Price vs EMA
    const currentPrice = recentCandles[recentCandles.length - 1].close;
    const currentEMA = ema[ema.length - 1];
    if (currentPrice > currentEMA) bullishSignals++;
    else bearishSignals++;

    // MACD trend
    if (indicators.macd.MACD > indicators.macd.signal) bullishSignals++;
    else bearishSignals++;

    // Supertrend
    if (indicators.supertrend.trend === 'up') bullishSignals++;
    else bearishSignals++;

    // Market structure
    if (structure.higherHighs && structure.higherLows) bullishSignals += 2;
    if (structure.lowerHighs && structure.lowerLows) bearishSignals += 2;

    // Volume confirmation
    if (currentPrice > indicators.vwap) bullishSignals++;
    else bearishSignals++;

    // Calculate trend direction and strength
    const totalSignals = bullishSignals + bearishSignals;
    let direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
    
    if (Math.abs(bullishSignals - bearishSignals) <= 1) {
      direction = 'SIDEWAYS';
      strength = 30; // Low strength for sideways
    } else if (bullishSignals > bearishSignals) {
      direction = 'BULLISH';
      strength = Math.min((bullishSignals / totalSignals) * 100, 100);
    } else {
      direction = 'BEARISH';
      strength = Math.min((bearishSignals / totalSignals) * 100, 100);
    }

    return { direction, strength };
  }

  // Analyze market structure
  private analyzeMarketStructure(candleData: CandleData[]): MarketStructure {
    const swingHighs: number[] = [];
    const swingLows: number[] = [];

    // Find swing highs and lows
    for (let i = 2; i < candleData.length - 2; i++) {
      const current = candleData[i];
      const prev2 = candleData[i - 2];
      const prev1 = candleData[i - 1];
      const next1 = candleData[i + 1];
      const next2 = candleData[i + 2];

      // Swing high
      if (current.high > prev2.high && current.high > prev1.high &&
          current.high > next1.high && current.high > next2.high) {
        swingHighs.push(current.high);
      }

      // Swing low
      if (current.low < prev2.low && current.low < prev1.low &&
          current.low < next1.low && current.low < next2.low) {
        swingLows.push(current.low);
      }
    }

    // Analyze structure
    const higherHighs = this.isSequenceIncreasing(swingHighs.slice(-3));
    const higherLows = this.isSequenceIncreasing(swingLows.slice(-3));
    const lowerHighs = this.isSequenceDecreasing(swingHighs.slice(-3));
    const lowerLows = this.isSequenceDecreasing(swingLows.slice(-3));

    return {
      higherHighs,
      higherLows,
      lowerHighs,
      lowerLows,
      keyLevels: {
        support: swingLows.slice(-5),
        resistance: swingHighs.slice(-5)
      },
      trendLines: {
        support: [],
        resistance: []
      }
    };
  }

  private isSequenceIncreasing(sequence: number[]): boolean {
    if (sequence.length < 2) return false;
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] <= sequence[i - 1]) return false;
    }
    return true;
  }

  private isSequenceDecreasing(sequence: number[]): boolean {
    if (sequence.length < 2) return false;
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] >= sequence[i - 1]) return false;
    }
    return true;
  }

  // Analyze volatility
  private analyzeVolatility(candleData: CandleData[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    const recentCandles = candleData.slice(-20);
    const ranges = recentCandles.map(candle => 
      ((candle.high - candle.low) / candle.close) * 100
    );
    
    const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length;
    
    if (avgRange < 0.5) return 'LOW';
    if (avgRange < 1.5) return 'MEDIUM';
    return 'HIGH';
  }

  // Analyze liquidity
  private analyzeLiquidity(candleData: CandleData[], indicators: TechnicalIndicators): 'LOW' | 'MEDIUM' | 'HIGH' {
    const recentCandles = candleData.slice(-20);
    const avgVolume = recentCandles.reduce((sum, candle) => sum + candle.volume, 0) / recentCandles.length;
    const currentVolume = recentCandles[recentCandles.length - 1].volume;
    
    const volumeRatio = currentVolume / avgVolume;
    
    // Also consider volume profile
    const volumeProfile = indicators.volumeProfile;
    const pocStrength = volumeProfile.levels.length > 0 ? 
      Math.max(...volumeProfile.levels.map(l => l.percentage)) : 0;
    
    const liquidityScore = (volumeRatio * 0.7) + (pocStrength / 100 * 0.3);
    
    if (liquidityScore < 0.8) return 'LOW';
    if (liquidityScore < 1.5) return 'MEDIUM';
    return 'HIGH';
  }

  // Analyze momentum
  private analyzeMomentum(indicators: TechnicalIndicators, candleData: CandleData[]): 
    'STRONG_BULLISH' | 'WEAK_BULLISH' | 'NEUTRAL' | 'WEAK_BEARISH' | 'STRONG_BEARISH' {
    
    let momentumScore = 0;
    
    // RSI momentum
    if (indicators.rsi > 60) momentumScore += 2;
    else if (indicators.rsi > 50) momentumScore += 1;
    else if (indicators.rsi < 40) momentumScore -= 2;
    else if (indicators.rsi < 50) momentumScore -= 1;
    
    // MACD momentum
    if (indicators.macd.histogram > 0) {
      momentumScore += indicators.macd.histogram > 0.001 ? 2 : 1;
    } else {
      momentumScore += indicators.macd.histogram < -0.001 ? -2 : -1;
    }
    
    // Price momentum (last 5 candles)
    const recentCandles = candleData.slice(-5);
    const priceChange = (recentCandles[recentCandles.length - 1].close - recentCandles[0].close) / recentCandles[0].close;
    
    if (priceChange > 0.01) momentumScore += 2; // 1% gain
    else if (priceChange > 0.005) momentumScore += 1; // 0.5% gain
    else if (priceChange < -0.01) momentumScore -= 2; // 1% loss
    else if (priceChange < -0.005) momentumScore -= 1; // 0.5% loss
    
    // Volume momentum
    const recentVolumes = candleData.slice(-5).map(c => c.volume);
    const avgRecentVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
    const prevAvgVolume = candleData.slice(-10, -5).map(c => c.volume).reduce((sum, vol) => sum + vol, 0) / 5;
    
    if (avgRecentVolume > prevAvgVolume * 1.2) momentumScore += 1;
    else if (avgRecentVolume < prevAvgVolume * 0.8) momentumScore -= 1;
    
    // Classify momentum
    if (momentumScore >= 5) return 'STRONG_BULLISH';
    if (momentumScore >= 2) return 'WEAK_BULLISH';
    if (momentumScore <= -5) return 'STRONG_BEARISH';
    if (momentumScore <= -2) return 'WEAK_BEARISH';
    return 'NEUTRAL';
  }

  // Get current trading session
  getCurrentTradingSession(symbol: string): TradingSession {
    const now = new Date();
    const utcHour = now.getUTCHours();
    const utcMinute = now.getUTCMinutes();
    const currentMinutes = utcHour * 60 + utcMinute;

    // Trading session times in UTC
    const sessions = {
      ASIAN: { start: 0, end: 9 * 60 }, // 00:00 - 09:00 UTC
      LONDON: { start: 8 * 60, end: 17 * 60 }, // 08:00 - 17:00 UTC
      NEW_YORK: { start: 13 * 60, end: 22 * 60 }, // 13:00 - 22:00 UTC
    };

    // Determine current session
    let currentSession: TradingSession['name'] = 'ASIAN';
    let isActive = false;
    let timeRemaining = 0;
    let volatilityExpected: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';
    let liquidityExpected: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';

    // Check overlaps first
    if (currentMinutes >= sessions.LONDON.start && currentMinutes <= sessions.NEW_YORK.end) {
      if (currentMinutes >= sessions.NEW_YORK.start && currentMinutes <= sessions.LONDON.end) {
        currentSession = 'OVERLAP_LONDON_NY';
        volatilityExpected = 'HIGH';
        liquidityExpected = 'HIGH';
        isActive = true;
        timeRemaining = sessions.LONDON.end - currentMinutes;
      } else if (currentMinutes >= sessions.LONDON.start && currentMinutes < sessions.NEW_YORK.start) {
        currentSession = 'LONDON';
        volatilityExpected = 'MEDIUM';
        liquidityExpected = 'HIGH';
        isActive = true;
        timeRemaining = sessions.NEW_YORK.start - currentMinutes;
      } else {
        currentSession = 'NEW_YORK';
        volatilityExpected = 'MEDIUM';
        liquidityExpected = 'MEDIUM';
        isActive = true;
        timeRemaining = sessions.NEW_YORK.end - currentMinutes;
      }
    } else if (currentMinutes >= sessions.ASIAN.start && currentMinutes < sessions.LONDON.start) {
      if (currentMinutes >= (sessions.LONDON.start - 60)) {
        currentSession = 'OVERLAP_ASIAN_LONDON';
        volatilityExpected = 'MEDIUM';
        liquidityExpected = 'MEDIUM';
      } else {
        currentSession = 'ASIAN';
        volatilityExpected = 'LOW';
        liquidityExpected = 'LOW';
      }
      isActive = true;
      timeRemaining = sessions.LONDON.start - currentMinutes;
    }

    // Adjust for forex pairs
    if (symbol.includes('JPY') && currentSession === 'ASIAN') {
      volatilityExpected = 'MEDIUM';
      liquidityExpected = 'MEDIUM';
    }

    return {
      name: currentSession,
      isActive,
      timeRemaining,
      volatilityExpected,
      liquidityExpected
    };
  }

  // Analyze market phase (Wyckoff method)
  private analyzeMarketPhase(candleData: CandleData[], indicators: TechnicalIndicators): 
    'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN' {
    
    const recentCandles = candleData.slice(-100);
    const priceRange = Math.max(...recentCandles.map(c => c.high)) - Math.min(...recentCandles.map(c => c.low));
    const currentPrice = recentCandles[recentCandles.length - 1].close;
    
    // Volume analysis
    const avgVolume = recentCandles.reduce((sum, c) => sum + c.volume, 0) / recentCandles.length;
    const recentVolume = recentCandles.slice(-10).reduce((sum, c) => sum + c.volume, 0) / 10;
    
    // Price position in range
    const minPrice = Math.min(...recentCandles.map(c => c.low));
    const maxPrice = Math.max(...recentCandles.map(c => c.high));
    const pricePosition = (currentPrice - minPrice) / (maxPrice - minPrice);
    
    // Volatility analysis
    const volatility = this.analyzeVolatility(recentCandles);
    
    // Determine phase
    if (volatility === 'LOW' && recentVolume > avgVolume && pricePosition < 0.3) {
      return 'ACCUMULATION';
    } else if (indicators.supertrend.trend === 'up' && pricePosition > 0.3 && pricePosition < 0.7) {
      return 'MARKUP';
    } else if (volatility === 'LOW' && recentVolume > avgVolume && pricePosition > 0.7) {
      return 'DISTRIBUTION';
    } else {
      return 'MARKDOWN';
    }
  }

  // Calculate risk level
  private calculateRiskLevel(
    volatility: 'LOW' | 'MEDIUM' | 'HIGH',
    liquidity: 'LOW' | 'MEDIUM' | 'HIGH',
    trend: { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'; strength: number }
  ): 'LOW' | 'MEDIUM' | 'HIGH' {
    let riskScore = 0;
    
    // Volatility risk
    if (volatility === 'HIGH') riskScore += 3;
    else if (volatility === 'MEDIUM') riskScore += 2;
    else riskScore += 1;
    
    // Liquidity risk (inverse)
    if (liquidity === 'LOW') riskScore += 3;
    else if (liquidity === 'MEDIUM') riskScore += 2;
    else riskScore += 1;
    
    // Trend clarity risk
    if (trend.direction === 'SIDEWAYS') riskScore += 2;
    else if (trend.strength < 60) riskScore += 1;
    
    if (riskScore <= 3) return 'LOW';
    if (riskScore <= 5) return 'MEDIUM';
    return 'HIGH';
  }

  // Calculate confidence in analysis
  private calculateConfidence(
    trend: { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'; strength: number },
    indicators: TechnicalIndicators,
    candleData: CandleData[]
  ): number {
    let confidence = 0;
    
    // Trend strength contributes to confidence
    confidence += trend.strength * 0.3;
    
    // Multiple timeframe confirmation (simulated)
    confidence += 20;
    
    // Volume confirmation
    const recentVolumes = candleData.slice(-5).map(c => c.volume);
    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
    const currentVolume = recentVolumes[recentVolumes.length - 1];
    
    if (currentVolume > avgVolume * 1.2) confidence += 15;
    else if (currentVolume > avgVolume) confidence += 10;
    
    // Indicator alignment
    let alignedIndicators = 0;
    const currentPrice = candleData[candleData.length - 1].close;
    
    if ((trend.direction === 'BULLISH' && indicators.rsi > 50) || 
        (trend.direction === 'BEARISH' && indicators.rsi < 50)) alignedIndicators++;
    
    if ((trend.direction === 'BULLISH' && indicators.macd.MACD > indicators.macd.signal) ||
        (trend.direction === 'BEARISH' && indicators.macd.MACD < indicators.macd.signal)) alignedIndicators++;
    
    if ((trend.direction === 'BULLISH' && currentPrice > indicators.vwap) ||
        (trend.direction === 'BEARISH' && currentPrice < indicators.vwap)) alignedIndicators++;
    
    confidence += (alignedIndicators / 3) * 25;
    
    return Math.min(Math.max(confidence, 0), 100);
  }
}
