// Technical Analysis Indicators Library
import { 
  EMA, 
  RSI, 
  MACD, 
  BollingerBands,
  SMA,
  StochasticOscillator,
  ATR,
  ADX
} from 'technicalindicators';

export interface CandleData {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: number;
}

export interface TechnicalIndicators {
  ema: number[];
  rsi: number;
  macd: {
    MACD: number;
    signal: number;
    histogram: number;
  };
  vwap: number;
  supertrend: {
    value: number;
    trend: 'up' | 'down';
  };
  volumeProfile: VolumeProfileData;
  fvg: FairValueGap[];
  choch: ChangeOfCharacter[];
  bos: BreakOfStructure[];
  supportResistance: SupportResistanceLevel[];
  orderBlocks: OrderBlock[];
  breakerBlocks: BreakerBlock[];
}

export interface VolumeProfileData {
  levels: {
    price: number;
    volume: number;
    percentage: number;
  }[];
  poc: number; // Point of Control
  vah: number; // Value Area High
  val: number; // Value Area Low
}

export interface FairValueGap {
  start: number;
  end: number;
  price: number;
  type: 'bullish' | 'bearish';
  filled: boolean;
}

export interface ChangeOfCharacter {
  timestamp: number;
  price: number;
  type: 'bullish' | 'bearish';
  strength: number;
}

export interface BreakOfStructure {
  timestamp: number;
  price: number;
  type: 'bullish' | 'bearish';
  previousHigh: number;
  previousLow: number;
}

export interface SupportResistanceLevel {
  price: number;
  strength: number;
  type: 'support' | 'resistance';
  touches: number;
  lastTouch: number;
}

export interface OrderBlock {
  start: number;
  end: number;
  high: number;
  low: number;
  type: 'bullish' | 'bearish';
  mitigation: boolean;
}

export interface BreakerBlock {
  start: number;
  end: number;
  high: number;
  low: number;
  type: 'bullish' | 'bearish';
  broken: boolean;
}

export class TechnicalAnalysisEngine {
  private data: CandleData[] = [];
  
  constructor(data: CandleData[]) {
    this.data = data;
  }

  // Calculate EMA (Exponential Moving Average)
  calculateEMA(period: number = 20): number[] {
    const closes = this.data.map(d => d.close);
    return EMA.calculate({ period, values: closes });
  }

  // Calculate RSI (Relative Strength Index)
  calculateRSI(period: number = 14): number {
    const closes = this.data.map(d => d.close);
    const rsiValues = RSI.calculate({ period, values: closes });
    return rsiValues[rsiValues.length - 1] || 0;
  }

  // Calculate MACD
  calculateMACD(fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const closes = this.data.map(d => d.close);
    const macdValues = MACD.calculate({
      fastPeriod,
      slowPeriod,
      signalPeriod,
      values: closes
    });
    
    const latest = macdValues[macdValues.length - 1];
    return {
      MACD: latest?.MACD || 0,
      signal: latest?.signal || 0,
      histogram: latest?.histogram || 0
    };
  }

  // Calculate VWAP (Volume Weighted Average Price)
  calculateVWAP(): number {
    let totalVolume = 0;
    let totalVolumePrice = 0;

    for (const candle of this.data) {
      const typicalPrice = (candle.high + candle.low + candle.close) / 3;
      totalVolumePrice += typicalPrice * candle.volume;
      totalVolume += candle.volume;
    }

    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
  }

  // Calculate Supertrend
  calculateSupertrend(period: number = 10, multiplier: number = 3) {
    const atrValues = this.calculateATR(period);
    const closes = this.data.map(d => d.close);
    const highs = this.data.map(d => d.high);
    const lows = this.data.map(d => d.low);
    
    let trend = 'up';
    let supertrend = 0;
    
    if (atrValues.length > 0 && this.data.length > 0) {
      const hl2 = (highs[highs.length - 1] + lows[lows.length - 1]) / 2;
      const atr = atrValues[atrValues.length - 1];
      
      const upperBand = hl2 + (multiplier * atr);
      const lowerBand = hl2 - (multiplier * atr);
      const close = closes[closes.length - 1];
      
      if (close > upperBand) {
        trend = 'up';
        supertrend = lowerBand;
      } else if (close < lowerBand) {
        trend = 'down';
        supertrend = upperBand;
      }
    }
    
    return {
      value: supertrend,
      trend: trend as 'up' | 'down'
    };
  }

  // Calculate ATR (Average True Range)
  private calculateATR(period: number = 14): number[] {
    const input = {
      high: this.data.map(d => d.high),
      low: this.data.map(d => d.low),
      close: this.data.map(d => d.close),
      period
    };
    
    return ATR.calculate(input);
  }

  // Calculate Volume Profile
  calculateVolumeProfile(bins: number = 50): VolumeProfileData {
    if (this.data.length === 0) {
      return {
        levels: [],
        poc: 0,
        vah: 0,
        val: 0
      };
    }

    const minPrice = Math.min(...this.data.map(d => d.low));
    const maxPrice = Math.max(...this.data.map(d => d.high));
    const priceStep = (maxPrice - minPrice) / bins;
    
    const volumeLevels: { price: number; volume: number }[] = [];
    
    for (let i = 0; i < bins; i++) {
      const priceLevel = minPrice + (i * priceStep);
      let volumeAtLevel = 0;
      
      for (const candle of this.data) {
        if (candle.low <= priceLevel && candle.high >= priceLevel) {
          volumeAtLevel += candle.volume;
        }
      }
      
      volumeLevels.push({ price: priceLevel, volume: volumeAtLevel });
    }
    
    const totalVolume = volumeLevels.reduce((sum, level) => sum + level.volume, 0);
    const levels = volumeLevels.map(level => ({
      ...level,
      percentage: totalVolume > 0 ? (level.volume / totalVolume) * 100 : 0
    }));
    
    // Find Point of Control (highest volume level)
    const poc = levels.reduce((max, level) => 
      level.volume > max.volume ? level : max
    ).price;
    
    // Calculate Value Area (70% of volume)
    const sortedByVolume = [...levels].sort((a, b) => b.volume - a.volume);
    let valueAreaVolume = 0;
    const valueAreaLevels = [];
    
    for (const level of sortedByVolume) {
      valueAreaLevels.push(level);
      valueAreaVolume += level.volume;
      if (valueAreaVolume >= totalVolume * 0.7) break;
    }
    
    const valueAreaPrices = valueAreaLevels.map(l => l.price);
    const vah = Math.max(...valueAreaPrices);
    const val = Math.min(...valueAreaPrices);
    
    return {
      levels,
      poc,
      vah,
      val
    };
  }

  // Detect Fair Value Gaps (Enhanced)
  detectFairValueGaps(): FairValueGap[] {
    const gaps: FairValueGap[] = [];

    for (let i = 2; i < this.data.length; i++) {
      const prev = this.data[i - 2];
      const current = this.data[i - 1];
      const next = this.data[i];

      // Enhanced Bullish FVG detection
      if (prev.high < next.low) {
        const gapSize = next.low - prev.high;
        const avgRange = (prev.high - prev.low + current.high - current.low + next.high - next.low) / 3;

        // Only consider significant gaps (at least 20% of average range)
        if (gapSize > avgRange * 0.2) {
          gaps.push({
            start: i - 2,
            end: i,
            price: (prev.high + next.low) / 2,
            type: 'bullish',
            filled: this.isGapFilled(prev.high, next.low, i)
          });
        }
      }

      // Enhanced Bearish FVG detection
      if (prev.low > next.high) {
        const gapSize = prev.low - next.high;
        const avgRange = (prev.high - prev.low + current.high - current.low + next.high - next.low) / 3;

        // Only consider significant gaps (at least 20% of average range)
        if (gapSize > avgRange * 0.2) {
          gaps.push({
            start: i - 2,
            end: i,
            price: (prev.low + next.high) / 2,
            type: 'bearish',
            filled: this.isGapFilled(next.high, prev.low, i)
          });
        }
      }
    }

    return gaps;
  }

  private isGapFilled(gapLow: number, gapHigh: number, gapIndex: number): boolean {
    // Check if the gap has been filled by subsequent price action
    for (let i = gapIndex + 1; i < this.data.length; i++) {
      const candle = this.data[i];
      if (candle.low <= gapLow && candle.high >= gapHigh) {
        return true;
      }
    }
    return false;
  }

  // Detect Change of Character (CHoCH) - Enhanced
  detectChangeOfCharacter(): ChangeOfCharacter[] {
    const chochs: ChangeOfCharacter[] = [];
    const swingPoints = this.identifySwingPoints();

    if (swingPoints.length < 4) return chochs;

    let currentTrend: 'bullish' | 'bearish' | null = null;

    for (let i = 2; i < swingPoints.length; i++) {
      const prevSwing = swingPoints[i - 2];
      const currentSwing = swingPoints[i - 1];
      const nextSwing = swingPoints[i];

      // Determine initial trend if not set
      if (currentTrend === null) {
        if (currentSwing.type === 'high' && currentSwing.price > prevSwing.price) {
          currentTrend = 'bullish';
        } else if (currentSwing.type === 'low' && currentSwing.price < prevSwing.price) {
          currentTrend = 'bearish';
        }
        continue;
      }

      // Detect CHoCH - Change of Character
      if (currentTrend === 'bullish') {
        // Look for lower high followed by break of previous low
        if (currentSwing.type === 'high' && currentSwing.price < prevSwing.price) {
          const previousLow = this.findPreviousSwingLow(swingPoints, i - 1);
          if (previousLow && nextSwing.type === 'low' && nextSwing.price < previousLow.price) {
            chochs.push({
              timestamp: nextSwing.timestamp,
              price: nextSwing.price,
              type: 'bearish',
              strength: this.calculateCHoCHStrength(prevSwing, currentSwing, nextSwing)
            });
            currentTrend = 'bearish';
          }
        }
      } else if (currentTrend === 'bearish') {
        // Look for higher low followed by break of previous high
        if (currentSwing.type === 'low' && currentSwing.price > prevSwing.price) {
          const previousHigh = this.findPreviousSwingHigh(swingPoints, i - 1);
          if (previousHigh && nextSwing.type === 'high' && nextSwing.price > previousHigh.price) {
            chochs.push({
              timestamp: nextSwing.timestamp,
              price: nextSwing.price,
              type: 'bullish',
              strength: this.calculateCHoCHStrength(prevSwing, currentSwing, nextSwing)
            });
            currentTrend = 'bullish';
          }
        }
      }
    }

    return chochs;
  }

  private identifySwingPoints(): Array<{type: 'high' | 'low', price: number, timestamp: number, index: number}> {
    const swingPoints = [];
    const lookback = 5;

    for (let i = lookback; i < this.data.length - lookback; i++) {
      const current = this.data[i];
      let isSwingHigh = true;
      let isSwingLow = true;

      // Check if current point is a swing high or low
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j === i) continue;

        if (this.data[j].high >= current.high) isSwingHigh = false;
        if (this.data[j].low <= current.low) isSwingLow = false;
      }

      if (isSwingHigh) {
        swingPoints.push({
          type: 'high' as const,
          price: current.high,
          timestamp: current.timestamp,
          index: i
        });
      }

      if (isSwingLow) {
        swingPoints.push({
          type: 'low' as const,
          price: current.low,
          timestamp: current.timestamp,
          index: i
        });
      }
    }

    return swingPoints.sort((a, b) => a.index - b.index);
  }

  private findPreviousSwingLow(swingPoints: any[], currentIndex: number) {
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (swingPoints[i].type === 'low') {
        return swingPoints[i];
      }
    }
    return null;
  }

  private findPreviousSwingHigh(swingPoints: any[], currentIndex: number) {
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (swingPoints[i].type === 'high') {
        return swingPoints[i];
      }
    }
    return null;
  }

  private calculateCHoCHStrength(prev: any, current: any, next: any): number {
    const priceMove = Math.abs(next.price - prev.price);
    const avgPrice = (prev.price + current.price + next.price) / 3;
    return Math.min(priceMove / avgPrice, 1); // Normalize to 0-1
  }

  // Detect Break of Structure (BOS) - Enhanced
  detectBreakOfStructure(): BreakOfStructure[] {
    const bosEvents: BreakOfStructure[] = [];
    const swingPoints = this.identifySwingPoints();

    if (swingPoints.length < 3) return bosEvents;

    for (let i = 2; i < swingPoints.length; i++) {
      const currentSwing = swingPoints[i];
      const current = this.data[currentSwing.index];

      // Find the most recent significant swing high and low
      const recentHigh = this.findMostRecentSwingHigh(swingPoints, i);
      const recentLow = this.findMostRecentSwingLow(swingPoints, i);

      if (!recentHigh || !recentLow) continue;

      // Bullish BOS: break above recent swing high with volume confirmation
      if (currentSwing.type === 'high' && currentSwing.price > recentHigh.price) {
        const volumeConfirmation = this.checkVolumeConfirmation(currentSwing.index, 'bullish');

        if (volumeConfirmation) {
          bosEvents.push({
            timestamp: currentSwing.timestamp,
            price: currentSwing.price,
            type: 'bullish',
            previousHigh: recentHigh.price,
            previousLow: recentLow.price
          });
        }
      }

      // Bearish BOS: break below recent swing low with volume confirmation
      if (currentSwing.type === 'low' && currentSwing.price < recentLow.price) {
        const volumeConfirmation = this.checkVolumeConfirmation(currentSwing.index, 'bearish');

        if (volumeConfirmation) {
          bosEvents.push({
            timestamp: currentSwing.timestamp,
            price: currentSwing.price,
            type: 'bearish',
            previousHigh: recentHigh.price,
            previousLow: recentLow.price
          });
        }
      }
    }

    return bosEvents;
  }

  private findMostRecentSwingHigh(swingPoints: any[], currentIndex: number) {
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (swingPoints[i].type === 'high') {
        return swingPoints[i];
      }
    }
    return null;
  }

  private findMostRecentSwingLow(swingPoints: any[], currentIndex: number) {
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (swingPoints[i].type === 'low') {
        return swingPoints[i];
      }
    }
    return null;
  }

  private checkVolumeConfirmation(candleIndex: number, direction: 'bullish' | 'bearish'): boolean {
    if (candleIndex < 10) return false;

    const currentVolume = this.data[candleIndex].volume;
    const avgVolume = this.data.slice(candleIndex - 10, candleIndex)
      .reduce((sum, candle) => sum + candle.volume, 0) / 10;

    // Volume should be at least 20% above average for confirmation
    return currentVolume > avgVolume * 1.2;
  }

  // Calculate all indicators
  calculateAllIndicators(): TechnicalIndicators {
    return {
      ema: this.calculateEMA(),
      rsi: this.calculateRSI(),
      macd: this.calculateMACD(),
      vwap: this.calculateVWAP(),
      supertrend: this.calculateSupertrend(),
      volumeProfile: this.calculateVolumeProfile(),
      fvg: this.detectFairValueGaps(),
      choch: this.detectChangeOfCharacter(),
      bos: this.detectBreakOfStructure(),
      supportResistance: this.detectSupportResistance(),
      orderBlocks: this.detectOrderBlocks(),
      breakerBlocks: this.detectBreakerBlocks()
    };
  }

  // Detect Support and Resistance levels
  private detectSupportResistance(): SupportResistanceLevel[] {
    const levels: SupportResistanceLevel[] = [];
    const tolerance = 0.001; // 0.1% tolerance
    
    for (let i = 2; i < this.data.length - 2; i++) {
      const current = this.data[i];
      const prev2 = this.data[i - 2];
      const prev1 = this.data[i - 1];
      const next1 = this.data[i + 1];
      const next2 = this.data[i + 2];
      
      // Resistance: local high
      if (current.high > prev2.high && current.high > prev1.high && 
          current.high > next1.high && current.high > next2.high) {
        levels.push({
          price: current.high,
          strength: this.calculateLevelStrength(current.high, 'resistance'),
          type: 'resistance',
          touches: 1,
          lastTouch: current.timestamp
        });
      }
      
      // Support: local low
      if (current.low < prev2.low && current.low < prev1.low && 
          current.low < next1.low && current.low < next2.low) {
        levels.push({
          price: current.low,
          strength: this.calculateLevelStrength(current.low, 'support'),
          type: 'support',
          touches: 1,
          lastTouch: current.timestamp
        });
      }
    }
    
    return this.consolidateLevels(levels, tolerance);
  }

  private calculateLevelStrength(price: number, type: 'support' | 'resistance'): number {
    let touches = 0;
    const tolerance = price * 0.001; // 0.1% tolerance
    
    for (const candle of this.data) {
      if (type === 'resistance' && Math.abs(candle.high - price) <= tolerance) {
        touches++;
      } else if (type === 'support' && Math.abs(candle.low - price) <= tolerance) {
        touches++;
      }
    }
    
    return Math.min(touches / 5, 1); // Normalize to 0-1
  }

  private consolidateLevels(levels: SupportResistanceLevel[], tolerance: number): SupportResistanceLevel[] {
    const consolidated: SupportResistanceLevel[] = [];
    
    for (const level of levels) {
      const existing = consolidated.find(l => 
        Math.abs(l.price - level.price) / level.price <= tolerance && l.type === level.type
      );
      
      if (existing) {
        existing.touches += level.touches;
        existing.strength = Math.max(existing.strength, level.strength);
        existing.lastTouch = Math.max(existing.lastTouch, level.lastTouch);
      } else {
        consolidated.push(level);
      }
    }
    
    return consolidated.sort((a, b) => b.strength - a.strength);
  }

  // Detect Order Blocks - Enhanced
  private detectOrderBlocks(): OrderBlock[] {
    const orderBlocks: OrderBlock[] = [];
    const swingPoints = this.identifySwingPoints();

    for (let i = 0; i < swingPoints.length; i++) {
      const swing = swingPoints[i];
      const orderBlock = this.analyzeOrderBlock(swing);

      if (orderBlock) {
        orderBlocks.push(orderBlock);
      }
    }

    return orderBlocks;
  }

  private analyzeOrderBlock(swing: any): OrderBlock | null {
    const swingIndex = swing.index;

    // Look for consolidation before the swing
    const consolidationPeriod = this.findConsolidationPeriod(swingIndex);
    if (!consolidationPeriod) return null;

    // Check for strong move after the swing
    const strongMove = this.detectStrongMoveAfterSwing(swingIndex, swing.type);
    if (!strongMove) return null;

    // Determine order block type based on swing type and subsequent move
    const orderBlockType = this.determineOrderBlockType(swing, strongMove);

    return {
      start: consolidationPeriod.start,
      end: consolidationPeriod.end,
      high: consolidationPeriod.high,
      low: consolidationPeriod.low,
      type: orderBlockType,
      mitigation: this.checkOrderBlockMitigation(consolidationPeriod, swingIndex)
    };
  }

  private findConsolidationPeriod(swingIndex: number): {start: number, end: number, high: number, low: number} | null {
    const lookback = 10;
    const startIndex = Math.max(0, swingIndex - lookback);
    const consolidationCandles = this.data.slice(startIndex, swingIndex);

    if (consolidationCandles.length < 3) return null;

    // Check if price range is relatively small (consolidation)
    const high = Math.max(...consolidationCandles.map(c => c.high));
    const low = Math.min(...consolidationCandles.map(c => c.low));
    const range = high - low;
    const avgPrice = (high + low) / 2;

    // Range should be less than 1% of average price for consolidation
    if (range / avgPrice > 0.01) return null;

    return {
      start: startIndex,
      end: swingIndex,
      high,
      low
    };
  }

  private detectStrongMoveAfterSwing(swingIndex: number, swingType: 'high' | 'low'): {direction: 'up' | 'down', strength: number} | null {
    const lookAhead = 10;
    const endIndex = Math.min(this.data.length, swingIndex + lookAhead);
    const futureCandles = this.data.slice(swingIndex, endIndex);

    if (futureCandles.length < 3) return null;

    const startPrice = futureCandles[0].close;
    const endPrice = futureCandles[futureCandles.length - 1].close;
    const priceChange = (endPrice - startPrice) / startPrice;

    // Strong move threshold: 1.5%
    if (Math.abs(priceChange) < 0.015) return null;

    return {
      direction: priceChange > 0 ? 'up' : 'down',
      strength: Math.abs(priceChange)
    };
  }

  private determineOrderBlockType(swing: any, strongMove: any): 'bullish' | 'bearish' {
    // If swing is low and strong move is up, it's a bullish order block
    if (swing.type === 'low' && strongMove.direction === 'up') {
      return 'bullish';
    }

    // If swing is high and strong move is down, it's a bearish order block
    if (swing.type === 'high' && strongMove.direction === 'down') {
      return 'bearish';
    }

    // Default based on strong move direction
    return strongMove.direction === 'up' ? 'bullish' : 'bearish';
  }

  private checkOrderBlockMitigation(consolidationPeriod: any, swingIndex: number): boolean {
    // Check if price has returned to the order block area after the swing
    for (let i = swingIndex + 1; i < this.data.length; i++) {
      const candle = this.data[i];

      // If price overlaps with the order block area, it's mitigated
      if (candle.low <= consolidationPeriod.high && candle.high >= consolidationPeriod.low) {
        return true;
      }
    }

    return false;
  }

  // Detect Breaker Blocks - Enhanced
  private detectBreakerBlocks(): BreakerBlock[] {
    const breakerBlocks: BreakerBlock[] = [];
    const orderBlocks = this.detectOrderBlocks();

    for (const orderBlock of orderBlocks) {
      const breakerBlock = this.analyzeBreakerBlock(orderBlock);
      if (breakerBlock) {
        breakerBlocks.push(breakerBlock);
      }
    }

    return breakerBlocks;
  }

  private analyzeBreakerBlock(orderBlock: OrderBlock): BreakerBlock | null {
    // Find where the order block was broken
    const breakPoint = this.findOrderBlockBreak(orderBlock);
    if (!breakPoint) return null;

    // Check for retest after break
    const retestPoint = this.findRetestAfterBreak(orderBlock, breakPoint);
    if (!retestPoint) return null;

    // Confirm the breaker block with volume analysis
    const volumeConfirmation = this.confirmBreakerBlockWithVolume(breakPoint, retestPoint);
    if (!volumeConfirmation) return null;

    return {
      start: orderBlock.start,
      end: orderBlock.end,
      high: orderBlock.high,
      low: orderBlock.low,
      type: orderBlock.type === 'bullish' ? 'bearish' : 'bullish', // Inverse type
      broken: true
    };
  }

  private findOrderBlockBreak(orderBlock: OrderBlock): {index: number, price: number} | null {
    for (let i = orderBlock.end + 1; i < this.data.length; i++) {
      const candle = this.data[i];

      // Check for significant break with volume
      if (orderBlock.type === 'bullish') {
        // Bullish order block broken when price goes below with strong volume
        if (candle.close < orderBlock.low && candle.volume > this.getAverageVolume(i)) {
          return {index: i, price: candle.close};
        }
      } else {
        // Bearish order block broken when price goes above with strong volume
        if (candle.close > orderBlock.high && candle.volume > this.getAverageVolume(i)) {
          return {index: i, price: candle.close};
        }
      }
    }

    return null;
  }

  private findRetestAfterBreak(orderBlock: OrderBlock, breakPoint: {index: number, price: number}): {index: number, price: number} | null {
    const maxLookAhead = 20;
    const endIndex = Math.min(this.data.length, breakPoint.index + maxLookAhead);

    for (let i = breakPoint.index + 1; i < endIndex; i++) {
      const candle = this.data[i];

      // Look for retest of the broken order block area
      if (orderBlock.type === 'bullish') {
        // After bullish OB is broken, look for retest from below
        if (candle.high >= orderBlock.low && candle.high <= orderBlock.high) {
          return {index: i, price: candle.high};
        }
      } else {
        // After bearish OB is broken, look for retest from above
        if (candle.low <= orderBlock.high && candle.low >= orderBlock.low) {
          return {index: i, price: candle.low};
        }
      }
    }

    return null;
  }

  private confirmBreakerBlockWithVolume(breakPoint: any, retestPoint: any): boolean {
    const breakVolume = this.data[breakPoint.index].volume;
    const retestVolume = this.data[retestPoint.index].volume;
    const avgVolume = this.getAverageVolume(breakPoint.index);

    // Break should have high volume, retest can have lower volume
    return breakVolume > avgVolume * 1.3 && retestVolume > avgVolume * 0.8;
  }

  private getAverageVolume(index: number): number {
    const lookback = 20;
    const startIndex = Math.max(0, index - lookback);
    const volumes = this.data.slice(startIndex, index).map(c => c.volume);

    if (volumes.length === 0) return 0;

    return volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  }
}
