// Technical Analysis Indicators Library
import { 
  EMA, 
  RSI, 
  MACD, 
  BollingerBands,
  SMA,
  StochasticOscillator,
  ATR,
  ADX
} from 'technicalindicators';

export interface CandleData {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: number;
}

export interface TechnicalIndicators {
  ema: number[];
  rsi: number;
  macd: {
    MACD: number;
    signal: number;
    histogram: number;
  };
  vwap: number;
  supertrend: {
    value: number;
    trend: 'up' | 'down';
  };
  volumeProfile: VolumeProfileData;
  fvg: FairValueGap[];
  choch: ChangeOfCharacter[];
  bos: BreakOfStructure[];
  supportResistance: SupportResistanceLevel[];
  orderBlocks: OrderBlock[];
  breakerBlocks: BreakerBlock[];
}

export interface VolumeProfileData {
  levels: {
    price: number;
    volume: number;
    percentage: number;
  }[];
  poc: number; // Point of Control
  vah: number; // Value Area High
  val: number; // Value Area Low
}

export interface FairValueGap {
  start: number;
  end: number;
  price: number;
  type: 'bullish' | 'bearish';
  filled: boolean;
}

export interface ChangeOfCharacter {
  timestamp: number;
  price: number;
  type: 'bullish' | 'bearish';
  strength: number;
}

export interface BreakOfStructure {
  timestamp: number;
  price: number;
  type: 'bullish' | 'bearish';
  previousHigh: number;
  previousLow: number;
}

export interface SupportResistanceLevel {
  price: number;
  strength: number;
  type: 'support' | 'resistance';
  touches: number;
  lastTouch: number;
}

export interface OrderBlock {
  start: number;
  end: number;
  high: number;
  low: number;
  type: 'bullish' | 'bearish';
  mitigation: boolean;
}

export interface BreakerBlock {
  start: number;
  end: number;
  high: number;
  low: number;
  type: 'bullish' | 'bearish';
  broken: boolean;
}

export class TechnicalAnalysisEngine {
  private data: CandleData[] = [];
  
  constructor(data: CandleData[]) {
    this.data = data;
  }

  // Calculate EMA (Exponential Moving Average)
  calculateEMA(period: number = 20): number[] {
    const closes = this.data.map(d => d.close);
    return EMA.calculate({ period, values: closes });
  }

  // Calculate RSI (Relative Strength Index)
  calculateRSI(period: number = 14): number {
    const closes = this.data.map(d => d.close);
    const rsiValues = RSI.calculate({ period, values: closes });
    return rsiValues[rsiValues.length - 1] || 0;
  }

  // Calculate MACD
  calculateMACD(fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const closes = this.data.map(d => d.close);
    const macdValues = MACD.calculate({
      fastPeriod,
      slowPeriod,
      signalPeriod,
      values: closes
    });
    
    const latest = macdValues[macdValues.length - 1];
    return {
      MACD: latest?.MACD || 0,
      signal: latest?.signal || 0,
      histogram: latest?.histogram || 0
    };
  }

  // Calculate VWAP (Volume Weighted Average Price)
  calculateVWAP(): number {
    let totalVolume = 0;
    let totalVolumePrice = 0;

    for (const candle of this.data) {
      const typicalPrice = (candle.high + candle.low + candle.close) / 3;
      totalVolumePrice += typicalPrice * candle.volume;
      totalVolume += candle.volume;
    }

    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
  }

  // Calculate Supertrend
  calculateSupertrend(period: number = 10, multiplier: number = 3) {
    const atrValues = this.calculateATR(period);
    const closes = this.data.map(d => d.close);
    const highs = this.data.map(d => d.high);
    const lows = this.data.map(d => d.low);
    
    let trend = 'up';
    let supertrend = 0;
    
    if (atrValues.length > 0 && this.data.length > 0) {
      const hl2 = (highs[highs.length - 1] + lows[lows.length - 1]) / 2;
      const atr = atrValues[atrValues.length - 1];
      
      const upperBand = hl2 + (multiplier * atr);
      const lowerBand = hl2 - (multiplier * atr);
      const close = closes[closes.length - 1];
      
      if (close > upperBand) {
        trend = 'up';
        supertrend = lowerBand;
      } else if (close < lowerBand) {
        trend = 'down';
        supertrend = upperBand;
      }
    }
    
    return {
      value: supertrend,
      trend: trend as 'up' | 'down'
    };
  }

  // Calculate ATR (Average True Range)
  private calculateATR(period: number = 14): number[] {
    const input = {
      high: this.data.map(d => d.high),
      low: this.data.map(d => d.low),
      close: this.data.map(d => d.close),
      period
    };
    
    return ATR.calculate(input);
  }

  // Calculate Volume Profile
  calculateVolumeProfile(bins: number = 50): VolumeProfileData {
    if (this.data.length === 0) {
      return {
        levels: [],
        poc: 0,
        vah: 0,
        val: 0
      };
    }

    const minPrice = Math.min(...this.data.map(d => d.low));
    const maxPrice = Math.max(...this.data.map(d => d.high));
    const priceStep = (maxPrice - minPrice) / bins;
    
    const volumeLevels: { price: number; volume: number }[] = [];
    
    for (let i = 0; i < bins; i++) {
      const priceLevel = minPrice + (i * priceStep);
      let volumeAtLevel = 0;
      
      for (const candle of this.data) {
        if (candle.low <= priceLevel && candle.high >= priceLevel) {
          volumeAtLevel += candle.volume;
        }
      }
      
      volumeLevels.push({ price: priceLevel, volume: volumeAtLevel });
    }
    
    const totalVolume = volumeLevels.reduce((sum, level) => sum + level.volume, 0);
    const levels = volumeLevels.map(level => ({
      ...level,
      percentage: totalVolume > 0 ? (level.volume / totalVolume) * 100 : 0
    }));
    
    // Find Point of Control (highest volume level)
    const poc = levels.reduce((max, level) => 
      level.volume > max.volume ? level : max
    ).price;
    
    // Calculate Value Area (70% of volume)
    const sortedByVolume = [...levels].sort((a, b) => b.volume - a.volume);
    let valueAreaVolume = 0;
    const valueAreaLevels = [];
    
    for (const level of sortedByVolume) {
      valueAreaLevels.push(level);
      valueAreaVolume += level.volume;
      if (valueAreaVolume >= totalVolume * 0.7) break;
    }
    
    const valueAreaPrices = valueAreaLevels.map(l => l.price);
    const vah = Math.max(...valueAreaPrices);
    const val = Math.min(...valueAreaPrices);
    
    return {
      levels,
      poc,
      vah,
      val
    };
  }

  // Detect Fair Value Gaps
  detectFairValueGaps(): FairValueGap[] {
    const gaps: FairValueGap[] = [];
    
    for (let i = 2; i < this.data.length; i++) {
      const prev = this.data[i - 2];
      const current = this.data[i - 1];
      const next = this.data[i];
      
      // Bullish FVG: previous high < next low
      if (prev.high < next.low) {
        gaps.push({
          start: i - 2,
          end: i,
          price: (prev.high + next.low) / 2,
          type: 'bullish',
          filled: false
        });
      }
      
      // Bearish FVG: previous low > next high
      if (prev.low > next.high) {
        gaps.push({
          start: i - 2,
          end: i,
          price: (prev.low + next.high) / 2,
          type: 'bearish',
          filled: false
        });
      }
    }
    
    return gaps;
  }

  // Detect Change of Character (CHoCH)
  detectChangeOfCharacter(): ChangeOfCharacter[] {
    const chochs: ChangeOfCharacter[] = [];
    let currentTrend: 'bullish' | 'bearish' | null = null;
    
    for (let i = 20; i < this.data.length; i++) {
      const recentData = this.data.slice(i - 20, i);
      const highs = recentData.map(d => d.high);
      const lows = recentData.map(d => d.low);
      
      const recentHigh = Math.max(...highs);
      const recentLow = Math.min(...lows);
      const current = this.data[i];
      
      // Detect trend change
      if (currentTrend === 'bullish' && current.close < recentLow) {
        chochs.push({
          timestamp: current.timestamp,
          price: current.close,
          type: 'bearish',
          strength: Math.abs(current.close - recentHigh) / recentHigh
        });
        currentTrend = 'bearish';
      } else if (currentTrend === 'bearish' && current.close > recentHigh) {
        chochs.push({
          timestamp: current.timestamp,
          price: current.close,
          type: 'bullish',
          strength: Math.abs(current.close - recentLow) / recentLow
        });
        currentTrend = 'bullish';
      } else if (currentTrend === null) {
        currentTrend = current.close > (recentHigh + recentLow) / 2 ? 'bullish' : 'bearish';
      }
    }
    
    return chochs;
  }

  // Detect Break of Structure (BOS)
  detectBreakOfStructure(): BreakOfStructure[] {
    const bosEvents: BreakOfStructure[] = [];
    
    for (let i = 50; i < this.data.length; i++) {
      const lookback = this.data.slice(i - 50, i);
      const current = this.data[i];
      
      const previousHigh = Math.max(...lookback.map(d => d.high));
      const previousLow = Math.min(...lookback.map(d => d.low));
      
      // Bullish BOS: break above previous high
      if (current.high > previousHigh) {
        bosEvents.push({
          timestamp: current.timestamp,
          price: current.high,
          type: 'bullish',
          previousHigh,
          previousLow
        });
      }
      
      // Bearish BOS: break below previous low
      if (current.low < previousLow) {
        bosEvents.push({
          timestamp: current.timestamp,
          price: current.low,
          type: 'bearish',
          previousHigh,
          previousLow
        });
      }
    }
    
    return bosEvents;
  }

  // Calculate all indicators
  calculateAllIndicators(): TechnicalIndicators {
    return {
      ema: this.calculateEMA(),
      rsi: this.calculateRSI(),
      macd: this.calculateMACD(),
      vwap: this.calculateVWAP(),
      supertrend: this.calculateSupertrend(),
      volumeProfile: this.calculateVolumeProfile(),
      fvg: this.detectFairValueGaps(),
      choch: this.detectChangeOfCharacter(),
      bos: this.detectBreakOfStructure(),
      supportResistance: this.detectSupportResistance(),
      orderBlocks: this.detectOrderBlocks(),
      breakerBlocks: this.detectBreakerBlocks()
    };
  }

  // Detect Support and Resistance levels
  private detectSupportResistance(): SupportResistanceLevel[] {
    const levels: SupportResistanceLevel[] = [];
    const tolerance = 0.001; // 0.1% tolerance
    
    for (let i = 2; i < this.data.length - 2; i++) {
      const current = this.data[i];
      const prev2 = this.data[i - 2];
      const prev1 = this.data[i - 1];
      const next1 = this.data[i + 1];
      const next2 = this.data[i + 2];
      
      // Resistance: local high
      if (current.high > prev2.high && current.high > prev1.high && 
          current.high > next1.high && current.high > next2.high) {
        levels.push({
          price: current.high,
          strength: this.calculateLevelStrength(current.high, 'resistance'),
          type: 'resistance',
          touches: 1,
          lastTouch: current.timestamp
        });
      }
      
      // Support: local low
      if (current.low < prev2.low && current.low < prev1.low && 
          current.low < next1.low && current.low < next2.low) {
        levels.push({
          price: current.low,
          strength: this.calculateLevelStrength(current.low, 'support'),
          type: 'support',
          touches: 1,
          lastTouch: current.timestamp
        });
      }
    }
    
    return this.consolidateLevels(levels, tolerance);
  }

  private calculateLevelStrength(price: number, type: 'support' | 'resistance'): number {
    let touches = 0;
    const tolerance = price * 0.001; // 0.1% tolerance
    
    for (const candle of this.data) {
      if (type === 'resistance' && Math.abs(candle.high - price) <= tolerance) {
        touches++;
      } else if (type === 'support' && Math.abs(candle.low - price) <= tolerance) {
        touches++;
      }
    }
    
    return Math.min(touches / 5, 1); // Normalize to 0-1
  }

  private consolidateLevels(levels: SupportResistanceLevel[], tolerance: number): SupportResistanceLevel[] {
    const consolidated: SupportResistanceLevel[] = [];
    
    for (const level of levels) {
      const existing = consolidated.find(l => 
        Math.abs(l.price - level.price) / level.price <= tolerance && l.type === level.type
      );
      
      if (existing) {
        existing.touches += level.touches;
        existing.strength = Math.max(existing.strength, level.strength);
        existing.lastTouch = Math.max(existing.lastTouch, level.lastTouch);
      } else {
        consolidated.push(level);
      }
    }
    
    return consolidated.sort((a, b) => b.strength - a.strength);
  }

  // Detect Order Blocks
  private detectOrderBlocks(): OrderBlock[] {
    const orderBlocks: OrderBlock[] = [];
    
    for (let i = 10; i < this.data.length - 5; i++) {
      const current = this.data[i];
      const next5 = this.data.slice(i + 1, i + 6);
      
      // Bullish Order Block: strong buying after consolidation
      const strongBullishMove = next5.some(candle => 
        (candle.close - current.close) / current.close > 0.02 // 2% move
      );
      
      if (strongBullishMove) {
        orderBlocks.push({
          start: i,
          end: i + 5,
          high: current.high,
          low: current.low,
          type: 'bullish',
          mitigation: false
        });
      }
      
      // Bearish Order Block: strong selling after consolidation
      const strongBearishMove = next5.some(candle => 
        (current.close - candle.close) / current.close > 0.02 // 2% move
      );
      
      if (strongBearishMove) {
        orderBlocks.push({
          start: i,
          end: i + 5,
          high: current.high,
          low: current.low,
          type: 'bearish',
          mitigation: false
        });
      }
    }
    
    return orderBlocks;
  }

  // Detect Breaker Blocks
  private detectBreakerBlocks(): BreakerBlock[] {
    const breakerBlocks: BreakerBlock[] = [];
    const orderBlocks = this.detectOrderBlocks();
    
    for (const orderBlock of orderBlocks) {
      // Check if order block was broken and then retested
      for (let i = orderBlock.end + 1; i < this.data.length; i++) {
        const candle = this.data[i];
        
        // Check if order block was broken
        const wasBroken = orderBlock.type === 'bullish' 
          ? candle.low < orderBlock.low
          : candle.high > orderBlock.high;
        
        if (wasBroken) {
          breakerBlocks.push({
            start: orderBlock.start,
            end: orderBlock.end,
            high: orderBlock.high,
            low: orderBlock.low,
            type: orderBlock.type === 'bullish' ? 'bearish' : 'bullish', // Inverse type
            broken: true
          });
          break;
        }
      }
    }
    
    return breakerBlocks;
  }
}
