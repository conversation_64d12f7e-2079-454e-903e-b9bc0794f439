import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "AI Trading Bot - Professional Trading System",
  description: "Advanced AI-powered trading bot with technical analysis, pattern recognition, and risk management",
  icons: {
    icon: [
      { url: '/icon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', sizes: '32x32' }
    ],
    apple: '/icon.svg',
    shortcut: '/favicon.ico'
  },
  keywords: ['AI Trading', 'Forex', 'Technical Analysis', 'TradingView', 'Professional Trading', 'ICT', 'Smart Money'],
  authors: [{ name: 'AI Trading Bot Team' }],
  creator: 'AI Trading Bot',
  publisher: 'AI Trading Bot',
  robots: 'index, follow',
  openGraph: {
    title: 'AI Trading Bot - Professional Trading System',
    description: 'Advanced AI-powered trading bot with TradingView integration',
    type: 'website',
    images: ['/logo.svg']
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
