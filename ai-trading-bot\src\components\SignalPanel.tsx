'use client';

import React from 'react';
import { TradeSignal, RiskManagementEngine } from '@/lib/trading/risk-management';
import { RealTimeQuote } from '@/lib/data-providers/tradingview';
import { TrendingUp, TrendingDown, Clock, Target, Shield } from 'lucide-react';

interface SignalPanelProps {
  signals: TradeSignal[];
  selectedSymbol: string;
  currentQuote?: RealTimeQuote;
  riskManager: RiskManagementEngine;
}

const SignalPanel: React.FC<SignalPanelProps> = ({
  signals,
  selectedSymbol,
  currentQuote,
  riskManager
}) => {
  const filteredSignals = signals.filter(signal => signal.symbol === selectedSymbol);
  const allSignals = signals.slice(0, 10); // Show last 10 signals

  const getSignalIcon = (type: string) => {
    return type === 'BUY' ? 
      <TrendingUp className="w-5 h-5 text-green-500" /> : 
      <TrendingDown className="w-5 h-5 text-red-500" />;
  };

  const getSignalColor = (type: string) => {
    return type === 'BUY' ? 
      'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' : 
      'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const calculatePnL = (signal: TradeSignal, currentPrice?: number) => {
    if (!currentPrice) return null;
    
    const priceDiff = signal.type === 'BUY' ? 
      currentPrice - signal.entry : 
      signal.entry - currentPrice;
    
    const pnlPercentage = (priceDiff / signal.entry) * 100;
    return pnlPercentage;
  };

  return (
    <div className="space-y-6">
      {/* Signal Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <Target className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Signals</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{signals.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingUp className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Buy Signals</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {signals.filter(s => s.type === 'BUY').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingDown className="w-8 h-8 text-red-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Sell Signals</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {signals.filter(s => s.type === 'SELL').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Symbol Signals */}
      {filteredSignals.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedSymbol} Signals
            </h3>
          </div>
          
          <div className="p-6 space-y-4">
            {filteredSignals.map(signal => {
              const pnl = currentQuote ? calculatePnL(signal, currentQuote.price) : null;
              
              return (
                <div key={signal.id} className={`border rounded-lg p-4 ${getSignalColor(signal.type)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {getSignalIcon(signal.type)}
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold text-gray-900 dark:text-white">
                            {signal.type} {signal.symbol}
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {signal.confidence.toFixed(1)}% confidence
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {formatTime(signal.timestamp)}
                        </div>
                      </div>
                    </div>
                    
                    {pnl !== null && (
                      <div className={`text-right ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        <div className="font-semibold">
                          {pnl >= 0 ? '+' : ''}{pnl.toFixed(2)}%
                        </div>
                        <div className="text-xs">P&L</div>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Entry:</span>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {signal.entry.toFixed(5)}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Stop Loss:</span>
                      <div className="font-medium text-red-600">
                        {signal.stopLoss.toFixed(5)}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">TP1:</span>
                      <div className="font-medium text-green-600">
                        {signal.takeProfit1.toFixed(5)}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">TP2:</span>
                      <div className="font-medium text-green-600">
                        {signal.takeProfit2.toFixed(5)}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">R/R:</span>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {signal.riskRewardRatio.toFixed(2)}:1
                      </div>
                    </div>
                  </div>

                  {signal.reasoning.length > 0 && (
                    <div className="mt-4 p-3 bg-white dark:bg-gray-700 rounded border">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Analysis Reasoning:
                      </h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        {signal.reasoning.map((reason, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>{reason}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* All Signals */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Signals (All Pairs)
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Signal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Entry
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Stop Loss
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Take Profit
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  R/R Ratio
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Confidence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Time
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {allSignals.map(signal => (
                <tr key={signal.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getSignalIcon(signal.type)}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {signal.type} {signal.symbol}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {signal.entry.toFixed(5)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                    {signal.stopLoss.toFixed(5)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    {signal.takeProfit1.toFixed(5)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {signal.riskRewardRatio.toFixed(2)}:1
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      signal.confidence >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                      signal.confidence >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                    }`}>
                      {signal.confidence.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatTime(signal.timestamp)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {allSignals.length === 0 && (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No signals generated yet. The AI is analyzing market conditions...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SignalPanel;
