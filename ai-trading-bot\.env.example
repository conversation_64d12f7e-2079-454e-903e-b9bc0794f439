# AI Trading Bot Professional - Environment Configuration Template
# Copy this file to .env.local and fill in your actual values

# ================================
# APPLICATION SETTINGS
# ================================
NEXT_PUBLIC_APP_NAME="AI Trading Bot Professional"
NEXT_PUBLIC_APP_VERSION="2.0.0"
NEXT_PUBLIC_BASE_URL="http://localhost:3000"
NODE_ENV=development

# ================================
# TRADINGVIEW INTEGRATION
# ================================
TRADINGVIEW_API_KEY=your_tradingview_api_key_here
TRADINGVIEW_USERNAME=your_tradingview_username
TRADINGVIEW_PASSWORD=your_tradingview_password

# ================================
# WEBHOOK CONFIGURATION
# ================================
WEBHOOK_ENABLED=true
WEBHOOK_SECRET_KEY=super-secret-webhook-key-2024
WEBHOOK_ALLOWED_IPS=
WEBHOOK_RATE_LIMIT=60

# ================================
# TELEGRAM BOT CONFIGURATION
# ================================
TELEGRAM_ENABLED=true
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ================================
# EMAIL CONFIGURATION
# ================================
EMAIL_ENABLED=true
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM_NAME="AI Trading Bot Professional"
EMAIL_FROM_EMAIL=<EMAIL>
EMAIL_TO=<EMAIL>,<EMAIL>

# ================================
# DATA PROVIDERS
# ================================
TWELVE_DATA_API_KEY=your_twelve_data_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FOREX_FACTORY_ENABLED=true
INVESTING_COM_API_KEY=your_investing_com_key_here

# ================================
# TRADING CONFIGURATION
# ================================
DEMO_MODE=true
DEMO_ACCOUNT_BALANCE=10000
MAX_RISK_PER_TRADE=2
MAX_DAILY_RISK=6
MAX_OPEN_TRADES=5
MIN_RISK_REWARD_RATIO=1.5
DEFAULT_CURRENCY=USD

# ================================
# TECHNICAL ANALYSIS SETTINGS
# ================================
RSI_PERIOD=14
EMA_PERIOD=20
MACD_FAST=12
MACD_SLOW=26
MACD_SIGNAL=9
SUPERTREND_PERIOD=10
SUPERTREND_MULTIPLIER=3
ATR_PERIOD=14

# ================================
# ICT SMART MONEY SETTINGS
# ================================
ORDER_BLOCK_LOOKBACK=50
FVG_MIN_SIZE_PERCENT=0.2
CHOCH_CONFIRMATION_PERIOD=20
BOS_VOLUME_MULTIPLIER=1.2

# ================================
# AI & PATTERN RECOGNITION
# ================================
AI_CONFIDENCE_THRESHOLD=75
HIGH_CONFIDENCE_THRESHOLD=85
MEDIUM_CONFIDENCE_THRESHOLD=75
PATTERN_RECOGNITION_ENABLED=true
AUTO_TRADING_ENABLED=false
ENABLE_AI_LEARNING=true

# ================================
# NOTIFICATION SETTINGS
# ================================
ALERT_COUNTDOWN_TIMES=10,5,3,1
SEND_DAILY_REPORTS=true
SEND_WEEKLY_REPORTS=true

# ================================
# MULTI-LANGUAGE SUPPORT
# ================================
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en,fr
DEFAULT_TIMEZONE=Asia/Riyadh

# ================================
# SECURITY & AUTHENTICATION
# ================================
JWT_SECRET=your-super-secret-jwt-key-2024
ENCRYPTION_KEY=your-32-character-encryption-key
API_SECRET_KEY=your-api-secret-key-2024
CORS_ORIGIN=http://localhost:3000

# ================================
# DATABASE (OPTIONAL)
# ================================
DATABASE_URL=postgresql://username:password@localhost:5432/trading_bot
REDIS_URL=redis://localhost:6379

# ================================
# RATE LIMITING
# ================================
API_RATE_LIMIT=100
EMAIL_RATE_LIMIT=10
TELEGRAM_RATE_LIMIT=30

# ================================
# DEVELOPMENT & DEBUGGING
# ================================
ENABLE_DEBUG_MODE=true
ENABLE_SIMULATION_MODE=true
MOCK_DATA_ENABLED=true
LOG_LEVEL=info

# ================================
# PERFORMANCE MONITORING
# ================================
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true
ENABLE_USAGE_ANALYTICS=false

# ================================
# BACKUP & RECOVERY
# ================================
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30

# ================================
# COMPLIANCE & REGULATION
# ================================
ENABLE_COMPLIANCE_MODE=true
REGULATORY_WARNINGS=true
RISK_DISCLOSURE_REQUIRED=true

# ================================
# SETUP INSTRUCTIONS
# ================================
# 1. Copy this file to .env.local
# 2. Replace all "your_*_here" values with actual credentials
# 3. Get Telegram bot token from @BotFather
# 4. Setup Gmail app password for email notifications
# 5. Register for API keys from data providers
# 6. Generate strong random keys for security settings
# 7. Configure webhook secret for TradingView integration
# 8. Set DEMO_MODE=false only when ready for live trading

# ================================
# IMPORTANT SECURITY NOTES
# ================================
# - Never commit .env.local to version control
# - Use strong, unique passwords and keys
# - Regularly rotate API keys and secrets
# - Enable 2FA on all external accounts
# - Monitor webhook logs for suspicious activity
# - Keep backup of configuration in secure location

# ================================
# SUPPORT & DOCUMENTATION
# ================================
# Telegram Setup: https://core.telegram.org/bots#creating-a-new-bot
# TradingView API: https://www.tradingview.com/rest-api-spec/
# Twelve Data: https://twelvedata.com/docs
# Gmail SMTP: https://support.google.com/accounts/answer/185833
