// Main library exports for AI Trading Bot

// Technical Analysis
export * from './technical-analysis/indicators';

// Trading & Risk Management
export * from './trading/risk-management';

// Market Analysis
export * from './market-analysis/market-state';

// AI & Pattern Recognition
export * from './ai/pattern-recognition';

// Data Providers
export * from './data-providers/tradingview';

// Notifications
export * from './notifications/alert-system';

// Configuration
export * from './config/trading-config';

// Types (if we had a separate types file)
// export * from './types';

// Utility functions
export const formatCurrency = (value: number, decimals: number = 5): string => {
  return value.toFixed(decimals);
};

export const formatPercentage = (value: number, decimals: number = 2): string => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
};

export const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

export const calculatePipValue = (symbol: string, price: number): number => {
  if (symbol.includes('JPY')) {
    return 0.01; // 1 pip = 0.01 for JPY pairs
  }
  return 0.0001; // 1 pip = 0.0001 for other pairs
};

export const getSymbolDecimals = (symbol: string): number => {
  if (symbol.includes('JPY')) return 3;
  if (symbol.startsWith('XAU') || symbol.startsWith('XAG')) return 2;
  if (symbol.includes('OIL') || symbol === 'NATGAS') return 2;
  return 5;
};

export const isMarketOpen = (symbol: string): boolean => {
  const now = new Date();
  const utcHour = now.getUTCHours();
  const dayOfWeek = now.getUTCDay();
  
  // Forex market is open 24/5
  if (symbol.length === 6 && !symbol.startsWith('XAU') && !symbol.startsWith('XAG')) {
    if (dayOfWeek === 0 || (dayOfWeek === 6 && utcHour >= 22)) {
      return false; // Weekend
    }
    if (dayOfWeek === 1 && utcHour < 1) {
      return false; // Monday before 1 AM UTC
    }
    return true;
  }
  
  // Commodities have different hours
  return utcHour >= 1 && utcHour < 23;
};

export const getMarketSession = (): string => {
  const now = new Date();
  const utcHour = now.getUTCHours();
  
  if (utcHour >= 0 && utcHour < 9) return 'ASIAN';
  if (utcHour >= 8 && utcHour < 17) return 'LONDON';
  if (utcHour >= 13 && utcHour < 22) return 'NEW_YORK';
  return 'CLOSED';
};

// Version info
export const VERSION = '1.0.0';
export const BUILD_DATE = new Date().toISOString();

// Default export with all main classes
export default {
  // Classes
  TechnicalAnalysisEngine: require('./technical-analysis/indicators').TechnicalAnalysisEngine,
  RiskManagementEngine: require('./trading/risk-management').RiskManagementEngine,
  MarketStateAnalyzer: require('./market-analysis/market-state').MarketStateAnalyzer,
  AIPatternRecognition: require('./ai/pattern-recognition').AIPatternRecognition,
  TradingViewProvider: require('./data-providers/tradingview').TradingViewProvider,
  AlertSystem: require('./notifications/alert-system').AlertSystem,
  
  // Configuration
  TRADING_CONFIG: require('./config/trading-config').TRADING_CONFIG,
  
  // Utilities
  formatCurrency,
  formatPercentage,
  formatTime,
  calculatePipValue,
  getSymbolDecimals,
  isMarketOpen,
  getMarketSession,
  
  // Version
  VERSION,
  BUILD_DATE,
};
