{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function Home() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    // Simulate initialization\n    const timer = setTimeout(() => {\n      setIsInitialized(true);\n    }, 3000);\n\n    // Update time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => {\n      clearTimeout(timer);\n      clearInterval(timeInterval);\n    };\n  }, []);\n\n  // Enhanced real-time market data with advanced analytics\n  const [marketData, setMarketData] = useState([\n    {\n      symbol: 'EURUSD',\n      price: 1.0850,\n      change: 0.0012,\n      changePercent: 0.11,\n      trend: 'BULLISH',\n      volume: 1250000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0\n    },\n    {\n      symbol: 'GBPUSD',\n      price: 1.2650,\n      change: -0.0025,\n      changePercent: -0.20,\n      trend: 'BEARISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1\n    },\n    {\n      symbol: 'USDJPY',\n      price: 149.50,\n      change: 0.35,\n      changePercent: 0.23,\n      trend: 'BULLISH',\n      volume: 1100000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'ASIAN',\n      riskLevel: 'LOW',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 1\n    },\n    {\n      symbol: 'XAUUSD',\n      price: 2050.00,\n      change: 15.50,\n      changePercent: 0.76,\n      trend: 'BULLISH',\n      volume: 750000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 5,\n      fvg: 4,\n      choch: 2,\n      bos: 1\n    },\n  ]);\n\n  // Enhanced signals with ICT concepts\n  const [signals, setSignals] = useState([\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit1: 1.0900,\n      takeProfit2: 1.0950,\n      takeProfit3: 1.1000,\n      riskReward: 1.67,\n      confidence: 87,\n      timestamp: Date.now() - 300000,\n      reasoning: [\n        'Bullish Order Block at 1.0845-1.0855',\n        'Fair Value Gap filled and holding',\n        'CHoCH confirmed bullish structure',\n        'RSI oversold with divergence',\n        'MACD bullish crossover',\n        'Price above VWAP',\n        'London session high liquidity'\n      ],\n      patterns: ['Bullish Order Block', 'Fair Value Gap', 'CHoCH'],\n      session: 'LONDON',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit1: 1.2600,\n      takeProfit2: 1.2550,\n      takeProfit3: 1.2500,\n      riskReward: 1.67,\n      confidence: 82,\n      timestamp: Date.now() - 600000,\n      reasoning: [\n        'Bearish Breaker Block at 1.2655-1.2665',\n        'Break of Structure confirmed',\n        'Bearish engulfing pattern',\n        'RSI overbought rejection',\n        'Volume spike on breakdown',\n        'Below key support level'\n      ],\n      patterns: ['Bearish Breaker Block', 'BOS', 'Engulfing'],\n      session: 'LONDON',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    },\n    {\n      id: '3',\n      symbol: 'XAUUSD',\n      type: 'BUY',\n      entry: 2050.00,\n      stopLoss: 2035.00,\n      takeProfit1: 2075.00,\n      takeProfit2: 2100.00,\n      takeProfit3: 2125.00,\n      riskReward: 1.67,\n      confidence: 91,\n      timestamp: Date.now() - 900000,\n      reasoning: [\n        'Premium Discount Array (PDA) setup',\n        'Institutional Order Block respected',\n        'Liquidity sweep completed',\n        'Fair Value Gap acting as support',\n        'Smart money accumulation zone',\n        'Dollar weakness confluence'\n      ],\n      patterns: ['Order Block', 'Liquidity Sweep', 'PDA'],\n      session: 'NEW_YORK',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    }\n  ]);\n\n  // Real-time updates simulation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setMarketData(prevData =>\n        prevData.map(market => ({\n          ...market,\n          price: market.price + (Math.random() - 0.5) * 0.001 * market.price,\n          change: market.change + (Math.random() - 0.5) * 0.0005,\n          changePercent: market.changePercent + (Math.random() - 0.5) * 0.05,\n          volume: market.volume + Math.floor((Math.random() - 0.5) * 50000)\n        }))\n      );\n    }, 2000); // Update every 2 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const mockSignals = [\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit: 1.0900,\n      confidence: 85,\n      timestamp: Date.now() - 300000,\n      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit: 1.2600,\n      confidence: 78,\n      timestamp: Date.now() - 600000,\n      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']\n    }\n  ];\n\n  if (!isInitialized) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">🤖 AI Trading Bot</h1>\n          <p className=\"text-blue-200\">Initializing advanced trading systems...</p>\n          <div className=\"mt-4 space-y-2 text-sm text-blue-300\">\n            <p>✅ Loading technical indicators</p>\n            <p>✅ Connecting to market data</p>\n            <p>✅ Initializing AI pattern recognition</p>\n            <p>✅ Setting up risk management</p>\n            <p>🔄 Starting real-time analysis...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                🤖 AI Trading Bot\n              </h1>\n              <div className=\"ml-6 flex items-center space-x-2\">\n                <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-300\">Live</span>\n              </div>\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n              {currentTime.toLocaleTimeString()}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Dashboard */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Enhanced Market Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {marketData.map(market => (\n            <div key={market.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-l-4 border-blue-500\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {market.symbol}\n                </h3>\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    market.trend === 'BULLISH' ? 'bg-green-500' : 'bg-red-500'\n                  }`}></div>\n                  <span className=\"text-xs text-gray-500\">{market.session}</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}\n                  </span>\n                  <span className={`text-sm font-medium ${\n                    market.change >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.trend === 'BULLISH'\n                      ? 'text-green-600 bg-green-50 dark:bg-green-900/20'\n                      : 'text-red-600 bg-red-50 dark:bg-red-900/20'\n                  }`}>\n                    {market.trend}\n                  </div>\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.riskLevel === 'LOW' ? 'text-green-600 bg-green-50' :\n                    market.riskLevel === 'MEDIUM' ? 'text-yellow-600 bg-yellow-50' :\n                    'text-red-600 bg-red-50'\n                  } dark:bg-opacity-20`}>\n                    {market.riskLevel}\n                  </div>\n                </div>\n\n                <div className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex justify-between\">\n                    <span>Volume:</span>\n                    <span>{(market.volume / 1000000).toFixed(1)}M</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>Volatility:</span>\n                    <span>{market.volatility}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>Liquidity:</span>\n                    <span>{market.liquidity}</span>\n                  </div>\n                </div>\n\n                {/* ICT Concepts Summary */}\n                <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"grid grid-cols-4 gap-1 text-xs\">\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-blue-600\">{market.orderBlocks}</div>\n                      <div className=\"text-gray-500\">OB</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-purple-600\">{market.fvg}</div>\n                      <div className=\"text-gray-500\">FVG</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-green-600\">{market.choch}</div>\n                      <div className=\"text-gray-500\">CHoCH</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-orange-600\">{market.bos}</div>\n                      <div className=\"text-gray-500\">BOS</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Enhanced Trading Signals with ICT Analysis */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🎯 AI Trading Signals\n                <span className=\"ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                  ICT Concepts\n                </span>\n              </h3>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {signals.length} Active Signals\n              </div>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-6\">\n              {signals.map(signal => (\n                <div key={signal.id} className={`border-2 rounded-xl p-6 transition-all hover:shadow-lg ${\n                  signal.type === 'BUY'\n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800'\n                    : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200 dark:from-red-900/20 dark:to-rose-900/20 dark:border-red-800'\n                }`}>\n                  {/* Signal Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\n                        signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'\n                      }`}>\n                        <span className=\"text-white text-xl\">\n                          {signal.type === 'BUY' ? '📈' : '📉'}\n                        </span>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                            {signal.type} {signal.symbol}\n                          </span>\n                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                            signal.confidence >= 85 ? 'bg-green-100 text-green-800' :\n                            signal.confidence >= 75 ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-orange-100 text-orange-800'\n                          }`}>\n                            {signal.confidence}% Confidence\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-600 dark:text-gray-400\">\n                          <span>{new Date(signal.timestamp).toLocaleString()}</span>\n                          <span>•</span>\n                          <span>{signal.session} Session</span>\n                          <span>•</span>\n                          <span>{signal.marketStructure} Structure</span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                        R/R: {signal.riskReward}:1\n                      </div>\n                      <div className={`text-sm px-2 py-1 rounded ${\n                        signal.smartMoney === 'ACCUMULATION' ? 'bg-green-100 text-green-800' :\n                        'bg-red-100 text-red-800'\n                      }`}>\n                        {signal.smartMoney}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Price Levels */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mb-4\">\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">Entry</div>\n                      <div className=\"font-bold text-gray-900 dark:text-white\">\n                        {signal.entry.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">Stop Loss</div>\n                      <div className=\"font-bold text-red-600\">\n                        {signal.stopLoss.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">TP1</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit1.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">TP2</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit2.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">TP3</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit3.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* ICT Patterns */}\n                  <div className=\"mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      🔍 Detected Patterns:\n                    </h5>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {signal.patterns.map((pattern, index) => (\n                        <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\n                          {pattern}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Enhanced Analysis */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n                      🧠 Advanced ICT Analysis:\n                      <span className=\"ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">\n                        Smart Money Concepts\n                      </span>\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(0, Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-blue-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-purple-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n\n                  {/* Action Buttons */}\n                  <div className=\"mt-4 flex items-center justify-between\">\n                    <div className=\"flex space-x-2\">\n                      <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                        📊 View Chart\n                      </button>\n                      <button className=\"px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors\">\n                        📋 Copy Signal\n                      </button>\n                    </div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      Signal ID: {signal.id}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Technical Indicators Dashboard */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n              📊 Advanced Technical Analysis\n              <span className=\"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                Real-time\n              </span>\n            </h3>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Traditional Indicators */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🔢 Traditional Indicators\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">RSI (14)</h3>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">EURUSD</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">45.2</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Neutral</div>\n                    <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '45%'}}></div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-purple-700 dark:text-purple-300\">MACD</h3>\n                    <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">12,26,9</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">0.0012</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Bullish Cross</div>\n                    <div className=\"text-xs text-gray-500\">Signal: 0.0008</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-green-700 dark:text-green-300\">VWAP</h3>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">Volume</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0845</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-blue-600\">Above Price</div>\n                    <div className=\"text-xs text-gray-500\">Support</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-orange-700 dark:text-orange-300\">Supertrend</h3>\n                    <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">10,3</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0820</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Uptrend</div>\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* ICT Concepts */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🎯 ICT Smart Money Concepts\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n                <div className=\"bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-cyan-200 dark:border-cyan-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-cyan-700 dark:text-cyan-300 mb-2\">Order Blocks</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">14</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Active Zones</div>\n                    <div className=\"mt-2 flex justify-center space-x-1\">\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-red-500 rounded-full\"></span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-violet-200 dark:border-violet-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-violet-700 dark:text-violet-300 mb-2\">Fair Value Gaps</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">7</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Unfilled</div>\n                    <div className=\"mt-2 text-xs text-violet-600\">3 Bullish, 4 Bearish</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-emerald-700 dark:text-emerald-300 mb-2\">CHoCH</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">2</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Recent</div>\n                    <div className=\"mt-2 text-xs text-emerald-600\">Bullish Structure</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-amber-700 dark:text-amber-300 mb-2\">BOS</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">3</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Confirmed</div>\n                    <div className=\"mt-2 text-xs text-amber-600\">Strong Momentum</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-rose-200 dark:border-rose-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-rose-700 dark:text-rose-300 mb-2\">Liquidity</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">HIGH</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">London Session</div>\n                    <div className=\"mt-2 text-xs text-rose-600\">Optimal Trading</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Market Structure Analysis */}\n            <div>\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🏗️ Market Structure Analysis\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Trend Analysis</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Highs:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Lows:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Structure:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Volume Profile</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">POC:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0842</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAH:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0865</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAL:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0825</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Smart Money</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Flow:</span>\n                      <span className=\"text-xs font-medium text-green-600\">ACCUMULATION</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Sentiment:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Confidence:</span>\n                      <span className=\"text-xs font-medium text-purple-600\">87%</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Risk Management Dashboard */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🛡️ Advanced Risk Management\n                <span className=\"ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full\">\n                  Real-time Monitoring\n                </span>\n              </h3>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Risk Engine Active</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Key Metrics */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">💰</span>\n                  </div>\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">Demo</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">$10,000</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Account Balance</div>\n                <div className=\"flex items-center text-xs text-green-600\">\n                  <span className=\"mr-1\">↗</span>\n                  <span>+2.5% This Month</span>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">⚡</span>\n                  </div>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">Safe</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">2.5%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Daily Risk Exposure</div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">🎯</span>\n                  </div>\n                  <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full\">Excellent</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">72.3%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Win Rate (30 Days)</div>\n                <div className=\"text-xs text-purple-600\">23 Wins / 9 Losses</div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">📊</span>\n                  </div>\n                  <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full\">Active</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">3</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Active Signals</div>\n                <div className=\"text-xs text-orange-600\">2 BUY / 1 SELL</div>\n              </div>\n            </div>\n\n            {/* Risk Analysis */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  📈 Performance Metrics\n                </h4>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Profit Factor:</span>\n                    <span className=\"text-sm font-bold text-green-600\">1.85</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sharpe Ratio:</span>\n                    <span className=\"text-sm font-bold text-blue-600\">1.42</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Drawdown:</span>\n                    <span className=\"text-sm font-bold text-red-600\">-3.2%</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Avg R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.67:1</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Recovery Factor:</span>\n                    <span className=\"text-sm font-bold text-indigo-600\">2.1</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  ⚙️ Risk Parameters\n                </h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Risk Per Trade:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">2.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-green-500 h-2 rounded-full\" style={{width: '40%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Daily Risk:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">6.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Open Trades:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">3 / 5</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-orange-500 h-2 rounded-full\" style={{width: '60%'}}></div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Min R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.5:1</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Risk Alerts */}\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                  <span className=\"text-white text-sm\">⚠</span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                    Risk Management Active\n                  </h4>\n                  <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                    All trades are automatically monitored for risk compliance. Current exposure is within safe limits.\n                    Demo mode ensures no real money is at risk.\n                  </p>\n                  <div className=\"mt-2 flex items-center space-x-4 text-xs text-yellow-600 dark:text-yellow-400\">\n                    <span>✓ Position sizing active</span>\n                    <span>✓ Stop loss enforcement</span>\n                    <span>✓ Daily limit monitoring</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Enhanced Footer */}\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white mt-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* Bot Info */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 flex items-center\">\n                🤖 AI Trading Bot\n                <span className=\"ml-2 text-xs bg-blue-600 px-2 py-1 rounded-full\">v2.0</span>\n              </h4>\n              <p className=\"text-sm text-gray-300 mb-4\">\n                Professional AI-powered trading system with advanced ICT concepts and smart money analysis.\n              </p>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm\">Live & Active</span>\n              </div>\n            </div>\n\n            {/* Features */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">🎯 Features</h4>\n              <ul className=\"text-sm text-gray-300 space-y-2\">\n                <li>• ICT Smart Money Concepts</li>\n                <li>• Order Blocks & Fair Value Gaps</li>\n                <li>• CHoCH & BOS Detection</li>\n                <li>• Advanced Risk Management</li>\n                <li>• Real-time Market Analysis</li>\n                <li>• Multi-timeframe Analysis</li>\n              </ul>\n            </div>\n\n            {/* Status */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">📊 System Status</h4>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Market Data:</span>\n                  <span className=\"text-green-400\">✓ Connected</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">AI Engine:</span>\n                  <span className=\"text-green-400\">✓ Active</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Risk Manager:</span>\n                  <span className=\"text-green-400\">✓ Monitoring</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Signals Generated:</span>\n                  <span className=\"text-blue-400\">{signals.length} Today</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Last Update:</span>\n                  <span className=\"text-yellow-400\">{currentTime.toLocaleTimeString()}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Disclaimer */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">⚠️ Important</h4>\n              <div className=\"text-xs text-gray-300 space-y-2\">\n                <p className=\"bg-red-900/30 border border-red-700 rounded p-2\">\n                  <strong>DEMO MODE:</strong> This is a demonstration. No real money is involved.\n                </p>\n                <p className=\"bg-yellow-900/30 border border-yellow-700 rounded p-2\">\n                  <strong>RISK WARNING:</strong> Trading involves substantial risk. Never trade with money you cannot afford to lose.\n                </p>\n                <p className=\"bg-blue-900/30 border border-blue-700 rounded p-2\">\n                  <strong>EDUCATIONAL:</strong> This software is for educational purposes only.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Bottom Bar */}\n          <div className=\"border-t border-gray-700 mt-8 pt-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <div className=\"flex items-center space-x-6 text-sm text-gray-300 mb-4 md:mb-0\">\n                <span>© 2024 AI Trading Bot</span>\n                <span>•</span>\n                <span>Built with Next.js & TypeScript</span>\n                <span>•</span>\n                <span>Powered by Advanced AI</span>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2 text-sm\">\n                  <span className=\"text-gray-300\">Performance:</span>\n                  <span className=\"text-green-400 font-medium\">+2.5% MTD</span>\n                </div>\n                <div className=\"flex items-center space-x-2 text-sm\">\n                  <span className=\"text-gray-300\">Uptime:</span>\n                  <span className=\"text-blue-400 font-medium\">99.9%</span>\n                </div>\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,0BAA0B;YAC1B,MAAM,QAAQ;wCAAW;oBACvB,iBAAiB;gBACnB;uCAAG;YAEH,2BAA2B;YAC3B,MAAM,eAAe;+CAAY;oBAC/B,eAAe,IAAI;gBACrB;8CAAG;YAEH;kCAAO;oBACL,aAAa;oBACb,cAAc;gBAChB;;QACF;yBAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;KACD;IAED,qCAAqC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAuB;gBAAkB;aAAQ;YAC5D,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAyB;gBAAO;aAAY;YACvD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAe;gBAAmB;aAAM;YACnD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,WAAW;2CAAY;oBAC3B;mDAAc,CAAA,WACZ,SAAS,GAAG;2DAAC,CAAA,SAAU,CAAC;wCACtB,GAAG,MAAM;wCACT,OAAO,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,OAAO,KAAK;wCAClE,QAAQ,OAAO,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wCAChD,eAAe,OAAO,aAAa,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wCAC9D,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oCAC7D,CAAC;;;gBAEL;0CAAG,OAAO,yBAAyB;YAEnC;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAAgB;gBAA0B;aAAmB;QAC3E;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAA6B;gBAAkB;aAAmB;QAChF;KACD;IAED,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG/D,6LAAC;gCAAI,WAAU;0CACZ,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA,uBACd,6LAAC;gCAAwB,WAAU;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,OAAO,MAAM;;;;;;0DAEhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAEhB,OADC,OAAO,KAAK,KAAK,YAAY,iBAAiB;;;;;;kEAEhD,6LAAC;wDAAK,WAAU;kEAAyB,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAI3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE5D,6LAAC;wDAAK,WAAW,AAAC,uBAEjB,OADC,OAAO,MAAM,IAAI,IAAI,mBAAmB;;4DAEvC,OAAO,MAAM,IAAI,IAAI,MAAM;4DAAI,OAAO,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAIpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,iCAIhB,OAHC,OAAO,KAAK,KAAK,YACb,oDACA;kEAEH,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAI,WAAW,AAAC,iCAIhB,OAHC,OAAO,SAAS,KAAK,QAAQ,+BAC7B,OAAO,SAAS,KAAK,WAAW,iCAChC,0BACD;kEACE,OAAO,SAAS;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,CAAC,OAAO,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,OAAO,UAAU;;;;;;;;;;;;kEAE1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,OAAO,SAAS;;;;;;;;;;;;;;;;;;0DAK3B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B,OAAO,WAAW;;;;;;8EAC9D,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA8B,OAAO,KAAK;;;;;;8EACzD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA1E/B,OAAO,MAAM;;;;;;;;;;kCAoF3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,6LAAC;oDAAK,WAAU;8DAAgE;;;;;;;;;;;;sDAIlF,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;4CAAoB,WAAW,AAAC,0DAIhC,OAHC,OAAO,IAAI,KAAK,QACZ,uIACA;;8DAGJ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,AAAC,2DAEhB,OADC,OAAO,IAAI,KAAK,QAAQ,iBAAiB;8EAEzC,cAAA,6LAAC;wEAAK,WAAU;kFACb,OAAO,IAAI,KAAK,QAAQ,OAAO;;;;;;;;;;;8EAGpC,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;;wFACb,OAAO,IAAI;wFAAC;wFAAE,OAAO,MAAM;;;;;;;8FAE9B,6LAAC;oFAAK,WAAW,AAAC,8CAIjB,OAHC,OAAO,UAAU,IAAI,KAAK,gCAC1B,OAAO,UAAU,IAAI,KAAK,kCAC1B;;wFAEC,OAAO,UAAU;wFAAC;;;;;;;;;;;;;sFAGvB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;8FAAM,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;8FAChD,6LAAC;8FAAK;;;;;;8FACN,6LAAC;;wFAAM,OAAO,OAAO;wFAAC;;;;;;;8FACtB,6LAAC;8FAAK;;;;;;8FACN,6LAAC;;wFAAM,OAAO,eAAe;wFAAC;;;;;;;;;;;;;;;;;;;;;;;;;sEAIpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAkD;wEACzD,OAAO,UAAU;wEAAC;;;;;;;8EAE1B,6LAAC;oEAAI,WAAW,AAAC,6BAGhB,OAFC,OAAO,UAAU,KAAK,iBAAiB,gCACvC;8EAEC,OAAO,UAAU;;;;;;;;;;;;;;;;;;8DAMxB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAG9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;8DAMtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAG1E,6LAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC;oEAAiB,WAAU;8EACzB;mEADQ;;;;;;;;;;;;;;;;8DAQjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;gEAA2E;8EAEvF,6LAAC;oEAAK,WAAU;8EAA+D;;;;;;;;;;;;sEAIjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC9E,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC;oFAAK,WAAU;8FAAqB;;;;;;8FACrC,6LAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;8EAMb,6LAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC3E,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC;oFAAK,WAAU;8FAAuB;;;;;;8FACvC,6LAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;;;;;;;;;;;;;8DAUjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAAsG;;;;;;8EAGxH,6LAAC;oEAAO,WAAU;8EAAsG;;;;;;;;;;;;sEAI1H,6LAAC;4DAAI,WAAU;;gEAA2C;gEAC5C,OAAO,EAAE;;;;;;;;;;;;;;2CAzIjB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCAmJ3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAwE;sDAEpF,6LAAC;4CAAK,WAAU;sDAAkE;;;;;;;;;;;;;;;;;0CAMtF,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAK,WAAU;kFAAsD;;;;;;;;;;;;0EAExE,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;;;;;;;kEAKxE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,6LAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAyD;;;;;;kFACvE,6LAAC;wEAAK,WAAU;kFAAwD;;;;;;;;;;;;0EAE1E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,6LAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA+B;;;;;;;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAkE;;;;;;8EAChF,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA8D;;;;;;8EAC5E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA8B;;;;;;;;;;;;;;;;;kEAIjD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;;;;;;;;;;;;;kEAK1D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;;;;;;;;;;;;;kEAK1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;;;;;;;sDAIhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;0CAKjE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA6D;;;;;;;;;;;;kEAE/E,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA2D;;;;;;;;;;;;kEAE7E,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAC,OAAO;4DAAK;;;;;;;;;;;;;;;;;0DAItE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;kDAK7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;;0EAEpD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAiC;;;;;;;;;;;;0EAEnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;0DAK1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAgC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIvE,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAItE,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAiC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIxE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;8DAEvC,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAE,WAAU;sEAA+C;;;;;;sEAI5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAA+C;8DAE3D,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;;sDAEpE,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAiB,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;8CAMvE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAmB;;;;;;;8DAE7B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAsB;;;;;;;8DAEhC,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GA58BwB;KAAA", "debugId": null}}]}