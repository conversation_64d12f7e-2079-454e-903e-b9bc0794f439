'use client';

import { useState, useEffect } from 'react';

interface OrderBlock {
  id: string;
  type: 'BULLISH' | 'BEARISH';
  level: number;
  strength: number;
  volume: number;
  timeframe: string;
  age: number; // hours since formation
  tested: number; // how many times tested
  validity: 'FRESH' | 'TESTED' | 'BROKEN';
  institutionalFlow: 'ACCUMULATION' | 'DISTRIBUTION' | 'NEUTRAL';
  confluence: string[];
  probability: number;
}

interface SmartMoneyAnalysis {
  symbol: string;
  currentPrice: number;
  orderBlocks: OrderBlock[];
  marketStructure: 'BULLISH' | 'BEARISH' | 'RANGING';
  institutionalSentiment: number; // -100 to +100
  liquidityLevels: {
    buyLiquidity: number[];
    sellLiquidity: number[];
  };
  recommendation: {
    action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';
    confidence: number;
    reasoning: string[];
    entryZone: { min: number; max: number };
    stopLoss: number;
    targets: number[];
    riskReward: number;
  };
}

export default function ProfessionalOrderBlocks() {
  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');
  const [analysis, setAnalysis] = useState<SmartMoneyAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const forexPairs = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'
  ];

  const timeframes = [
    { key: '15m', name: '15 دقيقة', weight: 1 },
    { key: '1h', name: '1 ساعة', weight: 2 },
    { key: '4h', name: '4 ساعات', weight: 3 },
    { key: '1d', name: '1 يوم', weight: 4 }
  ];

  const runProfessionalAnalysis = async () => {
    setIsAnalyzing(true);
    
    try {
      // Simulate professional analysis delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const basePrice = getBasePrice(selectedPair);
      const volatility = getVolatility(selectedPair);
      
      // Generate professional order blocks
      const orderBlocks: OrderBlock[] = generateOrderBlocks(basePrice, volatility);
      
      // Analyze market structure
      const marketStructure = analyzeMarketStructure(orderBlocks, basePrice);
      
      // Calculate institutional sentiment
      const institutionalSentiment = calculateInstitutionalSentiment(orderBlocks);
      
      // Generate liquidity levels
      const liquidityLevels = generateLiquidityLevels(basePrice, volatility);
      
      // Generate professional recommendation
      const recommendation = generateProfessionalRecommendation(
        orderBlocks, marketStructure, institutionalSentiment, basePrice
      );
      
      setAnalysis({
        symbol: selectedPair,
        currentPrice: basePrice + (Math.random() - 0.5) * volatility * basePrice * 0.01,
        orderBlocks,
        marketStructure,
        institutionalSentiment,
        liquidityLevels,
        recommendation
      });
      
      setLastUpdate(new Date());
      
    } catch (error) {
      console.error('Professional Analysis Error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateOrderBlocks = (basePrice: number, volatility: number): OrderBlock[] => {
    const blocks: OrderBlock[] = [];
    const timeframeWeight = timeframes.find(tf => tf.key === selectedTimeframe)?.weight || 2;
    
    // Generate 3-7 order blocks based on timeframe
    const blockCount = 3 + Math.floor(Math.random() * 5);
    
    for (let i = 0; i < blockCount; i++) {
      const type = Math.random() > 0.5 ? 'BULLISH' : 'BEARISH';
      const distance = (Math.random() * 0.02 + 0.005) * basePrice; // 0.5% to 2.5% from current price
      const level = type === 'BULLISH' 
        ? basePrice - distance 
        : basePrice + distance;
      
      const age = Math.floor(Math.random() * 48); // 0-48 hours
      const tested = Math.floor(Math.random() * 4); // 0-3 times tested
      const volume = 500000 + Math.random() * 2000000;
      
      // Calculate strength based on multiple factors
      let strength = 50;
      strength += timeframeWeight * 10; // Higher timeframe = stronger
      strength += Math.max(0, (48 - age) / 48 * 20); // Fresher = stronger
      strength -= tested * 15; // More tested = weaker
      strength += (volume / 1000000) * 5; // Higher volume = stronger
      strength = Math.max(20, Math.min(95, strength));
      
      // Determine validity
      let validity: 'FRESH' | 'TESTED' | 'BROKEN' = 'FRESH';
      if (tested > 0) validity = 'TESTED';
      if (tested > 2 && strength < 40) validity = 'BROKEN';
      
      // Institutional flow analysis
      const institutionalFlow = type === 'BULLISH' 
        ? (Math.random() > 0.3 ? 'ACCUMULATION' : 'NEUTRAL')
        : (Math.random() > 0.3 ? 'DISTRIBUTION' : 'NEUTRAL');
      
      // Generate confluence factors
      const confluence = generateConfluence(level, basePrice, type);
      
      // Calculate probability
      const probability = calculateBlockProbability(strength, validity, confluence.length, timeframeWeight);
      
      blocks.push({
        id: `OB_${i}_${Date.now()}`,
        type,
        level,
        strength,
        volume,
        timeframe: selectedTimeframe,
        age,
        tested,
        validity,
        institutionalFlow,
        confluence,
        probability
      });
    }
    
    return blocks.sort((a, b) => b.probability - a.probability);
  };

  const generateConfluence = (level: number, basePrice: number, type: string): string[] => {
    const confluence: string[] = [];
    
    // Random confluence factors
    const factors = [
      'Fibonacci 61.8%',
      'Previous Structure',
      'Volume Profile POC',
      'Daily Pivot',
      'Weekly Support/Resistance',
      'Psychological Level',
      'Trendline Confluence',
      'Moving Average',
      'Institutional Level'
    ];
    
    const confluenceCount = 1 + Math.floor(Math.random() * 4); // 1-4 confluence factors
    const selectedFactors = factors.sort(() => 0.5 - Math.random()).slice(0, confluenceCount);
    
    return selectedFactors;
  };

  const calculateBlockProbability = (strength: number, validity: string, confluenceCount: number, timeframeWeight: number): number => {
    let probability = strength;
    
    // Validity adjustment
    if (validity === 'FRESH') probability += 10;
    else if (validity === 'TESTED') probability -= 5;
    else probability -= 20; // BROKEN
    
    // Confluence adjustment
    probability += confluenceCount * 5;
    
    // Timeframe adjustment
    probability += timeframeWeight * 3;
    
    return Math.max(10, Math.min(95, probability));
  };

  const analyzeMarketStructure = (blocks: OrderBlock[], currentPrice: number): 'BULLISH' | 'BEARISH' | 'RANGING' => {
    const bullishBlocks = blocks.filter(b => b.type === 'BULLISH' && b.validity !== 'BROKEN');
    const bearishBlocks = blocks.filter(b => b.type === 'BEARISH' && b.validity !== 'BROKEN');
    
    const bullishStrength = bullishBlocks.reduce((sum, b) => sum + b.strength, 0);
    const bearishStrength = bearishBlocks.reduce((sum, b) => sum + b.strength, 0);
    
    const difference = Math.abs(bullishStrength - bearishStrength);
    const total = bullishStrength + bearishStrength;
    
    if (total === 0) return 'RANGING';
    
    const dominancePercentage = difference / total;
    
    if (dominancePercentage < 0.2) return 'RANGING';
    return bullishStrength > bearishStrength ? 'BULLISH' : 'BEARISH';
  };

  const calculateInstitutionalSentiment = (blocks: OrderBlock[]): number => {
    let sentiment = 0;
    let totalWeight = 0;
    
    blocks.forEach(block => {
      const weight = block.strength * (block.validity === 'FRESH' ? 1.5 : block.validity === 'TESTED' ? 1 : 0.5);
      const blockSentiment = block.type === 'BULLISH' ? 1 : -1;
      
      if (block.institutionalFlow === 'ACCUMULATION') {
        sentiment += blockSentiment * weight * 1.3;
      } else if (block.institutionalFlow === 'DISTRIBUTION') {
        sentiment += blockSentiment * weight * 1.3;
      } else {
        sentiment += blockSentiment * weight;
      }
      
      totalWeight += weight;
    });
    
    return totalWeight > 0 ? (sentiment / totalWeight) * 100 : 0;
  };

  const generateLiquidityLevels = (basePrice: number, volatility: number) => {
    const buyLiquidity: number[] = [];
    const sellLiquidity: number[] = [];
    
    // Generate 3-5 liquidity levels above and below current price
    for (let i = 0; i < 4; i++) {
      const distance = (i + 1) * 0.005 * basePrice; // 0.5%, 1%, 1.5%, 2%
      buyLiquidity.push(basePrice - distance);
      sellLiquidity.push(basePrice + distance);
    }
    
    return { buyLiquidity, sellLiquidity };
  };

  const generateProfessionalRecommendation = (
    blocks: OrderBlock[],
    marketStructure: string,
    sentiment: number,
    currentPrice: number
  ) => {
    const strongBlocks = blocks.filter(b => b.strength > 70 && b.validity !== 'BROKEN');
    const nearestBlock = blocks.reduce((nearest, current) => {
      const nearestDistance = Math.abs(nearest.level - currentPrice);
      const currentDistance = Math.abs(current.level - currentPrice);
      return currentDistance < nearestDistance ? current : nearest;
    });
    
    let action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' = 'HOLD';
    let confidence = 50;
    const reasoning: string[] = [];
    
    // Determine action based on multiple factors
    if (marketStructure === 'BULLISH' && sentiment > 30) {
      action = sentiment > 60 ? 'STRONG_BUY' : 'BUY';
      confidence = 70 + Math.min(25, sentiment / 4);
      reasoning.push(`هيكل السوق صاعد مع معنويات مؤسسية إيجابية (${sentiment.toFixed(1)})`);
    } else if (marketStructure === 'BEARISH' && sentiment < -30) {
      action = sentiment < -60 ? 'STRONG_SELL' : 'SELL';
      confidence = 70 + Math.min(25, Math.abs(sentiment) / 4);
      reasoning.push(`هيكل السوق هابط مع معنويات مؤسسية سلبية (${sentiment.toFixed(1)})`);
    } else {
      reasoning.push(`السوق في حالة توازن - انتظار إشارة واضحة`);
    }
    
    // Add order block analysis
    if (nearestBlock.strength > 80) {
      reasoning.push(`منطقة طلبات قوية عند ${nearestBlock.level.toFixed(5)} (قوة: ${nearestBlock.strength.toFixed(0)}%)`);
    }
    
    if (strongBlocks.length > 0) {
      reasoning.push(`${strongBlocks.length} منطقة طلبات قوية تدعم التحليل`);
    }
    
    // Calculate entry zone and levels
    const entryZone = {
      min: nearestBlock.level - Math.abs(nearestBlock.level * 0.001),
      max: nearestBlock.level + Math.abs(nearestBlock.level * 0.001)
    };
    
    const stopLoss = action.includes('BUY') 
      ? nearestBlock.level - Math.abs(nearestBlock.level * 0.015)
      : nearestBlock.level + Math.abs(nearestBlock.level * 0.015);
    
    const targets = action.includes('BUY') 
      ? [
          nearestBlock.level + Math.abs(nearestBlock.level * 0.02),
          nearestBlock.level + Math.abs(nearestBlock.level * 0.035),
          nearestBlock.level + Math.abs(nearestBlock.level * 0.05)
        ]
      : [
          nearestBlock.level - Math.abs(nearestBlock.level * 0.02),
          nearestBlock.level - Math.abs(nearestBlock.level * 0.035),
          nearestBlock.level - Math.abs(nearestBlock.level * 0.05)
        ];
    
    const riskReward = Math.abs(targets[0] - nearestBlock.level) / Math.abs(nearestBlock.level - stopLoss);
    
    return {
      action,
      confidence,
      reasoning,
      entryZone,
      stopLoss,
      targets,
      riskReward
    };
  };

  const getBasePrice = (symbol: string): number => {
    const prices: { [key: string]: number } = {
      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,
      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,
      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,
      'USOIL': 78.50, 'BTCUSD': 43250.00
    };
    return prices[symbol] || 1.0000;
  };

  const getVolatility = (symbol: string): number => {
    const volatilities: { [key: string]: number } = {
      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,
      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,
      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,
      'USOIL': 3.0, 'BTCUSD': 4.0
    };
    return volatilities[symbol] || 1.0;
  };

  const getPairFlag = (symbol: string): string => {
    const flags: { [key: string]: string } = {
      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',
      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',
      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',
      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'
    };
    return flags[symbol] || '💱';
  };

  const getBlockColor = (block: OrderBlock): string => {
    if (block.validity === 'BROKEN') return 'bg-gray-100 border-gray-400 text-gray-600';
    
    if (block.type === 'BULLISH') {
      if (block.strength > 80) return 'bg-green-100 border-green-600 text-green-800';
      if (block.strength > 60) return 'bg-green-50 border-green-400 text-green-700';
      return 'bg-green-25 border-green-300 text-green-600';
    } else {
      if (block.strength > 80) return 'bg-red-100 border-red-600 text-red-800';
      if (block.strength > 60) return 'bg-red-50 border-red-400 text-red-700';
      return 'bg-red-25 border-red-300 text-red-600';
    }
  };

  const getActionColor = (action: string): string => {
    switch (action) {
      case 'STRONG_BUY': return 'bg-green-600 text-white';
      case 'BUY': return 'bg-green-500 text-white';
      case 'HOLD': return 'bg-yellow-500 text-white';
      case 'SELL': return 'bg-red-500 text-white';
      case 'STRONG_SELL': return 'bg-red-600 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getActionText = (action: string): string => {
    switch (action) {
      case 'STRONG_BUY': return 'شراء قوي';
      case 'BUY': return 'شراء';
      case 'HOLD': return 'انتظار';
      case 'SELL': return 'بيع';
      case 'STRONG_SELL': return 'بيع قوي';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            🏛️ تحليل Order Blocks الاحترافي
            <span className="mr-3 px-2 py-1 bg-indigo-600 text-white rounded text-sm">
              ICT Pro
            </span>
          </h3>
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}
            </div>
            <button
              onClick={runProfessionalAnalysis}
              disabled={isAnalyzing}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isAnalyzing 
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : 'bg-indigo-600 text-white hover:bg-indigo-700'
              }`}
            >
              {isAnalyzing ? '🔍 جاري التحليل...' : '🚀 تحليل احترافي'}
            </button>
          </div>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              زوج العملة:
            </label>
            <select
              value={selectedPair}
              onChange={(e) => setSelectedPair(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {forexPairs.map(pair => (
                <option key={pair} value={pair}>
                  {getPairFlag(pair)} {pair}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الإطار الزمني:
            </label>
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {timeframes.map(tf => (
                <option key={tf.key} value={tf.key}>
                  {tf.name} (وزن: {tf.weight})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Loading State */}
        {isAnalyzing && (
          <div className="text-center py-12">
            <div className="inline-flex items-center space-x-3 space-x-reverse">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              <span className="text-lg text-gray-600 dark:text-gray-400">
                🔍 تحليل مناطق الطلبات المؤسسية...
              </span>
            </div>
            <div className="mt-4 text-sm text-gray-500 space-y-1">
              <div>• فحص هيكل السوق</div>
              <div>• تحليل تدفق الأموال الذكية</div>
              <div>• حساب مستويات السيولة</div>
              <div>• توليد توصيات احترافية</div>
            </div>
          </div>
        )}

        {/* No Analysis Yet */}
        {!isAnalyzing && !analysis && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏛️</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              تحليل Order Blocks الاحترافي
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              اكتشف مناطق الطلبات المؤسسية وتحليل تدفق الأموال الذكية باستخدام مفاهيم ICT المتقدمة
            </p>
            <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 max-w-lg mx-auto">
              <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                💡 ما هي Order Blocks؟
              </h4>
              <ul className="text-sm text-indigo-800 dark:text-indigo-200 space-y-1 text-right">
                <li>• مناطق تراكم الطلبات المؤسسية الكبيرة</li>
                <li>• مستويات دعم ومقاومة قوية جداً</li>
                <li>• تحليل تدفق الأموال الذكية</li>
                <li>• دقة عالية في تحديد نقاط الانعكاس</li>
              </ul>
            </div>
          </div>
        )}

        {/* Analysis Results */}
        {!isAnalyzing && analysis && (
          <div className="space-y-6">
            {/* Market Overview */}
            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <span className="text-2xl">{getPairFlag(analysis.symbol)}</span>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white">
                      {analysis.symbol} - {selectedTimeframe}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      هيكل السوق: {analysis.marketStructure === 'BULLISH' ? '📈 صاعد' : analysis.marketStructure === 'BEARISH' ? '📉 هابط' : '➡️ متذبذب'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold text-gray-900 dark:text-white">
                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">السعر الحالي</div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className={`text-lg font-bold ${analysis.institutionalSentiment > 0 ? 'text-green-600' : analysis.institutionalSentiment < 0 ? 'text-red-600' : 'text-yellow-600'}`}>
                    {analysis.institutionalSentiment.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">معنويات مؤسسية</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {analysis.orderBlocks.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">مناطق طلبات</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">
                    {analysis.orderBlocks.filter(b => b.validity === 'FRESH').length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">مناطق جديدة</div>
                </div>
              </div>
            </div>

            {/* Professional Recommendation */}
            <div className="bg-white dark:bg-gray-700 rounded-lg p-6 border-2 border-indigo-200">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                🎯 التوصية الاحترافية
                <span className={`mr-3 px-3 py-1 rounded-full text-sm font-medium ${getActionColor(analysis.recommendation.action)}`}>
                  {getActionText(analysis.recommendation.action)}
                </span>
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white mb-3">📋 تحليل الأسباب:</h5>
                  <ul className="space-y-2">
                    {analysis.recommendation.reasoning.map((reason, i) => (
                      <li key={`reasoning-${i}`} className="flex items-start text-sm">
                        <span className="mr-2 text-indigo-500">▶</span>
                        <span className="text-gray-700 dark:text-gray-300">{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h5 className="font-medium text-gray-900 dark:text-white mb-3">💰 مستويات التداول:</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">منطقة الدخول:</span>
                      <span className="font-medium">
                        {analysis.recommendation.entryZone.min.toFixed(5)} - {analysis.recommendation.entryZone.max.toFixed(5)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">وقف الخسارة:</span>
                      <span className="font-medium text-red-600">
                        {analysis.recommendation.stopLoss.toFixed(5)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">الهدف الأول:</span>
                      <span className="font-medium text-green-600">
                        {analysis.recommendation.targets[0].toFixed(5)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">نسبة R/R:</span>
                      <span className="font-medium text-blue-600">
                        {analysis.recommendation.riskReward.toFixed(2)}:1
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">مستوى الثقة:</span>
                      <span className="font-medium text-purple-600">
                        {analysis.recommendation.confidence.toFixed(0)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Final ICT Trading Conclusion */}
            <div className={`rounded-xl p-6 border-4 ${
              analysis.recommendation.action.includes('BUY')
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'
                : analysis.recommendation.action.includes('SELL')
                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'
                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'
            }`}>
              <div className="text-center mb-6">
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  🏛️ خلاصة تحليل ICT
                </h3>
                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${getActionColor(analysis.recommendation.action)}`}>
                  {analysis.recommendation.action.includes('BUY') && '📈 '}
                  {analysis.recommendation.action.includes('SELL') && '📉 '}
                  {analysis.recommendation.action === 'انتظار' && '➡️ '}
                  {getActionText(analysis.recommendation.action)}
                </div>
                <p className="text-lg text-gray-600 dark:text-gray-400 mt-2">
                  بناءً على تحليل Order Blocks المؤسسية ومفاهيم ICT المتقدمة
                </p>
              </div>

              {analysis.recommendation.action !== 'انتظار' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* ICT Setup */}
                  <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                      🎯 إعداد ICT المقترح
                    </h4>

                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center">
                          <div className="text-sm text-gray-600 dark:text-gray-400">منطقة الدخول</div>
                          <div className="text-lg font-bold text-blue-600">
                            {((analysis.recommendation.entryZone.min + analysis.recommendation.entryZone.max) / 2).toFixed(5)}
                          </div>
                        </div>
                        <div className="bg-red-50 dark:bg-red-900/20 rounded p-3 text-center">
                          <div className="text-sm text-gray-600 dark:text-gray-400">وقف الخسارة</div>
                          <div className="text-lg font-bold text-red-600">
                            {analysis.recommendation.stopLoss.toFixed(5)}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-2">
                        {analysis.recommendation.targets.map((target, i) => (
                          <div key={`target-${i}`} className="bg-green-50 dark:bg-green-900/20 rounded p-2 text-center">
                            <div className="text-xs text-gray-600 dark:text-gray-400">هدف {i + 1}</div>
                            <div className="text-sm font-bold text-green-600">
                              {target.toFixed(5)}
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded p-3">
                        <div className="text-center">
                          <div className="text-sm text-gray-600 dark:text-gray-400">نسبة المخاطرة/العائد</div>
                          <div className="text-xl font-bold text-indigo-600">
                            {analysis.recommendation.riskReward.toFixed(2)}:1
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* ICT Analysis */}
                  <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                      🏛️ تحليل ICT المتقدم
                    </h4>

                    <div className="space-y-4">
                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-4">
                        <h5 className="font-medium text-purple-800 dark:text-purple-200 mb-2">
                          📊 Order Blocks النشطة:
                        </h5>
                        <div className="text-sm space-y-1">
                          <div>مناطق جديدة: {analysis.orderBlocks.filter(b => b.validity === 'FRESH').length}</div>
                          <div>مناطق مختبرة: {analysis.orderBlocks.filter(b => b.validity === 'TESTED').length}</div>
                          <div>قوة متوسطة: {(analysis.orderBlocks.reduce((sum, b) => sum + b.strength, 0) / analysis.orderBlocks.length).toFixed(0)}%</div>
                        </div>
                      </div>

                      <div className="bg-orange-50 dark:bg-orange-900/20 rounded p-4">
                        <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-2">
                          💰 تدفق الأموال الذكية:
                        </h5>
                        <div className="text-sm">
                          معنويات مؤسسية: {analysis.institutionalSentiment > 0 ? 'إيجابية' : analysis.institutionalSentiment < 0 ? 'سلبية' : 'محايدة'}
                          ({analysis.institutionalSentiment.toFixed(1)})
                        </div>
                      </div>

                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-4">
                        <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                          🎯 مستويات السيولة:
                        </h5>
                        <div className="text-sm">
                          سيولة شراء: {analysis.liquidityLevels.buyLiquidity.length} مستوى<br/>
                          سيولة بيع: {analysis.liquidityLevels.sellLiquidity.length} مستوى
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Confidence and Best Order Block */}
              <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">مستوى الثقة ICT:</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{analysis.recommendation.confidence.toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        analysis.recommendation.confidence > 80 ? 'bg-green-500' :
                        analysis.recommendation.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${analysis.recommendation.confidence}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4">
                  <h5 className="font-medium text-indigo-900 dark:text-indigo-100 mb-2">
                    🏆 أقوى Order Block: {analysis.nearestLevel.percentage}
                  </h5>
                  <div className="text-sm text-indigo-800 dark:text-indigo-200">
                    قوة: {analysis.nearestLevel.strength.toFixed(0)}% |
                    نوع: {analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة'} |
                    حالة: {analysis.nearestLevel.validity}
                  </div>
                </div>
              </div>
            </div>

            {/* Order Blocks */}
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                🏛️ مناطق الطلبات المؤسسية
              </h4>
              
              <div className="space-y-3">
                {analysis.orderBlocks.map((block, index) => (
                  <div 
                    key={block.id}
                    className={`border-2 rounded-lg p-4 transition-all duration-200 ${getBlockColor(block)}`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="text-lg font-bold">
                          {block.type === 'BULLISH' ? '🟢' : '🔴'} {block.type}
                        </div>
                        <div>
                          <div className="font-medium">
                            {block.level.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                          </div>
                          <div className="text-xs">
                            {block.validity} | {block.timeframe}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          احتمالية: {block.probability.toFixed(0)}%
                        </div>
                        <div className="text-xs">
                          قوة: {block.strength.toFixed(0)}%
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div>
                        <span className="text-gray-600">العمر:</span>
                        <span className="font-medium mr-1">{block.age}س</span>
                      </div>
                      <div>
                        <span className="text-gray-600">اختبار:</span>
                        <span className="font-medium mr-1">{block.tested}x</span>
                      </div>
                      <div>
                        <span className="text-gray-600">الحجم:</span>
                        <span className="font-medium mr-1">{(block.volume / 1000000).toFixed(1)}M</span>
                      </div>
                      <div>
                        <span className="text-gray-600">التدفق:</span>
                        <span className="font-medium mr-1">{block.institutionalFlow}</span>
                      </div>
                    </div>

                    {block.confluence.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-current">
                        <div className="text-xs">
                          <span className="font-medium">التقارب: </span>
                          {block.confluence.join(', ')}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Liquidity Levels */}
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                💧 مستويات السيولة
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-green-600 mb-2">🟢 سيولة الشراء:</h5>
                  <div className="space-y-1 text-sm">
                    {analysis.liquidityLevels.buyLiquidity.map((level, i) => (
                      <div key={`buy-liquidity-${i}`} className="flex justify-between">
                        <span>مستوى {i + 1}:</span>
                        <span className="font-medium">{level.toFixed(5)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-red-600 mb-2">🔴 سيولة البيع:</h5>
                  <div className="space-y-1 text-sm">
                    {analysis.liquidityLevels.sellLiquidity.map((level, i) => (
                      <div key={`sell-liquidity-${i}`} className="flex justify-between">
                        <span>مستوى {i + 1}:</span>
                        <span className="font-medium">{level.toFixed(5)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
