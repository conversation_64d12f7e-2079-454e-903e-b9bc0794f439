{"dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@types/node-telegram-bot-api": "^0.64.9", "@types/nodemailer": "^6.4.17", "@types/ws": "^8.18.1", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lightweight-charts": "^5.0.8", "lucide-react": "^0.525.0", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^7.0.5", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "technicalindicators": "^3.1.0", "ws": "^8.18.3"}}