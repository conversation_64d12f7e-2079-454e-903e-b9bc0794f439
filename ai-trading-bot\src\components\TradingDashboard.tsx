'use client';

import React, { useState, useEffect } from 'react';
import { TradingViewProvider, RealTimeQuote } from '@/lib/data-providers/tradingview';
import { RiskManagementEngine, TradeSignal } from '@/lib/trading/risk-management';
import { MarketStateAnalyzer, MarketState } from '@/lib/market-analysis/market-state';
import { AIPatternRecognition, AIDecision } from '@/lib/ai/pattern-recognition';
import { AlertSystem } from '@/lib/notifications/alert-system';
import { TechnicalAnalysisEngine, TechnicalIndicators } from '@/lib/technical-analysis/indicators';
import TradingChart from './TradingChart';
import MarketOverview from './MarketOverview';
import SignalPanel from './SignalPanel';
import RiskPanel from './RiskPanel';
import AlertPanel from './AlertPanel';
import { Activity, TrendingUp, Shield, Bell, Settings, BarChart3 } from 'lucide-react';

interface TradingDashboardProps {
  tradingProvider: TradingViewProvider;
  riskManager: RiskManagementEngine;
  marketAnalyzer: MarketStateAnalyzer;
  aiEngine: AIPatternRecognition;
  alertSystem: AlertSystem;
}

interface DashboardData {
  quotes: Map<string, RealTimeQuote>;
  signals: TradeSignal[];
  marketStates: Map<string, MarketState>;
  aiDecisions: Map<string, AIDecision>;
  indicators: Map<string, TechnicalIndicators>;
}

const TradingDashboard: React.FC<TradingDashboardProps> = ({
  tradingProvider,
  riskManager,
  marketAnalyzer,
  aiEngine,
  alertSystem
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    quotes: new Map(),
    signals: [],
    marketStates: new Map(),
    aiDecisions: new Map(),
    indicators: new Map()
  });
  const [isLive, setIsLive] = useState(true);

  const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'];

  useEffect(() => {
    setupRealTimeUpdates();
    return () => {
      // Cleanup
    };
  }, []);

  const setupRealTimeUpdates = () => {
    // Listen for real-time quotes
    tradingProvider.on('quote', (quote: RealTimeQuote) => {
      setDashboardData(prev => ({
        ...prev,
        quotes: new Map(prev.quotes.set(quote.symbol, quote))
      }));
    });

    // Listen for candle updates and perform analysis
    tradingProvider.on('candle', async (data: any) => {
      await performAnalysis(data.symbol);
    });

    // Initial analysis for all symbols
    symbols.forEach(symbol => {
      performAnalysis(symbol);
    });
  };

  const performAnalysis = async (symbol: string) => {
    try {
      const chartData = tradingProvider.getChartData(symbol);
      if (!chartData || chartData.data.length < 50) return;

      // Technical analysis
      const engine = new TechnicalAnalysisEngine(chartData.data);
      const indicators = engine.calculateAllIndicators();

      // Market state analysis
      const marketState = marketAnalyzer.analyzeMarketState(chartData.data, indicators, symbol);

      // AI decision
      const aiDecision = aiEngine.makeDecision(chartData.data, indicators, marketState, symbol);

      // Update dashboard data
      setDashboardData(prev => ({
        ...prev,
        marketStates: new Map(prev.marketStates.set(symbol, marketState)),
        aiDecisions: new Map(prev.aiDecisions.set(symbol, aiDecision)),
        indicators: new Map(prev.indicators.set(symbol, indicators))
      }));

      // Generate trade signal if conditions are met
      const currentQuote = tradingProvider.getCurrentQuote(symbol);
      if (currentQuote && aiDecision.confidence > 75) {
        const signal = riskManager.generateTradeSignal(
          symbol,
          currentQuote.price,
          indicators,
          chartData.data
        );

        if (signal) {
          setDashboardData(prev => ({
            ...prev,
            signals: [signal, ...prev.signals.slice(0, 9)] // Keep last 10 signals
          }));
        }
      }
    } catch (error) {
      console.error(`Analysis error for ${symbol}:`, error);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Market Overview', icon: BarChart3 },
    { id: 'chart', label: 'Trading Chart', icon: TrendingUp },
    { id: 'signals', label: 'Trade Signals', icon: Activity },
    { id: 'risk', label: 'Risk Management', icon: Shield },
    { id: 'alerts', label: 'Alerts', icon: Bell },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const currentQuote = dashboardData.quotes.get(selectedSymbol);
  const currentMarketState = dashboardData.marketStates.get(selectedSymbol);
  const currentAIDecision = dashboardData.aiDecisions.get(selectedSymbol);
  const currentIndicators = dashboardData.indicators.get(selectedSymbol);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  🤖 AI Trading Bot
                </h1>
              </div>
              <div className="ml-6 flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {isLive ? 'Live' : 'Offline'}
                </span>
              </div>
            </div>

            {/* Symbol Selector */}
            <div className="flex items-center space-x-4">
              <select
                value={selectedSymbol}
                onChange={(e) => setSelectedSymbol(e.target.value)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {symbols.map(symbol => (
                  <option key={symbol} value={symbol}>{symbol}</option>
                ))}
              </select>

              {currentQuote && (
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    {currentQuote.price.toFixed(5)}
                  </span>
                  <span className={`text-sm ${currentQuote.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {currentQuote.change >= 0 ? '+' : ''}{currentQuote.changePercent.toFixed(2)}%
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'overview' && (
          <MarketOverview
            symbols={symbols}
            quotes={dashboardData.quotes}
            marketStates={dashboardData.marketStates}
            aiDecisions={dashboardData.aiDecisions}
            signals={dashboardData.signals}
          />
        )}

        {activeTab === 'chart' && (
          <TradingChart
            symbol={selectedSymbol}
            tradingProvider={tradingProvider}
            indicators={currentIndicators}
            marketState={currentMarketState}
            aiDecision={currentAIDecision}
          />
        )}

        {activeTab === 'signals' && (
          <SignalPanel
            signals={dashboardData.signals}
            selectedSymbol={selectedSymbol}
            currentQuote={currentQuote}
            riskManager={riskManager}
          />
        )}

        {activeTab === 'risk' && (
          <RiskPanel
            riskManager={riskManager}
            signals={dashboardData.signals}
            quotes={dashboardData.quotes}
          />
        )}

        {activeTab === 'alerts' && (
          <AlertPanel
            alertSystem={alertSystem}
            signals={dashboardData.signals}
          />
        )}

        {activeTab === 'settings' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Settings
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Trading Mode
                </label>
                <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                  <option>Demo Mode</option>
                  <option>Live Trading (Disabled)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Risk Level
                </label>
                <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                  <option>Conservative</option>
                  <option>Moderate</option>
                  <option>Aggressive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  AI Confidence Threshold
                </label>
                <input
                  type="range"
                  min="50"
                  max="95"
                  defaultValue="75"
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>50%</span>
                  <span>95%</span>
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifications"
                  defaultChecked
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="notifications" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Enable notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoTrade"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="autoTrade" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Enable auto-trading (Demo only)
                </label>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Status Bar */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>Active Signals: {dashboardData.signals.length}</span>
              <span>•</span>
              <span>Market Session: {currentMarketState?.session.name || 'Unknown'}</span>
              <span>•</span>
              <span>Last Update: {new Date().toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Powered by AI</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TradingDashboard;
