'use client';

import { useState, useEffect } from 'react';

interface TimeframeData {
  timeframe: string;
  trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  strength: number;
  rsi: number;
  macd: number;
  volume: number;
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
  confidence: number;
  keyLevel: number;
  nextTarget: number;
}

interface MultiTimeframeAnalysis {
  symbol: string;
  currentPrice: number;
  overallTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  timeframes: TimeframeData[];
  consensus: {
    buy: number;
    sell: number;
    neutral: number;
    recommendation: string;
    confidence: number;
  };
}

export default function TimeframeAnalysis({ symbol }: { symbol: string }) {
  const [analysis, setAnalysis] = useState<MultiTimeframeAnalysis | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');
  const [isLoading, setIsLoading] = useState(true);

  const timeframes = [
    { key: '1m', name: '1 دقيقة', color: 'bg-red-100 text-red-800' },
    { key: '5m', name: '5 دقائق', color: 'bg-orange-100 text-orange-800' },
    { key: '15m', name: '15 دقيقة', color: 'bg-yellow-100 text-yellow-800' },
    { key: '30m', name: '30 دقيقة', color: 'bg-green-100 text-green-800' },
    { key: '1h', name: '1 ساعة', color: 'bg-blue-100 text-blue-800' },
    { key: '4h', name: '4 ساعات', color: 'bg-indigo-100 text-indigo-800' },
    { key: '1d', name: '1 يوم', color: 'bg-purple-100 text-purple-800' },
    { key: '1w', name: '1 أسبوع', color: 'bg-pink-100 text-pink-800' }
  ];

  useEffect(() => {
    generateMultiTimeframeAnalysis();
    
    const interval = setInterval(() => {
      generateMultiTimeframeAnalysis();
    }, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, [symbol]);

  const generateMultiTimeframeAnalysis = () => {
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const basePrice = getBasePrice(symbol);
      const timeframeData: TimeframeData[] = timeframes.map(tf => {
        const trend = Math.random() > 0.5 ? 'BULLISH' : Math.random() > 0.5 ? 'BEARISH' : 'NEUTRAL';
        const strength = Math.random() * 100;
        const rsi = Math.random() * 100;
        const macd = (Math.random() - 0.5) * 0.02;
        const volume = Math.random() * 2000000 + 500000;
        const confidence = 70 + Math.random() * 25;
        
        return {
          timeframe: tf.key,
          trend,
          strength,
          rsi,
          macd,
          volume,
          recommendation: getRecommendation(trend, rsi, strength),
          confidence,
          keyLevel: basePrice + (Math.random() - 0.5) * basePrice * 0.02,
          nextTarget: basePrice + (Math.random() - 0.5) * basePrice * 0.03
        };
      });

      // Calculate consensus
      const buyCount = timeframeData.filter(tf => tf.recommendation.includes('BUY')).length;
      const sellCount = timeframeData.filter(tf => tf.recommendation.includes('SELL')).length;
      const neutralCount = timeframeData.filter(tf => tf.recommendation === 'NEUTRAL').length;
      
      const total = timeframeData.length;
      const buyPercent = (buyCount / total) * 100;
      const sellPercent = (sellCount / total) * 100;
      const neutralPercent = (neutralCount / total) * 100;
      
      let overallRecommendation = 'NEUTRAL';
      if (buyPercent > 60) overallRecommendation = 'STRONG_BUY';
      else if (buyPercent > 40) overallRecommendation = 'BUY';
      else if (sellPercent > 60) overallRecommendation = 'STRONG_SELL';
      else if (sellPercent > 40) overallRecommendation = 'SELL';
      
      const overallTrend = buyPercent > sellPercent ? 'BULLISH' : sellPercent > buyPercent ? 'BEARISH' : 'NEUTRAL';
      const consensusConfidence = Math.abs(buyPercent - sellPercent);

      setAnalysis({
        symbol,
        currentPrice: basePrice + (Math.random() - 0.5) * basePrice * 0.01,
        overallTrend,
        timeframes: timeframeData,
        consensus: {
          buy: buyPercent,
          sell: sellPercent,
          neutral: neutralPercent,
          recommendation: overallRecommendation,
          confidence: consensusConfidence
        }
      });
      
      setIsLoading(false);
    }, 1000);
  };

  const getBasePrice = (symbol: string): number => {
    const prices: { [key: string]: number } = {
      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,
      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,
      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,
      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00
    };
    return prices[symbol] || 1.0000;
  };

  const getRecommendation = (trend: string, rsi: number, strength: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {
    if (trend === 'BULLISH' && rsi < 70 && strength > 70) return 'STRONG_BUY';
    if (trend === 'BULLISH' && rsi < 80) return 'BUY';
    if (trend === 'BEARISH' && rsi > 30 && strength > 70) return 'STRONG_SELL';
    if (trend === 'BEARISH' && rsi > 20) return 'SELL';
    return 'NEUTRAL';
  };

  const getRecommendationColor = (rec: string): string => {
    switch (rec) {
      case 'STRONG_BUY': return 'text-green-700 bg-green-100';
      case 'BUY': return 'text-green-600 bg-green-50';
      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';
      case 'SELL': return 'text-red-600 bg-red-50';
      case 'STRONG_SELL': return 'text-red-700 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRecommendationText = (rec: string): string => {
    switch (rec) {
      case 'STRONG_BUY': return 'شراء قوي';
      case 'BUY': return 'شراء';
      case 'NEUTRAL': return 'محايد';
      case 'SELL': return 'بيع';
      case 'STRONG_SELL': return 'بيع قوي';
      default: return 'غير محدد';
    }
  };

  const getTrendIcon = (trend: string): string => {
    switch (trend) {
      case 'BULLISH': return '📈';
      case 'BEARISH': return '📉';
      default: return '➡️';
    }
  };

  if (isLoading || !analysis) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            ⏰ تحليل متعدد الإطارات الزمنية - {symbol}
            <span className="mr-3 text-lg">{getTrendIcon(analysis.overallTrend)}</span>
          </h3>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {analysis.currentPrice.toFixed(symbol.includes('JPY') ? 3 : 5)}
            </div>
            <div className={`text-sm font-medium px-2 py-1 rounded ${getRecommendationColor(analysis.consensus.recommendation)}`}>
              {getRecommendationText(analysis.consensus.recommendation)}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Consensus Summary */}
        <div className="mb-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 rounded-lg p-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">📊 إجماع الإطارات الزمنية</h4>
          
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{analysis.consensus.buy.toFixed(0)}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">شراء</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{analysis.consensus.neutral.toFixed(0)}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">محايد</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{analysis.consensus.sell.toFixed(0)}%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">بيع</div>
            </div>
          </div>

          {/* Consensus Bar */}
          <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
            <div className="flex h-4 rounded-full overflow-hidden">
              <div 
                className="bg-green-500" 
                style={{ width: `${analysis.consensus.buy}%` }}
              ></div>
              <div 
                className="bg-yellow-500" 
                style={{ width: `${analysis.consensus.neutral}%` }}
              ></div>
              <div 
                className="bg-red-500" 
                style={{ width: `${analysis.consensus.sell}%` }}
              ></div>
            </div>
          </div>
          
          <div className="text-center">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              مستوى الثقة: {analysis.consensus.confidence.toFixed(1)}%
            </span>
          </div>
        </div>

        {/* Timeframe Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {analysis.timeframes.map((tf, index) => {
            const timeframeInfo = timeframes.find(t => t.key === tf.timeframe);
            return (
              <div 
                key={tf.timeframe}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  selectedTimeframe === tf.timeframe 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : 'border-gray-200 hover:border-gray-300 dark:border-gray-600'
                }`}
                onClick={() => setSelectedTimeframe(tf.timeframe)}
              >
                {/* Timeframe Header */}
                <div className="flex items-center justify-between mb-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${timeframeInfo?.color || 'bg-gray-100 text-gray-800'}`}>
                    {timeframeInfo?.name || tf.timeframe}
                  </span>
                  <span className="text-lg">{getTrendIcon(tf.trend)}</span>
                </div>

                {/* Recommendation */}
                <div className={`text-center py-2 rounded mb-3 ${getRecommendationColor(tf.recommendation)}`}>
                  <div className="font-bold text-sm">{getRecommendationText(tf.recommendation)}</div>
                  <div className="text-xs">{tf.confidence.toFixed(0)}% ثقة</div>
                </div>

                {/* Technical Indicators */}
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">RSI:</span>
                    <span className={`font-medium ${
                      tf.rsi > 70 ? 'text-red-600' : tf.rsi < 30 ? 'text-green-600' : 'text-gray-900 dark:text-white'
                    }`}>
                      {tf.rsi.toFixed(1)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">MACD:</span>
                    <span className={`font-medium ${tf.macd > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {tf.macd.toFixed(4)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">القوة:</span>
                    <span className="font-medium text-blue-600">{tf.strength.toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">الحجم:</span>
                    <span className="font-medium text-purple-600">{(tf.volume / 1000000).toFixed(1)}M</span>
                  </div>
                </div>

                {/* Key Levels */}
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                  <div className="text-xs space-y-1">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">مستوى مهم:</span>
                      <span className="font-medium text-orange-600">
                        {tf.keyLevel.toFixed(symbol.includes('JPY') ? 3 : 5)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">الهدف التالي:</span>
                      <span className="font-medium text-indigo-600">
                        {tf.nextTarget.toFixed(symbol.includes('JPY') ? 3 : 5)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Selected Timeframe Details */}
        {selectedTimeframe && (
          <div className="mt-6 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              📋 تفاصيل الإطار الزمني: {timeframes.find(t => t.key === selectedTimeframe)?.name}
            </h4>
            
            {(() => {
              const selectedTf = analysis.timeframes.find(tf => tf.timeframe === selectedTimeframe);
              if (!selectedTf) return null;
              
              return (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white dark:bg-gray-700 rounded p-3">
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">📈 تحليل الاتجاه</h5>
                    <div className="space-y-1 text-sm">
                      <div>الاتجاه: <span className="font-medium">{selectedTf.trend === 'BULLISH' ? 'صاعد' : selectedTf.trend === 'BEARISH' ? 'هابط' : 'محايد'}</span></div>
                      <div>القوة: <span className="font-medium">{selectedTf.strength.toFixed(1)}%</span></div>
                      <div>الثقة: <span className="font-medium">{selectedTf.confidence.toFixed(1)}%</span></div>
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-700 rounded p-3">
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">🔢 المؤشرات الفنية</h5>
                    <div className="space-y-1 text-sm">
                      <div>RSI: <span className="font-medium">{selectedTf.rsi.toFixed(1)}</span></div>
                      <div>MACD: <span className="font-medium">{selectedTf.macd.toFixed(4)}</span></div>
                      <div>الحجم: <span className="font-medium">{(selectedTf.volume / 1000000).toFixed(1)}M</span></div>
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-700 rounded p-3">
                    <h5 className="font-medium text-gray-900 dark:text-white mb-2">🎯 المستويات المهمة</h5>
                    <div className="space-y-1 text-sm">
                      <div>مستوى مهم: <span className="font-medium">{selectedTf.keyLevel.toFixed(5)}</span></div>
                      <div>الهدف التالي: <span className="font-medium">{selectedTf.nextTarget.toFixed(5)}</span></div>
                      <div>التوصية: <span className="font-medium">{getRecommendationText(selectedTf.recommendation)}</span></div>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
      </div>
    </div>
  );
}
