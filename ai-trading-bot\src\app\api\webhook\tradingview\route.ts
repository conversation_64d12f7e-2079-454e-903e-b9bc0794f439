// TradingView Webhook API Endpoint
import { NextRequest, NextResponse } from 'next/server';
import { TradingViewWebhookHandler, WebhookConfig } from '../../../../lib/webhooks/tradingview-webhook';

// Webhook configuration
const webhookConfig: WebhookConfig = {
  enabled: process.env.WEBHOOK_ENABLED === 'true',
  secretKey: process.env.WEBHOOK_SECRET_KEY || 'your-secret-key-here',
  allowedIPs: process.env.WEBHOOK_ALLOWED_IPS?.split(',') || [],
  maxRequestsPerMinute: parseInt(process.env.WEBHOOK_RATE_LIMIT || '60')
};

// Create webhook handler instance
const webhookHandler = new TradingViewWebhookHandler(webhookConfig);

// Handle POST requests from TradingView
export async function POST(request: NextRequest) {
  console.log('📡 Received TradingView webhook request');
  
  try {
    // Process the webhook
    const response = await webhookHandler.handleWebhook(request);
    
    // Log successful processing
    if (response.status === 200) {
      console.log('✅ Webhook processed successfully');
    } else {
      console.log(`⚠️ Webhook processing failed with status: ${response.status}`);
    }
    
    return response;
    
  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    
    return NextResponse.json({
      error: 'Internal server error',
      message: 'Failed to process webhook',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Handle GET requests for webhook info/testing
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');
  
  // Return webhook setup instructions
  if (action === 'setup') {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const webhookUrl = `${baseUrl}/api/webhook/tradingview`;
    const instructions = TradingViewWebhookHandler.getSetupInstructions(webhookUrl);
    
    return NextResponse.json({
      success: true,
      webhookUrl,
      instructions,
      config: {
        enabled: webhookConfig.enabled,
        rateLimit: webhookConfig.maxRequestsPerMinute,
        allowedIPs: webhookConfig.allowedIPs.length > 0 ? webhookConfig.allowedIPs : 'All IPs allowed'
      }
    });
  }
  
  // Return webhook status
  return NextResponse.json({
    success: true,
    message: 'TradingView Webhook Endpoint',
    status: webhookConfig.enabled ? 'enabled' : 'disabled',
    timestamp: new Date().toISOString(),
    endpoints: {
      webhook: '/api/webhook/tradingview',
      setup: '/api/webhook/tradingview?action=setup',
      test: '/api/webhook/tradingview?action=test'
    }
  });
}

// Handle OPTIONS requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
