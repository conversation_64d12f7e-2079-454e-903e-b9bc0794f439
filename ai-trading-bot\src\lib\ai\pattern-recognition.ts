// AI Pattern Recognition and Decision Making System
import { CandleData, TechnicalIndicators } from '../technical-analysis/indicators';
import { MarketState } from '../market-analysis/market-state';
import { TradeSignal } from '../trading/risk-management';

export interface PatternMatch {
  name: string;
  confidence: number;
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  timeframe: string;
  startIndex: number;
  endIndex: number;
  targetPrice?: number;
  stopLoss?: number;
  description: string;
}

export interface AIDecision {
  action: 'BUY' | 'SELL' | 'HOLD' | 'CLOSE';
  confidence: number;
  reasoning: string[];
  patterns: PatternMatch[];
  riskScore: number;
  expectedReturn: number;
  timeHorizon: number; // in minutes
}

export interface MarketRegime {
  type: 'TRENDING' | 'RANGING' | 'VOLATILE' | 'QUIET';
  strength: number;
  duration: number; // in candles
  characteristics: string[];
}

export class AIPatternRecognition {
  private learningData: Map<string, number[]> = new Map();
  private patternDatabase: Map<string, PatternMatch[]> = new Map();
  private successRates: Map<string, { wins: number; losses: number }> = new Map();

  constructor() {
    this.initializePatternDatabase();
  }

  // Enhanced AI decision making with multi-layer analysis
  makeDecision(
    candleData: CandleData[],
    indicators: TechnicalIndicators,
    marketState: MarketState,
    symbol: string
  ): AIDecision {
    // Multi-layer pattern detection
    const patterns = this.detectAdvancedPatterns(candleData, indicators);

    // Enhanced market regime analysis
    const regime = this.analyzeAdvancedMarketRegime(candleData, indicators, marketState);

    // Multi-factor sentiment calculation
    const sentiment = this.calculateAdvancedMarketSentiment(indicators, marketState, patterns);

    // Market structure analysis
    const marketStructure = this.analyzeMarketStructure(candleData, indicators);

    // Volume analysis
    const volumeAnalysis = this.analyzeVolumeProfile(indicators, candleData);

    // Generate enhanced decision with confidence scoring
    const decision = this.generateEnhancedDecision(
      patterns,
      regime,
      sentiment,
      marketState,
      marketStructure,
      volumeAnalysis
    );

    // Advanced learning and adaptation
    this.updateAdvancedLearningData(symbol, candleData, indicators, decision, marketState);

    return decision;
  }

  // Advanced pattern detection with ICT concepts
  private detectAdvancedPatterns(candleData: CandleData[], indicators: TechnicalIndicators): PatternMatch[] {
    const patterns: PatternMatch[] = [];

    // Traditional patterns
    patterns.push(...this.detectPatterns(candleData));

    // ICT-specific patterns
    patterns.push(...this.detectICTPatterns(candleData, indicators));

    // Smart Money Concepts patterns
    patterns.push(...this.detectSmartMoneyPatterns(candleData, indicators));

    // Filter and rank patterns by confidence
    return patterns
      .filter(pattern => pattern.confidence > 65)
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10); // Top 10 patterns
  }

  private detectICTPatterns(candleData: CandleData[], indicators: TechnicalIndicators): PatternMatch[] {
    const ictPatterns: PatternMatch[] = [];

    // Fair Value Gap patterns
    const fvgPatterns = this.analyzeFVGPatterns(indicators.fvg, candleData);
    ictPatterns.push(...fvgPatterns);

    // Order Block patterns
    const obPatterns = this.analyzeOrderBlockPatterns(indicators.orderBlocks, candleData);
    ictPatterns.push(...obPatterns);

    // Breaker Block patterns
    const bbPatterns = this.analyzeBreakerBlockPatterns(indicators.breakerBlocks, candleData);
    ictPatterns.push(...bbPatterns);

    // Liquidity patterns
    const liquidityPatterns = this.analyzeLiquidityPatterns(candleData, indicators);
    ictPatterns.push(...liquidityPatterns);

    return ictPatterns;
  }

  private detectSmartMoneyPatterns(candleData: CandleData[], indicators: TechnicalIndicators): PatternMatch[] {
    const smPatterns: PatternMatch[] = [];

    // Change of Character patterns
    const chochPatterns = this.analyzeCHoCHPatterns(indicators.choch, candleData);
    smPatterns.push(...chochPatterns);

    // Break of Structure patterns
    const bosPatterns = this.analyzeBOSPatterns(indicators.bos, candleData);
    smPatterns.push(...bosPatterns);

    // Market Structure Shift patterns
    const mssPatterns = this.analyzeMarketStructureShifts(candleData, indicators);
    smPatterns.push(...mssPatterns);

    return smPatterns;
  }

  private analyzeFVGPatterns(fvgs: any[], candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];

    // Find unfilled FVGs near current price
    const currentPrice = candleData[candleData.length - 1].close;
    const nearbyFVGs = fvgs.filter(fvg =>
      !fvg.filled && Math.abs(fvg.price - currentPrice) / currentPrice < 0.02 // Within 2%
    );

    for (const fvg of nearbyFVGs) {
      const confidence = this.calculateFVGConfidence(fvg, candleData);

      if (confidence > 70) {
        patterns.push({
          name: `${fvg.type === 'bullish' ? 'Bullish' : 'Bearish'} Fair Value Gap`,
          confidence,
          type: fvg.type === 'bullish' ? 'BULLISH' : 'BEARISH',
          timeframe: '1h',
          startIndex: fvg.start,
          endIndex: fvg.end,
          targetPrice: fvg.price,
          description: `Unfilled ${fvg.type} FVG acting as ${fvg.type === 'bullish' ? 'support' : 'resistance'}`
        });
      }
    }

    return patterns;
  }

  private analyzeOrderBlockPatterns(orderBlocks: any[], candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];

    const currentPrice = candleData[candleData.length - 1].close;
    const nearbyOBs = orderBlocks.filter(ob =>
      !ob.mitigation && Math.abs((ob.high + ob.low) / 2 - currentPrice) / currentPrice < 0.015 // Within 1.5%
    );

    for (const ob of nearbyOBs) {
      const confidence = this.calculateOrderBlockConfidence(ob, candleData);

      if (confidence > 75) {
        patterns.push({
          name: `${ob.type === 'bullish' ? 'Bullish' : 'Bearish'} Order Block`,
          confidence,
          type: ob.type === 'bullish' ? 'BULLISH' : 'BEARISH',
          timeframe: '1h',
          startIndex: ob.start,
          endIndex: ob.end,
          targetPrice: (ob.high + ob.low) / 2,
          description: `Unmitigated ${ob.type} order block with institutional interest`
        });
      }
    }

    return patterns;
  }

  private analyzeBreakerBlockPatterns(breakerBlocks: any[], candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];

    const currentPrice = candleData[candleData.length - 1].close;
    const nearbyBBs = breakerBlocks.filter(bb =>
      Math.abs((bb.high + bb.low) / 2 - currentPrice) / currentPrice < 0.02 // Within 2%
    );

    for (const bb of nearbyBBs) {
      const confidence = this.calculateBreakerBlockConfidence(bb, candleData);

      if (confidence > 70) {
        patterns.push({
          name: `${bb.type === 'bullish' ? 'Bullish' : 'Bearish'} Breaker Block`,
          confidence,
          type: bb.type === 'bullish' ? 'BULLISH' : 'BEARISH',
          timeframe: '1h',
          startIndex: bb.start,
          endIndex: bb.end,
          targetPrice: (bb.high + bb.low) / 2,
          description: `Broken order block now acting as ${bb.type === 'bullish' ? 'support' : 'resistance'}`
        });
      }
    }

    return patterns;
  }

  private calculateFVGConfidence(fvg: any, candleData: CandleData[]): number {
    let confidence = 70; // Base confidence

    // Age factor - newer FVGs are more reliable
    const currentIndex = candleData.length - 1;
    const age = currentIndex - fvg.end;
    if (age < 10) confidence += 10;
    else if (age < 20) confidence += 5;

    // Size factor - larger gaps are more significant
    const currentPrice = candleData[currentIndex].close;
    const gapSize = Math.abs(fvg.price - currentPrice) / currentPrice;
    if (gapSize > 0.005) confidence += 10; // 0.5% or larger

    // Volume factor - check volume around FVG formation
    const fvgVolume = candleData[fvg.start]?.volume || 0;
    const avgVolume = this.getAverageVolume(candleData, fvg.start);
    if (fvgVolume > avgVolume * 1.5) confidence += 10;

    return Math.min(confidence, 95);
  }

  private calculateOrderBlockConfidence(ob: any, candleData: CandleData[]): number {
    let confidence = 75; // Base confidence for order blocks

    // Freshness factor
    const currentIndex = candleData.length - 1;
    const age = currentIndex - ob.end;
    if (age < 15) confidence += 10;
    else if (age < 30) confidence += 5;

    // Size and significance
    const obSize = ob.high - ob.low;
    const avgRange = this.getAverageRange(candleData, ob.start);
    if (obSize > avgRange * 0.5) confidence += 10;

    // Volume confirmation
    const obVolume = this.getVolumeInRange(candleData, ob.start, ob.end);
    const avgVolume = this.getAverageVolume(candleData, ob.start);
    if (obVolume > avgVolume * 1.3) confidence += 10;

    return Math.min(confidence, 95);
  }

  private calculateBreakerBlockConfidence(bb: any, candleData: CandleData[]): number {
    let confidence = 70; // Base confidence for breaker blocks

    // Recency of break
    const currentIndex = candleData.length - 1;
    const age = currentIndex - bb.end;
    if (age < 20) confidence += 15;
    else if (age < 40) confidence += 10;

    // Strength of break (volume)
    // This would need to be calculated based on the break candle
    confidence += 10; // Placeholder

    return Math.min(confidence, 95);
  }

  private getAverageVolume(candleData: CandleData[], index: number): number {
    const lookback = 20;
    const start = Math.max(0, index - lookback);
    const volumes = candleData.slice(start, index).map(c => c.volume);
    return volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  }

  private getAverageRange(candleData: CandleData[], index: number): number {
    const lookback = 20;
    const start = Math.max(0, index - lookback);
    const ranges = candleData.slice(start, index).map(c => c.high - c.low);
    return ranges.reduce((sum, range) => sum + range, 0) / ranges.length;
  }

  private getVolumeInRange(candleData: CandleData[], start: number, end: number): number {
    return candleData.slice(start, end + 1).reduce((sum, candle) => sum + candle.volume, 0);
  }

  // Detect chart patterns using AI algorithms
  private detectPatterns(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    
    // Head and Shoulders pattern
    patterns.push(...this.detectHeadAndShoulders(candleData));
    
    // Double Top/Bottom patterns
    patterns.push(...this.detectDoubleTopBottom(candleData));
    
    // Triangle patterns
    patterns.push(...this.detectTriangles(candleData));
    
    // Flag and Pennant patterns
    patterns.push(...this.detectFlagsAndPennants(candleData));
    
    // Candlestick patterns
    patterns.push(...this.detectCandlestickPatterns(candleData));
    
    // Support/Resistance breaks
    patterns.push(...this.detectBreakoutPatterns(candleData));
    
    return patterns.filter(pattern => pattern.confidence > 60);
  }

  // Detect Head and Shoulders pattern
  private detectHeadAndShoulders(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    const minPatternLength = 20;
    
    if (candleData.length < minPatternLength) return patterns;
    
    for (let i = minPatternLength; i < candleData.length - 5; i++) {
      const window = candleData.slice(i - minPatternLength, i);
      const peaks = this.findPeaks(window);
      
      if (peaks.length >= 3) {
        const [leftShoulder, head, rightShoulder] = peaks.slice(-3);
        
        // Check if it forms a head and shoulders pattern
        if (this.isHeadAndShoulders(leftShoulder, head, rightShoulder, window)) {
          const confidence = this.calculatePatternConfidence('HEAD_AND_SHOULDERS', window);
          
          patterns.push({
            name: 'Head and Shoulders',
            confidence,
            type: 'BEARISH',
            timeframe: '1h',
            startIndex: i - minPatternLength,
            endIndex: i,
            targetPrice: this.calculateHSTarget(leftShoulder, head, rightShoulder, window),
            stopLoss: head.price * 1.02,
            description: 'Bearish reversal pattern with three peaks'
          });
        }
      }
    }
    
    return patterns;
  }

  // Detect Double Top/Bottom patterns
  private detectDoubleTopBottom(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    const minPatternLength = 15;
    
    if (candleData.length < minPatternLength) return patterns;
    
    for (let i = minPatternLength; i < candleData.length - 3; i++) {
      const window = candleData.slice(i - minPatternLength, i);
      
      // Double Top
      const peaks = this.findPeaks(window);
      if (peaks.length >= 2) {
        const [peak1, peak2] = peaks.slice(-2);
        if (this.isDoubleTop(peak1, peak2, window)) {
          const confidence = this.calculatePatternConfidence('DOUBLE_TOP', window);
          
          patterns.push({
            name: 'Double Top',
            confidence,
            type: 'BEARISH',
            timeframe: '1h',
            startIndex: i - minPatternLength,
            endIndex: i,
            targetPrice: this.calculateDoubleTopTarget(peak1, peak2, window),
            stopLoss: Math.max(peak1.price, peak2.price) * 1.01,
            description: 'Bearish reversal pattern with two equal peaks'
          });
        }
      }
      
      // Double Bottom
      const troughs = this.findTroughs(window);
      if (troughs.length >= 2) {
        const [trough1, trough2] = troughs.slice(-2);
        if (this.isDoubleBottom(trough1, trough2, window)) {
          const confidence = this.calculatePatternConfidence('DOUBLE_BOTTOM', window);
          
          patterns.push({
            name: 'Double Bottom',
            confidence,
            type: 'BULLISH',
            timeframe: '1h',
            startIndex: i - minPatternLength,
            endIndex: i,
            targetPrice: this.calculateDoubleBottomTarget(trough1, trough2, window),
            stopLoss: Math.min(trough1.price, trough2.price) * 0.99,
            description: 'Bullish reversal pattern with two equal lows'
          });
        }
      }
    }
    
    return patterns;
  }

  // Detect Triangle patterns
  private detectTriangles(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    const minPatternLength = 12;
    
    if (candleData.length < minPatternLength) return patterns;
    
    for (let i = minPatternLength; i < candleData.length - 3; i++) {
      const window = candleData.slice(i - minPatternLength, i);
      
      const highs = window.map((candle, idx) => ({ price: candle.high, index: idx }));
      const lows = window.map((candle, idx) => ({ price: candle.low, index: idx }));
      
      // Ascending Triangle
      if (this.isAscendingTriangle(highs, lows)) {
        patterns.push({
          name: 'Ascending Triangle',
          confidence: this.calculatePatternConfidence('ASCENDING_TRIANGLE', window),
          type: 'BULLISH',
          timeframe: '1h',
          startIndex: i - minPatternLength,
          endIndex: i,
          description: 'Bullish continuation pattern with horizontal resistance'
        });
      }
      
      // Descending Triangle
      if (this.isDescendingTriangle(highs, lows)) {
        patterns.push({
          name: 'Descending Triangle',
          confidence: this.calculatePatternConfidence('DESCENDING_TRIANGLE', window),
          type: 'BEARISH',
          timeframe: '1h',
          startIndex: i - minPatternLength,
          endIndex: i,
          description: 'Bearish continuation pattern with horizontal support'
        });
      }
      
      // Symmetrical Triangle
      if (this.isSymmetricalTriangle(highs, lows)) {
        patterns.push({
          name: 'Symmetrical Triangle',
          confidence: this.calculatePatternConfidence('SYMMETRICAL_TRIANGLE', window),
          type: 'NEUTRAL',
          timeframe: '1h',
          startIndex: i - minPatternLength,
          endIndex: i,
          description: 'Neutral pattern indicating potential breakout'
        });
      }
    }
    
    return patterns;
  }

  // Detect Flag and Pennant patterns
  private detectFlagsAndPennants(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    const minPatternLength = 8;
    
    if (candleData.length < minPatternLength + 5) return patterns;
    
    for (let i = minPatternLength + 5; i < candleData.length - 2; i++) {
      const trendWindow = candleData.slice(i - minPatternLength - 5, i - 5);
      const flagWindow = candleData.slice(i - 5, i);
      
      // Check for strong trend before flag
      const trendStrength = this.calculateTrendStrength(trendWindow);
      
      if (trendStrength > 0.7) { // Strong uptrend
        if (this.isBullishFlag(flagWindow)) {
          patterns.push({
            name: 'Bullish Flag',
            confidence: this.calculatePatternConfidence('BULLISH_FLAG', flagWindow),
            type: 'BULLISH',
            timeframe: '30m',
            startIndex: i - 5,
            endIndex: i,
            description: 'Bullish continuation pattern after strong uptrend'
          });
        }
      } else if (trendStrength < -0.7) { // Strong downtrend
        if (this.isBearishFlag(flagWindow)) {
          patterns.push({
            name: 'Bearish Flag',
            confidence: this.calculatePatternConfidence('BEARISH_FLAG', flagWindow),
            type: 'BEARISH',
            timeframe: '30m',
            startIndex: i - 5,
            endIndex: i,
            description: 'Bearish continuation pattern after strong downtrend'
          });
        }
      }
    }
    
    return patterns;
  }

  // Detect Candlestick patterns
  private detectCandlestickPatterns(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    
    if (candleData.length < 3) return patterns;
    
    for (let i = 2; i < candleData.length; i++) {
      const current = candleData[i];
      const prev1 = candleData[i - 1];
      const prev2 = candleData[i - 2];
      
      // Doji
      if (this.isDoji(current)) {
        patterns.push({
          name: 'Doji',
          confidence: 70,
          type: 'NEUTRAL',
          timeframe: '15m',
          startIndex: i,
          endIndex: i,
          description: 'Indecision candle indicating potential reversal'
        });
      }
      
      // Hammer
      if (this.isHammer(current)) {
        patterns.push({
          name: 'Hammer',
          confidence: 75,
          type: 'BULLISH',
          timeframe: '15m',
          startIndex: i,
          endIndex: i,
          description: 'Bullish reversal pattern with long lower shadow'
        });
      }
      
      // Shooting Star
      if (this.isShootingStar(current)) {
        patterns.push({
          name: 'Shooting Star',
          confidence: 75,
          type: 'BEARISH',
          timeframe: '15m',
          startIndex: i,
          endIndex: i,
          description: 'Bearish reversal pattern with long upper shadow'
        });
      }
      
      // Engulfing patterns
      if (this.isBullishEngulfing(prev1, current)) {
        patterns.push({
          name: 'Bullish Engulfing',
          confidence: 80,
          type: 'BULLISH',
          timeframe: '15m',
          startIndex: i - 1,
          endIndex: i,
          description: 'Strong bullish reversal pattern'
        });
      }
      
      if (this.isBearishEngulfing(prev1, current)) {
        patterns.push({
          name: 'Bearish Engulfing',
          confidence: 80,
          type: 'BEARISH',
          timeframe: '15m',
          startIndex: i - 1,
          endIndex: i,
          description: 'Strong bearish reversal pattern'
        });
      }
    }
    
    return patterns;
  }

  // Detect Breakout patterns
  private detectBreakoutPatterns(candleData: CandleData[]): PatternMatch[] {
    const patterns: PatternMatch[] = [];
    const lookback = 20;
    
    if (candleData.length < lookback + 1) return patterns;
    
    for (let i = lookback; i < candleData.length; i++) {
      const window = candleData.slice(i - lookback, i);
      const current = candleData[i];
      
      const resistance = Math.max(...window.map(c => c.high));
      const support = Math.min(...window.map(c => c.low));
      
      // Resistance breakout
      if (current.close > resistance && current.volume > this.getAverageVolume(window) * 1.5) {
        patterns.push({
          name: 'Resistance Breakout',
          confidence: 85,
          type: 'BULLISH',
          timeframe: '1h',
          startIndex: i - lookback,
          endIndex: i,
          targetPrice: resistance + (resistance - support) * 0.5,
          stopLoss: resistance * 0.98,
          description: 'Bullish breakout above resistance with high volume'
        });
      }
      
      // Support breakdown
      if (current.close < support && current.volume > this.getAverageVolume(window) * 1.5) {
        patterns.push({
          name: 'Support Breakdown',
          confidence: 85,
          type: 'BEARISH',
          timeframe: '1h',
          startIndex: i - lookback,
          endIndex: i,
          targetPrice: support - (resistance - support) * 0.5,
          stopLoss: support * 1.02,
          description: 'Bearish breakdown below support with high volume'
        });
      }
    }
    
    return patterns;
  }

  // Analyze market regime
  private analyzeMarketRegime(candleData: CandleData[], indicators: TechnicalIndicators): MarketRegime {
    const recentData = candleData.slice(-50);
    const volatility = this.calculateVolatility(recentData);
    const trendStrength = this.calculateTrendStrength(recentData);
    
    let type: MarketRegime['type'];
    let characteristics: string[] = [];
    
    if (Math.abs(trendStrength) > 0.6) {
      type = 'TRENDING';
      characteristics.push(trendStrength > 0 ? 'Strong uptrend' : 'Strong downtrend');
    } else if (volatility > 0.02) {
      type = 'VOLATILE';
      characteristics.push('High volatility', 'Choppy price action');
    } else if (volatility < 0.005) {
      type = 'QUIET';
      characteristics.push('Low volatility', 'Consolidation phase');
    } else {
      type = 'RANGING';
      characteristics.push('Sideways movement', 'Range-bound trading');
    }
    
    // Add volume characteristics
    const avgVolume = this.getAverageVolume(recentData);
    const currentVolume = recentData[recentData.length - 1].volume;
    
    if (currentVolume > avgVolume * 1.5) {
      characteristics.push('High volume');
    } else if (currentVolume < avgVolume * 0.7) {
      characteristics.push('Low volume');
    }
    
    return {
      type,
      strength: Math.abs(trendStrength),
      duration: this.calculateRegimeDuration(recentData, type),
      characteristics
    };
  }

  // Calculate market sentiment
  private calculateMarketSentiment(indicators: TechnicalIndicators, marketState: MarketState): number {
    let sentiment = 0;
    let factors = 0;
    
    // RSI sentiment
    if (indicators.rsi > 70) sentiment -= 2;
    else if (indicators.rsi > 60) sentiment -= 1;
    else if (indicators.rsi < 30) sentiment += 2;
    else if (indicators.rsi < 40) sentiment += 1;
    factors++;
    
    // MACD sentiment
    if (indicators.macd.MACD > indicators.macd.signal) sentiment += 1;
    else sentiment -= 1;
    factors++;
    
    // Trend sentiment
    if (marketState.trend === 'BULLISH') sentiment += marketState.strength / 50;
    else if (marketState.trend === 'BEARISH') sentiment -= marketState.strength / 50;
    factors++;
    
    // Momentum sentiment
    const momentumScore = {
      'STRONG_BULLISH': 2,
      'WEAK_BULLISH': 1,
      'NEUTRAL': 0,
      'WEAK_BEARISH': -1,
      'STRONG_BEARISH': -2
    }[marketState.momentum];
    
    sentiment += momentumScore;
    factors++;
    
    return factors > 0 ? sentiment / factors : 0;
  }

  // Generate AI decision
  private generateDecision(
    patterns: PatternMatch[],
    regime: MarketRegime,
    sentiment: number,
    marketState: MarketState
  ): AIDecision {
    let action: AIDecision['action'] = 'HOLD';
    let confidence = 0;
    let reasoning: string[] = [];
    let riskScore = 50; // Base risk score
    let expectedReturn = 0;
    
    // Analyze patterns
    const bullishPatterns = patterns.filter(p => p.type === 'BULLISH');
    const bearishPatterns = patterns.filter(p => p.type === 'BEARISH');
    
    const bullishConfidence = bullishPatterns.reduce((sum, p) => sum + p.confidence, 0) / Math.max(bullishPatterns.length, 1);
    const bearishConfidence = bearishPatterns.reduce((sum, p) => sum + p.confidence, 0) / Math.max(bearishPatterns.length, 1);
    
    // Decision logic
    if (bullishPatterns.length > bearishPatterns.length && sentiment > 0.5) {
      action = 'BUY';
      confidence = Math.min((bullishConfidence + sentiment * 20 + marketState.confidence) / 3, 95);
      reasoning.push(`${bullishPatterns.length} bullish patterns detected`);
      reasoning.push(`Positive market sentiment (${sentiment.toFixed(2)})`);
      expectedReturn = 2.5; // Expected 2.5% return
    } else if (bearishPatterns.length > bullishPatterns.length && sentiment < -0.5) {
      action = 'SELL';
      confidence = Math.min((bearishConfidence + Math.abs(sentiment) * 20 + marketState.confidence) / 3, 95);
      reasoning.push(`${bearishPatterns.length} bearish patterns detected`);
      reasoning.push(`Negative market sentiment (${sentiment.toFixed(2)})`);
      expectedReturn = 2.5; // Expected 2.5% return
    } else {
      reasoning.push('Mixed signals or insufficient confidence');
      reasoning.push(`Market regime: ${regime.type}`);
    }
    
    // Adjust for market regime
    if (regime.type === 'VOLATILE') {
      riskScore += 20;
      reasoning.push('High volatility increases risk');
    } else if (regime.type === 'QUIET') {
      riskScore -= 10;
      reasoning.push('Low volatility reduces risk');
    }
    
    // Adjust for market state
    if (marketState.riskLevel === 'HIGH') {
      riskScore += 15;
      confidence *= 0.9;
    } else if (marketState.riskLevel === 'LOW') {
      riskScore -= 10;
      confidence *= 1.1;
    }
    
    return {
      action,
      confidence: Math.max(Math.min(confidence, 95), 5),
      reasoning,
      patterns,
      riskScore: Math.max(Math.min(riskScore, 100), 0),
      expectedReturn,
      timeHorizon: this.calculateTimeHorizon(patterns, regime)
    };
  }

  // Helper methods for pattern detection
  private findPeaks(data: CandleData[]): { price: number; index: number }[] {
    const peaks: { price: number; index: number }[] = [];
    
    for (let i = 2; i < data.length - 2; i++) {
      if (data[i].high > data[i-1].high && data[i].high > data[i-2].high &&
          data[i].high > data[i+1].high && data[i].high > data[i+2].high) {
        peaks.push({ price: data[i].high, index: i });
      }
    }
    
    return peaks;
  }

  private findTroughs(data: CandleData[]): { price: number; index: number }[] {
    const troughs: { price: number; index: number }[] = [];
    
    for (let i = 2; i < data.length - 2; i++) {
      if (data[i].low < data[i-1].low && data[i].low < data[i-2].low &&
          data[i].low < data[i+1].low && data[i].low < data[i+2].low) {
        troughs.push({ price: data[i].low, index: i });
      }
    }
    
    return troughs;
  }

  private isHeadAndShoulders(left: any, head: any, right: any, data: CandleData[]): boolean {
    // Head should be higher than both shoulders
    if (head.price <= left.price || head.price <= right.price) return false;
    
    // Shoulders should be roughly equal (within 2%)
    const shoulderDiff = Math.abs(left.price - right.price) / Math.max(left.price, right.price);
    return shoulderDiff < 0.02;
  }

  private isDoubleTop(peak1: any, peak2: any, data: CandleData[]): boolean {
    const priceDiff = Math.abs(peak1.price - peak2.price) / Math.max(peak1.price, peak2.price);
    return priceDiff < 0.015; // Within 1.5%
  }

  private isDoubleBottom(trough1: any, trough2: any, data: CandleData[]): boolean {
    const priceDiff = Math.abs(trough1.price - trough2.price) / Math.max(trough1.price, trough2.price);
    return priceDiff < 0.015; // Within 1.5%
  }

  private isAscendingTriangle(highs: any[], lows: any[]): boolean {
    // Implementation for ascending triangle detection
    return false; // Simplified for now
  }

  private isDescendingTriangle(highs: any[], lows: any[]): boolean {
    // Implementation for descending triangle detection
    return false; // Simplified for now
  }

  private isSymmetricalTriangle(highs: any[], lows: any[]): boolean {
    // Implementation for symmetrical triangle detection
    return false; // Simplified for now
  }

  private isBullishFlag(data: CandleData[]): boolean {
    // Check for slight downward slope in consolidation
    const firstPrice = data[0].close;
    const lastPrice = data[data.length - 1].close;
    const decline = (firstPrice - lastPrice) / firstPrice;
    return decline > 0 && decline < 0.05; // 0-5% decline
  }

  private isBearishFlag(data: CandleData[]): boolean {
    // Check for slight upward slope in consolidation
    const firstPrice = data[0].close;
    const lastPrice = data[data.length - 1].close;
    const rise = (lastPrice - firstPrice) / firstPrice;
    return rise > 0 && rise < 0.05; // 0-5% rise
  }

  private isDoji(candle: CandleData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    return bodySize / totalRange < 0.1; // Body is less than 10% of total range
  }

  private isHammer(candle: CandleData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
  }

  private isShootingStar(candle: CandleData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5;
  }

  private isBullishEngulfing(prev: CandleData, current: CandleData): boolean {
    return prev.close < prev.open && // Previous candle is bearish
           current.close > current.open && // Current candle is bullish
           current.open < prev.close && // Current opens below previous close
           current.close > prev.open; // Current closes above previous open
  }

  private isBearishEngulfing(prev: CandleData, current: CandleData): boolean {
    return prev.close > prev.open && // Previous candle is bullish
           current.close < current.open && // Current candle is bearish
           current.open > prev.close && // Current opens above previous close
           current.close < prev.open; // Current closes below previous open
  }

  private calculatePatternConfidence(patternName: string, data: CandleData[]): number {
    // Base confidence from historical success rate
    const successRate = this.successRates.get(patternName);
    let baseConfidence = 70;
    
    if (successRate) {
      const total = successRate.wins + successRate.losses;
      if (total > 10) {
        baseConfidence = (successRate.wins / total) * 100;
      }
    }
    
    // Adjust based on volume and volatility
    const avgVolume = this.getAverageVolume(data);
    const currentVolume = data[data.length - 1].volume;
    const volumeMultiplier = currentVolume / avgVolume;
    
    let confidence = baseConfidence;
    if (volumeMultiplier > 1.5) confidence += 10;
    else if (volumeMultiplier < 0.7) confidence -= 10;
    
    return Math.max(Math.min(confidence, 95), 30);
  }

  private calculateTrendStrength(data: CandleData[]): number {
    if (data.length < 2) return 0;
    
    const firstPrice = data[0].close;
    const lastPrice = data[data.length - 1].close;
    const change = (lastPrice - firstPrice) / firstPrice;
    
    // Normalize to -1 to 1 range
    return Math.max(Math.min(change * 10, 1), -1);
  }

  private calculateVolatility(data: CandleData[]): number {
    const returns = [];
    for (let i = 1; i < data.length; i++) {
      const ret = (data[i].close - data[i-1].close) / data[i-1].close;
      returns.push(ret);
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  }

  private getAverageVolume(data: CandleData[]): number {
    return data.reduce((sum, candle) => sum + candle.volume, 0) / data.length;
  }

  private calculateRegimeDuration(data: CandleData[], type: MarketRegime['type']): number {
    // Simplified calculation - count consecutive candles of same regime
    return Math.min(data.length, 20);
  }

  private calculateTimeHorizon(patterns: PatternMatch[], regime: MarketRegime): number {
    // Calculate expected time horizon based on patterns and regime
    let horizon = 60; // Default 1 hour
    
    if (patterns.some(p => p.name.includes('Triangle') || p.name.includes('Flag'))) {
      horizon = 30; // Shorter for continuation patterns
    }
    
    if (regime.type === 'VOLATILE') {
      horizon *= 0.7; // Faster moves in volatile markets
    } else if (regime.type === 'QUIET') {
      horizon *= 1.5; // Slower moves in quiet markets
    }
    
    return Math.round(horizon);
  }

  private calculateHSTarget(left: any, head: any, right: any, data: CandleData[]): number {
    // Calculate neckline and project target
    const neckline = (left.price + right.price) / 2;
    const headHeight = head.price - neckline;
    return neckline - headHeight;
  }

  private calculateDoubleTopTarget(peak1: any, peak2: any, data: CandleData[]): number {
    const support = Math.min(...data.map(c => c.low));
    const resistance = Math.max(peak1.price, peak2.price);
    return support - (resistance - support) * 0.5;
  }

  private calculateDoubleBottomTarget(trough1: any, trough2: any, data: CandleData[]): number {
    const resistance = Math.max(...data.map(c => c.high));
    const support = Math.min(trough1.price, trough2.price);
    return resistance + (resistance - support) * 0.5;
  }

  private initializePatternDatabase(): void {
    // Initialize with some default success rates
    this.successRates.set('HEAD_AND_SHOULDERS', { wins: 7, losses: 3 });
    this.successRates.set('DOUBLE_TOP', { wins: 6, losses: 4 });
    this.successRates.set('DOUBLE_BOTTOM', { wins: 6, losses: 4 });
    this.successRates.set('BULLISH_FLAG', { wins: 8, losses: 2 });
    this.successRates.set('BEARISH_FLAG', { wins: 8, losses: 2 });
  }

  private updateLearningData(
    symbol: string,
    candleData: CandleData[],
    indicators: TechnicalIndicators,
    decision: AIDecision
  ): void {
    // Store learning data for future improvement
    const key = `${symbol}_${decision.action}`;
    const features = [
      indicators.rsi,
      indicators.macd.MACD,
      indicators.vwap,
      decision.confidence,
      decision.riskScore
    ];
    
    this.learningData.set(key, features);
  }
}
