# 🤖 AI Trading Bot - Professional Trading System

A sophisticated AI-powered trading bot built with Next.js, TypeScript, and advanced technical analysis algorithms. This system provides real-time market analysis, pattern recognition, risk management, and automated trading signals for Forex and commodity markets.

## ✨ Features

### 🔍 Advanced Technical Analysis
- **EMA (Exponential Moving Average)** - Trend following indicator
- **RSI (Relative Strength Index)** - Momentum oscillator
- **MACD** - Moving Average Convergence Divergence
- **VWAP** - Volume Weighted Average Price
- **Supertrend** - Trend following indicator
- **Volume Profile** - Market structure analysis
- **Fair Value Gaps (FVG)** - Price inefficiency detection
- **Change of Character (CHoCH)** - Trend reversal identification
- **Break of Structure (BOS)** - Market structure breaks
- **Support/Resistance Levels** - Key price levels
- **Order Blocks & Breaker Blocks** - Institutional trading zones

### 🧠 AI Pattern Recognition
- **Chart Pattern Detection** - Head & Shoulders, Double Tops/Bottoms, Triangles
- **Candlestick Patterns** - Doji, <PERSON>, Engulfing patterns
- **Market Regime Analysis** - Trending, Ranging, Volatile, Quiet markets
- **Sentiment Analysis** - Multi-factor market sentiment calculation
- **Confidence Scoring** - AI-driven signal confidence assessment

### 📊 Real-Time Market Analysis
- **Live Price Feeds** - Real-time quotes and market data
- **Multi-Timeframe Analysis** - 1m, 5m, 15m, 1h, 4h, 1d
- **Trading Session Detection** - Asian, London, New York sessions
- **Market State Analysis** - Trend, volatility, liquidity assessment
- **Risk Level Calculation** - Dynamic risk assessment

### 🛡️ Risk Management
- **Position Sizing** - Automatic position size calculation
- **Stop Loss & Take Profit** - Multiple TP levels (TP1, TP2, TP3)
- **Risk/Reward Ratios** - Minimum 1.5:1 R/R enforcement
- **Daily Risk Limits** - Maximum daily exposure controls
- **Portfolio Management** - Multi-symbol risk monitoring

### 🔔 Alert System
- **Telegram Notifications** - Real-time trade signals via Telegram
- **Email Alerts** - Comprehensive email notifications
- **Countdown Alerts** - 5-minute trade execution warnings
- **Risk Warnings** - Automatic risk threshold alerts
- **System Status** - Bot health monitoring

### 📈 Supported Markets
- **Major Forex Pairs** - EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD
- **Minor Forex Pairs** - EUR/JPY, GBP/JPY, EUR/GBP, EUR/AUD, etc.
- **Exotic Pairs** - USD/TRY, USD/ZAR, USD/MXN, etc.
- **Commodities** - Gold (XAU/USD), Silver (XAG/USD), Oil, Natural Gas

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Modern web browser

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/ai-trading-bot.git
cd ai-trading-bot
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment variables**
```bash
cp .env.local.example .env.local
# Edit .env.local with your configuration
```

4. **Start the development server**
```bash
npm run dev
```

5. **Open your browser**
Navigate to `http://localhost:3000`

## ⚙️ Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Demo Mode (recommended for testing)
DEMO_MODE=true
DEMO_ACCOUNT_BALANCE=10000

# Telegram Notifications (optional)
NEXT_PUBLIC_TELEGRAM_BOT_TOKEN=your_bot_token
NEXT_PUBLIC_TELEGRAM_CHAT_ID=your_chat_id

# Email Notifications (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Risk Management
MAX_RISK_PER_TRADE=2
MAX_DAILY_RISK=6
MAX_OPEN_TRADES=3
MIN_RISK_REWARD_RATIO=1.5

# AI Settings
AI_CONFIDENCE_THRESHOLD=75
```

### Telegram Setup (Optional)

1. Create a Telegram bot via [@BotFather](https://t.me/botfather)
2. Get your bot token
3. Get your chat ID by messaging [@userinfobot](https://t.me/userinfobot)
4. Add the credentials to your `.env.local` file

## 📱 Usage

### Dashboard Overview
- **Market Overview** - Real-time market summary for all tracked symbols
- **Trading Chart** - Interactive charts with technical indicators
- **Trade Signals** - AI-generated buy/sell signals with analysis
- **Risk Management** - Portfolio risk monitoring and controls
- **Alerts** - Notification history and configuration
- **Settings** - Bot configuration and preferences

### Understanding Signals

Each trade signal includes:
- **Entry Price** - Recommended entry level
- **Stop Loss** - Risk management exit point
- **Take Profit Levels** - TP1, TP2, TP3 targets
- **Risk/Reward Ratio** - Expected return vs risk
- **Confidence Score** - AI confidence percentage
- **Analysis Reasoning** - Why the signal was generated

### Risk Management

The bot automatically:
- Calculates position sizes based on account balance
- Enforces maximum risk per trade (default: 2%)
- Monitors daily risk exposure (default: 6%)
- Limits concurrent open trades (default: 3)
- Maintains minimum risk/reward ratios (default: 1.5:1)

## ⚠️ Important Disclaimers

### Demo Mode
- This bot runs in **DEMO MODE** by default
- All trades are simulated - no real money is involved
- Market data is simulated for demonstration purposes
- Performance results are hypothetical

### Risk Warning
- **Trading involves substantial risk of loss**
- Past performance does not guarantee future results
- Never risk more than you can afford to lose
- This software is for educational purposes only
- Always test thoroughly before live trading

### Legal Notice
- This software is provided "as is" without warranty
- Users are responsible for their own trading decisions
- The developers are not liable for any financial losses
- Comply with your local financial regulations

## � **Getting Started - خطوات تشغيل التطبيق**

### 📋 **المتطلبات الأساسية (Prerequisites):**

#### 1. **Node.js (الإصدار 18 أو أحدث):**
```bash
# تحقق من إصدار Node.js
node --version

# إذا لم يكن مثبت، حمله من:
# https://nodejs.org/
```

#### 2. **npm أو yarn:**
```bash
# تحقق من npm
npm --version

# أو yarn (اختياري)
yarn --version
```

### 🔧 **خطوات التثبيت والتشغيل:**

#### **الخطوة 1: تحميل المشروع**
```bash
# إذا كان لديك Git
git clone <repository-url>
cd ai-trading-bot

# أو إذا كان لديك ملف مضغوط
# فك الضغط وانتقل إلى المجلد
cd ai-trading-bot
```

#### **الخطوة 2: تثبيت التبعيات**
```bash
# باستخدام npm (الأكثر شيوعاً)
npm install

# أو باستخدام yarn
yarn install

# أو باستخدام pnpm
pnpm install
```

#### **الخطوة 3: تشغيل الخادم**
```bash
# باستخدام npm
npm run dev

# أو باستخدام yarn
yarn dev

# أو باستخدام pnpm
pnpm dev
```

#### **الخطوة 4: فتح التطبيق**
```
افتح المتصفح وانتقل إلى:
http://localhost:3000
```

---

## ⚡ **التشغيل السريع (Quick Start)**

### 🔥 **للمبتدئين - 4 خطوات فقط:**

1. **افتح Terminal/Command Prompt**
2. **انتقل إلى مجلد المشروع:**
   ```bash
   cd path/to/ai-trading-bot
   ```
3. **ثبت التبعيات:**
   ```bash
   npm install
   ```
4. **شغل التطبيق:**
   ```bash
   npm run dev
   ```
5. **افتح المتصفح:**
   ```
   http://localhost:3000
   ```

### 🔄 **للتشغيل اليومي:**
```bash
# انتقل إلى مجلد المشروع
cd ai-trading-bot

# شغل التطبيق
npm run dev

# افتح http://localhost:3000 في المتصفح
```

---

## 🛠️ **حل المشاكل الشائعة (Troubleshooting)**

### ❌ **مشكلة: "npm not found"**
```bash
# الحل: ثبت Node.js من الموقع الرسمي
# https://nodejs.org/
# ثم أعد تشغيل Terminal
```

### ❌ **مشكلة: "Port 3000 already in use"**
```bash
# الحل 1: استخدم منفذ آخر
npm run dev -- --port 3001

# الحل 2: أوقف العملية التي تستخدم المنفذ
# Windows:
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Mac/Linux:
lsof -ti:3000 | xargs kill
```

### ❌ **مشكلة: "Module not found"**
```bash
# الحل: أعد تثبيت التبعيات
rm -rf node_modules
rm package-lock.json
npm install
```

### ❌ **مشكلة: "Permission denied"**
```bash
# Windows: شغل Terminal كمدير (Run as Administrator)
# Mac/Linux: استخدم sudo
sudo npm install
```

### ❌ **مشكلة: التطبيق لا يفتح**
```bash
# تأكد من:
1. الخادم يعمل (npm run dev)
2. لا توجد أخطاء في Terminal
3. المنفذ 3000 متاح
4. المتصفح يدعم JavaScript
```

---

## 🎯 **كيفية الاستخدام (How to Use)**

### 🔧 **للمبتدئين:**
1. **ابدأ بمحرك التوصيات** - اختر مستوى "أساسي"
2. **جرب تحليل فيبوناتشي** - لفهم المستويات
3. **انتقل للنظام الاحترافي** - عندما تصبح جاهزاً

### 🏆 **للمحترفين:**
1. **استخدم النظام الاحترافي الشامل** - للتحليل الكامل
2. **ادمج Order Blocks** - مع التحليل الفني
3. **راقب البيانات الحقيقية** - من TradingView

### 📱 **نصائح الاستخدام:**
- **افتح Developer Tools (F12)** - لمراقبة الأداء
- **جرب أزواج مختلفة** - لفهم السوق
- **اقرأ التوصيات بعناية** - مع الأسباب المفصلة
- **لا تعتمد على التوصيات فقط** - استخدم تحليلك الخاص

---

## 📁 **هيكل المشروع (Project Structure)**

```
ai-trading-bot/
├── 📁 src/
│   ├── 📁 app/                 # صفحات التطبيق
│   │   ├── 📄 page.tsx         # الصفحة الرئيسية
│   │   ├── 📄 layout.tsx       # تخطيط عام
│   │   └── 📄 globals.css      # أنماط عامة
│   ├── 📁 components/          # مكونات React
│   │   ├── 📄 ProfessionalTradingSystem.tsx
│   │   ├── 📄 ProfessionalRecommendationEngine.tsx
│   │   ├── 📄 ProfessionalOrderBlocks.tsx
│   │   ├── 📄 FibonacciAnalysis.tsx
│   │   └── 📄 ...
│   └── 📁 utils/               # أدوات مساعدة
│       └── 📄 tradingViewAPI.ts
├── 📁 public/                  # ملفات عامة
│   ├── 🖼️ icon.svg            # أيقونة التطبيق
│   ├── 🖼️ logo.svg            # شعار التطبيق
│   └── 🖼️ favicon.ico         # أيقونة المتصفح
├── 📄 package.json             # تبعيات المشروع
├── 📄 next.config.js           # إعدادات Next.js
├── 📄 tailwind.config.js       # إعدادات Tailwind CSS
└── 📄 README.md               # هذا الملف
```

---

## 📞 **الدعم والمساعدة (Support)**

### 🐛 **الإبلاغ عن الأخطاء:**
- افتح Developer Tools (F12)
- تحقق من Console للأخطاء
- أرسل تفاصيل الخطأ مع لقطة شاشة

### 💡 **طلب ميزات جديدة:**
- اقترح تحسينات
- شارك أفكارك
- ساهم في التطوير

---

## �📄 License

This project is licensed under the MIT License.

---

**⚡ Built with passion for algorithmic trading and AI innovation**

*Remember: This is a demo trading bot. Always practice proper risk management and never trade with money you cannot afford to lose.*
