{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AutoTradingRobot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface RobotSettings {\n  enabled: boolean;\n  maxRiskPerTrade: number;\n  maxDailyRisk: number;\n  minConfidence: number;\n  allowedPairs: string[];\n  tradingHours: {\n    start: string;\n    end: string;\n  };\n  maxOpenTrades: number;\n}\n\ninterface RobotStats {\n  totalTrades: number;\n  winningTrades: number;\n  losingTrades: number;\n  totalProfit: number;\n  winRate: number;\n  profitFactor: number;\n  currentDrawdown: number;\n}\n\nexport default function AutoTradingRobot() {\n  const [robotSettings, setRobotSettings] = useState<RobotSettings>({\n    enabled: false,\n    maxRiskPerTrade: 2,\n    maxDailyRisk: 6,\n    minConfidence: 80,\n    allowedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],\n    tradingHours: {\n      start: '08:00',\n      end: '18:00'\n    },\n    maxOpenTrades: 3\n  });\n\n  const [robotStats, setRobotStats] = useState<RobotStats>({\n    totalTrades: 47,\n    winningTrades: 34,\n    losingTrades: 13,\n    totalProfit: 2850.50,\n    winRate: 72.3,\n    profitFactor: 1.85,\n    currentDrawdown: 3.2\n  });\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [lastAction, setLastAction] = useState('');\n\n  // Simulate robot activity\n  useEffect(() => {\n    if (robotSettings.enabled && isRunning) {\n      const interval = setInterval(() => {\n        const actions = [\n          'تحليل إشارة EUR/USD...',\n          'فحص مستويات الدعم والمقاومة...',\n          'تقييم مخاطر الصفقة...',\n          'مراقبة Order Blocks...',\n          'تحليل Fair Value Gaps...',\n          'فحص تدفق الأموال الذكية...',\n          'تنفيذ صفقة شراء EUR/USD',\n          'إغلاق صفقة بربح +45 نقطة',\n          'وضع وقف خسارة متحرك...',\n          'مراقبة الجلسة الآسيوية...'\n        ];\n        \n        const randomAction = actions[Math.floor(Math.random() * actions.length)];\n        setLastAction(randomAction);\n      }, 3000);\n\n      return () => clearInterval(interval);\n    }\n  }, [robotSettings.enabled, isRunning]);\n\n  const toggleRobot = () => {\n    setRobotSettings(prev => ({ ...prev, enabled: !prev.enabled }));\n    setIsRunning(!isRunning);\n    if (!robotSettings.enabled) {\n      setLastAction('تم تشغيل الروبوت - بدء المراقبة...');\n    } else {\n      setLastAction('تم إيقاف الروبوت');\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n            🤖 روبوت التداول الآلي\n            <span className={`mr-2 text-xs px-2 py-1 rounded-full ${\n              robotSettings.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n            }`}>\n              {robotSettings.enabled ? 'نشط' : 'متوقف'}\n            </span>\n          </h3>\n          <button\n            onClick={toggleRobot}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              robotSettings.enabled\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-green-600 hover:bg-green-700 text-white'\n            }`}\n          >\n            {robotSettings.enabled ? '⏹️ إيقاف الروبوت' : '▶️ تشغيل الروبوت'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Robot Settings */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              ⚙️ إعدادات الروبوت\n            </h4>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للمخاطرة لكل صفقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  value={robotSettings.maxRiskPerTrade}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxRiskPerTrade: parseFloat(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.maxRiskPerTrade}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأدنى لمستوى الثقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"60\"\n                  max=\"95\"\n                  step=\"5\"\n                  value={robotSettings.minConfidence}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    minConfidence: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.minConfidence}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للصفقات المفتوحة\n                </label>\n                <select\n                  value={robotSettings.maxOpenTrades}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxOpenTrades: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                >\n                  <option value={1}>1 صفقة</option>\n                  <option value={2}>2 صفقة</option>\n                  <option value={3}>3 صفقات</option>\n                  <option value={5}>5 صفقات</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  ساعات التداول\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.start}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, start: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.end}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, end: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Robot Stats */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              📊 إحصائيات الأداء\n            </h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{robotStats.totalTrades}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الصفقات</div>\n              </div>\n              \n              <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{robotStats.winRate.toFixed(1)}%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">معدل النجاح</div>\n              </div>\n              \n              <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">${robotStats.totalProfit.toFixed(2)}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الربح</div>\n              </div>\n              \n              <div className=\"bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600\">{robotStats.profitFactor}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">عامل الربح</div>\n              </div>\n            </div>\n\n            {/* Robot Activity */}\n            <div className=\"mt-6\">\n              <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                🔄 النشاط الحالي\n              </h5>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  {robotSettings.enabled && (\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  )}\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {lastAction || 'الروبوت في وضع الانتظار...'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Warning */}\n        <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n              <span className=\"text-white text-sm\">⚠</span>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                تحذير مهم - الروبوت التجريبي\n              </h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                هذا روبوت تجريبي للأغراض التعليمية فقط. لا يتم تنفيذ صفقات حقيقية. \n                اختبر جميع الإعدادات بعناية قبل استخدام أي نظام تداول آلي حقيقي.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,cAAc;YAAC;YAAU;YAAU;YAAU;SAAS;QACtD,cAAc;YACZ,OAAO;YACP,KAAK;QACP;QACA,eAAe;IACjB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,OAAO,IAAI,WAAW;gBACtC,MAAM,WAAW;2DAAY;wBAC3B,MAAM,UAAU;4BACd;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;wBAED,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;wBACxE,cAAc;oBAChB;0DAAG;gBAEH;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC,cAAc,OAAO;QAAE;KAAU;IAErC,MAAM,cAAc;QAClB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS,CAAC,KAAK,OAAO;YAAC,CAAC;QAC7D,aAAa,CAAC;QACd,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,cAAc;QAChB,OAAO;YACL,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAwE;8CAEpF,6LAAC;oCAAK,WAAW,AAAC,uCAEjB,OADC,cAAc,OAAO,GAAG,gCAAgC;8CAEvD,cAAc,OAAO,GAAG,QAAQ;;;;;;;;;;;;sCAGrC,6LAAC;4BACC,SAAS;4BACT,WAAW,AAAC,sDAIX,OAHC,cAAc,OAAO,GACjB,2CACA;sCAGL,cAAc,OAAO,GAAG,qBAAqB;;;;;;;;;;;;;;;;;0BAKpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,eAAe;wDACpC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5C,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,eAAe;4DAAC;;;;;;;;;;;;;0DAInC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,aAAa;4DAAC;;;;;;;;;;;;;0DAIjC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;0EAEV,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;;;;;;;;;;;;;0DAItB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,KAAK;gEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC9D,CAAC;gEACD,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,GAAG;gEACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC5D,CAAC;gEACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,WAAW,WAAW;;;;;;kEAC1E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAoC,WAAW,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEACjF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAqC;4DAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;kEACrF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,WAAW,YAAY;;;;;;kEAC5E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,kBACpB,6LAAC;4DAAI,WAAU;;;;;;sEAEjB,6LAAC;4DAAK,WAAU;sEACb,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AdvancedMarketAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport TimeframeAnalysis from './TimeframeAnalysis';\n\ninterface MarketCondition {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  buyPressure: number;\n  sellPressure: number;\n  trend: {\n    direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    strength: number;\n    timeframes: {\n      '1m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '5m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '15m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '4h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1d': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    };\n  };\n  technicalAnalysis: {\n    rsi: number;\n    macd: number;\n    ema: number;\n    support: number;\n    resistance: number;\n    recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n    confidence: number;\n  };\n  flag: string;\n}\n\ninterface AIRecommendation {\n  symbol: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  timeframe: string;\n  reasoning: string[];\n  accuracy: number;\n}\n\nexport default function AdvancedMarketAnalysis() {\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']);\n  const [marketConditions, setMarketConditions] = useState<MarketCondition[]>([]);\n  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n  const allPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'EURCHF', 'GBPCHF', 'AUDCHF', 'CADJPY',\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD', 'USDJPY'\n  ];\n\n  // محاكاة الاتصال بـ TradingView\n  useEffect(() => {\n    const connectToTradingView = async () => {\n      setIsConnected(true);\n      // محاكاة تحميل البيانات\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      generateMarketData();\n    };\n\n    connectToTradingView();\n    \n    // تحديث البيانات كل 5 ثوانٍ\n    const interval = setInterval(() => {\n      generateMarketData();\n      setLastUpdate(new Date());\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [selectedTimeframe, selectedPairs]);\n\n  const generateMarketData = () => {\n    const conditions: MarketCondition[] = selectedPairs.map(symbol => {\n      const basePrice = getBasePrice(symbol);\n      const change = (Math.random() - 0.5) * basePrice * 0.02;\n      const buyPressure = Math.random() * 100;\n      const sellPressure = 100 - buyPressure;\n      \n      return {\n        symbol,\n        name: getPairName(symbol),\n        price: basePrice + change,\n        change,\n        changePercent: (change / basePrice) * 100,\n        volume: Math.floor(Math.random() * 2000000) + 500000,\n        buyPressure,\n        sellPressure,\n        trend: {\n          direction: buyPressure > 60 ? 'BULLISH' : buyPressure < 40 ? 'BEARISH' : 'NEUTRAL',\n          strength: Math.abs(buyPressure - 50) * 2,\n          timeframes: {\n            '1m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '5m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '15m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1h': buyPressure > 50 ? 'BULLISH' : 'BEARISH',\n            '4h': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1d': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n          }\n        },\n        technicalAnalysis: {\n          rsi: Math.random() * 100,\n          macd: (Math.random() - 0.5) * 0.01,\n          ema: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n          support: basePrice - Math.random() * basePrice * 0.02,\n          resistance: basePrice + Math.random() * basePrice * 0.02,\n          recommendation: getRecommendation(buyPressure),\n          confidence: 70 + Math.random() * 25\n        },\n        flag: getPairFlag(symbol)\n      };\n    });\n\n    setMarketConditions(conditions);\n    generateAIRecommendations(conditions);\n  };\n\n  const generateAIRecommendations = (conditions: MarketCondition[]) => {\n    const recommendations: AIRecommendation[] = conditions\n      .filter(condition => condition.technicalAnalysis.confidence > 80)\n      .slice(0, 3)\n      .map(condition => {\n        const action = condition.buyPressure > 65 ? 'BUY' : condition.buyPressure < 35 ? 'SELL' : 'HOLD';\n        const entry = condition.price;\n        const stopLoss = action === 'BUY' \n          ? entry - (entry * 0.015) \n          : entry + (entry * 0.015);\n        const tp1 = action === 'BUY' \n          ? entry + (entry * 0.02) \n          : entry - (entry * 0.02);\n        const tp2 = action === 'BUY' \n          ? entry + (entry * 0.035) \n          : entry - (entry * 0.035);\n        const tp3 = action === 'BUY' \n          ? entry + (entry * 0.05) \n          : entry - (entry * 0.05);\n\n        return {\n          symbol: condition.symbol,\n          action,\n          entry,\n          stopLoss,\n          takeProfit1: tp1,\n          takeProfit2: tp2,\n          takeProfit3: tp3,\n          riskReward: Math.abs(tp1 - entry) / Math.abs(entry - stopLoss),\n          confidence: condition.technicalAnalysis.confidence,\n          timeframe: selectedTimeframe,\n          reasoning: generateReasoning(condition, action),\n          accuracy: 85 + Math.random() * 10\n        };\n      });\n\n    setAIRecommendations(recommendations);\n  };\n\n  const generateReasoning = (condition: MarketCondition, action: string): string[] => {\n    const reasons = [];\n    \n    if (action === 'BUY') {\n      reasons.push(`ضغط الشراء قوي: ${condition.buyPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم صاعد: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر فوق EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى المقاومة: ${condition.technicalAnalysis.resistance.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    } else if (action === 'SELL') {\n      reasons.push(`ضغط البيع قوي: ${condition.sellPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم هابط: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر تحت EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى الدعم: ${condition.technicalAnalysis.support.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    }\n\n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getRecommendation = (buyPressure: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    if (buyPressure > 80) return 'STRONG_BUY';\n    if (buyPressure > 60) return 'BUY';\n    if (buyPressure > 40) return 'NEUTRAL';\n    if (buyPressure > 20) return 'SELL';\n    return 'STRONG_SELL';\n  };\n\n  const getRecommendationColor = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'text-green-600 bg-green-100';\n      case 'BUY': return 'text-green-500 bg-green-50';\n      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';\n      case 'SELL': return 'text-red-500 bg-red-50';\n      case 'STRONG_SELL': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getRecommendationText = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Controls */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📊 تحليل السوق المتقدم\n            <span className={`mr-3 w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>\n            <span className=\"text-sm font-normal text-gray-600 dark:text-gray-400\">\n              {isConnected ? 'متصل بـ TradingView' : 'غير متصل'}\n            </span>\n          </h2>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf}\n                  onClick={() => setSelectedTimeframe(tf)}\n                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                    selectedTimeframe === tf\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Enhanced Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات المختارة ({selectedPairs.length}):\n            </label>\n\n            {/* Quick Selection Buttons */}\n            <div className=\"flex flex-wrap gap-2 mb-3\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700\"\n              >\n                العملات الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['BTCUSD', 'ETHUSD'])}\n                className=\"px-3 py-1 bg-purple-600 text-white rounded text-xs font-medium hover:bg-purple-700\"\n              >\n                العملات الرقمية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allPairs)}\n                className=\"px-3 py-1 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700\"\n              >\n                الكل ({allPairs.length})\n              </button>\n              <button\n                onClick={() => setSelectedPairs([])}\n                className=\"px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700\"\n              >\n                مسح الكل\n              </button>\n            </div>\n\n            {/* Individual Pair Selection */}\n            <div className=\"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              {allPairs.map(pair => (\n                <button\n                  key={pair}\n                  onClick={() => {\n                    if (selectedPairs.includes(pair)) {\n                      setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                    } else {\n                      setSelectedPairs([...selectedPairs, pair]);\n                    }\n                  }}\n                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${\n                    selectedPairs.includes(pair)\n                      ? 'bg-green-600 text-white shadow-md'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                  title={getPairName(pair)}\n                >\n                  {pair}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Conditions Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {marketConditions.map(condition => (\n          <div key={condition.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-lg\">{condition.flag}</span>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {condition.symbol}\n                  </h3>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {condition.name}\n                  </p>\n                </div>\n              </div>\n              <div className={`px-2 py-1 rounded text-xs font-medium ${getRecommendationColor(condition.technicalAnalysis.recommendation)}`}>\n                {getRecommendationText(condition.technicalAnalysis.recommendation)}\n              </div>\n            </div>\n\n            {/* Price and Change */}\n            <div className=\"mb-4\">\n              <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {condition.price.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n              </div>\n              <div className={`text-sm font-medium ${condition.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {condition.changePercent >= 0 ? '+' : ''}{condition.changePercent.toFixed(2)}%\n              </div>\n            </div>\n\n            {/* Buy/Sell Pressure */}\n            <div className=\"mb-4\">\n              <div className=\"flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1\">\n                <span>ضغط الشراء: {condition.buyPressure.toFixed(1)}%</span>\n                <span>ضغط البيع: {condition.sellPressure.toFixed(1)}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-green-500 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${condition.buyPressure}%` }}\n                ></div>\n              </div>\n            </div>\n\n            {/* Multi-timeframe Trend */}\n            <div className=\"mb-4\">\n              <h4 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">الاتجاه متعدد الإطارات:</h4>\n              <div className=\"grid grid-cols-3 gap-1 text-xs\">\n                {Object.entries(condition.trend.timeframes).map(([tf, trend]) => (\n                  <div key={tf} className={`text-center py-1 rounded ${\n                    trend === 'BULLISH' ? 'bg-green-100 text-green-800' :\n                    trend === 'BEARISH' ? 'bg-red-100 text-red-800' :\n                    'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    <div className=\"font-medium\">{tf}</div>\n                    <div>{trend === 'BULLISH' ? '↗' : trend === 'BEARISH' ? '↘' : '→'}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Technical Indicators */}\n            <div className=\"space-y-2 text-xs\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">RSI:</span>\n                <span className={`font-medium ${\n                  condition.technicalAnalysis.rsi > 70 ? 'text-red-600' :\n                  condition.technicalAnalysis.rsi < 30 ? 'text-green-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {condition.technicalAnalysis.rsi.toFixed(1)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الدعم:</span>\n                <span className=\"font-medium text-green-600\">\n                  {condition.technicalAnalysis.support.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">المقاومة:</span>\n                <span className=\"font-medium text-red-600\">\n                  {condition.technicalAnalysis.resistance.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الثقة:</span>\n                <span className=\"font-medium text-blue-600\">\n                  {condition.technicalAnalysis.confidence.toFixed(1)}%\n                </span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* AI Recommendations */}\n      {aiRecommendations.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المربحة\n            <span className=\"mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n              دقة عالية\n            </span>\n          </h3>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n            {aiRecommendations.map((rec, index) => (\n              <div key={`ai-recommendation-${rec.symbol}-${index}`} className={`border-2 rounded-xl p-6 ${\n                rec.action === 'BUY' \n                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                  : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n              }`}>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                    <div>\n                      <h4 className=\"text-lg font-bold text-gray-900\">\n                        {rec.action} {rec.symbol}\n                      </h4>\n                      <p className=\"text-xs text-gray-600\">{rec.timeframe}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900\">\n                      {rec.confidence.toFixed(1)}%\n                    </div>\n                    <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(1)}%</div>\n                  </div>\n                </div>\n\n                {/* Price Levels */}\n                <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">الدخول</div>\n                    <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                    <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">هدف 1</div>\n                    <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">R/R</div>\n                    <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                  </div>\n                </div>\n\n                {/* AI Reasoning */}\n                <div className=\"bg-white rounded p-3\">\n                  <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                  <ul className=\"text-xs text-gray-600 space-y-1\">\n                    {rec.reasoning.slice(0, 3).map((reason, i) => (\n                      <li key={`ai-reasoning-${rec.symbol}-${i}`} className=\"flex items-start\">\n                        <span className=\"mr-1 text-blue-500\">▶</span>\n                        <span>{reason}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqDe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;QAAU;KAAS;IACrG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IACrE,MAAM,WAAW;QACf;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAS;QAAU;QAAU;KAClD;IAED,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;yEAAuB;oBAC3B,eAAe;oBACf,wBAAwB;oBACxB,MAAM,IAAI;iFAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD;gBACF;;YAEA;YAEA,4BAA4B;YAC5B,MAAM,WAAW;6DAAY;oBAC3B;oBACA,cAAc,IAAI;gBACpB;4DAAG;YAEH;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAmB;KAAc;IAErC,MAAM,qBAAqB;QACzB,MAAM,aAAgC,cAAc,GAAG,CAAC,CAAA;YACtD,MAAM,YAAY,aAAa;YAC/B,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACnD,MAAM,cAAc,KAAK,MAAM,KAAK;YACpC,MAAM,eAAe,MAAM;YAE3B,OAAO;gBACL;gBACA,MAAM,YAAY;gBAClB,OAAO,YAAY;gBACnB;gBACA,eAAe,AAAC,SAAS,YAAa;gBACtC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;gBAC9C;gBACA;gBACA,OAAO;oBACL,WAAW,cAAc,KAAK,YAAY,cAAc,KAAK,YAAY;oBACzE,UAAU,KAAK,GAAG,CAAC,cAAc,MAAM;oBACvC,YAAY;wBACV,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;wBACzC,MAAM,cAAc,KAAK,YAAY;wBACrC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;oBAC1C;gBACF;gBACA,mBAAmB;oBACjB,KAAK,KAAK,MAAM,KAAK;oBACrB,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC9B,KAAK,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBACrD,SAAS,YAAY,KAAK,MAAM,KAAK,YAAY;oBACjD,YAAY,YAAY,KAAK,MAAM,KAAK,YAAY;oBACpD,gBAAgB,kBAAkB;oBAClC,YAAY,KAAK,KAAK,MAAM,KAAK;gBACnC;gBACA,MAAM,YAAY;YACpB;QACF;QAEA,oBAAoB;QACpB,0BAA0B;IAC5B;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,kBAAsC,WACzC,MAAM,CAAC,CAAA,YAAa,UAAU,iBAAiB,CAAC,UAAU,GAAG,IAC7D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA;YACH,MAAM,SAAS,UAAU,WAAW,GAAG,KAAK,QAAQ,UAAU,WAAW,GAAG,KAAK,SAAS;YAC1F,MAAM,QAAQ,UAAU,KAAK;YAC7B,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YAErB,OAAO;gBACL,QAAQ,UAAU,MAAM;gBACxB;gBACA;gBACA;gBACA,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,YAAY,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;gBACrD,YAAY,UAAU,iBAAiB,CAAC,UAAU;gBAClD,WAAW;gBACX,WAAW,kBAAkB,WAAW;gBACxC,UAAU,KAAK,KAAK,MAAM,KAAK;YACjC;QACF;QAEF,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC,WAA4B;QACrD,MAAM,UAAU,EAAE;QAElB,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,mBAAmD,OAAjC,UAAU,WAAW,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,uBAAwE,OAAlD,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;YACnF,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,kBAAmD,OAAlC,UAAU,YAAY,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,oBAAkE,OAA/C,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC;YAC7E,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAqE;kDAEjF,6LAAC;wCAAK,WAAW,AAAC,6BAAsF,OAA1D,cAAc,+BAA+B;;;;;;kDAC3F,6LAAC;wCAAK,WAAU;kDACb,cAAc,wBAAwB;;;;;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;;oCAA2C;oCAC5C,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;kCAK9C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,AAAC,8DAIX,OAHC,sBAAsB,KAClB,2BACA;0DAGL;+CARI;;;;;;;;;;;;;;;;0CAeb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACxD,cAAc,MAAM;4CAAC;;;;;;;kDAIhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;qDAAS;gDACpD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,SAAS,MAAM;oDAAC;;;;;;;0DAEzB,6LAAC;gDACC,SAAS,IAAM,iBAAiB,EAAE;gDAClC,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAA,qBACZ,6LAAC;gDAEC,SAAS;oDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;wDAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;oDACnD,OAAO;wDACL,iBAAiB;+DAAI;4DAAe;yDAAK;oDAC3C;gDACF;gDACA,WAAW,AAAC,2DAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,sCACA;gDAEN,OAAO,YAAY;0DAElB;+CAfI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwBjB,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAA,0BACpB,6LAAC;wBAA2B,WAAU;;0CAEpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAW,UAAU,IAAI;;;;;;0DACzC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,UAAU,IAAI;;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAW,AAAC,yCAA2G,OAAnE,uBAAuB,UAAU,iBAAiB,CAAC,cAAc;kDACvH,sBAAsB,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,UAAU,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kDAElE,6LAAC;wCAAI,WAAW,AAAC,uBAAuF,OAAjE,UAAU,aAAa,IAAI,IAAI,mBAAmB;;4CACtF,UAAU,aAAa,IAAI,IAAI,MAAM;4CAAI,UAAU,aAAa,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAKjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAa,UAAU,WAAW,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DACpD,6LAAC;;oDAAK;oDAAY,UAAU,YAAY,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAwB,OAAtB,UAAU,WAAW,EAAC;4CAAG;;;;;;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,UAAU,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC;gDAAC,CAAC,IAAI,MAAM;iEAC1D,6LAAC;gDAAa,WAAW,AAAC,4BAIzB,OAHC,UAAU,YAAY,gCACtB,UAAU,YAAY,4BACtB;;kEAEA,6LAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,6LAAC;kEAAK,UAAU,YAAY,MAAM,UAAU,YAAY,MAAM;;;;;;;+CANtD;;;;;;;;;;;;;;;;;0CAahB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAW,AAAC,eAIjB,OAHC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,iBACvC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,mBACvC;0DAEC,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAGxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAG3F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;uBAvFjD,UAAU,MAAM;;;;;;;;;;YAgG7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAyE;0CAErF,6LAAC;gCAAK,WAAU;0CAAkE;;;;;;;;;;;;kCAKpF,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;gCAAqD,WAAW,AAAC,2BAIjE,OAHC,IAAI,MAAM,KAAK,QACX,mEACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,YAAY,IAAI,MAAM;;;;;;kEACjD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,IAAI,MAAM;oEAAC;oEAAE,IAAI,MAAM;;;;;;;0EAE1B,6LAAC;gEAAE,WAAU;0EAAyB,IAAI,SAAS;;;;;;;;;;;;;;;;;;0DAGvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;4DAAwB;4DAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAErE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;;4DAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;wDAA2C,WAAU;;0EACpD,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;0EACrC,6LAAC;0EAAM;;;;;;;uDAFA,AAAC,gBAA6B,OAAd,IAAI,MAAM,EAAC,KAAK,OAAF;;;;;;;;;;;;;;;;;+BAhDrC,AAAC,qBAAkC,OAAd,IAAI,MAAM,EAAC,KAAS,OAAN;;;;;;;;;;;;;;;;;;;;;;AAgE3D;GArewB;KAAA", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ManualAIRecommendations.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AIRecommendation {\n  id: string;\n  symbol: string;\n  timeframe: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  accuracy: number;\n  candleCount: number;\n  reasoning: string[];\n  technicalScore: number;\n  sentimentScore: number;\n  volumeScore: number;\n  trendScore: number;\n  overallScore: number;\n  timestamp: number;\n}\n\nexport default function ManualAIRecommendations() {\n  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [selectedTimeframes, setSelectedTimeframes] = useState<string[]>(['1h', '4h', '1d']);\n  const [candleCount, setCandleCount] = useState<number>(100);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [lastAnalysis, setLastAnalysis] = useState<Date | null>(null);\n\n  const allForexPairs = [\n    // Major Pairs\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    // Minor Pairs\n    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',\n    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',\n    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',\n    'NZDJPY', 'NZDCHF', 'NZDCAD',\n    'CADJPY', 'CADCHF', 'CHFJPY',\n    // Commodities & Crypto\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD'\n  ];\n\n  const timeframes = [\n    { key: '1m', name: '1 دقيقة' },\n    { key: '5m', name: '5 دقائق' },\n    { key: '15m', name: '15 دقيقة' },\n    { key: '30m', name: '30 دقيقة' },\n    { key: '1h', name: '1 ساعة' },\n    { key: '4h', name: '4 ساعات' },\n    { key: '1d', name: '1 يوم' },\n    { key: '1w', name: '1 أسبوع' }\n  ];\n\n  const candleOptions = [20, 30, 50, 75, 100, 150, 200];\n\n  // Manual AI Analysis Function\n  const runAIAnalysis = async () => {\n    if (selectedPairs.length === 0 || selectedTimeframes.length === 0) {\n      alert('يرجى اختيار أزواج عملات وإطارات زمنية للتحليل');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    \n    // Simulate analysis time based on complexity\n    const analysisTime = selectedPairs.length * selectedTimeframes.length * (candleCount / 50) * 500;\n    const maxTime = Math.min(analysisTime, 5000); // Max 5 seconds\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, maxTime));\n      \n      const newRecommendations: AIRecommendation[] = [];\n      \n      selectedPairs.forEach(symbol => {\n        selectedTimeframes.forEach(timeframe => {\n          const basePrice = getBasePrice(symbol);\n          \n          // Advanced AI scoring based on candle count\n          const accuracyBonus = Math.min((candleCount - 20) / 180 * 20, 20);\n          const technicalScore = 50 + Math.random() * 40 + (accuracyBonus / 4);\n          const sentimentScore = 40 + Math.random() * 40 + (accuracyBonus / 5);\n          const volumeScore = 45 + Math.random() * 35 + (accuracyBonus / 6);\n          const trendScore = 50 + Math.random() * 35 + (accuracyBonus / 4);\n          \n          const overallScore = (\n            technicalScore * 0.4 +\n            sentimentScore * 0.2 +\n            volumeScore * 0.2 +\n            trendScore * 0.2\n          );\n          \n          const action = overallScore > 70 ? 'BUY' : overallScore < 40 ? 'SELL' : 'HOLD';\n          const confidence = Math.min(70 + accuracyBonus + Math.random() * 20, 95);\n          const accuracy = Math.min(80 + accuracyBonus + Math.random() * 15, 98);\n          \n          // Calculate levels\n          const entry = basePrice + (Math.random() - 0.5) * basePrice * 0.005;\n          const stopLoss = action === 'BUY' \n            ? entry - (entry * 0.015) \n            : entry + (entry * 0.015);\n          const tp1 = action === 'BUY' \n            ? entry + (entry * 0.02) \n            : entry - (entry * 0.02);\n          const tp2 = action === 'BUY' \n            ? entry + (entry * 0.035) \n            : entry - (entry * 0.035);\n          const tp3 = action === 'BUY' \n            ? entry + (entry * 0.05) \n            : entry - (entry * 0.05);\n          \n          const riskReward = Math.abs(tp1 - entry) / Math.abs(entry - stopLoss);\n          \n          newRecommendations.push({\n            id: `${symbol}_${timeframe}_${Date.now()}`,\n            symbol,\n            timeframe,\n            action,\n            entry,\n            stopLoss,\n            takeProfit1: tp1,\n            takeProfit2: tp2,\n            takeProfit3: tp3,\n            riskReward,\n            confidence,\n            accuracy,\n            candleCount,\n            reasoning: generateAdvancedReasoning(symbol, action, technicalScore, sentimentScore, candleCount),\n            technicalScore,\n            sentimentScore,\n            volumeScore,\n            trendScore,\n            overallScore,\n            timestamp: Date.now()\n          });\n        });\n      });\n      \n      // Sort by overall score (best recommendations first)\n      newRecommendations.sort((a, b) => b.overallScore - a.overallScore);\n      \n      setRecommendations(newRecommendations);\n      setLastAnalysis(new Date());\n      \n    } catch (error) {\n      console.error('AI Analysis Error:', error);\n      alert('حدث خطأ في التحليل. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const generateAdvancedReasoning = (symbol: string, action: string, technicalScore: number, sentimentScore: number, candleCount: number): string[] => {\n    const reasons = [];\n    \n    reasons.push(`🔍 تحليل متقدم باستخدام ${candleCount} شمعة`);\n    \n    if (action === 'BUY') {\n      reasons.push(`📈 إشارة شراء قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💹 معنويات السوق إيجابية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات المقاومة مع حجم تداول مرتفع`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الصاعد`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else if (action === 'SELL') {\n      reasons.push(`📉 إشارة بيع قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💸 معنويات السوق سلبية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات الدعم مع ضغط بيع قوي`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الهابط`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else {\n      reasons.push(`⚖️ السوق في حالة توازن - انتظار إشارة واضحة`);\n      reasons.push(`📊 المؤشرات متضاربة - يُنصح بالانتظار`);\n    }\n    \n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getActionColor = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'bg-green-100 text-green-800 border-green-200';\n      case 'SELL': return 'bg-red-100 text-red-800 border-red-200';\n      case 'HOLD': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getActionText = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'شراء';\n      case 'SELL': return 'بيع';\n      case 'HOLD': return 'انتظار';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المتقدمة\n            <span className=\"mr-3 px-2 py-1 bg-purple-600 text-white rounded text-sm\">\n              تحكم يدوي\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            {lastAnalysis && (\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                آخر تحليل: {lastAnalysis.toLocaleTimeString('ar-SA')}\n              </div>\n            )}\n            <button\n              onClick={runAIAnalysis}\n              disabled={isAnalyzing}\n              className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n                isAnalyzing \n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-purple-600 text-white hover:bg-purple-700'\n              }`}\n            >\n              {isAnalyzing ? '🧠 جاري التحليل...' : '🚀 تشغيل التحليل الذكي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات ({selectedPairs.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"max-h-24 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-1\">\n              <div className=\"grid grid-cols-3 gap-1\">\n                {allForexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطارات الزمنية ({selectedTimeframes.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedTimeframes(['1h', '4h', '1d'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الأساسية\n              </button>\n              <button\n                onClick={() => setSelectedTimeframes(timeframes.map(t => t.key))}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-4 gap-1\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf.key}\n                  onClick={() => {\n                    if (selectedTimeframes.includes(tf.key)) {\n                      setSelectedTimeframes(selectedTimeframes.filter(t => t !== tf.key));\n                    } else {\n                      setSelectedTimeframes([...selectedTimeframes, tf.key]);\n                    }\n                  }}\n                  className={`px-1 py-1 rounded text-xs transition-colors ${\n                    selectedTimeframes.includes(tf.key)\n                      ? 'bg-green-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf.key}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Candle Count */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عدد الشموع للتحليل:\n            </label>\n            <select\n              value={candleCount}\n              onChange={(e) => setCandleCount(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white mb-2\"\n            >\n              {candleOptions.map(count => (\n                <option key={count} value={count}>\n                  {count} شمعة\n                </option>\n              ))}\n            </select>\n            <div className=\"text-xs text-gray-500\">\n              دقة متوقعة: {candleCount >= 150 ? '98%' : candleCount >= 100 ? '95%' : candleCount >= 50 ? '85%' : '75%'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Analysis Progress */}\n        {isAnalyzing && (\n          <div className=\"mb-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-3\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600\"></div>\n              <span className=\"font-medium text-gray-900 dark:text-white\">\n                🧠 الذكاء الاصطناعي يحلل السوق...\n              </span>\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n              <div>• تحليل {selectedPairs.length} زوج عملة</div>\n              <div>• فحص {selectedTimeframes.length} إطار زمني</div>\n              <div>• معالجة {candleCount} شمعة لكل إطار</div>\n              <div>• إجمالي نقاط البيانات: {selectedPairs.length * selectedTimeframes.length * candleCount}</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && recommendations.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🤖</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              مرحباً بك في نظام الذكاء الاصطناعي المتقدم\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اختر أزواج العملات والإطارات الزمنية وعدد الشموع، ثم اضغط على \"تشغيل التحليل الذكي\" للحصول على توصيات دقيقة\n            </p>\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">💡 نصائح للحصول على أفضل النتائج:</h4>\n              <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1 text-right\">\n                <li>• استخدم 100+ شمعة للحصول على دقة عالية</li>\n                <li>• اختر 3-5 أزواج للتحليل المفصل</li>\n                <li>• ركز على الإطارات الزمنية الأساسية (1h, 4h, 1d)</li>\n                <li>• التحليل اليدوي يعطي نتائج أكثر دقة</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Recommendations Results */}\n        {!isAnalyzing && recommendations.length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Summary Stats */}\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص التحليل\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {recommendations.filter(r => r.action === 'BUY').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات شراء</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {recommendations.filter(r => r.action === 'SELL').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات بيع</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-yellow-600\">\n                    {recommendations.filter(r => r.action === 'HOLD').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">انتظار</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {(recommendations.reduce((sum, r) => sum + r.accuracy, 0) / recommendations.length).toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">متوسط الدقة</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recommendations Grid */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n              {recommendations.slice(0, 9).map((rec) => (\n                <div key={rec.id} className={`border-2 rounded-xl p-6 ${\n                  rec.action === 'BUY' \n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                    : rec.action === 'SELL'\n                    ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n                    : 'bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200'\n                }`}>\n                  {/* Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                      <div>\n                        <h4 className=\"text-lg font-bold text-gray-900\">\n                          {getActionText(rec.action)} {rec.symbol}\n                        </h4>\n                        <p className=\"text-xs text-gray-600\">\n                          {timeframes.find(t => t.key === rec.timeframe)?.name} | {rec.candleCount} شمعة\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900\">\n                        {rec.confidence.toFixed(0)}%\n                      </div>\n                      <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(0)}%</div>\n                    </div>\n                  </div>\n\n                  {/* Action Badge */}\n                  <div className={`text-center py-2 rounded-lg mb-4 border-2 ${getActionColor(rec.action)}`}>\n                    <div className=\"font-bold text-sm\">{getActionText(rec.action)}</div>\n                    <div className=\"text-xs\">النتيجة الإجمالية: {rec.overallScore.toFixed(1)}/100</div>\n                  </div>\n\n                  {/* Price Levels */}\n                  {rec.action !== 'HOLD' && (\n                    <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">الدخول</div>\n                        <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                        <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">هدف 1</div>\n                        <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">R/R</div>\n                        <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* AI Scores */}\n                  <div className=\"bg-white rounded p-3 mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🎯 نتائج التحليل:</h5>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"flex justify-between\">\n                        <span>فني:</span>\n                        <span className=\"font-medium\">{rec.technicalScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>مشاعر:</span>\n                        <span className=\"font-medium\">{rec.sentimentScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>حجم:</span>\n                        <span className=\"font-medium\">{rec.volumeScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>اتجاه:</span>\n                        <span className=\"font-medium\">{rec.trendScore.toFixed(0)}/100</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* AI Reasoning */}\n                  <div className=\"bg-white rounded p-3\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                    <ul className=\"text-xs text-gray-600 space-y-1\">\n                      {rec.reasoning.slice(0, 3).map((reason, i) => (\n                        <li key={`manual-reasoning-${rec.id}-${i}`} className=\"flex items-start\">\n                          <span className=\"mr-1 text-blue-500\">▶</span>\n                          <span>{reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {recommendations.length > 9 && (\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  يتم عرض أفضل 9 توصيات من أصل {recommendations.length}\n                </p>\n                <button\n                  onClick={() => {\n                    // Could implement pagination or show all\n                    alert(`إجمالي التوصيات: ${recommendations.length}\\nيتم عرض أفضل 9 توصيات بناءً على النتيجة الإجمالية`);\n                  }}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700\"\n                >\n                  عرض جميع التوصيات ({recommendations.length})\n                </button>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAM;QAAM;KAAK;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,gBAAgB;QACpB,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QACxC;QAAU;QAAU;QAAU;QAC9B;QAAU;QAAU;QACpB;QAAU;QAAU;QACpB,uBAAuB;QACvB;QAAU;QAAU;QAAS;QAAU;KACxC;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAM,MAAM;QAAS;QAC5B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAQ;QAC3B;YAAE,KAAK;YAAM,MAAM;QAAU;KAC9B;IAED,MAAM,gBAAgB;QAAC;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAErD,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI,cAAc,MAAM,KAAK,KAAK,mBAAmB,MAAM,KAAK,GAAG;YACjE,MAAM;YACN;QACF;QAEA,eAAe;QAEf,6CAA6C;QAC7C,MAAM,eAAe,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG,CAAC,cAAc,EAAE,IAAI;QAC7F,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,OAAO,gBAAgB;QAE9D,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,qBAAyC,EAAE;YAEjD,cAAc,OAAO,CAAC,CAAA;gBACpB,mBAAmB,OAAO,CAAC,CAAA;oBACzB,MAAM,YAAY,aAAa;oBAE/B,4CAA4C;oBAC5C,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,MAAM,IAAI;oBAC9D,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,cAAc,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAC/D,MAAM,aAAa,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAE9D,MAAM,eACJ,iBAAiB,MACjB,iBAAiB,MACjB,cAAc,MACd,aAAa;oBAGf,MAAM,SAAS,eAAe,KAAK,QAAQ,eAAe,KAAK,SAAS;oBACxE,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBACrE,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBAEnE,mBAAmB;oBACnB,MAAM,QAAQ,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBAErB,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;oBAE5D,mBAAmB,IAAI,CAAC;wBACtB,IAAI,AAAC,GAAY,OAAV,QAAO,KAAgB,OAAb,WAAU,KAAc,OAAX,KAAK,GAAG;wBACtC;wBACA;wBACA;wBACA;wBACA;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;wBACA;wBACA;wBACA;wBACA,WAAW,0BAA0B,QAAQ,QAAQ,gBAAgB,gBAAgB;wBACrF;wBACA;wBACA;wBACA;wBACA;wBACA,WAAW,KAAK,GAAG;oBACrB;gBACF;YACF;YAEA,qDAAqD;YACrD,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YAEjE,mBAAmB;YACnB,gBAAgB,IAAI;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,4BAA4B,CAAC,QAAgB,QAAgB,gBAAwB,gBAAwB;QACjH,MAAM,UAAU,EAAE;QAElB,QAAQ,IAAI,CAAC,AAAC,2BAAsC,OAAZ,aAAY;QAEpD,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,wCAAiE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC/E,QAAQ,IAAI,CAAC,AAAC,6BAAsD,OAA1B,eAAe,OAAO,CAAC,IAAG;YACpE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,uCAAgE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC9E,QAAQ,IAAI,CAAC,AAAC,2BAAoD,OAA1B,eAAe,OAAO,CAAC,IAAG;YAClE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO;YACL,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;QAChB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;oCACZ,8BACC,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,aAAa,kBAAkB,CAAC;;;;;;;kDAGhD,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;kDAGL,cAAc,uBAAuB;;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACjE,cAAc,MAAM;4CAAC;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,+CAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;8DAGL;mDAdI;;;;;;;;;;;;;;;;;;;;;0CAsBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CAC9D,mBAAmB,MAAM;4CAAC;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,sBAAsB;wDAAC;wDAAM;wDAAM;qDAAK;gDACvD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,sBAAsB,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gDAC9D,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS;oDACP,IAAI,mBAAmB,QAAQ,CAAC,GAAG,GAAG,GAAG;wDACvC,sBAAsB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM,GAAG,GAAG;oDACnE,OAAO;wDACL,sBAAsB;+DAAI;4DAAoB,GAAG,GAAG;yDAAC;oDACvD;gDACF;gDACA,WAAW,AAAC,+CAIX,OAHC,mBAAmB,QAAQ,CAAC,GAAG,GAAG,IAC9B,4BACA;0DAGL,GAAG,GAAG;+CAdF,GAAG,GAAG;;;;;;;;;;;;;;;;0CAqBnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;gDAAmB,OAAO;;oDACxB;oDAAM;;+CADI;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;;4CAAwB;4CACxB,eAAe,MAAM,QAAQ,eAAe,MAAM,QAAQ,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3G,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAS,cAAc,MAAM;4CAAC;;;;;;;kDACnC,6LAAC;;4CAAI;4CAAO,mBAAmB,MAAM;4CAAC;;;;;;;kDACtC,6LAAC;;4CAAI;4CAAU;4CAAY;;;;;;;kDAC3B,6LAAC;;4CAAI;4CAAyB,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oBAMtF,CAAC,eAAe,gBAAgB,MAAM,KAAK,mBAC1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,gBAAgB,MAAM,GAAG,mBACxC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,OAAO,MAAM;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,CAAC,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,gBAAgB,MAAM,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEjG,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;0CAMhE,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wCAiBrB;yDAhBX,6LAAC;wCAAiB,WAAW,AAAC,2BAM7B,OALC,IAAI,MAAM,KAAK,QACX,mEACA,IAAI,MAAM,KAAK,SACf,4DACA;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAW,YAAY,IAAI,MAAM;;;;;;0EACjD,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;4EACX,cAAc,IAAI,MAAM;4EAAE;4EAAE,IAAI,MAAM;;;;;;;kFAEzC,6LAAC;wEAAE,WAAU;;6EACV,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,IAAI,SAAS,eAA5C,uCAAA,iBAA+C,IAAI;4EAAC;4EAAI,IAAI,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;kEAI/E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE7B,6LAAC;gEAAI,WAAU;;oEAAwB;oEAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAKzE,6LAAC;gDAAI,WAAW,AAAC,6CAAuE,OAA3B,eAAe,IAAI,MAAM;;kEACpF,6LAAC;wDAAI,WAAU;kEAAqB,cAAc,IAAI,MAAM;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;;4DAAU;4DAAoB,IAAI,YAAY,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAI1E,IAAI,MAAM,KAAK,wBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAErE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;;oEAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAM1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,WAAW,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE5D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;kEACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;gEAA2C,WAAU;;kFACpD,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;kFACrC,6LAAC;kFAAM;;;;;;;+DAFA,AAAC,oBAA6B,OAAV,IAAI,EAAE,EAAC,KAAK,OAAF;;;;;;;;;;;;;;;;;uCApFrC,IAAI,EAAE;;;;;;;;;;;4BA+FnB,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgD;4CAC7B,gBAAgB,MAAM;;;;;;;kDAEtD,6LAAC;wCACC,SAAS;4CACP,yCAAyC;4CACzC,MAAM,AAAC,oBAA0C,OAAvB,gBAAgB,MAAM,EAAC;wCACnD;wCACA,WAAU;;4CACX;4CACqB,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D;GA/iBwB;KAAA", "debugId": null}}, {"offset": {"line": 3138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AdvancedRiskManagement.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface RiskMetrics {\n  accountBalance: number;\n  dailyRiskLimit: number;\n  currentDailyLoss: number;\n  maxPositionSize: number;\n  correlationRisk: number;\n  winRate: number;\n  profitFactor: number;\n  sharpeRatio: number;\n  maxDrawdown: number;\n}\n\ninterface PositionSizing {\n  pair: string;\n  riskAmount: number;\n  stopLossDistance: number;\n  recommendedLotSize: number;\n  maxLotSize: number;\n  riskRewardRatio: number;\n}\n\nconst AdvancedRiskManagement: React.FC = () => {\n  const [accountBalance, setAccountBalance] = useState<number>(10000);\n  const [riskPercentage, setRiskPercentage] = useState<number>(1);\n  const [dailyRiskLimit, setDailyRiskLimit] = useState<number>(5);\n  const [currentDailyPnL, setCurrentDailyPnL] = useState<number>(0);\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [stopLossDistance, setStopLossDistance] = useState<number>(20);\n  const [targetRR, setTargetRR] = useState<number>(2);\n\n  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({\n    accountBalance: 10000,\n    dailyRiskLimit: 500,\n    currentDailyLoss: 0,\n    maxPositionSize: 100,\n    correlationRisk: 0,\n    winRate: 65,\n    profitFactor: 1.8,\n    sharpeRatio: 1.2,\n    maxDrawdown: 8.5\n  });\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURJPY', 'GBPAUD', 'AUDCAD'\n  ];\n\n  const calculatePositionSize = (): PositionSizing => {\n    const riskAmount = (accountBalance * riskPercentage) / 100;\n    const pipValue = selectedPair.includes('JPY') ? 0.01 : 0.0001;\n    const stopLossPips = stopLossDistance;\n    \n    // Standard lot calculation\n    const recommendedLotSize = riskAmount / (stopLossPips * pipValue * 100000);\n    const maxLotSize = (accountBalance * 2) / 100000; // Max 2% of account as position size\n    \n    return {\n      pair: selectedPair,\n      riskAmount,\n      stopLossDistance: stopLossPips,\n      recommendedLotSize: Math.min(recommendedLotSize, maxLotSize),\n      maxLotSize,\n      riskRewardRatio: targetRR\n    };\n  };\n\n  const calculateKellyCriterion = (): number => {\n    const winRate = riskMetrics.winRate / 100;\n    const lossRate = 1 - winRate;\n    const avgWin = targetRR; // Assuming average win is based on R:R ratio\n    const avgLoss = 1;\n    \n    const kelly = (winRate * avgWin - lossRate * avgLoss) / avgWin;\n    return Math.max(0, Math.min(kelly * 100, 25)); // Cap at 25% for safety\n  };\n\n  const getTradingStatus = (): { status: string; color: string; message: string } => {\n    const dailyLossPercentage = (Math.abs(currentDailyPnL) / accountBalance) * 100;\n    \n    if (currentDailyPnL < 0 && dailyLossPercentage >= dailyRiskLimit) {\n      return {\n        status: 'STOP TRADING',\n        color: 'text-red-600 bg-red-100',\n        message: 'Daily risk limit reached. Stop trading for today.'\n      };\n    } else if (currentDailyPnL < 0 && dailyLossPercentage >= dailyRiskLimit * 0.7) {\n      return {\n        status: 'HIGH RISK',\n        color: 'text-orange-600 bg-orange-100',\n        message: 'Approaching daily risk limit. Reduce position sizes.'\n      };\n    } else if (currentDailyPnL > 0) {\n      return {\n        status: 'PROFITABLE',\n        color: 'text-green-600 bg-green-100',\n        message: 'Good performance today. Maintain discipline.'\n      };\n    } else {\n      return {\n        status: 'NORMAL',\n        color: 'text-blue-600 bg-blue-100',\n        message: 'Ready to trade. Follow your plan.'\n      };\n    }\n  };\n\n  const positionSizing = calculatePositionSize();\n  const kellyPercentage = calculateKellyCriterion();\n  const tradingStatus = getTradingStatus();\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          🛡️ Advanced Risk Management\n        </h2>\n        <div className={`px-4 py-2 rounded-lg font-bold ${tradingStatus.color}`}>\n          {tradingStatus.status}\n        </div>\n      </div>\n\n      {/* Account Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">Account Balance</h3>\n          <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n            ${accountBalance.toLocaleString()}\n          </p>\n        </div>\n        \n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-green-600 dark:text-green-400\">Daily P&L</h3>\n          <p className={`text-2xl font-bold ${currentDailyPnL >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>\n            ${currentDailyPnL >= 0 ? '+' : ''}{currentDailyPnL.toFixed(2)}\n          </p>\n        </div>\n        \n        <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-purple-600 dark:text-purple-400\">Win Rate</h3>\n          <p className=\"text-2xl font-bold text-purple-900 dark:text-purple-100\">\n            {riskMetrics.winRate}%\n          </p>\n        </div>\n        \n        <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-orange-600 dark:text-orange-400\">Profit Factor</h3>\n          <p className=\"text-2xl font-bold text-orange-900 dark:text-orange-100\">\n            {riskMetrics.profitFactor}\n          </p>\n        </div>\n      </div>\n\n      {/* Risk Settings */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Risk Parameters</h3>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Account Balance ($)\n            </label>\n            <input\n              type=\"number\"\n              value={accountBalance}\n              onChange={(e) => setAccountBalance(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Risk Per Trade (%)\n            </label>\n            <input\n              type=\"number\"\n              value={riskPercentage}\n              onChange={(e) => setRiskPercentage(Number(e.target.value))}\n              min=\"0.1\"\n              max=\"5\"\n              step=\"0.1\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Daily Risk Limit (%)\n            </label>\n            <input\n              type=\"number\"\n              value={dailyRiskLimit}\n              onChange={(e) => setDailyRiskLimit(Number(e.target.value))}\n              min=\"1\"\n              max=\"10\"\n              step=\"0.5\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Current Daily P&L ($)\n            </label>\n            <input\n              type=\"number\"\n              value={currentDailyPnL}\n              onChange={(e) => setCurrentDailyPnL(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Position Sizing</h3>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Currency Pair\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>{pair}</option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Stop Loss Distance (pips)\n            </label>\n            <input\n              type=\"number\"\n              value={stopLossDistance}\n              onChange={(e) => setStopLossDistance(Number(e.target.value))}\n              min=\"5\"\n              max=\"100\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Target Risk:Reward Ratio\n            </label>\n            <input\n              type=\"number\"\n              value={targetRR}\n              onChange={(e) => setTargetRR(Number(e.target.value))}\n              min=\"1\"\n              max=\"5\"\n              step=\"0.1\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Position Sizing Results */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          📊 Position Sizing Calculator\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Risk Amount</p>\n            <p className=\"text-xl font-bold text-blue-600\">${positionSizing.riskAmount.toFixed(2)}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Recommended Lot Size</p>\n            <p className=\"text-xl font-bold text-green-600\">{positionSizing.recommendedLotSize.toFixed(2)}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Kelly Criterion</p>\n            <p className=\"text-xl font-bold text-purple-600\">{kellyPercentage.toFixed(1)}%</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Trading Status Alert */}\n      <div className={`rounded-lg p-4 ${tradingStatus.color}`}>\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            <span className=\"text-2xl\">\n              {tradingStatus.status === 'STOP TRADING' ? '🛑' : \n               tradingStatus.status === 'HIGH RISK' ? '⚠️' : \n               tradingStatus.status === 'PROFITABLE' ? '✅' : '🟢'}\n            </span>\n          </div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium\">\n              {tradingStatus.status}\n            </h3>\n            <p className=\"text-sm\">\n              {tradingStatus.message}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">Sharpe Ratio</p>\n          <p className=\"text-lg font-bold text-gray-900 dark:text-white\">{riskMetrics.sharpeRatio}</p>\n        </div>\n        \n        <div className=\"text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">Max Drawdown</p>\n          <p className=\"text-lg font-bold text-red-600\">{riskMetrics.maxDrawdown}%</p>\n        </div>\n        \n        <div className=\"text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">Daily Risk Used</p>\n          <p className=\"text-lg font-bold text-orange-600\">\n            {((Math.abs(currentDailyPnL) / accountBalance) * 100).toFixed(1)}%\n          </p>\n        </div>\n        \n        <div className=\"text-center bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">Risk Remaining</p>\n          <p className=\"text-lg font-bold text-green-600\">\n            {Math.max(0, dailyRiskLimit - ((Math.abs(currentDailyPnL) / accountBalance) * 100)).toFixed(1)}%\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdvancedRiskManagement;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAyBA,MAAM,yBAAmC;;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,SAAS;QACT,cAAc;QACd,aAAa;QACb,aAAa;IACf;IAEA,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;KAC7D;IAED,MAAM,wBAAwB;QAC5B,MAAM,aAAa,AAAC,iBAAiB,iBAAkB;QACvD,MAAM,WAAW,aAAa,QAAQ,CAAC,SAAS,OAAO;QACvD,MAAM,eAAe;QAErB,2BAA2B;QAC3B,MAAM,qBAAqB,aAAa,CAAC,eAAe,WAAW,MAAM;QACzE,MAAM,aAAa,AAAC,iBAAiB,IAAK,QAAQ,qCAAqC;QAEvF,OAAO;YACL,MAAM;YACN;YACA,kBAAkB;YAClB,oBAAoB,KAAK,GAAG,CAAC,oBAAoB;YACjD;YACA,iBAAiB;QACnB;IACF;IAEA,MAAM,0BAA0B;QAC9B,MAAM,UAAU,YAAY,OAAO,GAAG;QACtC,MAAM,WAAW,IAAI;QACrB,MAAM,SAAS,UAAU,6CAA6C;QACtE,MAAM,UAAU;QAEhB,MAAM,QAAQ,CAAC,UAAU,SAAS,WAAW,OAAO,IAAI;QACxD,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,KAAK,MAAM,wBAAwB;IACzE;IAEA,MAAM,mBAAmB;QACvB,MAAM,sBAAsB,AAAC,KAAK,GAAG,CAAC,mBAAmB,iBAAkB;QAE3E,IAAI,kBAAkB,KAAK,uBAAuB,gBAAgB;YAChE,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;QACF,OAAO,IAAI,kBAAkB,KAAK,uBAAuB,iBAAiB,KAAK;YAC7E,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;QACF,OAAO,IAAI,kBAAkB,GAAG;YAC9B,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;QACF,OAAO;YACL,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;YACX;QACF;IACF;IAEA,MAAM,iBAAiB;IACvB,MAAM,kBAAkB;IACxB,MAAM,gBAAgB;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,6LAAC;wBAAI,WAAW,AAAC,kCAAqD,OAApB,cAAc,KAAK;kCAClE,cAAc,MAAM;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CACrE,6LAAC;gCAAE,WAAU;;oCAAsD;oCAC/D,eAAe,cAAc;;;;;;;;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CACvE,6LAAC;gCAAE,WAAW,AAAC,sBAAoH,OAA/F,mBAAmB,IAAI,uCAAuC;;oCAAoC;oCAClI,mBAAmB,IAAI,MAAM;oCAAI,gBAAgB,OAAO,CAAC;;;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CACzE,6LAAC;gCAAE,WAAU;;oCACV,YAAY,OAAO;oCAAC;;;;;;;;;;;;;kCAIzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CACzE,6LAAC;gCAAE,WAAU;0CACV,YAAY,YAAY;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAEpE,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACxD,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACxD,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACxD,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAEpE,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;gDAAkB,OAAO;0DAAO;+CAApB;;;;;;;;;;;;;;;;0CAKnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAC1D,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wCAClD,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAAkC;4CAAE,eAAe,UAAU,CAAC,OAAO,CAAC;;;;;;;;;;;;;0CAGrF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAAoC,eAAe,kBAAkB,CAAC,OAAO,CAAC;;;;;;;;;;;;0CAG7F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAAqC,gBAAgB,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAMnF,6LAAC;gBAAI,WAAW,AAAC,kBAAqC,OAApB,cAAc,KAAK;0BACnD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACb,cAAc,MAAM,KAAK,iBAAiB,OAC1C,cAAc,MAAM,KAAK,cAAc,OACvC,cAAc,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;sCAGnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,cAAc,MAAM;;;;;;8CAEvB,6LAAC;oCAAE,WAAU;8CACV,cAAc,OAAO;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;0CAAmD,YAAY,WAAW;;;;;;;;;;;;kCAGzF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;;oCAAkC,YAAY,WAAW;oCAAC;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;;oCACV,CAAC,AAAC,KAAK,GAAG,CAAC,mBAAmB,iBAAkB,GAAG,EAAE,OAAO,CAAC;oCAAG;;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;;oCACV,KAAK,GAAG,CAAC,GAAG,iBAAkB,AAAC,KAAK,GAAG,CAAC,mBAAmB,iBAAkB,KAAM,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAM3G;GAzTM;KAAA;uCA2TS", "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/SessionBasedTrading.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface TradingSession {\n  name: string;\n  timezone: string;\n  startTime: string;\n  endTime: string;\n  isActive: boolean;\n  volatility: 'Low' | 'Medium' | 'High';\n  bestPairs: string[];\n  strategies: string[];\n  avgSpread: number;\n  volume: number;\n}\n\ninterface SessionPerformance {\n  session: string;\n  winRate: number;\n  avgReturn: number;\n  totalTrades: number;\n  profitFactor: number;\n}\n\nconst SessionBasedTrading: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState<Date>(new Date());\n  const [selectedSession, setSelectedSession] = useState<string>('London');\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const tradingSessions: TradingSession[] = [\n    {\n      name: 'Asian',\n      timezone: 'GMT+8',\n      startTime: '00:00',\n      endTime: '09:00',\n      isActive: false,\n      volatility: 'Low',\n      bestPairs: ['USDJPY', 'AUDJPY', 'NZDJPY', 'AUDUSD'],\n      strategies: ['Range Trading', 'Carry Trade', 'Consolidation Breakouts'],\n      avgSpread: 1.2,\n      volume: 65\n    },\n    {\n      name: 'London',\n      timezone: 'GMT+0',\n      startTime: '08:00',\n      endTime: '17:00',\n      isActive: true,\n      volatility: 'High',\n      bestPairs: ['GBPUSD', 'EURGBP', 'GBPJPY', 'EURUSD'],\n      strategies: ['Breakout Trading', 'News Trading', 'Trend Following'],\n      avgSpread: 0.8,\n      volume: 95\n    },\n    {\n      name: 'New York',\n      timezone: 'GMT-5',\n      startTime: '13:00',\n      endTime: '22:00',\n      isActive: false,\n      volatility: 'High',\n      bestPairs: ['EURUSD', 'GBPUSD', 'USDCAD', 'USDCHF'],\n      strategies: ['Momentum Trading', 'Reversal Trading', 'Scalping'],\n      avgSpread: 0.9,\n      volume: 88\n    }\n  ];\n\n  const sessionPerformance: SessionPerformance[] = [\n    {\n      session: 'Asian',\n      winRate: 68,\n      avgReturn: 0.8,\n      totalTrades: 145,\n      profitFactor: 1.6\n    },\n    {\n      session: 'London',\n      winRate: 72,\n      avgReturn: 1.4,\n      totalTrades: 298,\n      profitFactor: 2.1\n    },\n    {\n      session: 'New York',\n      winRate: 65,\n      avgReturn: 1.2,\n      totalTrades: 234,\n      profitFactor: 1.8\n    },\n    {\n      session: 'London-NY Overlap',\n      winRate: 78,\n      avgReturn: 1.8,\n      totalTrades: 89,\n      profitFactor: 2.4\n    }\n  ];\n\n  const getCurrentSession = (): TradingSession | null => {\n    const gmtHour = currentTime.getUTCHours();\n    \n    if (gmtHour >= 0 && gmtHour < 9) {\n      return tradingSessions[0]; // Asian\n    } else if (gmtHour >= 8 && gmtHour < 17) {\n      return tradingSessions[1]; // London\n    } else if (gmtHour >= 13 && gmtHour < 22) {\n      return tradingSessions[2]; // New York\n    }\n    \n    return null;\n  };\n\n  const getOverlapSession = (): string | null => {\n    const gmtHour = currentTime.getUTCHours();\n    \n    if (gmtHour >= 13 && gmtHour < 17) {\n      return 'London-New York Overlap';\n    }\n    \n    return null;\n  };\n\n  const getSessionColor = (session: TradingSession): string => {\n    if (session.isActive) {\n      return 'border-green-500 bg-green-50 dark:bg-green-900/20';\n    }\n    return 'border-gray-300 bg-gray-50 dark:bg-gray-700';\n  };\n\n  const getVolatilityColor = (volatility: string): string => {\n    switch (volatility) {\n      case 'High': return 'text-red-600 bg-red-100';\n      case 'Medium': return 'text-yellow-600 bg-yellow-100';\n      case 'Low': return 'text-green-600 bg-green-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const currentSession = getCurrentSession();\n  const overlapSession = getOverlapSession();\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          🌍 Session-Based Trading Analysis\n        </h2>\n        <div className=\"text-right\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">Current GMT Time</p>\n          <p className=\"text-lg font-bold text-blue-600\">\n            {currentTime.toUTCString().split(' ')[4]}\n          </p>\n        </div>\n      </div>\n\n      {/* Current Session Alert */}\n      {currentSession && (\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100\">\n                🔥 Active Session: {currentSession.name}\n              </h3>\n              <p className=\"text-blue-700 dark:text-blue-300\">\n                Best time to trade {currentSession.bestPairs.slice(0, 2).join(', ')}\n              </p>\n            </div>\n            {overlapSession && (\n              <div className=\"text-right\">\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800\">\n                  ⚡ {overlapSession}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Trading Sessions Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n        {tradingSessions.map((session, index) => (\n          <div\n            key={session.name}\n            className={`border-2 rounded-lg p-4 transition-all duration-200 ${getSessionColor(session)}`}\n          >\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {session.name} Session\n              </h3>\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getVolatilityColor(session.volatility)}`}>\n                {session.volatility}\n              </span>\n            </div>\n\n            <div className=\"space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">Time:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {session.startTime} - {session.endTime} GMT\n                </span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">Volume:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{session.volume}%</span>\n              </div>\n              \n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">Avg Spread:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{session.avgSpread} pips</span>\n              </div>\n            </div>\n\n            <div className=\"mt-3\">\n              <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">Best Pairs:</p>\n              <div className=\"flex flex-wrap gap-1\">\n                {session.bestPairs.slice(0, 3).map(pair => (\n                  <span key={pair} className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded\">\n                    {pair}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"mt-3\">\n              <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">Strategies:</p>\n              <div className=\"space-y-1\">\n                {session.strategies.slice(0, 2).map(strategy => (\n                  <div key={strategy} className=\"text-xs text-gray-700 dark:text-gray-300\">\n                    • {strategy}\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Session Performance Analysis */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          📊 Historical Session Performance\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {sessionPerformance.map((perf, index) => (\n            <div key={perf.session} className=\"bg-white dark:bg-gray-600 rounded-lg p-4\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">{perf.session}</h4>\n              \n              <div className=\"space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">Win Rate:</span>\n                  <span className=\"font-bold text-green-600\">{perf.winRate}%</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">Avg Return:</span>\n                  <span className=\"font-bold text-blue-600\">{perf.avgReturn}%</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">Total Trades:</span>\n                  <span className=\"font-bold text-gray-900 dark:text-white\">{perf.totalTrades}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">Profit Factor:</span>\n                  <span className=\"font-bold text-purple-600\">{perf.profitFactor}</span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Trading Recommendations */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\n          <h3 className=\"text-lg font-semibold text-green-900 dark:text-green-100 mb-3\">\n            ✅ Current Recommendations\n          </h3>\n          \n          {currentSession ? (\n            <div className=\"space-y-2 text-sm\">\n              <p className=\"text-green-800 dark:text-green-200\">\n                <strong>Active Session:</strong> {currentSession.name}\n              </p>\n              <p className=\"text-green-800 dark:text-green-200\">\n                <strong>Best Pairs:</strong> {currentSession.bestPairs.slice(0, 3).join(', ')}\n              </p>\n              <p className=\"text-green-800 dark:text-green-200\">\n                <strong>Recommended Strategy:</strong> {currentSession.strategies[0]}\n              </p>\n              <p className=\"text-green-800 dark:text-green-200\">\n                <strong>Expected Volatility:</strong> {currentSession.volatility}\n              </p>\n              {overlapSession && (\n                <p className=\"text-orange-800 dark:text-orange-200 font-bold\">\n                  ⚡ High-opportunity overlap period active!\n                </p>\n              )}\n            </div>\n          ) : (\n            <p className=\"text-green-800 dark:text-green-200\">\n              Market is currently in low-activity period. Consider waiting for the next major session.\n            </p>\n          )}\n        </div>\n\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4\">\n          <h3 className=\"text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-3\">\n            ⚠️ Session Trading Tips\n          </h3>\n          \n          <div className=\"space-y-2 text-sm text-yellow-800 dark:text-yellow-200\">\n            <p>• <strong>Asian Session:</strong> Focus on range trading and JPY pairs</p>\n            <p>• <strong>London Session:</strong> Best for breakouts and GBP pairs</p>\n            <p>• <strong>New York Session:</strong> High volatility, good for USD pairs</p>\n            <p>• <strong>Overlaps:</strong> Highest liquidity and best opportunities</p>\n            <p>• <strong>Avoid:</strong> Trading during session transitions</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Session Timeline */}\n      <div className=\"mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n          🕐 24-Hour Session Timeline\n        </h3>\n        \n        <div className=\"relative\">\n          <div className=\"flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-2\">\n            <span>00:00</span>\n            <span>06:00</span>\n            <span>12:00</span>\n            <span>18:00</span>\n            <span>24:00</span>\n          </div>\n          \n          <div className=\"h-8 bg-gray-200 dark:bg-gray-600 rounded-lg relative overflow-hidden\">\n            {/* Asian Session */}\n            <div className=\"absolute left-0 top-0 h-full w-[37.5%] bg-blue-400 opacity-70\"></div>\n            {/* London Session */}\n            <div className=\"absolute left-[33.3%] top-0 h-full w-[37.5%] bg-green-400 opacity-70\"></div>\n            {/* New York Session */}\n            <div className=\"absolute left-[54.2%] top-0 h-full w-[37.5%] bg-red-400 opacity-70\"></div>\n            \n            {/* Current Time Indicator */}\n            <div \n              className=\"absolute top-0 h-full w-1 bg-yellow-500\"\n              style={{ left: `${(currentTime.getUTCHours() / 24) * 100}%` }}\n            ></div>\n          </div>\n          \n          <div className=\"flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-1\">\n            <span className=\"text-blue-600\">Asian</span>\n            <span className=\"text-green-600\">London</span>\n            <span className=\"text-red-600\">New York</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SessionBasedTrading;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAyBA,MAAM,sBAAgC;;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,QAAQ;uDAAY;oBACxB,eAAe,IAAI;gBACrB;sDAAG;YAEH;iDAAO,IAAM,cAAc;;QAC7B;wCAAG,EAAE;IAEL,MAAM,kBAAoC;QACxC;YACE,MAAM;YACN,UAAU;YACV,WAAW;YACX,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;gBAAC;gBAAU;gBAAU;gBAAU;aAAS;YACnD,YAAY;gBAAC;gBAAiB;gBAAe;aAA0B;YACvE,WAAW;YACX,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,WAAW;YACX,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;gBAAC;gBAAU;gBAAU;gBAAU;aAAS;YACnD,YAAY;gBAAC;gBAAoB;gBAAgB;aAAkB;YACnE,WAAW;YACX,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,WAAW;YACX,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;gBAAC;gBAAU;gBAAU;gBAAU;aAAS;YACnD,YAAY;gBAAC;gBAAoB;gBAAoB;aAAW;YAChE,WAAW;YACX,QAAQ;QACV;KACD;IAED,MAAM,qBAA2C;QAC/C;YACE,SAAS;YACT,SAAS;YACT,WAAW;YACX,aAAa;YACb,cAAc;QAChB;QACA;YACE,SAAS;YACT,SAAS;YACT,WAAW;YACX,aAAa;YACb,cAAc;QAChB;QACA;YACE,SAAS;YACT,SAAS;YACT,WAAW;YACX,aAAa;YACb,cAAc;QAChB;QACA;YACE,SAAS;YACT,SAAS;YACT,WAAW;YACX,aAAa;YACb,cAAc;QAChB;KACD;IAED,MAAM,oBAAoB;QACxB,MAAM,UAAU,YAAY,WAAW;QAEvC,IAAI,WAAW,KAAK,UAAU,GAAG;YAC/B,OAAO,eAAe,CAAC,EAAE,EAAE,QAAQ;QACrC,OAAO,IAAI,WAAW,KAAK,UAAU,IAAI;YACvC,OAAO,eAAe,CAAC,EAAE,EAAE,SAAS;QACtC,OAAO,IAAI,WAAW,MAAM,UAAU,IAAI;YACxC,OAAO,eAAe,CAAC,EAAE,EAAE,WAAW;QACxC;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU,YAAY,WAAW;QAEvC,IAAI,WAAW,MAAM,UAAU,IAAI;YACjC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,QAAQ,QAAQ,EAAE;YACpB,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;IACvB,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;0CACV,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;YAM7C,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAyD;wCACjD,eAAe,IAAI;;;;;;;8CAEzC,6LAAC;oCAAE,WAAU;;wCAAmC;wCAC1B,eAAe,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;wBAGjE,gCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;oCAAoG;oCAC/G;;;;;;;;;;;;;;;;;;;;;;;0BASf,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC;wBAEC,WAAW,AAAC,uDAA+E,OAAzB,gBAAgB;;0CAElF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CACX,QAAQ,IAAI;4CAAC;;;;;;;kDAEhB,6LAAC;wCAAK,WAAW,AAAC,8CAAoF,OAAvC,mBAAmB,QAAQ,UAAU;kDACjG,QAAQ,UAAU;;;;;;;;;;;;0CAIvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDACb,QAAQ,SAAS;oDAAC;oDAAI,QAAQ,OAAO;oDAAC;;;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDAA6C,QAAQ,MAAM;oDAAC;;;;;;;;;;;;;kDAG9E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDAA6C,QAAQ,SAAS;oDAAC;;;;;;;;;;;;;;;;;;;0CAInF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAC7D,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,qBACjC,6LAAC;gDAAgB,WAAU;0DACxB;+CADQ;;;;;;;;;;;;;;;;0CAOjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAC7D,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,yBAClC,6LAAC;gDAAmB,WAAU;;oDAA2C;oDACpE;;+CADK;;;;;;;;;;;;;;;;;uBA9CX,QAAQ,IAAI;;;;;;;;;;0BAyDvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;gCAAuB,WAAU;;kDAChC,6LAAC;wCAAG,WAAU;kDAAkD,KAAK,OAAO;;;;;;kDAE5E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;;4DAA4B,KAAK,OAAO;4DAAC;;;;;;;;;;;;;0DAG3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;;4DAA2B,KAAK,SAAS;4DAAC;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;kEAA2C,KAAK,WAAW;;;;;;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAK,WAAU;kEAA6B,KAAK,YAAY;;;;;;;;;;;;;;;;;;;+BArB1D,KAAK,OAAO;;;;;;;;;;;;;;;;0BA8B5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;4BAI7E,+BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAAwB;4CAAE,eAAe,IAAI;;;;;;;kDAEvD,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAAoB;4CAAE,eAAe,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;kDAE1E,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAA8B;4CAAE,eAAe,UAAU,CAAC,EAAE;;;;;;;kDAEtE,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;0DAAO;;;;;;4CAA6B;4CAAE,eAAe,UAAU;;;;;;;oCAEjE,gCACC,6LAAC;wCAAE,WAAU;kDAAiD;;;;;;;;;;;yFAMlE,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAMtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAIhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAuB;;;;;;;kDACpC,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAwB;;;;;;;kDACrC,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAA0B;;;;;;;kDACvC,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAkB;;;;;;;kDAC/B,6LAAC;;4CAAE;0DAAE,6LAAC;0DAAO;;;;;;4CAAe;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,MAAM,AAAC,GAAyC,OAAvC,AAAC,YAAY,WAAW,KAAK,KAAM,KAAI;wCAAG;;;;;;;;;;;;0CAIhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GA3VM;KAAA;uCA6VS", "debugId": null}}]}