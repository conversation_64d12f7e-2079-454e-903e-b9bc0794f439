# 🚀 دليل الإعداد الشامل - AI Trading Bot Professional

## 📋 **المتطلبات الأساسية**

### 🔧 **متطلبات النظام:**
- Node.js 18+ 
- npm أو yarn
- Git
- متصفح حديث (Chrome, Firefox, Safari)

### 🔑 **الحسابات المطلوبة:**
- حساب TradingView (اختياري للبيانات الحقيقية)
- حساب Telegram (للتنبيهات)
- حساب Gmail (للإشعارات بالبريد الإلكتروني)
- حساب Twelve Data (للبيانات المالية)

## 🛠️ **خطوات الإعداد**

### 1. **تحميل وتثبيت المشروع**
```bash
# استنساخ المشروع
git clone <repository-url>
cd ai-trading-bot

# تثبيت المكتبات
npm install

# نسخ ملف البيئة
cp .env.example .env.local
```

### 2. **إعداد Telegram Bot**

#### أ. إنشاء البوت:
1. افتح Telegram وابحث عن `@BotFather`
2. أرسل `/newbot`
3. اختر اسم للبوت (مثل: My Trading Bot)
4. اختر username (مثل: my_trading_bot)
5. احفظ الـ Token المُعطى

#### ب. الحصول على Chat ID:
1. ابحث عن `@userinfobot` في Telegram
2. أرسل `/start`
3. احفظ الـ Chat ID المُعطى

#### ج. تحديث .env.local:
```env
TELEGRAM_ENABLED=true
TELEGRAM_BOT_TOKEN=*********0:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=*********
```

### 3. **إعداد Gmail للإشعارات**

#### أ. تفعيل App Password:
1. اذهب إلى Google Account Settings
2. Security → 2-Step Verification
3. App passwords → Generate new password
4. اختر "Mail" و "Other device"
5. احفظ كلمة المرور المُولدة

#### ب. تحديث .env.local:
```env
EMAIL_ENABLED=true
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_16_character_app_password
EMAIL_FROM_NAME="AI Trading Bot Professional"
EMAIL_FROM_EMAIL=<EMAIL>
EMAIL_TO=<EMAIL>
```

### 4. **إعداد TradingView Webhook**

#### أ. إنشاء Alert في TradingView:
1. افتح TradingView وانتقل إلى الرسم البياني
2. انقر على "Alert" (🔔)
3. اختر الشروط المطلوبة
4. في "Webhook URL" ضع:
   ```
   http://localhost:3000/api/webhook/tradingview
   ```

#### ب. رسالة JSON للـ Alert:
```json
{
  "symbol": "{{ticker}}",
  "action": "{{strategy.order.action}}",
  "price": {{close}},
  "time": "{{time}}",
  "strategy": "{{strategy.order.comment}}",
  "exchange": "{{exchange}}",
  "interval": "{{interval}}",
  "volume": {{volume}},
  "message": "Signal generated at {{time}} for {{ticker}} at price {{close}}"
}
```

#### ج. تحديث .env.local:
```env
WEBHOOK_ENABLED=true
WEBHOOK_SECRET_KEY=your-super-secret-webhook-key-2024
```

### 5. **إعداد مصادر البيانات**

#### أ. Twelve Data API:
1. اذهب إلى https://twelvedata.com/
2. سجل حساب مجاني
3. احصل على API Key
4. أضف إلى .env.local:
```env
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here
```

#### ب. Alpha Vantage (اختياري):
1. اذهب إلى https://www.alphavantage.co/
2. احصل على API Key مجاني
3. أضف إلى .env.local:
```env
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
```

### 6. **إعداد الأمان**

#### أ. توليد مفاتيح الأمان:
```bash
# توليد JWT Secret (32 حرف)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# توليد Encryption Key (32 حرف)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# توليد API Secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

#### ب. تحديث .env.local:
```env
JWT_SECRET=your_generated_jwt_secret_here
ENCRYPTION_KEY=your_generated_encryption_key_here
API_SECRET_KEY=your_generated_api_secret_here
```

## 🎯 **تشغيل البوت**

### 1. **وضع التطوير:**
```bash
npm run dev
```

### 2. **وضع الإنتاج:**
```bash
npm run build
npm start
```

### 3. **فتح المتصفح:**
```
http://localhost:3000
```

## ✅ **اختبار الإعداد**

### 1. **اختبار Telegram:**
- تأكد من ظهور رسالة ترحيب عند تشغيل البوت
- جرب إرسال إشارة تجريبية

### 2. **اختبار Email:**
- تحقق من وصول إشعارات البريد الإلكتروني
- راجع مجلد Spam إذا لم تصل

### 3. **اختبار Webhook:**
- اذهب إلى: `http://localhost:3000/api/webhook/tradingview?action=setup`
- تأكد من ظهور تعليمات الإعداد

### 4. **اختبار البيانات:**
- راقب تحديث الأسعار في الواجهة
- تأكد من عمل المؤشرات الفنية

## 🔧 **إعدادات متقدمة**

### 1. **تخصيص المؤشرات الفنية:**
```env
RSI_PERIOD=14
EMA_PERIOD=20
MACD_FAST=12
MACD_SLOW=26
SUPERTREND_PERIOD=10
```

### 2. **إعدادات ICT:**
```env
ORDER_BLOCK_LOOKBACK=50
FVG_MIN_SIZE_PERCENT=0.2
CHOCH_CONFIRMATION_PERIOD=20
BOS_VOLUME_MULTIPLIER=1.2
```

### 3. **إدارة المخاطر:**
```env
MAX_RISK_PER_TRADE=2
MAX_DAILY_RISK=6
MAX_OPEN_TRADES=5
MIN_RISK_REWARD_RATIO=1.5
```

### 4. **إعدادات الذكاء الاصطناعي:**
```env
AI_CONFIDENCE_THRESHOLD=75
HIGH_CONFIDENCE_THRESHOLD=85
PATTERN_RECOGNITION_ENABLED=true
ENABLE_AI_LEARNING=true
```

## 🌍 **دعم اللغات المتعددة**

### تغيير اللغة الافتراضية:
```env
DEFAULT_LANGUAGE=ar  # العربية
# DEFAULT_LANGUAGE=en  # الإنجليزية  
# DEFAULT_LANGUAGE=fr  # الفرنسية
```

## 🚨 **تحذيرات مهمة**

### ⚠️ **الأمان:**
- لا تشارك ملف `.env.local` مع أحد
- استخدم كلمات مرور قوية
- فعل المصادقة الثنائية على جميع الحسابات
- راقب سجلات الـ Webhook للأنشطة المشبوهة

### ⚠️ **التداول:**
- ابدأ دائماً بوضع Demo (`DEMO_MODE=true`)
- اختبر جميع الإعدادات قبل التداول الحقيقي
- لا تخاطر بأموال لا تستطيع تحمل خسارتها
- استشر خبير مالي قبل التداول

### ⚠️ **القانونية:**
- هذا البوت للأغراض التعليمية
- تحقق من القوانين المحلية للتداول الآلي
- اقرأ تحذيرات المخاطر بعناية

## 🆘 **حل المشاكل الشائعة**

### 1. **البوت لا يبدأ:**
```bash
# تحقق من Node.js
node --version

# أعد تثبيت المكتبات
rm -rf node_modules package-lock.json
npm install
```

### 2. **Telegram لا يعمل:**
- تحقق من صحة Bot Token
- تأكد من Chat ID الصحيح
- جرب إرسال `/start` للبوت

### 3. **Email لا يرسل:**
- تحقق من App Password
- تأكد من تفعيل 2FA
- جرب SMTP مختلف

### 4. **Webhook لا يستقبل:**
- تحقق من URL الصحيح
- تأكد من Secret Key
- راجع سجلات الأخطاء

## 📞 **الدعم والمساعدة**

### 📚 **الموارد:**
- [دليل TradingView API](https://www.tradingview.com/rest-api-spec/)
- [دليل Telegram Bots](https://core.telegram.org/bots)
- [دليل Twelve Data](https://twelvedata.com/docs)

### 🔗 **روابط مفيدة:**
- Telegram BotFather: `@BotFather`
- Gmail App Passwords: [Google Account Settings](https://myaccount.google.com/)
- TradingView: [www.tradingview.com](https://www.tradingview.com/)

---

**🎉 مبروك! بوت التداول الاحترافي جاهز للعمل!**
