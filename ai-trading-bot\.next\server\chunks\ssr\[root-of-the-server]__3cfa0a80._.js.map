{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AutoTradingRobot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface RobotSettings {\n  enabled: boolean;\n  maxRiskPerTrade: number;\n  maxDailyRisk: number;\n  minConfidence: number;\n  allowedPairs: string[];\n  tradingHours: {\n    start: string;\n    end: string;\n  };\n  maxOpenTrades: number;\n}\n\ninterface RobotStats {\n  totalTrades: number;\n  winningTrades: number;\n  losingTrades: number;\n  totalProfit: number;\n  winRate: number;\n  profitFactor: number;\n  currentDrawdown: number;\n}\n\nexport default function AutoTradingRobot() {\n  const [robotSettings, setRobotSettings] = useState<RobotSettings>({\n    enabled: false,\n    maxRiskPerTrade: 2,\n    maxDailyRisk: 6,\n    minConfidence: 80,\n    allowedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],\n    tradingHours: {\n      start: '08:00',\n      end: '18:00'\n    },\n    maxOpenTrades: 3\n  });\n\n  const [robotStats, setRobotStats] = useState<RobotStats>({\n    totalTrades: 47,\n    winningTrades: 34,\n    losingTrades: 13,\n    totalProfit: 2850.50,\n    winRate: 72.3,\n    profitFactor: 1.85,\n    currentDrawdown: 3.2\n  });\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [lastAction, setLastAction] = useState('');\n\n  // Simulate robot activity\n  useEffect(() => {\n    if (robotSettings.enabled && isRunning) {\n      const interval = setInterval(() => {\n        const actions = [\n          'تحليل إشارة EUR/USD...',\n          'فحص مستويات الدعم والمقاومة...',\n          'تقييم مخاطر الصفقة...',\n          'مراقبة Order Blocks...',\n          'تحليل Fair Value Gaps...',\n          'فحص تدفق الأموال الذكية...',\n          'تنفيذ صفقة شراء EUR/USD',\n          'إغلاق صفقة بربح +45 نقطة',\n          'وضع وقف خسارة متحرك...',\n          'مراقبة الجلسة الآسيوية...'\n        ];\n        \n        const randomAction = actions[Math.floor(Math.random() * actions.length)];\n        setLastAction(randomAction);\n      }, 3000);\n\n      return () => clearInterval(interval);\n    }\n  }, [robotSettings.enabled, isRunning]);\n\n  const toggleRobot = () => {\n    setRobotSettings(prev => ({ ...prev, enabled: !prev.enabled }));\n    setIsRunning(!isRunning);\n    if (!robotSettings.enabled) {\n      setLastAction('تم تشغيل الروبوت - بدء المراقبة...');\n    } else {\n      setLastAction('تم إيقاف الروبوت');\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n            🤖 روبوت التداول الآلي\n            <span className={`mr-2 text-xs px-2 py-1 rounded-full ${\n              robotSettings.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n            }`}>\n              {robotSettings.enabled ? 'نشط' : 'متوقف'}\n            </span>\n          </h3>\n          <button\n            onClick={toggleRobot}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              robotSettings.enabled\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-green-600 hover:bg-green-700 text-white'\n            }`}\n          >\n            {robotSettings.enabled ? '⏹️ إيقاف الروبوت' : '▶️ تشغيل الروبوت'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Robot Settings */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              ⚙️ إعدادات الروبوت\n            </h4>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للمخاطرة لكل صفقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  value={robotSettings.maxRiskPerTrade}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxRiskPerTrade: parseFloat(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.maxRiskPerTrade}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأدنى لمستوى الثقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"60\"\n                  max=\"95\"\n                  step=\"5\"\n                  value={robotSettings.minConfidence}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    minConfidence: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.minConfidence}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للصفقات المفتوحة\n                </label>\n                <select\n                  value={robotSettings.maxOpenTrades}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxOpenTrades: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                >\n                  <option value={1}>1 صفقة</option>\n                  <option value={2}>2 صفقة</option>\n                  <option value={3}>3 صفقات</option>\n                  <option value={5}>5 صفقات</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  ساعات التداول\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.start}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, start: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.end}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, end: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Robot Stats */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              📊 إحصائيات الأداء\n            </h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{robotStats.totalTrades}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الصفقات</div>\n              </div>\n              \n              <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{robotStats.winRate.toFixed(1)}%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">معدل النجاح</div>\n              </div>\n              \n              <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">${robotStats.totalProfit.toFixed(2)}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الربح</div>\n              </div>\n              \n              <div className=\"bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600\">{robotStats.profitFactor}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">عامل الربح</div>\n              </div>\n            </div>\n\n            {/* Robot Activity */}\n            <div className=\"mt-6\">\n              <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                🔄 النشاط الحالي\n              </h5>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  {robotSettings.enabled && (\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  )}\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {lastAction || 'الروبوت في وضع الانتظار...'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Warning */}\n        <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n              <span className=\"text-white text-sm\">⚠</span>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                تحذير مهم - الروبوت التجريبي\n              </h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                هذا روبوت تجريبي للأغراض التعليمية فقط. لا يتم تنفيذ صفقات حقيقية. \n                اختبر جميع الإعدادات بعناية قبل استخدام أي نظام تداول آلي حقيقي.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA2Be,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,cAAc;YAAC;YAAU;YAAU;YAAU;SAAS;QACtD,cAAc;YACZ,OAAO;YACP,KAAK;QACP;QACA,eAAe;IACjB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,IAAI,WAAW;YACtC,MAAM,WAAW,YAAY;gBAC3B,MAAM,UAAU;oBACd;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;gBACxE,cAAc;YAChB,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC,cAAc,OAAO;QAAE;KAAU;IAErC,MAAM,cAAc;QAClB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS,CAAC,KAAK,OAAO;YAAC,CAAC;QAC7D,aAAa,CAAC;QACd,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,cAAc;QAChB,OAAO;YACL,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAwE;8CAEpF,8OAAC;oCAAK,WAAW,CAAC,oCAAoC,EACpD,cAAc,OAAO,GAAG,gCAAgC,6BACxD;8CACC,cAAc,OAAO,GAAG,QAAQ;;;;;;;;;;;;sCAGrC,8OAAC;4BACC,SAAS;4BACT,WAAW,CAAC,mDAAmD,EAC7D,cAAc,OAAO,GACjB,2CACA,8CACJ;sCAED,cAAc,OAAO,GAAG,qBAAqB;;;;;;;;;;;;;;;;;0BAKpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,eAAe;wDACpC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5C,CAAC;wDACD,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;4DACZ,cAAc,eAAe;4DAAC;;;;;;;;;;;;;0DAInC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;4DACZ,cAAc,aAAa;4DAAC;;;;;;;;;;;;;0DAIjC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;0EAEV,8OAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,8OAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,8OAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,8OAAC;gEAAO,OAAO;0EAAG;;;;;;;;;;;;;;;;;;0DAItB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,KAAK;gEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC9D,CAAC;gEACD,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,GAAG;gEACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC5D,CAAC;gEACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAqC,WAAW,WAAW;;;;;;kEAC1E,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAoC,WAAW,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEACjF,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAqC;4DAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;kEACrF,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAsC,WAAW,YAAY;;;;;;kEAC5E,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,kBACpB,8OAAC;4DAAI,WAAU;;;;;;sEAEjB,8OAAC;4DAAK,WAAU;sEACb,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AutoTradingRobot from '../components/AutoTradingRobot';\n\nexport default function Home() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    // Simulate initialization\n    const timer = setTimeout(() => {\n      setIsInitialized(true);\n    }, 3000);\n\n    // Update time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => {\n      clearTimeout(timer);\n      clearInterval(timeInterval);\n    };\n  }, []);\n\n  // Enhanced real-time market data with all major currencies and commodities\n  const [marketData, setMarketData] = useState([\n    // Major Forex Pairs\n    {\n      symbol: 'EURUSD',\n      name: 'يورو/دولار أمريكي',\n      price: 1.0850,\n      change: 0.0012,\n      changePercent: 0.11,\n      trend: 'BULLISH',\n      volume: 1250000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇪🇺🇺🇸'\n    },\n    {\n      symbol: 'GBPUSD',\n      name: 'جنيه إسترليني/دولار أمريكي',\n      price: 1.2650,\n      change: -0.0025,\n      changePercent: -0.20,\n      trend: 'BEARISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1,\n      flag: '🇬🇧🇺🇸'\n    },\n    {\n      symbol: 'USDJPY',\n      name: 'دولار أمريكي/ين ياباني',\n      price: 149.50,\n      change: 0.35,\n      changePercent: 0.23,\n      trend: 'BULLISH',\n      volume: 1100000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'ASIAN',\n      riskLevel: 'LOW',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 1,\n      flag: '🇺🇸🇯🇵'\n    },\n    {\n      symbol: 'USDCHF',\n      name: 'دولار أمريكي/فرنك سويسري',\n      price: 0.8920,\n      change: 0.0008,\n      changePercent: 0.09,\n      trend: 'BULLISH',\n      volume: 650000,\n      volatility: 'LOW',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇺🇸🇨🇭'\n    },\n    {\n      symbol: 'AUDUSD',\n      name: 'دولار أسترالي/دولار أمريكي',\n      price: 0.6580,\n      change: -0.0015,\n      changePercent: -0.23,\n      trend: 'BEARISH',\n      volume: 720000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'ASIAN',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇦🇺🇺🇸'\n    },\n    {\n      symbol: 'USDCAD',\n      name: 'دولار أمريكي/دولار كندي',\n      price: 1.3650,\n      change: 0.0020,\n      changePercent: 0.15,\n      trend: 'BULLISH',\n      volume: 580000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1,\n      flag: '🇺🇸🇨🇦'\n    },\n    {\n      symbol: 'NZDUSD',\n      name: 'دولار نيوزيلندي/دولار أمريكي',\n      price: 0.6120,\n      change: -0.0012,\n      changePercent: -0.19,\n      trend: 'BEARISH',\n      volume: 420000,\n      volatility: 'HIGH',\n      liquidity: 'LOW',\n      session: 'ASIAN',\n      riskLevel: 'HIGH',\n      orderBlocks: 1,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇳🇿🇺🇸'\n    },\n    {\n      symbol: 'EURGBP',\n      name: 'يورو/جنيه إسترليني',\n      price: 0.8580,\n      change: 0.0005,\n      changePercent: 0.06,\n      trend: 'NEUTRAL',\n      volume: 480000,\n      volatility: 'LOW',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇪🇺🇬🇧'\n    },\n    // Commodities\n    {\n      symbol: 'XAUUSD',\n      name: 'الذهب/دولار أمريكي',\n      price: 2050.00,\n      change: 15.50,\n      changePercent: 0.76,\n      trend: 'BULLISH',\n      volume: 750000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 5,\n      fvg: 4,\n      choch: 2,\n      bos: 1,\n      flag: '🥇💰'\n    },\n    {\n      symbol: 'XAGUSD',\n      name: 'الفضة/دولار أمريكي',\n      price: 24.50,\n      change: 0.35,\n      changePercent: 1.45,\n      trend: 'BULLISH',\n      volume: 320000,\n      volatility: 'HIGH',\n      liquidity: 'LOW',\n      session: 'NEW_YORK',\n      riskLevel: 'HIGH',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 1,\n      flag: '🥈💰'\n    },\n    {\n      symbol: 'USOIL',\n      name: 'النفط الخام/دولار أمريكي',\n      price: 78.50,\n      change: -1.20,\n      changePercent: -1.51,\n      trend: 'BEARISH',\n      volume: 890000,\n      volatility: 'HIGH',\n      liquidity: 'HIGH',\n      session: 'NEW_YORK',\n      riskLevel: 'HIGH',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 2,\n      flag: '🛢️💰'\n    },\n    // Additional Major Pairs\n    {\n      symbol: 'EURJPY',\n      name: 'يورو/ين ياباني',\n      price: 162.30,\n      change: 0.45,\n      changePercent: 0.28,\n      trend: 'BULLISH',\n      volume: 680000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'ASIAN',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇪🇺🇯🇵'\n    },\n    {\n      symbol: 'GBPJPY',\n      name: 'جنيه إسترليني/ين ياباني',\n      price: 189.20,\n      change: -0.80,\n      changePercent: -0.42,\n      trend: 'BEARISH',\n      volume: 520000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'HIGH',\n      orderBlocks: 2,\n      fvg: 3,\n      choch: 0,\n      bos: 1,\n      flag: '🇬🇧🇯🇵'\n    },\n    // Crypto (if enabled)\n    {\n      symbol: 'BTCUSD',\n      name: 'بيتكوين/دولار أمريكي',\n      price: 43250.00,\n      change: 850.00,\n      changePercent: 2.01,\n      trend: 'BULLISH',\n      volume: 1200000,\n      volatility: 'HIGH',\n      liquidity: 'HIGH',\n      session: 'GLOBAL',\n      riskLevel: 'HIGH',\n      orderBlocks: 6,\n      fvg: 5,\n      choch: 2,\n      bos: 3,\n      flag: '₿💰'\n    },\n    {\n      symbol: 'ETHUSD',\n      name: 'إيثريوم/دولار أمريكي',\n      price: 2650.00,\n      change: 45.50,\n      changePercent: 1.75,\n      trend: 'BULLISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'GLOBAL',\n      riskLevel: 'HIGH',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 2,\n      flag: '⟠💰'\n    },\n  ]);\n\n  // Enhanced signals with ICT concepts\n  const [signals, setSignals] = useState([\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit1: 1.0900,\n      takeProfit2: 1.0950,\n      takeProfit3: 1.1000,\n      riskReward: 1.67,\n      confidence: 87,\n      timestamp: Date.now() - 300000,\n      reasoning: [\n        'Bullish Order Block at 1.0845-1.0855',\n        'Fair Value Gap filled and holding',\n        'CHoCH confirmed bullish structure',\n        'RSI oversold with divergence',\n        'MACD bullish crossover',\n        'Price above VWAP',\n        'London session high liquidity'\n      ],\n      patterns: ['Bullish Order Block', 'Fair Value Gap', 'CHoCH'],\n      session: 'LONDON',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit1: 1.2600,\n      takeProfit2: 1.2550,\n      takeProfit3: 1.2500,\n      riskReward: 1.67,\n      confidence: 82,\n      timestamp: Date.now() - 600000,\n      reasoning: [\n        'Bearish Breaker Block at 1.2655-1.2665',\n        'Break of Structure confirmed',\n        'Bearish engulfing pattern',\n        'RSI overbought rejection',\n        'Volume spike on breakdown',\n        'Below key support level'\n      ],\n      patterns: ['Bearish Breaker Block', 'BOS', 'Engulfing'],\n      session: 'LONDON',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    },\n    {\n      id: '3',\n      symbol: 'XAUUSD',\n      type: 'BUY',\n      entry: 2050.00,\n      stopLoss: 2035.00,\n      takeProfit1: 2075.00,\n      takeProfit2: 2100.00,\n      takeProfit3: 2125.00,\n      riskReward: 1.67,\n      confidence: 91,\n      timestamp: Date.now() - 900000,\n      reasoning: [\n        'Premium Discount Array (PDA) setup',\n        'Institutional Order Block respected',\n        'Liquidity sweep completed',\n        'Fair Value Gap acting as support',\n        'Smart money accumulation zone',\n        'Dollar weakness confluence'\n      ],\n      patterns: ['Order Block', 'Liquidity Sweep', 'PDA'],\n      session: 'NEW_YORK',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '4',\n      symbol: 'BTCUSD',\n      type: 'BUY',\n      entry: 43250.00,\n      stopLoss: 42500.00,\n      takeProfit1: 44500.00,\n      takeProfit2: 45500.00,\n      takeProfit3: 46500.00,\n      riskReward: 1.67,\n      confidence: 89,\n      timestamp: Date.now() - 1200000,\n      reasoning: [\n        'Crypto market showing strong bullish momentum',\n        'Breaking above key resistance at 43000',\n        'Volume surge indicating institutional interest',\n        'Fair Value Gap acting as strong support',\n        'Smart money accumulation pattern detected',\n        'Global adoption news driving sentiment'\n      ],\n      patterns: ['Volume Surge', 'Resistance Break', 'Institutional Flow'],\n      session: 'GLOBAL',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '5',\n      symbol: 'USOIL',\n      type: 'SELL',\n      entry: 78.50,\n      stopLoss: 80.00,\n      takeProfit1: 76.50,\n      takeProfit2: 75.00,\n      takeProfit3: 73.50,\n      riskReward: 1.33,\n      confidence: 78,\n      timestamp: Date.now() - 1500000,\n      reasoning: [\n        'Oil showing bearish divergence on RSI',\n        'Inventory data showing oversupply',\n        'Geopolitical tensions easing',\n        'Dollar strength pressuring commodities',\n        'Technical breakdown below support',\n        'OPEC production concerns'\n      ],\n      patterns: ['Bearish Divergence', 'Support Break', 'Supply Pressure'],\n      session: 'NEW_YORK',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    }\n  ]);\n\n  // Real-time updates simulation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setMarketData(prevData =>\n        prevData.map(market => ({\n          ...market,\n          price: market.price + (Math.random() - 0.5) * 0.001 * market.price,\n          change: market.change + (Math.random() - 0.5) * 0.0005,\n          changePercent: market.changePercent + (Math.random() - 0.5) * 0.05,\n          volume: market.volume + Math.floor((Math.random() - 0.5) * 50000)\n        }))\n      );\n    }, 2000); // Update every 2 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const mockSignals = [\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit: 1.0900,\n      confidence: 85,\n      timestamp: Date.now() - 300000,\n      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit: 1.2600,\n      confidence: 78,\n      timestamp: Date.now() - 600000,\n      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']\n    }\n  ];\n\n  if (!isInitialized) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">🤖 AI Trading Bot</h1>\n          <p className=\"text-blue-200\">Initializing advanced trading systems...</p>\n          <div className=\"mt-4 space-y-2 text-sm text-blue-300\">\n            <p>✅ Loading technical indicators</p>\n            <p>✅ Connecting to market data</p>\n            <p>✅ Initializing AI pattern recognition</p>\n            <p>✅ Setting up risk management</p>\n            <p>🔄 Starting real-time analysis...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                🤖 بوت التداول الذكي الاحترافي\n              </h1>\n              <div className=\"mr-6 flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 rounded-full bg-green-500 animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-300\">مباشر</span>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                {currentTime.toLocaleTimeString('ar-SA')}\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">الوضع التجريبي</span>\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Dashboard */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* نظرة عامة محسنة على السوق */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8\">\n          {marketData.map(market => (\n            <div key={market.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span className=\"text-lg\">{market.flag}</span>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {market.symbol}\n                    </h3>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {market.name}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    market.trend === 'BULLISH' ? 'bg-green-500' :\n                    market.trend === 'BEARISH' ? 'bg-red-500' : 'bg-yellow-500'\n                  }`}></div>\n                  <span className=\"text-xs text-gray-500\">{\n                    market.session === 'LONDON' ? 'لندن' :\n                    market.session === 'NEW_YORK' ? 'نيويورك' :\n                    market.session === 'ASIAN' ? 'آسيا' : market.session\n                  }</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}\n                  </span>\n                  <span className={`text-sm font-medium ${\n                    market.change >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.trend === 'BULLISH'\n                      ? 'text-green-600 bg-green-50 dark:bg-green-900/20'\n                      : market.trend === 'BEARISH'\n                      ? 'text-red-600 bg-red-50 dark:bg-red-900/20'\n                      : 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'\n                  }`}>\n                    {market.trend === 'BULLISH' ? 'صاعد' :\n                     market.trend === 'BEARISH' ? 'هابط' : 'محايد'}\n                  </div>\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.riskLevel === 'LOW' ? 'text-green-600 bg-green-50' :\n                    market.riskLevel === 'MEDIUM' ? 'text-yellow-600 bg-yellow-50' :\n                    'text-red-600 bg-red-50'\n                  } dark:bg-opacity-20`}>\n                    {market.riskLevel === 'LOW' ? 'منخفض' :\n                     market.riskLevel === 'MEDIUM' ? 'متوسط' : 'عالي'}\n                  </div>\n                </div>\n\n                <div className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex justify-between\">\n                    <span>الحجم:</span>\n                    <span>{(market.volume / 1000000).toFixed(1)} مليون</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>التقلب:</span>\n                    <span>{market.volatility === 'HIGH' ? 'عالي' :\n                           market.volatility === 'MEDIUM' ? 'متوسط' : 'منخفض'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>السيولة:</span>\n                    <span>{market.liquidity === 'HIGH' ? 'عالية' :\n                           market.liquidity === 'MEDIUM' ? 'متوسطة' : 'منخفضة'}</span>\n                  </div>\n                </div>\n\n                {/* ICT Concepts Summary */}\n                <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"grid grid-cols-4 gap-1 text-xs\">\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-blue-600\">{market.orderBlocks}</div>\n                      <div className=\"text-gray-500\">OB</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-purple-600\">{market.fvg}</div>\n                      <div className=\"text-gray-500\">FVG</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-green-600\">{market.choch}</div>\n                      <div className=\"text-gray-500\">CHoCH</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-orange-600\">{market.bos}</div>\n                      <div className=\"text-gray-500\">BOS</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Auto Trading Robot */}\n        <AutoTradingRobot />\n\n        {/* إشارات التداول المحسنة مع تحليل ICT */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🎯 إشارات التداول الذكية\n                <span className=\"mr-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                  مفاهيم ICT\n                </span>\n              </h3>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {signals.length} إشارة نشطة\n              </div>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-6\">\n              {signals.map(signal => (\n                <div key={signal.id} className={`border-2 rounded-xl p-6 transition-all hover:shadow-lg ${\n                  signal.type === 'BUY'\n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800'\n                    : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200 dark:from-red-900/20 dark:to-rose-900/20 dark:border-red-800'\n                }`}>\n                  {/* Signal Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\n                        signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'\n                      }`}>\n                        <span className=\"text-white text-xl\">\n                          {signal.type === 'BUY' ? '📈' : '📉'}\n                        </span>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                            {signal.type} {signal.symbol}\n                          </span>\n                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                            signal.confidence >= 85 ? 'bg-green-100 text-green-800' :\n                            signal.confidence >= 75 ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-orange-100 text-orange-800'\n                          }`}>\n                            {signal.confidence}% Confidence\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-4 space-x-reverse mt-1 text-sm text-gray-600 dark:text-gray-400\">\n                          <span>{new Date(signal.timestamp).toLocaleString('ar-SA')}</span>\n                          <span>•</span>\n                          <span>جلسة {signal.session === 'LONDON' ? 'لندن' :\n                                      signal.session === 'NEW_YORK' ? 'نيويورك' :\n                                      signal.session === 'ASIAN' ? 'آسيا' : signal.session}</span>\n                          <span>•</span>\n                          <span>هيكل {signal.marketStructure === 'BULLISH' ? 'صاعد' :\n                                     signal.marketStructure === 'BEARISH' ? 'هابط' : 'محايد'}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                        R/R: {signal.riskReward}:1\n                      </div>\n                      <div className={`text-sm px-2 py-1 rounded ${\n                        signal.smartMoney === 'ACCUMULATION' ? 'bg-green-100 text-green-800' :\n                        'bg-red-100 text-red-800'\n                      }`}>\n                        {signal.smartMoney}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* مستويات الأسعار */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mb-4\">\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">الدخول</div>\n                      <div className=\"font-bold text-gray-900 dark:text-white\">\n                        {signal.entry.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">وقف الخسارة</div>\n                      <div className=\"font-bold text-red-600\">\n                        {signal.stopLoss.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 1</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit1.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 2</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit2.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 3</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit3.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* أنماط ICT */}\n                  <div className=\"mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      🔍 الأنماط المكتشفة:\n                    </h5>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {signal.patterns.map((pattern, index) => (\n                        <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\n                          {pattern === 'Bullish Order Block' ? 'منطقة طلبات صاعدة' :\n                           pattern === 'Fair Value Gap' ? 'فجوة القيمة العادلة' :\n                           pattern === 'CHoCH' ? 'تغيير الطبيعة' :\n                           pattern === 'Bearish Breaker Block' ? 'كتلة كسر هابطة' :\n                           pattern === 'BOS' ? 'كسر الهيكل' :\n                           pattern === 'Engulfing' ? 'نمط الابتلاع' :\n                           pattern === 'Order Block' ? 'منطقة الطلبات' :\n                           pattern === 'Liquidity Sweep' ? 'اكتساح السيولة' :\n                           pattern === 'PDA' ? 'مصفوفة العرض والطلب' : pattern}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* التحليل المتقدم */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n                      🧠 تحليل ICT المتقدم:\n                      <span className=\"mr-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">\n                        مفاهيم الأموال الذكية\n                      </span>\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(0, Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-blue-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-purple-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n\n                  {/* أزرار الإجراءات */}\n                  <div className=\"mt-4 flex items-center justify-between\">\n                    <div className=\"flex space-x-2 space-x-reverse\">\n                      <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                        📊 عرض الرسم البياني\n                      </button>\n                      <button className=\"px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors\">\n                        📋 نسخ الإشارة\n                      </button>\n                    </div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      رقم الإشارة: {signal.id}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* لوحة المؤشرات الفنية المتقدمة */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n              📊 التحليل الفني المتقدم\n              <span className=\"mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                مباشر\n              </span>\n            </h3>\n          </div>\n\n          <div className=\"p-6\">\n            {/* المؤشرات التقليدية */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🔢 المؤشرات التقليدية\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">RSI (14)</h3>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">EURUSD</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">45.2</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Neutral</div>\n                    <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '45%'}}></div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-purple-700 dark:text-purple-300\">MACD</h3>\n                    <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">12,26,9</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">0.0012</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Bullish Cross</div>\n                    <div className=\"text-xs text-gray-500\">Signal: 0.0008</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-green-700 dark:text-green-300\">VWAP</h3>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">Volume</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0845</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-blue-600\">Above Price</div>\n                    <div className=\"text-xs text-gray-500\">Support</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-orange-700 dark:text-orange-300\">Supertrend</h3>\n                    <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">10,3</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0820</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Uptrend</div>\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* مفاهيم ICT */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🎯 مفاهيم الأموال الذكية ICT\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n                <div className=\"bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-cyan-200 dark:border-cyan-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-cyan-700 dark:text-cyan-300 mb-2\">Order Blocks</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">14</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Active Zones</div>\n                    <div className=\"mt-2 flex justify-center space-x-1\">\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-red-500 rounded-full\"></span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-violet-200 dark:border-violet-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-violet-700 dark:text-violet-300 mb-2\">Fair Value Gaps</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">7</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Unfilled</div>\n                    <div className=\"mt-2 text-xs text-violet-600\">3 Bullish, 4 Bearish</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-emerald-700 dark:text-emerald-300 mb-2\">CHoCH</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">2</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Recent</div>\n                    <div className=\"mt-2 text-xs text-emerald-600\">Bullish Structure</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-amber-700 dark:text-amber-300 mb-2\">BOS</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">3</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Confirmed</div>\n                    <div className=\"mt-2 text-xs text-amber-600\">Strong Momentum</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-rose-200 dark:border-rose-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-rose-700 dark:text-rose-300 mb-2\">Liquidity</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">HIGH</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">London Session</div>\n                    <div className=\"mt-2 text-xs text-rose-600\">Optimal Trading</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Market Structure Analysis */}\n            <div>\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🏗️ Market Structure Analysis\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Trend Analysis</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Highs:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Lows:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Structure:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Volume Profile</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">POC:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0842</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAH:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0865</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAL:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0825</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Smart Money</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Flow:</span>\n                      <span className=\"text-xs font-medium text-green-600\">ACCUMULATION</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Sentiment:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Confidence:</span>\n                      <span className=\"text-xs font-medium text-purple-600\">87%</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* لوحة إدارة المخاطر المتقدمة */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🛡️ إدارة المخاطر المتقدمة\n                <span className=\"mr-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full\">\n                  مراقبة مباشرة\n                </span>\n              </h3>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">محرك المخاطر نشط</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Key Metrics */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">💰</span>\n                  </div>\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">Demo</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">$10,000</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Account Balance</div>\n                <div className=\"flex items-center text-xs text-green-600\">\n                  <span className=\"mr-1\">↗</span>\n                  <span>+2.5% This Month</span>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">⚡</span>\n                  </div>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">Safe</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">2.5%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Daily Risk Exposure</div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">🎯</span>\n                  </div>\n                  <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full\">Excellent</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">72.3%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Win Rate (30 Days)</div>\n                <div className=\"text-xs text-purple-600\">23 Wins / 9 Losses</div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">📊</span>\n                  </div>\n                  <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full\">Active</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">3</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Active Signals</div>\n                <div className=\"text-xs text-orange-600\">2 BUY / 1 SELL</div>\n              </div>\n            </div>\n\n            {/* Risk Analysis */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  📈 Performance Metrics\n                </h4>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Profit Factor:</span>\n                    <span className=\"text-sm font-bold text-green-600\">1.85</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sharpe Ratio:</span>\n                    <span className=\"text-sm font-bold text-blue-600\">1.42</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Drawdown:</span>\n                    <span className=\"text-sm font-bold text-red-600\">-3.2%</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Avg R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.67:1</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Recovery Factor:</span>\n                    <span className=\"text-sm font-bold text-indigo-600\">2.1</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  ⚙️ Risk Parameters\n                </h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Risk Per Trade:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">2.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-green-500 h-2 rounded-full\" style={{width: '40%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Daily Risk:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">6.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Open Trades:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">3 / 5</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-orange-500 h-2 rounded-full\" style={{width: '60%'}}></div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Min R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.5:1</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Risk Alerts */}\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                  <span className=\"text-white text-sm\">⚠</span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                    Risk Management Active\n                  </h4>\n                  <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                    All trades are automatically monitored for risk compliance. Current exposure is within safe limits.\n                    Demo mode ensures no real money is at risk.\n                  </p>\n                  <div className=\"mt-2 flex items-center space-x-4 text-xs text-yellow-600 dark:text-yellow-400\">\n                    <span>✓ Position sizing active</span>\n                    <span>✓ Stop loss enforcement</span>\n                    <span>✓ Daily limit monitoring</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Enhanced Footer */}\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white mt-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* معلومات البوت */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 flex items-center\">\n                🤖 بوت التداول الذكي\n                <span className=\"mr-2 text-xs bg-blue-600 px-2 py-1 rounded-full\">الإصدار 2.0</span>\n              </h4>\n              <p className=\"text-sm text-gray-300 mb-4\">\n                نظام تداول احترافي مدعوم بالذكاء الاصطناعي مع مفاهيم ICT المتقدمة وتحليل الأموال الذكية.\n              </p>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm\">مباشر ونشط</span>\n              </div>\n            </div>\n\n            {/* الميزات */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">🎯 الميزات</h4>\n              <ul className=\"text-sm text-gray-300 space-y-2\">\n                <li>• مفاهيم الأموال الذكية ICT</li>\n                <li>• مناطق الطلبات وفجوات القيمة العادلة</li>\n                <li>• كشف CHoCH وBOS</li>\n                <li>• إدارة مخاطر متقدمة</li>\n                <li>• تحليل السوق المباشر</li>\n                <li>• تحليل متعدد الإطارات الزمنية</li>\n                <li>• روبوت التداول الآلي</li>\n                <li>• دعم جميع العملات الرئيسية</li>\n              </ul>\n            </div>\n\n            {/* حالة النظام */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">📊 حالة النظام</h4>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">بيانات السوق:</span>\n                  <span className=\"text-green-400\">✓ متصل</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">محرك الذكاء الاصطناعي:</span>\n                  <span className=\"text-green-400\">✓ نشط</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">مدير المخاطر:</span>\n                  <span className=\"text-green-400\">✓ يراقب</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">الروبوت الآلي:</span>\n                  <span className=\"text-blue-400\">✓ جاهز</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">الإشارات المولدة:</span>\n                  <span className=\"text-blue-400\">{signals.length} اليوم</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">آخر تحديث:</span>\n                  <span className=\"text-yellow-400\">{currentTime.toLocaleTimeString('ar-SA')}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* إخلاء المسؤولية */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">⚠️ مهم</h4>\n              <div className=\"text-xs text-gray-300 space-y-2\">\n                <p className=\"bg-red-900/30 border border-red-700 rounded p-2\">\n                  <strong>الوضع التجريبي:</strong> هذا عرض توضيحي. لا توجد أموال حقيقية متضمنة.\n                </p>\n                <p className=\"bg-yellow-900/30 border border-yellow-700 rounded p-2\">\n                  <strong>تحذير المخاطر:</strong> التداول ينطوي على مخاطر كبيرة. لا تتداول بأموال لا تستطيع تحمل خسارتها.\n                </p>\n                <p className=\"bg-blue-900/30 border border-blue-700 rounded p-2\">\n                  <strong>تعليمي:</strong> هذا البرنامج للأغراض التعليمية فقط.\n                </p>\n                <p className=\"bg-green-900/30 border border-green-700 rounded p-2\">\n                  <strong>الروبوت الآلي:</strong> ميزة تجريبية - اختبر جميع الإعدادات بعناية.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* الشريط السفلي */}\n          <div className=\"border-t border-gray-700 mt-8 pt-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <div className=\"flex items-center space-x-6 space-x-reverse text-sm text-gray-300 mb-4 md:mb-0\">\n                <span>© 2024 بوت التداول الذكي</span>\n                <span>•</span>\n                <span>مطور بـ Next.js و TypeScript</span>\n                <span>•</span>\n                <span>مدعوم بالذكاء الاصطناعي المتقدم</span>\n              </div>\n\n              <div className=\"flex items-center space-x-4 space-x-reverse\">\n                <div className=\"flex items-center space-x-2 space-x-reverse text-sm\">\n                  <span className=\"text-gray-300\">الأداء:</span>\n                  <span className=\"text-green-400 font-medium\">+2.5% شهرياً</span>\n                </div>\n                <div className=\"flex items-center space-x-2 space-x-reverse text-sm\">\n                  <span className=\"text-gray-300\">وقت التشغيل:</span>\n                  <span className=\"text-blue-400 font-medium\">99.9%</span>\n                </div>\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,QAAQ,WAAW;YACvB,iBAAiB;QACnB,GAAG;QAEH,2BAA2B;QAC3B,MAAM,eAAe,YAAY;YAC/B,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO;YACL,aAAa;YACb,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,2EAA2E;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,oBAAoB;QACpB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,cAAc;QACd;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,yBAAyB;QACzB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,sBAAsB;QACtB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;KACD;IAED,qCAAqC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAuB;gBAAkB;aAAQ;YAC5D,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAyB;gBAAO;aAAY;YACvD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAe;gBAAmB;aAAM;YACnD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAgB;gBAAoB;aAAqB;YACpE,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAsB;gBAAiB;aAAkB;YACpE,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAA,WACZ,SAAS,GAAG,CAAC,CAAA,SAAU,CAAC;wBACtB,GAAG,MAAM;wBACT,OAAO,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,OAAO,KAAK;wBAClE,QAAQ,OAAO,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAChD,eAAe,OAAO,aAAa,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC9D,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC7D,CAAC;QAEL,GAAG,OAAO,yBAAyB;QAEnC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAAgB;gBAA0B;aAAmB;QAC3E;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAA6B;gBAAkB;aAAmB;QAChF;KACD;IAED,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA2C,KAAI;;0BAE5D,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,kBAAkB,CAAC;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA,uBACd,8OAAC;gCAAwB,WAAU;;kDACjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;kEACtC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,OAAO,MAAM;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EACV,OAAO,IAAI;;;;;;;;;;;;;;;;;;0DAIlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,KAAK,KAAK,YAAY,iBAC7B,OAAO,KAAK,KAAK,YAAY,eAAe,iBAC5C;;;;;;kEACF,8OAAC;wDAAK,WAAU;kEACd,OAAO,OAAO,KAAK,WAAW,SAC9B,OAAO,OAAO,KAAK,aAAa,YAChC,OAAO,OAAO,KAAK,UAAU,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE5D,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,MAAM,IAAI,IAAI,mBAAmB,gBACxC;;4DACC,OAAO,MAAM,IAAI,IAAI,MAAM;4DAAI,OAAO,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,8BAA8B,EAC7C,OAAO,KAAK,KAAK,YACb,oDACA,OAAO,KAAK,KAAK,YACjB,8CACA,sDACJ;kEACC,OAAO,KAAK,KAAK,YAAY,SAC7B,OAAO,KAAK,KAAK,YAAY,SAAS;;;;;;kEAEzC,8OAAC;wDAAI,WAAW,CAAC,8BAA8B,EAC7C,OAAO,SAAS,KAAK,QAAQ,+BAC7B,OAAO,SAAS,KAAK,WAAW,iCAChC,yBACD,mBAAmB,CAAC;kEAClB,OAAO,SAAS,KAAK,QAAQ,UAC7B,OAAO,SAAS,KAAK,WAAW,UAAU;;;;;;;;;;;;0DAI/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,CAAC,OAAO,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,OAAO,UAAU,KAAK,SAAS,SAC/B,OAAO,UAAU,KAAK,WAAW,UAAU;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,OAAO,SAAS,KAAK,SAAS,UAC9B,OAAO,SAAS,KAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;0DAKtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B,OAAO,WAAW;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA8B,OAAO,KAAK;;;;;;8EACzD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7F/B,OAAO,MAAM;;;;;;;;;;kCAuG3B,8OAAC,sIAAA,CAAA,UAAgB;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,8OAAC;oDAAK,WAAU;8DAAgE;;;;;;;;;;;;sDAIlF,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAItB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;4CAAoB,WAAW,CAAC,uDAAuD,EACtF,OAAO,IAAI,KAAK,QACZ,uIACA,wHACJ;;8DAEA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,wDAAwD,EACvE,OAAO,IAAI,KAAK,QAAQ,iBAAiB,cACzC;8EACA,cAAA,8OAAC;wEAAK,WAAU;kFACb,OAAO,IAAI,KAAK,QAAQ,OAAO;;;;;;;;;;;8EAGpC,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;;wFACb,OAAO,IAAI;wFAAC;wFAAE,OAAO,MAAM;;;;;;;8FAE9B,8OAAC;oFAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,UAAU,IAAI,KAAK,gCAC1B,OAAO,UAAU,IAAI,KAAK,kCAC1B,iCACA;;wFACC,OAAO,UAAU;wFAAC;;;;;;;;;;;;;sFAGvB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;8FAAM,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,CAAC;;;;;;8FACjD,8OAAC;8FAAK;;;;;;8FACN,8OAAC;;wFAAK;wFAAM,OAAO,OAAO,KAAK,WAAW,SAC9B,OAAO,OAAO,KAAK,aAAa,YAChC,OAAO,OAAO,KAAK,UAAU,SAAS,OAAO,OAAO;;;;;;;8FAChE,8OAAC;8FAAK;;;;;;8FACN,8OAAC;;wFAAK;wFAAM,OAAO,eAAe,KAAK,YAAY,SACxC,OAAO,eAAe,KAAK,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;sEAIjE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAAkD;wEACzD,OAAO,UAAU;wEAAC;;;;;;;8EAE1B,8OAAC;oEAAI,WAAW,CAAC,0BAA0B,EACzC,OAAO,UAAU,KAAK,iBAAiB,gCACvC,2BACA;8EACC,OAAO,UAAU;;;;;;;;;;;;;;;;;;8DAMxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,8OAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAG9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGjE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,8OAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,8OAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,8OAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;8DAMtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAG1E,8OAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;oEAAiB,WAAU;8EACzB,YAAY,wBAAwB,sBACpC,YAAY,mBAAmB,wBAC/B,YAAY,UAAU,kBACtB,YAAY,0BAA0B,mBACtC,YAAY,QAAQ,eACpB,YAAY,cAAc,iBAC1B,YAAY,gBAAgB,kBAC5B,YAAY,oBAAoB,mBAChC,YAAY,QAAQ,wBAAwB;mEATpC;;;;;;;;;;;;;;;;8DAgBjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEAA2E;8EAEvF,8OAAC;oEAAK,WAAU;8EAA+D;;;;;;;;;;;;sEAIjF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC9E,8OAAC;4EAAe,WAAU;;8FACxB,8OAAC;oFAAK,WAAU;8FAAqB;;;;;;8FACrC,8OAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;8EAMb,8OAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC3E,8OAAC;4EAAe,WAAU;;8FACxB,8OAAC;oFAAK,WAAU;8FAAuB;;;;;;8FACvC,8OAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;;;;;;;;;;;;;8DAUjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAO,WAAU;8EAAsG;;;;;;8EAGxH,8OAAC;oEAAO,WAAU;8EAAsG;;;;;;;;;;;;sEAI1H,8OAAC;4DAAI,WAAU;;gEAA2C;gEAC1C,OAAO,EAAE;;;;;;;;;;;;;;2CApJnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8J3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCAAwE;sDAEpF,8OAAC;4CAAK,WAAU;sDAAkE;;;;;;;;;;;;;;;;;0CAMtF,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,8OAAC;wEAAK,WAAU;kFAAsD;;;;;;;;;;;;0EAExE,8OAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;;;;;;;kEAKxE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,8OAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,8OAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAyD;;;;;;kFACvE,8OAAC;wEAAK,WAAU;kFAAwD;;;;;;;;;;;;0EAE1E,8OAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,8OAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,8OAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,8OAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,8OAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAA+B;;;;;;;;;;;;;;;;;kEAIlD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAkE;;;;;;8EAChF,8OAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;;;;;;kEAInD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA8D;;;;;;8EAC5E,8OAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;;;;;;;;;;;;kEAIjD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,8OAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;;;;;;;;;;;;;kEAK1D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;;;;;;;;;;;;;kEAK1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,8OAAC;oDAAK,WAAU;8DAA8D;;;;;;;;;;;;sDAIhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;0CAKjE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAA6D;;;;;;;;;;;;kEAE/E,8OAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,8OAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAA2D;;;;;;;;;;;;kEAE7E,8OAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,8OAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAC,OAAO;4DAAK;;;;;;;;;;;;;;;;;0DAItE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,8OAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,8OAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,8OAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,8OAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,8OAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;kDAK7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;;0EAEpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAiC;;;;;;;;;;;;0EAEnD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;0DAK1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAAgC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIvE,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAItE,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,8OAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAAiC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIxE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO5D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;8DAEvC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;sEAA+C;;;;;;sEAI5D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAA+C;8DAE3D,8OAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;;sDAEpE,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAK9B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAiB,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;8DAElD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAmB,YAAY,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAMxE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAwB;;;;;;;8DAElC,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAuB;;;;;;;8DAEjC,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAgB;;;;;;;8DAE1B,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;sEAAO;;;;;;wDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B", "debugId": null}}]}