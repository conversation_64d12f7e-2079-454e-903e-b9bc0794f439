// Alert and Notification System
import TelegramBot from 'node-telegram-bot-api';
import nodemailer from 'nodemailer';
import { TradeSignal } from '../trading/risk-management';
import { MarketState } from '../market-analysis/market-state';
import { RealTimeQuote } from '../data-providers/tradingview';

export interface AlertConfig {
  telegram: {
    botToken: string;
    chatId: string;
    enabled: boolean;
  };
  email: {
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
    from: string;
    to: string[];
    enabled: boolean;
  };
  webhook?: {
    url: string;
    enabled: boolean;
  };
}

export interface Alert {
  id: string;
  type: 'TRADE_SIGNAL' | 'MARKET_UPDATE' | 'RISK_WARNING' | 'SYSTEM_STATUS' | 'COUNTDOWN';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  sent: boolean;
  channels: ('telegram' | 'email' | 'webhook')[];
}

export interface CountdownAlert {
  id: string;
  signalId: string;
  symbol: string;
  action: string;
  targetTime: number;
  intervalId?: NodeJS.Timeout;
  sent: boolean;
}

export class AlertSystem {
  private config: AlertConfig;
  private telegramBot: TelegramBot | null = null;
  private emailTransporter: nodemailer.Transporter | null = null;
  private alertQueue: Alert[] = [];
  private countdownAlerts: Map<string, CountdownAlert> = new Map();
  private isProcessing = false;

  constructor(config: AlertConfig) {
    this.config = config;
    this.initializeTelegram();
    this.initializeEmail();
    this.startAlertProcessor();
  }

  private initializeTelegram(): void {
    if (this.config.telegram.enabled && this.config.telegram.botToken) {
      try {
        this.telegramBot = new TelegramBot(this.config.telegram.botToken, { polling: false });
      } catch (error) {
        console.error('Failed to initialize Telegram bot:', error);
      }
    }
  }

  private initializeEmail(): void {
    if (this.config.email.enabled) {
      try {
        this.emailTransporter = nodemailer.createTransporter(this.config.email.smtp);
      } catch (error) {
        console.error('Failed to initialize email transporter:', error);
      }
    }
  }

  private startAlertProcessor(): void {
    setInterval(() => {
      this.processAlertQueue();
    }, 1000); // Process every second
  }

  // Send trade signal alert
  async sendTradeSignal(signal: TradeSignal, marketState: MarketState): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      type: 'TRADE_SIGNAL',
      priority: signal.confidence >= 80 ? 'HIGH' : signal.confidence >= 60 ? 'MEDIUM' : 'LOW',
      title: `🚨 ${signal.type} Signal - ${signal.symbol}`,
      message: this.formatTradeSignalMessage(signal, marketState),
      data: { signal, marketState },
      timestamp: Date.now(),
      sent: false,
      channels: ['telegram', 'email']
    };

    this.queueAlert(alert);

    // Set up countdown alert (5 minutes before signal execution)
    this.setupCountdownAlert(signal);
  }

  // Send market update alert
  async sendMarketUpdate(symbol: string, marketState: MarketState, quote: RealTimeQuote): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      type: 'MARKET_UPDATE',
      priority: 'MEDIUM',
      title: `📊 Market Update - ${symbol}`,
      message: this.formatMarketUpdateMessage(symbol, marketState, quote),
      data: { symbol, marketState, quote },
      timestamp: Date.now(),
      sent: false,
      channels: ['telegram']
    };

    this.queueAlert(alert);
  }

  // Send risk warning
  async sendRiskWarning(message: string, data?: any): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      type: 'RISK_WARNING',
      priority: 'CRITICAL',
      title: '⚠️ Risk Warning',
      message,
      data,
      timestamp: Date.now(),
      sent: false,
      channels: ['telegram', 'email']
    };

    this.queueAlert(alert);
  }

  // Send system status alert
  async sendSystemStatus(status: 'ONLINE' | 'OFFLINE' | 'ERROR', message: string): Promise<void> {
    const priority = status === 'ERROR' ? 'CRITICAL' : status === 'OFFLINE' ? 'HIGH' : 'LOW';
    const emoji = status === 'ONLINE' ? '✅' : status === 'OFFLINE' ? '🔴' : '❌';

    const alert: Alert = {
      id: this.generateAlertId(),
      type: 'SYSTEM_STATUS',
      priority,
      title: `${emoji} System ${status}`,
      message,
      timestamp: Date.now(),
      sent: false,
      channels: ['telegram', 'email']
    };

    this.queueAlert(alert);
  }

  // Setup countdown alert
  private setupCountdownAlert(signal: TradeSignal): void {
    const countdownTime = 5 * 60 * 1000; // 5 minutes
    const targetTime = Date.now() + countdownTime;

    const countdownAlert: CountdownAlert = {
      id: this.generateAlertId(),
      signalId: signal.id,
      symbol: signal.symbol,
      action: `${signal.type} at ${signal.entry}`,
      targetTime,
      sent: false
    };

    // Set up interval for countdown updates
    const intervalId = setInterval(() => {
      const timeRemaining = targetTime - Date.now();
      
      if (timeRemaining <= 0) {
        this.sendCountdownComplete(countdownAlert);
        clearInterval(intervalId);
        this.countdownAlerts.delete(countdownAlert.id);
      } else {
        this.sendCountdownUpdate(countdownAlert, timeRemaining);
      }
    }, 60000); // Update every minute

    countdownAlert.intervalId = intervalId;
    this.countdownAlerts.set(countdownAlert.id, countdownAlert);
  }

  private async sendCountdownUpdate(countdown: CountdownAlert, timeRemaining: number): Promise<void> {
    const minutes = Math.floor(timeRemaining / 60000);
    
    if (minutes === 5 || minutes === 3 || minutes === 1) {
      const alert: Alert = {
        id: this.generateAlertId(),
        type: 'COUNTDOWN',
        priority: 'MEDIUM',
        title: `⏰ Trade Alert - ${countdown.symbol}`,
        message: `${countdown.action} in ${minutes} minute${minutes > 1 ? 's' : ''}`,
        data: countdown,
        timestamp: Date.now(),
        sent: false,
        channels: ['telegram']
      };

      this.queueAlert(alert);
    }
  }

  private async sendCountdownComplete(countdown: CountdownAlert): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      type: 'COUNTDOWN',
      priority: 'HIGH',
      title: `🎯 Trade Execution - ${countdown.symbol}`,
      message: `Time to execute: ${countdown.action}`,
      data: countdown,
      timestamp: Date.now(),
      sent: false,
      channels: ['telegram', 'email']
    };

    this.queueAlert(alert);
  }

  // Queue alert for processing
  private queueAlert(alert: Alert): void {
    this.alertQueue.push(alert);
  }

  // Process alert queue
  private async processAlertQueue(): Promise<void> {
    if (this.isProcessing || this.alertQueue.length === 0) return;

    this.isProcessing = true;

    try {
      const alert = this.alertQueue.shift();
      if (alert && !alert.sent) {
        await this.sendAlert(alert);
        alert.sent = true;
      }
    } catch (error) {
      console.error('Error processing alert:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // Send alert through configured channels
  private async sendAlert(alert: Alert): Promise<void> {
    const promises: Promise<void>[] = [];

    if (alert.channels.includes('telegram') && this.config.telegram.enabled) {
      promises.push(this.sendTelegramAlert(alert));
    }

    if (alert.channels.includes('email') && this.config.email.enabled) {
      promises.push(this.sendEmailAlert(alert));
    }

    if (alert.channels.includes('webhook') && this.config.webhook?.enabled) {
      promises.push(this.sendWebhookAlert(alert));
    }

    await Promise.allSettled(promises);
  }

  // Send Telegram alert
  private async sendTelegramAlert(alert: Alert): Promise<void> {
    if (!this.telegramBot) return;

    try {
      const message = this.formatTelegramMessage(alert);
      await this.telegramBot.sendMessage(this.config.telegram.chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true
      });
    } catch (error) {
      console.error('Failed to send Telegram alert:', error);
    }
  }

  // Send Email alert
  private async sendEmailAlert(alert: Alert): Promise<void> {
    if (!this.emailTransporter) return;

    try {
      const mailOptions = {
        from: this.config.email.from,
        to: this.config.email.to.join(', '),
        subject: alert.title,
        html: this.formatEmailMessage(alert)
      };

      await this.emailTransporter.sendMail(mailOptions);
    } catch (error) {
      console.error('Failed to send email alert:', error);
    }
  }

  // Send Webhook alert
  private async sendWebhookAlert(alert: Alert): Promise<void> {
    if (!this.config.webhook?.url) return;

    try {
      const response = await fetch(this.config.webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(alert)
      });

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to send webhook alert:', error);
    }
  }

  // Format trade signal message
  private formatTradeSignalMessage(signal: TradeSignal, marketState: MarketState): string {
    const rrRatio = signal.riskRewardRatio.toFixed(2);
    const confidence = signal.confidence.toFixed(1);
    
    return `
🎯 <b>${signal.type} Signal</b>
📈 Symbol: ${signal.symbol}
💰 Entry: ${signal.entry}
🛑 Stop Loss: ${signal.stopLoss}
🎯 TP1: ${signal.takeProfit1}
🎯 TP2: ${signal.takeProfit2}
🎯 TP3: ${signal.takeProfit3}
📊 R/R Ratio: ${rrRatio}:1
🎲 Confidence: ${confidence}%

📊 <b>Market Analysis:</b>
📈 Trend: ${marketState.trend} (${marketState.strength}%)
⚡ Momentum: ${marketState.momentum}
💧 Liquidity: ${marketState.liquidity}
🌊 Volatility: ${marketState.volatility}
🕐 Session: ${marketState.session.name}
⚠️ Risk Level: ${marketState.riskLevel}

🧠 <b>Reasoning:</b>
${signal.reasoning.map(reason => `• ${reason}`).join('\n')}

⏰ <i>Signal generated at ${new Date(signal.timestamp).toLocaleString()}</i>
    `.trim();
  }

  // Format market update message
  private formatMarketUpdateMessage(symbol: string, marketState: MarketState, quote: RealTimeQuote): string {
    const changeEmoji = quote.change >= 0 ? '📈' : '📉';
    const changeColor = quote.change >= 0 ? '🟢' : '🔴';
    
    return `
📊 <b>Market Update - ${symbol}</b>

💰 Price: ${quote.price}
${changeEmoji} Change: ${quote.change} (${quote.changePercent.toFixed(2)}%) ${changeColor}
📊 Bid/Ask: ${quote.bid}/${quote.ask}
📈 Volume: ${quote.volume.toLocaleString()}

🔍 <b>Market State:</b>
📈 Trend: ${marketState.trend} (${marketState.strength}%)
⚡ Momentum: ${marketState.momentum}
💧 Liquidity: ${marketState.liquidity}
🌊 Volatility: ${marketState.volatility}
🕐 Session: ${marketState.session.name}
⚠️ Risk Level: ${marketState.riskLevel}
🎯 Confidence: ${marketState.confidence}%

⏰ <i>Updated at ${new Date().toLocaleString()}</i>
    `.trim();
  }

  // Format Telegram message
  private formatTelegramMessage(alert: Alert): string {
    const priorityEmoji = {
      'LOW': '🔵',
      'MEDIUM': '🟡',
      'HIGH': '🟠',
      'CRITICAL': '🔴'
    };

    return `
${priorityEmoji[alert.priority]} <b>${alert.title}</b>

${alert.message}

⏰ <i>${new Date(alert.timestamp).toLocaleString()}</i>
    `.trim();
  }

  // Format Email message
  private formatEmailMessage(alert: Alert): string {
    const priorityColor = {
      'LOW': '#007bff',
      'MEDIUM': '#ffc107',
      'HIGH': '#fd7e14',
      'CRITICAL': '#dc3545'
    };

    return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: ${priorityColor[alert.priority]}; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0;">${alert.title}</h1>
      </div>
      <div style="padding: 20px; background-color: #f8f9fa;">
        <div style="white-space: pre-line; line-height: 1.6;">
          ${alert.message.replace(/\n/g, '<br>')}
        </div>
        <hr style="margin: 20px 0;">
        <p style="color: #6c757d; font-size: 14px; margin: 0;">
          Generated at ${new Date(alert.timestamp).toLocaleString()}
        </p>
      </div>
    </div>
    `;
  }

  // Generate unique alert ID
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Update configuration
  updateConfig(newConfig: Partial<AlertConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.telegram) {
      this.initializeTelegram();
    }
    
    if (newConfig.email) {
      this.initializeEmail();
    }
  }

  // Get alert statistics
  getAlertStats(): {
    queueLength: number;
    activeCountdowns: number;
    totalSent: number;
  } {
    return {
      queueLength: this.alertQueue.length,
      activeCountdowns: this.countdownAlerts.size,
      totalSent: 0 // This would be tracked in a real implementation
    };
  }

  // Clear all countdown alerts
  clearCountdownAlerts(): void {
    this.countdownAlerts.forEach(countdown => {
      if (countdown.intervalId) {
        clearInterval(countdown.intervalId);
      }
    });
    this.countdownAlerts.clear();
  }

  // Cleanup
  destroy(): void {
    this.clearCountdownAlerts();
    this.alertQueue = [];
    
    if (this.telegramBot) {
      this.telegramBot.stopPolling();
    }
  }
}
