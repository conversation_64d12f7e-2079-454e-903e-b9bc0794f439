{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AutoTradingRobot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface RobotSettings {\n  enabled: boolean;\n  maxRiskPerTrade: number;\n  maxDailyRisk: number;\n  minConfidence: number;\n  allowedPairs: string[];\n  tradingHours: {\n    start: string;\n    end: string;\n  };\n  maxOpenTrades: number;\n}\n\ninterface RobotStats {\n  totalTrades: number;\n  winningTrades: number;\n  losingTrades: number;\n  totalProfit: number;\n  winRate: number;\n  profitFactor: number;\n  currentDrawdown: number;\n}\n\nexport default function AutoTradingRobot() {\n  const [robotSettings, setRobotSettings] = useState<RobotSettings>({\n    enabled: false,\n    maxRiskPerTrade: 2,\n    maxDailyRisk: 6,\n    minConfidence: 80,\n    allowedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],\n    tradingHours: {\n      start: '08:00',\n      end: '18:00'\n    },\n    maxOpenTrades: 3\n  });\n\n  const [robotStats, setRobotStats] = useState<RobotStats>({\n    totalTrades: 47,\n    winningTrades: 34,\n    losingTrades: 13,\n    totalProfit: 2850.50,\n    winRate: 72.3,\n    profitFactor: 1.85,\n    currentDrawdown: 3.2\n  });\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [lastAction, setLastAction] = useState('');\n\n  // Simulate robot activity\n  useEffect(() => {\n    if (robotSettings.enabled && isRunning) {\n      const interval = setInterval(() => {\n        const actions = [\n          'تحليل إشارة EUR/USD...',\n          'فحص مستويات الدعم والمقاومة...',\n          'تقييم مخاطر الصفقة...',\n          'مراقبة Order Blocks...',\n          'تحليل Fair Value Gaps...',\n          'فحص تدفق الأموال الذكية...',\n          'تنفيذ صفقة شراء EUR/USD',\n          'إغلاق صفقة بربح +45 نقطة',\n          'وضع وقف خسارة متحرك...',\n          'مراقبة الجلسة الآسيوية...'\n        ];\n        \n        const randomAction = actions[Math.floor(Math.random() * actions.length)];\n        setLastAction(randomAction);\n      }, 3000);\n\n      return () => clearInterval(interval);\n    }\n  }, [robotSettings.enabled, isRunning]);\n\n  const toggleRobot = () => {\n    setRobotSettings(prev => ({ ...prev, enabled: !prev.enabled }));\n    setIsRunning(!isRunning);\n    if (!robotSettings.enabled) {\n      setLastAction('تم تشغيل الروبوت - بدء المراقبة...');\n    } else {\n      setLastAction('تم إيقاف الروبوت');\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n            🤖 روبوت التداول الآلي\n            <span className={`mr-2 text-xs px-2 py-1 rounded-full ${\n              robotSettings.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n            }`}>\n              {robotSettings.enabled ? 'نشط' : 'متوقف'}\n            </span>\n          </h3>\n          <button\n            onClick={toggleRobot}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              robotSettings.enabled\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-green-600 hover:bg-green-700 text-white'\n            }`}\n          >\n            {robotSettings.enabled ? '⏹️ إيقاف الروبوت' : '▶️ تشغيل الروبوت'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Robot Settings */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              ⚙️ إعدادات الروبوت\n            </h4>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للمخاطرة لكل صفقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  value={robotSettings.maxRiskPerTrade}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxRiskPerTrade: parseFloat(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.maxRiskPerTrade}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأدنى لمستوى الثقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"60\"\n                  max=\"95\"\n                  step=\"5\"\n                  value={robotSettings.minConfidence}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    minConfidence: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.minConfidence}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للصفقات المفتوحة\n                </label>\n                <select\n                  value={robotSettings.maxOpenTrades}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxOpenTrades: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                >\n                  <option value={1}>1 صفقة</option>\n                  <option value={2}>2 صفقة</option>\n                  <option value={3}>3 صفقات</option>\n                  <option value={5}>5 صفقات</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  ساعات التداول\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.start}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, start: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.end}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, end: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Robot Stats */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              📊 إحصائيات الأداء\n            </h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{robotStats.totalTrades}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الصفقات</div>\n              </div>\n              \n              <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{robotStats.winRate.toFixed(1)}%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">معدل النجاح</div>\n              </div>\n              \n              <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">${robotStats.totalProfit.toFixed(2)}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الربح</div>\n              </div>\n              \n              <div className=\"bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600\">{robotStats.profitFactor}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">عامل الربح</div>\n              </div>\n            </div>\n\n            {/* Robot Activity */}\n            <div className=\"mt-6\">\n              <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                🔄 النشاط الحالي\n              </h5>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  {robotSettings.enabled && (\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  )}\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {lastAction || 'الروبوت في وضع الانتظار...'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Warning */}\n        <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n              <span className=\"text-white text-sm\">⚠</span>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                تحذير مهم - الروبوت التجريبي\n              </h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                هذا روبوت تجريبي للأغراض التعليمية فقط. لا يتم تنفيذ صفقات حقيقية. \n                اختبر جميع الإعدادات بعناية قبل استخدام أي نظام تداول آلي حقيقي.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,cAAc;YAAC;YAAU;YAAU;YAAU;SAAS;QACtD,cAAc;YACZ,OAAO;YACP,KAAK;QACP;QACA,eAAe;IACjB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,OAAO,IAAI,WAAW;gBACtC,MAAM,WAAW;2DAAY;wBAC3B,MAAM,UAAU;4BACd;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;wBAED,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;wBACxE,cAAc;oBAChB;0DAAG;gBAEH;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC,cAAc,OAAO;QAAE;KAAU;IAErC,MAAM,cAAc;QAClB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS,CAAC,KAAK,OAAO;YAAC,CAAC;QAC7D,aAAa,CAAC;QACd,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,cAAc;QAChB,OAAO;YACL,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAwE;8CAEpF,6LAAC;oCAAK,WAAW,AAAC,uCAEjB,OADC,cAAc,OAAO,GAAG,gCAAgC;8CAEvD,cAAc,OAAO,GAAG,QAAQ;;;;;;;;;;;;sCAGrC,6LAAC;4BACC,SAAS;4BACT,WAAW,AAAC,sDAIX,OAHC,cAAc,OAAO,GACjB,2CACA;sCAGL,cAAc,OAAO,GAAG,qBAAqB;;;;;;;;;;;;;;;;;0BAKpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,eAAe;wDACpC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5C,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,eAAe;4DAAC;;;;;;;;;;;;;0DAInC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,aAAa;4DAAC;;;;;;;;;;;;;0DAIjC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;0EAEV,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;;;;;;;;;;;;;0DAItB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,KAAK;gEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC9D,CAAC;gEACD,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,GAAG;gEACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC5D,CAAC;gEACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,WAAW,WAAW;;;;;;kEAC1E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAoC,WAAW,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEACjF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAqC;4DAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;kEACrF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,WAAW,YAAY;;;;;;kEAC5E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,kBACpB,6LAAC;4DAAI,WAAU;;;;;;sEAEjB,6LAAC;4DAAK,WAAU;sEACb,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AdvancedMarketAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport TimeframeAnalysis from './TimeframeAnalysis';\n\ninterface MarketCondition {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  buyPressure: number;\n  sellPressure: number;\n  trend: {\n    direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    strength: number;\n    timeframes: {\n      '1m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '5m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '15m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '4h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1d': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    };\n  };\n  technicalAnalysis: {\n    rsi: number;\n    macd: number;\n    ema: number;\n    support: number;\n    resistance: number;\n    recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n    confidence: number;\n  };\n  flag: string;\n}\n\ninterface AIRecommendation {\n  symbol: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  timeframe: string;\n  reasoning: string[];\n  accuracy: number;\n}\n\nexport default function AdvancedMarketAnalysis() {\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']);\n  const [marketConditions, setMarketConditions] = useState<MarketCondition[]>([]);\n  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n  const allPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'EURCHF', 'GBPCHF', 'AUDCHF', 'CADJPY',\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD', 'USDJPY'\n  ];\n\n  // محاكاة الاتصال بـ TradingView\n  useEffect(() => {\n    const connectToTradingView = async () => {\n      setIsConnected(true);\n      // محاكاة تحميل البيانات\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      generateMarketData();\n    };\n\n    connectToTradingView();\n    \n    // تحديث البيانات كل 5 ثوانٍ\n    const interval = setInterval(() => {\n      generateMarketData();\n      setLastUpdate(new Date());\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [selectedTimeframe, selectedPairs]);\n\n  const generateMarketData = () => {\n    const conditions: MarketCondition[] = selectedPairs.map(symbol => {\n      const basePrice = getBasePrice(symbol);\n      const change = (Math.random() - 0.5) * basePrice * 0.02;\n      const buyPressure = Math.random() * 100;\n      const sellPressure = 100 - buyPressure;\n      \n      return {\n        symbol,\n        name: getPairName(symbol),\n        price: basePrice + change,\n        change,\n        changePercent: (change / basePrice) * 100,\n        volume: Math.floor(Math.random() * 2000000) + 500000,\n        buyPressure,\n        sellPressure,\n        trend: {\n          direction: buyPressure > 60 ? 'BULLISH' : buyPressure < 40 ? 'BEARISH' : 'NEUTRAL',\n          strength: Math.abs(buyPressure - 50) * 2,\n          timeframes: {\n            '1m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '5m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '15m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1h': buyPressure > 50 ? 'BULLISH' : 'BEARISH',\n            '4h': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1d': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n          }\n        },\n        technicalAnalysis: {\n          rsi: Math.random() * 100,\n          macd: (Math.random() - 0.5) * 0.01,\n          ema: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n          support: basePrice - Math.random() * basePrice * 0.02,\n          resistance: basePrice + Math.random() * basePrice * 0.02,\n          recommendation: getRecommendation(buyPressure),\n          confidence: 70 + Math.random() * 25\n        },\n        flag: getPairFlag(symbol)\n      };\n    });\n\n    setMarketConditions(conditions);\n    generateAIRecommendations(conditions);\n  };\n\n  const generateAIRecommendations = (conditions: MarketCondition[]) => {\n    const recommendations: AIRecommendation[] = conditions\n      .filter(condition => condition.technicalAnalysis.confidence > 80)\n      .slice(0, 3)\n      .map(condition => {\n        const action = condition.buyPressure > 65 ? 'BUY' : condition.buyPressure < 35 ? 'SELL' : 'HOLD';\n        const entry = condition.price;\n        const stopLoss = action === 'BUY' \n          ? entry - (entry * 0.015) \n          : entry + (entry * 0.015);\n        const tp1 = action === 'BUY' \n          ? entry + (entry * 0.02) \n          : entry - (entry * 0.02);\n        const tp2 = action === 'BUY' \n          ? entry + (entry * 0.035) \n          : entry - (entry * 0.035);\n        const tp3 = action === 'BUY' \n          ? entry + (entry * 0.05) \n          : entry - (entry * 0.05);\n\n        return {\n          symbol: condition.symbol,\n          action,\n          entry,\n          stopLoss,\n          takeProfit1: tp1,\n          takeProfit2: tp2,\n          takeProfit3: tp3,\n          riskReward: Math.abs(tp1 - entry) / Math.abs(entry - stopLoss),\n          confidence: condition.technicalAnalysis.confidence,\n          timeframe: selectedTimeframe,\n          reasoning: generateReasoning(condition, action),\n          accuracy: 85 + Math.random() * 10\n        };\n      });\n\n    setAIRecommendations(recommendations);\n  };\n\n  const generateReasoning = (condition: MarketCondition, action: string): string[] => {\n    const reasons = [];\n    \n    if (action === 'BUY') {\n      reasons.push(`ضغط الشراء قوي: ${condition.buyPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم صاعد: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر فوق EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى المقاومة: ${condition.technicalAnalysis.resistance.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    } else if (action === 'SELL') {\n      reasons.push(`ضغط البيع قوي: ${condition.sellPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم هابط: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر تحت EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى الدعم: ${condition.technicalAnalysis.support.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    }\n\n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getRecommendation = (buyPressure: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    if (buyPressure > 80) return 'STRONG_BUY';\n    if (buyPressure > 60) return 'BUY';\n    if (buyPressure > 40) return 'NEUTRAL';\n    if (buyPressure > 20) return 'SELL';\n    return 'STRONG_SELL';\n  };\n\n  const getRecommendationColor = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'text-green-600 bg-green-100';\n      case 'BUY': return 'text-green-500 bg-green-50';\n      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';\n      case 'SELL': return 'text-red-500 bg-red-50';\n      case 'STRONG_SELL': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getRecommendationText = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Controls */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📊 تحليل السوق المتقدم\n            <span className={`mr-3 w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>\n            <span className=\"text-sm font-normal text-gray-600 dark:text-gray-400\">\n              {isConnected ? 'متصل بـ TradingView' : 'غير متصل'}\n            </span>\n          </h2>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf}\n                  onClick={() => setSelectedTimeframe(tf)}\n                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                    selectedTimeframe === tf\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Enhanced Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات المختارة ({selectedPairs.length}):\n            </label>\n\n            {/* Quick Selection Buttons */}\n            <div className=\"flex flex-wrap gap-2 mb-3\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700\"\n              >\n                العملات الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['BTCUSD', 'ETHUSD'])}\n                className=\"px-3 py-1 bg-purple-600 text-white rounded text-xs font-medium hover:bg-purple-700\"\n              >\n                العملات الرقمية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allPairs)}\n                className=\"px-3 py-1 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700\"\n              >\n                الكل ({allPairs.length})\n              </button>\n              <button\n                onClick={() => setSelectedPairs([])}\n                className=\"px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700\"\n              >\n                مسح الكل\n              </button>\n            </div>\n\n            {/* Individual Pair Selection */}\n            <div className=\"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              {allPairs.map(pair => (\n                <button\n                  key={pair}\n                  onClick={() => {\n                    if (selectedPairs.includes(pair)) {\n                      setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                    } else {\n                      setSelectedPairs([...selectedPairs, pair]);\n                    }\n                  }}\n                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${\n                    selectedPairs.includes(pair)\n                      ? 'bg-green-600 text-white shadow-md'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                  title={getPairName(pair)}\n                >\n                  {pair}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Conditions Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {marketConditions.map(condition => (\n          <div key={condition.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-lg\">{condition.flag}</span>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {condition.symbol}\n                  </h3>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {condition.name}\n                  </p>\n                </div>\n              </div>\n              <div className={`px-2 py-1 rounded text-xs font-medium ${getRecommendationColor(condition.technicalAnalysis.recommendation)}`}>\n                {getRecommendationText(condition.technicalAnalysis.recommendation)}\n              </div>\n            </div>\n\n            {/* Price and Change */}\n            <div className=\"mb-4\">\n              <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {condition.price.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n              </div>\n              <div className={`text-sm font-medium ${condition.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {condition.changePercent >= 0 ? '+' : ''}{condition.changePercent.toFixed(2)}%\n              </div>\n            </div>\n\n            {/* Buy/Sell Pressure */}\n            <div className=\"mb-4\">\n              <div className=\"flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1\">\n                <span>ضغط الشراء: {condition.buyPressure.toFixed(1)}%</span>\n                <span>ضغط البيع: {condition.sellPressure.toFixed(1)}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-green-500 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${condition.buyPressure}%` }}\n                ></div>\n              </div>\n            </div>\n\n            {/* Multi-timeframe Trend */}\n            <div className=\"mb-4\">\n              <h4 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">الاتجاه متعدد الإطارات:</h4>\n              <div className=\"grid grid-cols-3 gap-1 text-xs\">\n                {Object.entries(condition.trend.timeframes).map(([tf, trend]) => (\n                  <div key={tf} className={`text-center py-1 rounded ${\n                    trend === 'BULLISH' ? 'bg-green-100 text-green-800' :\n                    trend === 'BEARISH' ? 'bg-red-100 text-red-800' :\n                    'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    <div className=\"font-medium\">{tf}</div>\n                    <div>{trend === 'BULLISH' ? '↗' : trend === 'BEARISH' ? '↘' : '→'}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Technical Indicators */}\n            <div className=\"space-y-2 text-xs\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">RSI:</span>\n                <span className={`font-medium ${\n                  condition.technicalAnalysis.rsi > 70 ? 'text-red-600' :\n                  condition.technicalAnalysis.rsi < 30 ? 'text-green-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {condition.technicalAnalysis.rsi.toFixed(1)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الدعم:</span>\n                <span className=\"font-medium text-green-600\">\n                  {condition.technicalAnalysis.support.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">المقاومة:</span>\n                <span className=\"font-medium text-red-600\">\n                  {condition.technicalAnalysis.resistance.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الثقة:</span>\n                <span className=\"font-medium text-blue-600\">\n                  {condition.technicalAnalysis.confidence.toFixed(1)}%\n                </span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* AI Recommendations */}\n      {aiRecommendations.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المربحة\n            <span className=\"mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n              دقة عالية\n            </span>\n          </h3>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n            {aiRecommendations.map((rec, index) => (\n              <div key={index} className={`border-2 rounded-xl p-6 ${\n                rec.action === 'BUY' \n                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                  : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n              }`}>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                    <div>\n                      <h4 className=\"text-lg font-bold text-gray-900\">\n                        {rec.action} {rec.symbol}\n                      </h4>\n                      <p className=\"text-xs text-gray-600\">{rec.timeframe}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900\">\n                      {rec.confidence.toFixed(1)}%\n                    </div>\n                    <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(1)}%</div>\n                  </div>\n                </div>\n\n                {/* Price Levels */}\n                <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">الدخول</div>\n                    <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                    <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">هدف 1</div>\n                    <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">R/R</div>\n                    <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                  </div>\n                </div>\n\n                {/* AI Reasoning */}\n                <div className=\"bg-white rounded p-3\">\n                  <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                  <ul className=\"text-xs text-gray-600 space-y-1\">\n                    {rec.reasoning.slice(0, 3).map((reason, i) => (\n                      <li key={i} className=\"flex items-start\">\n                        <span className=\"mr-1 text-blue-500\">▶</span>\n                        <span>{reason}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqDe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;QAAU;KAAS;IACrG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IACrE,MAAM,WAAW;QACf;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAS;QAAU;QAAU;KAClD;IAED,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;yEAAuB;oBAC3B,eAAe;oBACf,wBAAwB;oBACxB,MAAM,IAAI;iFAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD;gBACF;;YAEA;YAEA,4BAA4B;YAC5B,MAAM,WAAW;6DAAY;oBAC3B;oBACA,cAAc,IAAI;gBACpB;4DAAG;YAEH;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAmB;KAAc;IAErC,MAAM,qBAAqB;QACzB,MAAM,aAAgC,cAAc,GAAG,CAAC,CAAA;YACtD,MAAM,YAAY,aAAa;YAC/B,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACnD,MAAM,cAAc,KAAK,MAAM,KAAK;YACpC,MAAM,eAAe,MAAM;YAE3B,OAAO;gBACL;gBACA,MAAM,YAAY;gBAClB,OAAO,YAAY;gBACnB;gBACA,eAAe,AAAC,SAAS,YAAa;gBACtC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;gBAC9C;gBACA;gBACA,OAAO;oBACL,WAAW,cAAc,KAAK,YAAY,cAAc,KAAK,YAAY;oBACzE,UAAU,KAAK,GAAG,CAAC,cAAc,MAAM;oBACvC,YAAY;wBACV,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;wBACzC,MAAM,cAAc,KAAK,YAAY;wBACrC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;oBAC1C;gBACF;gBACA,mBAAmB;oBACjB,KAAK,KAAK,MAAM,KAAK;oBACrB,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC9B,KAAK,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBACrD,SAAS,YAAY,KAAK,MAAM,KAAK,YAAY;oBACjD,YAAY,YAAY,KAAK,MAAM,KAAK,YAAY;oBACpD,gBAAgB,kBAAkB;oBAClC,YAAY,KAAK,KAAK,MAAM,KAAK;gBACnC;gBACA,MAAM,YAAY;YACpB;QACF;QAEA,oBAAoB;QACpB,0BAA0B;IAC5B;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,kBAAsC,WACzC,MAAM,CAAC,CAAA,YAAa,UAAU,iBAAiB,CAAC,UAAU,GAAG,IAC7D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA;YACH,MAAM,SAAS,UAAU,WAAW,GAAG,KAAK,QAAQ,UAAU,WAAW,GAAG,KAAK,SAAS;YAC1F,MAAM,QAAQ,UAAU,KAAK;YAC7B,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YAErB,OAAO;gBACL,QAAQ,UAAU,MAAM;gBACxB;gBACA;gBACA;gBACA,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,YAAY,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;gBACrD,YAAY,UAAU,iBAAiB,CAAC,UAAU;gBAClD,WAAW;gBACX,WAAW,kBAAkB,WAAW;gBACxC,UAAU,KAAK,KAAK,MAAM,KAAK;YACjC;QACF;QAEF,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC,WAA4B;QACrD,MAAM,UAAU,EAAE;QAElB,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,mBAAmD,OAAjC,UAAU,WAAW,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,uBAAwE,OAAlD,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;YACnF,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,kBAAmD,OAAlC,UAAU,YAAY,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,oBAAkE,OAA/C,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC;YAC7E,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAqE;kDAEjF,6LAAC;wCAAK,WAAW,AAAC,6BAAsF,OAA1D,cAAc,+BAA+B;;;;;;kDAC3F,6LAAC;wCAAK,WAAU;kDACb,cAAc,wBAAwB;;;;;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;;oCAA2C;oCAC5C,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;kCAK9C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,AAAC,8DAIX,OAHC,sBAAsB,KAClB,2BACA;0DAGL;+CARI;;;;;;;;;;;;;;;;0CAeb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACxD,cAAc,MAAM;4CAAC;;;;;;;kDAIhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;qDAAS;gDACpD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,SAAS,MAAM;oDAAC;;;;;;;0DAEzB,6LAAC;gDACC,SAAS,IAAM,iBAAiB,EAAE;gDAClC,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAA,qBACZ,6LAAC;gDAEC,SAAS;oDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;wDAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;oDACnD,OAAO;wDACL,iBAAiB;+DAAI;4DAAe;yDAAK;oDAC3C;gDACF;gDACA,WAAW,AAAC,2DAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,sCACA;gDAEN,OAAO,YAAY;0DAElB;+CAfI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwBjB,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAA,0BACpB,6LAAC;wBAA2B,WAAU;;0CAEpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAW,UAAU,IAAI;;;;;;0DACzC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,UAAU,IAAI;;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAW,AAAC,yCAA2G,OAAnE,uBAAuB,UAAU,iBAAiB,CAAC,cAAc;kDACvH,sBAAsB,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,UAAU,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kDAElE,6LAAC;wCAAI,WAAW,AAAC,uBAAuF,OAAjE,UAAU,aAAa,IAAI,IAAI,mBAAmB;;4CACtF,UAAU,aAAa,IAAI,IAAI,MAAM;4CAAI,UAAU,aAAa,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAKjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAa,UAAU,WAAW,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DACpD,6LAAC;;oDAAK;oDAAY,UAAU,YAAY,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAwB,OAAtB,UAAU,WAAW,EAAC;4CAAG;;;;;;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,UAAU,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC;gDAAC,CAAC,IAAI,MAAM;iEAC1D,6LAAC;gDAAa,WAAW,AAAC,4BAIzB,OAHC,UAAU,YAAY,gCACtB,UAAU,YAAY,4BACtB;;kEAEA,6LAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,6LAAC;kEAAK,UAAU,YAAY,MAAM,UAAU,YAAY,MAAM;;;;;;;+CANtD;;;;;;;;;;;;;;;;;0CAahB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAW,AAAC,eAIjB,OAHC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,iBACvC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,mBACvC;0DAEC,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAGxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAG3F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;uBAvFjD,UAAU,MAAM;;;;;;;;;;YAgG7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAyE;0CAErF,6LAAC;gCAAK,WAAU;0CAAkE;;;;;;;;;;;;kCAKpF,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;gCAAgB,WAAW,AAAC,2BAI5B,OAHC,IAAI,MAAM,KAAK,QACX,mEACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,YAAY,IAAI,MAAM;;;;;;kEACjD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,IAAI,MAAM;oEAAC;oEAAE,IAAI,MAAM;;;;;;;0EAE1B,6LAAC;gEAAE,WAAU;0EAAyB,IAAI,SAAS;;;;;;;;;;;;;;;;;;0DAGvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;4DAAwB;4DAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAErE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;;4DAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;wDAAW,WAAU;;0EACpB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;0EACrC,6LAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;;;;;;;;+BAhDP;;;;;;;;;;;;;;;;;;;;;;AAgExB;GArewB;KAAA", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/TimeframeAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface TimeframeData {\n  timeframe: string;\n  trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n  strength: number;\n  rsi: number;\n  macd: number;\n  volume: number;\n  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n  confidence: number;\n  keyLevel: number;\n  nextTarget: number;\n}\n\ninterface MultiTimeframeAnalysis {\n  symbol: string;\n  currentPrice: number;\n  overallTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n  timeframes: TimeframeData[];\n  consensus: {\n    buy: number;\n    sell: number;\n    neutral: number;\n    recommendation: string;\n    confidence: number;\n  };\n}\n\nexport default function TimeframeAnalysis() {\n  const [analysis, setAnalysis] = useState<{ [key: string]: MultiTimeframeAnalysis }>({});\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [candleCount, setCandleCount] = useState<number>(50);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isAutoUpdate, setIsAutoUpdate] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const timeframes = [\n    { key: '1m', name: '1 دقيقة', color: 'bg-red-100 text-red-800' },\n    { key: '5m', name: '5 دقائق', color: 'bg-orange-100 text-orange-800' },\n    { key: '15m', name: '15 دقيقة', color: 'bg-yellow-100 text-yellow-800' },\n    { key: '30m', name: '30 دقيقة', color: 'bg-green-100 text-green-800' },\n    { key: '1h', name: '1 ساعة', color: 'bg-blue-100 text-blue-800' },\n    { key: '4h', name: '4 ساعات', color: 'bg-indigo-100 text-indigo-800' },\n    { key: '1d', name: '1 يوم', color: 'bg-purple-100 text-purple-800' },\n    { key: '1w', name: '1 أسبوع', color: 'bg-pink-100 text-pink-800' }\n  ];\n\n  // All Forex pairs available\n  const allForexPairs = [\n    // Major Pairs\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    // Minor Pairs (Cross Currencies)\n    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',\n    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',\n    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',\n    'NZDJPY', 'NZDCHF', 'NZDCAD',\n    'CADJPY', 'CADCHF', 'CHFJPY',\n    // Exotic Pairs\n    'USDSEK', 'USDNOK', 'USDDKK', 'USDPLN', 'USDHUF', 'USDCZK',\n    'EURSEK', 'EURNOK', 'EURDKK', 'EURPLN', 'EURHUF', 'EURCZK',\n    'GBPSEK', 'GBPNOK', 'GBPDKK', 'GBPPLN',\n    // Commodities\n    'XAUUSD', 'XAGUSD', 'USOIL', 'UKOIL',\n    // Crypto\n    'BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD'\n  ];\n\n  const candleOptions = [20, 30, 50, 75, 100, 150, 200];\n\n  useEffect(() => {\n    // Only auto-update if enabled\n    if (isAutoUpdate) {\n      const interval = setInterval(() => {\n        handleManualUpdate();\n      }, 10000); // Update every 10 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [isAutoUpdate, selectedPairs, selectedTimeframe, candleCount]);\n\n  // Manual update function\n  const handleManualUpdate = () => {\n    if (selectedPairs.length === 0) {\n      alert('يرجى اختيار زوج عملة واحد على الأقل');\n      return;\n    }\n    generateMultiTimeframeAnalysis();\n  };\n\n  const generateMultiTimeframeAnalysis = () => {\n    setIsLoading(true);\n\n    // Simulate API call delay based on number of pairs and candles\n    const analysisDelay = Math.min(selectedPairs.length * 200 + candleCount * 10, 3000);\n\n    setTimeout(() => {\n      const newAnalysis: { [key: string]: MultiTimeframeAnalysis } = {};\n\n      selectedPairs.forEach(symbol => {\n        const basePrice = getBasePrice(symbol);\n\n        // Generate analysis based on candle count (more candles = more accurate)\n        const accuracyBonus = Math.min((candleCount - 20) / 180 * 15, 15); // Up to 15% bonus\n\n        const timeframeData: TimeframeData[] = timeframes.map(tf => {\n          // Simulate more sophisticated analysis with more candles\n          const trendStrength = Math.random() * 100;\n          const candleInfluence = (candleCount / 200) * 20; // More candles = better trend detection\n\n          const trend = Math.random() > 0.5 ? 'BULLISH' : Math.random() > 0.5 ? 'BEARISH' : 'NEUTRAL';\n          const strength = Math.min(trendStrength + candleInfluence, 100);\n          const rsi = Math.random() * 100;\n          const macd = (Math.random() - 0.5) * 0.02;\n          const volume = Math.random() * 2000000 + 500000;\n          const baseConfidence = 70 + Math.random() * 25;\n          const confidence = Math.min(baseConfidence + accuracyBonus, 95);\n\n          return {\n            timeframe: tf.key,\n            trend,\n            strength,\n            rsi,\n            macd,\n            volume,\n            recommendation: getRecommendation(trend, rsi, strength),\n            confidence,\n            keyLevel: basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n            nextTarget: basePrice + (Math.random() - 0.5) * basePrice * 0.03\n          };\n        });\n\n        // Calculate consensus\n        const buyCount = timeframeData.filter(tf => tf.recommendation.includes('BUY')).length;\n        const sellCount = timeframeData.filter(tf => tf.recommendation.includes('SELL')).length;\n        const neutralCount = timeframeData.filter(tf => tf.recommendation === 'NEUTRAL').length;\n\n        const total = timeframeData.length;\n        const buyPercent = (buyCount / total) * 100;\n        const sellPercent = (sellCount / total) * 100;\n        const neutralPercent = (neutralCount / total) * 100;\n\n        let overallRecommendation = 'NEUTRAL';\n        if (buyPercent > 60) overallRecommendation = 'STRONG_BUY';\n        else if (buyPercent > 40) overallRecommendation = 'BUY';\n        else if (sellPercent > 60) overallRecommendation = 'STRONG_SELL';\n        else if (sellPercent > 40) overallRecommendation = 'SELL';\n\n        const overallTrend = buyPercent > sellPercent ? 'BULLISH' : sellPercent > buyPercent ? 'BEARISH' : 'NEUTRAL';\n        const consensusConfidence = Math.abs(buyPercent - sellPercent);\n\n        newAnalysis[symbol] = {\n          symbol,\n          currentPrice: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n          overallTrend,\n          timeframes: timeframeData,\n          consensus: {\n            buy: buyPercent,\n            sell: sellPercent,\n            neutral: neutralPercent,\n            recommendation: overallRecommendation,\n            confidence: consensusConfidence\n          }\n        };\n      });\n\n      setAnalysis(newAnalysis);\n      setLastUpdate(new Date());\n      setIsLoading(false);\n    }, analysisDelay);\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getRecommendation = (trend: string, rsi: number, strength: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    if (trend === 'BULLISH' && rsi < 70 && strength > 70) return 'STRONG_BUY';\n    if (trend === 'BULLISH' && rsi < 80) return 'BUY';\n    if (trend === 'BEARISH' && rsi > 30 && strength > 70) return 'STRONG_SELL';\n    if (trend === 'BEARISH' && rsi > 20) return 'SELL';\n    return 'NEUTRAL';\n  };\n\n  const getRecommendationColor = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'text-green-700 bg-green-100';\n      case 'BUY': return 'text-green-600 bg-green-50';\n      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';\n      case 'SELL': return 'text-red-600 bg-red-50';\n      case 'STRONG_SELL': return 'text-red-700 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getRecommendationText = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  const getTrendIcon = (trend: string): string => {\n    switch (trend) {\n      case 'BULLISH': return '📈';\n      case 'BEARISH': return '📉';\n      default: return '➡️';\n    }\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'EURCHF': '🇪🇺🇨🇭', 'EURAUD': '🇪🇺🇦🇺',\n      'EURCAD': '🇪🇺🇨🇦', 'EURNZD': '🇪🇺🇳🇿', 'GBPCHF': '🇬🇧🇨🇭', 'GBPAUD': '🇬🇧🇦🇺',\n      'GBPCAD': '🇬🇧🇨🇦', 'GBPNZD': '🇬🇧🇳🇿', 'AUDJPY': '🇦🇺🇯🇵', 'AUDCHF': '🇦🇺🇨🇭',\n      'AUDCAD': '🇦🇺🇨🇦', 'AUDNZD': '🇦🇺🇳🇿', 'NZDJPY': '🇳🇿🇯🇵', 'NZDCHF': '🇳🇿🇨🇭',\n      'NZDCAD': '🇳🇿🇨🇦', 'CADJPY': '🇨🇦🇯🇵', 'CADCHF': '🇨🇦🇨🇭', 'CHFJPY': '🇨🇭🇯🇵',\n      'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰', 'USOIL': '🛢️💰', 'UKOIL': '🛢️🇬🇧',\n      'BTCUSD': '₿💰', 'ETHUSD': '⟠💰', 'LTCUSD': '🪙💰', 'XRPUSD': '💎💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Enhanced Header with Controls */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            ⏰ تحليل متعدد الإطارات الزمنية المتقدم\n            <span className=\"mr-3 px-2 py-1 bg-blue-600 text-white rounded text-sm\">\n              {selectedPairs.length} أزواج\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n            </div>\n            <button\n              onClick={handleManualUpdate}\n              disabled={isLoading}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                isLoading\n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-green-600 text-white hover:bg-green-700'\n              }`}\n            >\n              {isLoading ? '🔄 جاري التحديث...' : '🔄 تحديث يدوي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n          {/* Pair Selection */}\n          <div className=\"lg:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              اختيار أزواج العملات ({selectedPairs.length}):\n            </label>\n\n            {/* Quick Selection Buttons */}\n            <div className=\"flex flex-wrap gap-2 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'].includes(p)))}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية (4)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => p.startsWith('EUR') || p.startsWith('GBP')))}\n                className=\"px-2 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700\"\n              >\n                الثانوية (14)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => ['XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'].includes(p)))}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع (4)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل ({allForexPairs.length})\n              </button>\n              <button\n                onClick={() => setSelectedPairs([])}\n                className=\"px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700\"\n              >\n                مسح\n              </button>\n            </div>\n\n            {/* Individual Pair Selection */}\n            <div className=\"max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              <div className=\"grid grid-cols-4 md:grid-cols-6 gap-1\">\n                {allForexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs font-medium transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                    title={getPairName(pair)}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Candle Count Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عدد الشموع للتحليل:\n            </label>\n            <select\n              value={candleCount}\n              onChange={(e) => setCandleCount(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {candleOptions.map(count => (\n                <option key={count} value={count}>\n                  {count} شمعة {count >= 100 ? '(دقة عالية)' : count >= 50 ? '(دقة متوسطة)' : '(دقة أساسية)'}\n                </option>\n              ))}\n            </select>\n            <div className=\"text-xs text-gray-500 mt-1\">\n              المزيد من الشموع = دقة أعلى\n            </div>\n          </div>\n\n          {/* Auto Update Toggle */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              إعدادات التحديث:\n            </label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={isAutoUpdate}\n                  onChange={(e) => setIsAutoUpdate(e.target.checked)}\n                  className=\"mr-2 rounded\"\n                />\n                <span className=\"text-sm\">تحديث تلقائي (كل 10 ثوانٍ)</span>\n              </label>\n              <div className=\"text-xs text-gray-500\">\n                {isAutoUpdate ? '🟢 التحديث التلقائي مفعل' : '🔴 التحديث اليدوي فقط'}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isLoading && (\n          <div className=\"text-center py-8\">\n            <div className=\"inline-flex items-center space-x-2 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n              <span className=\"text-gray-600 dark:text-gray-400\">\n                جاري تحليل {selectedPairs.length} زوج باستخدام {candleCount} شمعة...\n              </span>\n            </div>\n            <div className=\"mt-2 text-sm text-gray-500\">\n              الوقت المتوقع: {Math.ceil((selectedPairs.length * 200 + candleCount * 10) / 1000)} ثانية\n            </div>\n          </div>\n        )}\n\n        {/* No Pairs Selected */}\n        {!isLoading && selectedPairs.length === 0 && (\n          <div className=\"text-center py-8\">\n            <div className=\"text-6xl mb-4\">📊</div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              اختر أزواج العملات للتحليل\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              يمكنك اختيار من {allForexPairs.length} زوج عملة متاح\n            </p>\n            <button\n              onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY'])}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n            >\n              اختيار الأزواج الرئيسية\n            </button>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Overall Market Summary */}\n            <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص السوق العام ({selectedPairs.length} أزواج)\n              </h4>\n\n              {(() => {\n                const allRecommendations = Object.values(analysis).flatMap(a => a.timeframes.map(tf => tf.recommendation));\n                const buyCount = allRecommendations.filter(r => r.includes('BUY')).length;\n                const sellCount = allRecommendations.filter(r => r.includes('SELL')).length;\n                const neutralCount = allRecommendations.filter(r => r === 'NEUTRAL').length;\n                const total = allRecommendations.length;\n\n                const buyPercent = (buyCount / total) * 100;\n                const sellPercent = (sellCount / total) * 100;\n                const neutralPercent = (neutralCount / total) * 100;\n\n                return (\n                  <>\n                    <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-green-600\">{buyPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">شراء ({buyCount})</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-yellow-600\">{neutralPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">محايد ({neutralCount})</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-red-600\">{sellPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">بيع ({sellCount})</div>\n                      </div>\n                    </div>\n\n                    <div className=\"w-full bg-gray-200 rounded-full h-4 mb-2\">\n                      <div className=\"flex h-4 rounded-full overflow-hidden\">\n                        <div className=\"bg-green-500\" style={{ width: `${buyPercent}%` }}></div>\n                        <div className=\"bg-yellow-500\" style={{ width: `${neutralPercent}%` }}></div>\n                        <div className=\"bg-red-500\" style={{ width: `${sellPercent}%` }}></div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-center text-sm text-gray-600 dark:text-gray-400\">\n                      إجمالي التحليلات: {total} | عدد الشموع: {candleCount} |\n                      اتجاه السوق: {buyPercent > sellPercent ? '📈 صاعد' : sellPercent > buyPercent ? '📉 هابط' : '➡️ محايد'}\n                    </div>\n                  </>\n                );\n              })()}\n            </div>\n\n            {/* Individual Pair Analysis */}\n            {selectedPairs.map(symbol => {\n              const pairAnalysis = analysis[symbol];\n              if (!pairAnalysis) return null;\n\n              return (\n                <div key={symbol} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  {/* Pair Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <span className=\"text-2xl\">{getPairFlag(symbol)}</span>\n                      <div>\n                        <h5 className=\"text-lg font-bold text-gray-900 dark:text-white\">{symbol}</h5>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">{getPairName(symbol)}</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                        {pairAnalysis.currentPrice.toFixed(symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                      <div className={`text-sm font-medium px-2 py-1 rounded ${getRecommendationColor(pairAnalysis.consensus.recommendation)}`}>\n                        {getRecommendationText(pairAnalysis.consensus.recommendation)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Pair Consensus */}\n                  <div className=\"mb-4 bg-gray-50 dark:bg-gray-700 rounded p-3\">\n                    <div className=\"grid grid-cols-3 gap-2 text-center text-sm\">\n                      <div>\n                        <div className=\"font-bold text-green-600\">{pairAnalysis.consensus.buy.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">شراء</div>\n                      </div>\n                      <div>\n                        <div className=\"font-bold text-yellow-600\">{pairAnalysis.consensus.neutral.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">محايد</div>\n                      </div>\n                      <div>\n                        <div className=\"font-bold text-red-600\">{pairAnalysis.consensus.sell.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">بيع</div>\n                      </div>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                      <div className=\"flex h-2 rounded-full overflow-hidden\">\n                        <div className=\"bg-green-500\" style={{ width: `${pairAnalysis.consensus.buy}%` }}></div>\n                        <div className=\"bg-yellow-500\" style={{ width: `${pairAnalysis.consensus.neutral}%` }}></div>\n                        <div className=\"bg-red-500\" style={{ width: `${pairAnalysis.consensus.sell}%` }}></div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Timeframe Grid */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2\">\n                    {pairAnalysis.timeframes.map((tf, index) => {\n                      const timeframeInfo = timeframes.find(t => t.key === tf.timeframe);\n                      return (\n                        <div\n                          key={tf.timeframe}\n                          className={`border rounded p-2 cursor-pointer transition-all duration-200 ${\n                            selectedTimeframe === tf.timeframe\n                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                              : 'border-gray-200 hover:border-gray-300 dark:border-gray-600'\n                          }`}\n                          onClick={() => setSelectedTimeframe(tf.timeframe)}\n                        >\n                          <div className={`text-center py-1 rounded text-xs font-medium mb-2 ${timeframeInfo?.color || 'bg-gray-100 text-gray-800'}`}>\n                            {timeframeInfo?.name || tf.timeframe}\n                          </div>\n\n                          <div className={`text-center py-1 rounded mb-2 text-xs ${getRecommendationColor(tf.recommendation)}`}>\n                            {getRecommendationText(tf.recommendation)}\n                          </div>\n\n                          <div className=\"space-y-1 text-xs\">\n                            <div className=\"flex justify-between\">\n                              <span>RSI:</span>\n                              <span className={tf.rsi > 70 ? 'text-red-600' : tf.rsi < 30 ? 'text-green-600' : 'text-gray-600'}>\n                                {tf.rsi.toFixed(0)}\n                              </span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>القوة:</span>\n                              <span className=\"text-blue-600\">{tf.strength.toFixed(0)}%</span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>الثقة:</span>\n                              <span className=\"text-purple-600\">{tf.confidence.toFixed(0)}%</span>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        )}\n\n        {/* AI Recommendation Section */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"mt-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                🤖 توصيات الذكاء الاصطناعي المتقدمة\n              </h4>\n              <button\n                onClick={() => {\n                  // Manual AI analysis trigger\n                  alert(`🤖 تحليل الذكاء الاصطناعي:\\n\\n` +\n                    `• عدد الأزواج: ${selectedPairs.length}\\n` +\n                    `• عدد الشموع: ${candleCount}\\n` +\n                    `• دقة التحليل: ${candleCount >= 100 ? 'عالية جداً' : candleCount >= 50 ? 'عالية' : 'متوسطة'}\\n\\n` +\n                    `سيتم تحليل ${selectedPairs.length * timeframes.length} إطار زمني باستخدام ${candleCount} شمعة لكل إطار.`\n                  );\n                }}\n                className=\"px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700\"\n              >\n                🧠 تحليل ذكي متقدم\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {selectedPairs.slice(0, 3).map(symbol => {\n                const pairAnalysis = analysis[symbol];\n                if (!pairAnalysis) return null;\n\n                const strongSignals = pairAnalysis.timeframes.filter(tf =>\n                  tf.recommendation.includes('STRONG') && tf.confidence > 80\n                );\n\n                return (\n                  <div key={symbol} className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse mb-3\">\n                      <span className=\"text-lg\">{getPairFlag(symbol)}</span>\n                      <div>\n                        <h5 className=\"font-bold text-gray-900 dark:text-white\">{symbol}</h5>\n                        <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                          {candleCount} شمعة | {strongSignals.length} إشارة قوية\n                        </p>\n                      </div>\n                    </div>\n\n                    {strongSignals.length > 0 ? (\n                      <div className=\"space-y-2\">\n                        <div className={`p-2 rounded text-center ${getRecommendationColor(strongSignals[0].recommendation)}`}>\n                          <div className=\"font-bold text-sm\">{getRecommendationText(strongSignals[0].recommendation)}</div>\n                          <div className=\"text-xs\">ثقة: {strongSignals[0].confidence.toFixed(0)}%</div>\n                        </div>\n                        <div className=\"text-xs space-y-1\">\n                          <div>🎯 الإطار: {timeframes.find(t => t.key === strongSignals[0].timeframe)?.name}</div>\n                          <div>📊 RSI: {strongSignals[0].rsi.toFixed(1)}</div>\n                          <div>💪 القوة: {strongSignals[0].strength.toFixed(0)}%</div>\n                          <div>🎯 الهدف: {strongSignals[0].nextTarget.toFixed(symbol.includes('JPY') ? 3 : 5)}</div>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-4\">\n                        <div className=\"text-yellow-600 font-medium text-sm\">⚠️ لا توجد إشارات قوية</div>\n                        <div className=\"text-xs text-gray-500 mt-1\">جرب زيادة عدد الشموع للحصول على دقة أعلى</div>\n                      </div>\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {selectedPairs.length > 3 && (\n              <div className=\"mt-4 text-center\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  يتم عرض أول 3 أزواج فقط في توصيات الذكاء الاصطناعي\n                </p>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Analysis Statistics */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"mt-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n            <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n              📈 إحصائيات التحليل المتقدم\n            </h4>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-blue-600\">{selectedPairs.length}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">أزواج محللة</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-purple-600\">{selectedPairs.length * timeframes.length}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إطار زمني</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-green-600\">{candleCount}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">شمعة لكل إطار</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-orange-600\">\n                  {candleCount >= 100 ? '95%' : candleCount >= 50 ? '85%' : '75%'}\n                </div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">دقة متوقعة</div>\n              </div>\n            </div>\n\n            <div className=\"mt-4 text-center text-sm text-gray-600 dark:text-gray-400\">\n              💡 نصيحة: استخدم 100+ شمعة للحصول على أعلى دقة في التحليل\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA+Be,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C,CAAC;IACrF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QACjB;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAA0B;QAC/D;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAAgC;QACrE;YAAE,KAAK;YAAO,MAAM;YAAY,OAAO;QAAgC;QACvE;YAAE,KAAK;YAAO,MAAM;YAAY,OAAO;QAA8B;QACrE;YAAE,KAAK;YAAM,MAAM;YAAU,OAAO;QAA4B;QAChE;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAAgC;QACrE;YAAE,KAAK;YAAM,MAAM;YAAS,OAAO;QAAgC;QACnE;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAA4B;KAClE;IAED,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D,iCAAiC;QACjC;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QACxC;QAAU;QAAU;QAAU;QAC9B;QAAU;QAAU;QACpB;QAAU;QAAU;QACpB,eAAe;QACf;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAC9B,cAAc;QACd;QAAU;QAAU;QAAS;QAC7B,SAAS;QACT;QAAU;QAAU;QAAU;KAC/B;IAED,MAAM,gBAAgB;QAAC;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,8BAA8B;YAC9B,IAAI,cAAc;gBAChB,MAAM,WAAW;4DAAY;wBAC3B;oBACF;2DAAG,QAAQ,0BAA0B;gBAErC;mDAAO,IAAM,cAAc;;YAC7B;QACF;sCAAG;QAAC;QAAc;QAAe;QAAmB;KAAY;IAEhE,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QACA;IACF;IAEA,MAAM,iCAAiC;QACrC,aAAa;QAEb,+DAA+D;QAC/D,MAAM,gBAAgB,KAAK,GAAG,CAAC,cAAc,MAAM,GAAG,MAAM,cAAc,IAAI;QAE9E,WAAW;YACT,MAAM,cAAyD,CAAC;YAEhE,cAAc,OAAO,CAAC,CAAA;gBACpB,MAAM,YAAY,aAAa;gBAE/B,yEAAyE;gBACzE,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,MAAM,IAAI,KAAK,kBAAkB;gBAErF,MAAM,gBAAiC,WAAW,GAAG,CAAC,CAAA;oBACpD,yDAAyD;oBACzD,MAAM,gBAAgB,KAAK,MAAM,KAAK;oBACtC,MAAM,kBAAkB,AAAC,cAAc,MAAO,IAAI,wCAAwC;oBAE1F,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,YAAY;oBAClF,MAAM,WAAW,KAAK,GAAG,CAAC,gBAAgB,iBAAiB;oBAC3D,MAAM,MAAM,KAAK,MAAM,KAAK;oBAC5B,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACrC,MAAM,SAAS,KAAK,MAAM,KAAK,UAAU;oBACzC,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK;oBAC5C,MAAM,aAAa,KAAK,GAAG,CAAC,iBAAiB,eAAe;oBAE5D,OAAO;wBACL,WAAW,GAAG,GAAG;wBACjB;wBACA;wBACA;wBACA;wBACA;wBACA,gBAAgB,kBAAkB,OAAO,KAAK;wBAC9C;wBACA,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;wBAC1D,YAAY,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D;gBACF;gBAEA,sBAAsB;gBACtB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,QAAQ,MAAM;gBACrF,MAAM,YAAY,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,SAAS,MAAM;gBACvF,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,KAAK,WAAW,MAAM;gBAEvF,MAAM,QAAQ,cAAc,MAAM;gBAClC,MAAM,aAAa,AAAC,WAAW,QAAS;gBACxC,MAAM,cAAc,AAAC,YAAY,QAAS;gBAC1C,MAAM,iBAAiB,AAAC,eAAe,QAAS;gBAEhD,IAAI,wBAAwB;gBAC5B,IAAI,aAAa,IAAI,wBAAwB;qBACxC,IAAI,aAAa,IAAI,wBAAwB;qBAC7C,IAAI,cAAc,IAAI,wBAAwB;qBAC9C,IAAI,cAAc,IAAI,wBAAwB;gBAEnD,MAAM,eAAe,aAAa,cAAc,YAAY,cAAc,aAAa,YAAY;gBACnG,MAAM,sBAAsB,KAAK,GAAG,CAAC,aAAa;gBAElD,WAAW,CAAC,OAAO,GAAG;oBACpB;oBACA,cAAc,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D;oBACA,YAAY;oBACZ,WAAW;wBACT,KAAK;wBACL,MAAM;wBACN,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF;YAEA,YAAY;YACZ,cAAc,IAAI;YAClB,aAAa;QACf,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,oBAAoB,CAAC,OAAe,KAAa;QACrD,IAAI,UAAU,aAAa,MAAM,MAAM,WAAW,IAAI,OAAO;QAC7D,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO;QAC5C,IAAI,UAAU,aAAa,MAAM,MAAM,WAAW,IAAI,OAAO;QAC7D,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO;QAC5C,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAQ,UAAU;YAAQ,SAAS;YAAS,SAAS;YAC/D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAQ,UAAU;QAChE;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;;4CACb,cAAc,MAAM;4CAAC;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,WAAW,kBAAkB,CAAC;;;;;;;kDAE5C,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,8DAIX,OAHC,YACI,8CACA;kDAGL,YAAY,uBAAuB;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;4CAAkE;4CAC1D,cAAc,MAAM;4CAAC;;;;;;;kDAI9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK;4DAAC;4DAAU;4DAAU;4DAAU;yDAAS,CAAC,QAAQ,CAAC;gDAC5G,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC;gDAC9F,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK;4DAAC;4DAAU;4DAAU;4DAAS;yDAAS,CAAC,QAAQ,CAAC;gDAC3G,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,cAAc,MAAM;oDAAC;;;;;;;0DAE9B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,EAAE;gDAClC,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,2DAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;oDAEN,OAAO,YAAY;8DAElB;mDAfI;;;;;;;;;;;;;;;;;;;;;0CAuBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;gDAAmB,OAAO;;oDACxB;oDAAM;oDAAO,SAAS,MAAM,gBAAgB,SAAS,KAAK,iBAAiB;;+CADjE;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;0DACZ,eAAe,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvD,6LAAC;gBAAI,WAAU;;oBAEZ,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;4CAAmC;4CACrC,cAAc,MAAM;4CAAC;4CAAe;4CAAY;;;;;;;;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;oCAA6B;oCAC1B,KAAK,IAAI,CAAC,CAAC,cAAc,MAAM,GAAG,MAAM,cAAc,EAAE,IAAI;oCAAM;;;;;;;;;;;;;oBAMvF,CAAC,aAAa,cAAc,MAAM,KAAK,mBACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;;oCAAwC;oCAClC,cAAc,MAAM;oCAAC;;;;;;;0CAExC,6LAAC;gCACC,SAAS,IAAM,iBAAiB;wCAAC;wCAAU;wCAAU;qCAAS;gCAC9D,WAAU;0CACX;;;;;;;;;;;;oBAOJ,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA2D;4CACjD,cAAc,MAAM;4CAAC;;;;;;;oCAG5C,CAAC;wCACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,UAAU,OAAO,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,cAAc;wCACxG,MAAM,WAAW,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,MAAM;wCACzE,MAAM,YAAY,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,SAAS,MAAM;wCAC3E,MAAM,eAAe,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM,WAAW,MAAM;wCAC3E,MAAM,QAAQ,mBAAmB,MAAM;wCAEvC,MAAM,aAAa,AAAC,WAAW,QAAS;wCACxC,MAAM,cAAc,AAAC,YAAY,QAAS;wCAC1C,MAAM,iBAAiB,AAAC,eAAe,QAAS;wCAEhD,qBACE;;8DACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAqC,WAAW,OAAO,CAAC;wEAAG;;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAO;wEAAS;;;;;;;;;;;;;sEAE5E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAsC,eAAe,OAAO,CAAC;wEAAG;;;;;;;8EAC/E,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAQ;wEAAa;;;;;;;;;;;;;sEAEjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAmC,YAAY,OAAO,CAAC;wEAAG;;;;;;;8EACzE,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAM;wEAAU;;;;;;;;;;;;;;;;;;;8DAI9E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,OAAO;oEAAE,OAAO,AAAC,GAAa,OAAX,YAAW;gEAAG;;;;;;0EAC/D,6LAAC;gEAAI,WAAU;gEAAgB,OAAO;oEAAE,OAAO,AAAC,GAAiB,OAAf,gBAAe;gEAAG;;;;;;0EACpE,6LAAC;gEAAI,WAAU;gEAAa,OAAO;oEAAE,OAAO,AAAC,GAAc,OAAZ,aAAY;gEAAG;;;;;;;;;;;;;;;;;8DAIlE,6LAAC;oDAAI,WAAU;;wDAAuD;wDACjD;wDAAM;wDAAgB;wDAAY;wDACvC,aAAa,cAAc,YAAY,cAAc,aAAa,YAAY;;;;;;;;;oCAIpG,CAAC;;;;;;;4BAIF,cAAc,GAAG,CAAC,CAAA;gCACjB,MAAM,eAAe,QAAQ,CAAC,OAAO;gCACrC,IAAI,CAAC,cAAc,OAAO;gCAE1B,qBACE,6LAAC;oCAAiB,WAAU;;sDAE1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,YAAY;;;;;;sEACxC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmD;;;;;;8EACjE,6LAAC;oEAAE,WAAU;8EAA4C,YAAY;;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,aAAa,YAAY,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;sEAElE,6LAAC;4DAAI,WAAW,AAAC,yCAAsG,OAA9D,uBAAuB,aAAa,SAAS,CAAC,cAAc;sEAClH,sBAAsB,aAAa,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;sDAMlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA4B,aAAa,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EACjF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;sEAEpD,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA6B,aAAa,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EACtF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;sEAEpD,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA0B,aAAa,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAChF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,OAAO;oEAAE,OAAO,AAAC,GAA6B,OAA3B,aAAa,SAAS,CAAC,GAAG,EAAC;gEAAG;;;;;;0EAC/E,6LAAC;gEAAI,WAAU;gEAAgB,OAAO;oEAAE,OAAO,AAAC,GAAiC,OAA/B,aAAa,SAAS,CAAC,OAAO,EAAC;gEAAG;;;;;;0EACpF,6LAAC;gEAAI,WAAU;gEAAa,OAAO;oEAAE,OAAO,AAAC,GAA8B,OAA5B,aAAa,SAAS,CAAC,IAAI,EAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;sDAMpF,6LAAC;4CAAI,WAAU;sDACZ,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI;gDAChC,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,GAAG,SAAS;gDACjE,qBACE,6LAAC;oDAEC,WAAW,AAAC,iEAIX,OAHC,sBAAsB,GAAG,SAAS,GAC9B,mDACA;oDAEN,SAAS,IAAM,qBAAqB,GAAG,SAAS;;sEAEhD,6LAAC;4DAAI,WAAW,AAAC,qDAAwG,OAApD,CAAA,0BAAA,oCAAA,cAAe,KAAK,KAAI;sEAC1F,CAAA,0BAAA,oCAAA,cAAe,IAAI,KAAI,GAAG,SAAS;;;;;;sEAGtC,6LAAC;4DAAI,WAAW,AAAC,yCAAkF,OAA1C,uBAAuB,GAAG,cAAc;sEAC9F,sBAAsB,GAAG,cAAc;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAW,GAAG,GAAG,GAAG,KAAK,iBAAiB,GAAG,GAAG,GAAG,KAAK,mBAAmB;sFAC9E,GAAG,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;8EAGpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;;gFAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;8EAE1D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;;gFAAmB,GAAG,UAAU,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;;;mDA7B3D,GAAG,SAAS;;;;;4CAkCvB;;;;;;;mCArFM;;;;;4BAyFd;;;;;;;oBAKH,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCACC,SAAS;4CACP,6BAA6B;4CAC7B,MAAM,AAAC,mCACL,AAAC,kBAAsC,OAArB,cAAc,MAAM,EAAC,QACvC,AAAC,iBAA4B,OAAZ,aAAY,QAC7B,AAAC,kBAA4F,OAA3E,eAAe,MAAM,eAAe,eAAe,KAAK,UAAU,UAAS,UAC7F,AAAC,cAA4E,OAA/D,cAAc,MAAM,GAAG,WAAW,MAAM,EAAC,wBAAkC,OAAZ,aAAY;wCAE7F;wCACA,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;wCA2BF;oCA1B3B,MAAM,eAAe,QAAQ,CAAC,OAAO;oCACrC,IAAI,CAAC,cAAc,OAAO;oCAE1B,MAAM,gBAAgB,aAAa,UAAU,CAAC,MAAM,CAAC,CAAA,KACnD,GAAG,cAAc,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,GAAG;oCAG1D,qBACE,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,YAAY;;;;;;kEACvC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;;oEACV;oEAAY;oEAAS,cAAc,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;4CAKhD,cAAc,MAAM,GAAG,kBACtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,2BAAkF,OAAxD,uBAAuB,aAAa,CAAC,EAAE,CAAC,cAAc;;0EAC/F,6LAAC;gEAAI,WAAU;0EAAqB,sBAAsB,aAAa,CAAC,EAAE,CAAC,cAAc;;;;;;0EACzF,6LAAC;gEAAI,WAAU;;oEAAU;oEAAM,aAAa,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAI;qEAAY,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,aAAa,CAAC,EAAE,CAAC,SAAS,eAAzD,uCAAA,iBAA4D,IAAI;;;;;;;0EACjF,6LAAC;;oEAAI;oEAAS,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;0EAC3C,6LAAC;;oEAAI;oEAAW,aAAa,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EACrD,6LAAC;;oEAAI;oEAAW,aAAa,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;qEAIrF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;;uCA3BxC;;;;;gCAgCd;;;;;;4BAGD,cAAc,MAAM,GAAG,mBACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;oBAS/D,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAIzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,cAAc,MAAM;;;;;;0DACvE,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsC,cAAc,MAAM,GAAG,WAAW,MAAM;;;;;;0DAC7F,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,QAAQ,eAAe,KAAK,QAAQ;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;0CAA4D;;;;;;;;;;;;;;;;;;;;;;;;AAQvF;GAzpBwB;KAAA", "debugId": null}}, {"offset": {"line": 3479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ManualAIRecommendations.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AIRecommendation {\n  id: string;\n  symbol: string;\n  timeframe: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  accuracy: number;\n  candleCount: number;\n  reasoning: string[];\n  technicalScore: number;\n  sentimentScore: number;\n  volumeScore: number;\n  trendScore: number;\n  overallScore: number;\n  timestamp: number;\n}\n\nexport default function ManualAIRecommendations() {\n  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [selectedTimeframes, setSelectedTimeframes] = useState<string[]>(['1h', '4h', '1d']);\n  const [candleCount, setCandleCount] = useState<number>(100);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [lastAnalysis, setLastAnalysis] = useState<Date | null>(null);\n\n  const allForexPairs = [\n    // Major Pairs\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    // Minor Pairs\n    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',\n    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',\n    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',\n    'NZDJPY', 'NZDCHF', 'NZDCAD',\n    'CADJPY', 'CADCHF', 'CHFJPY',\n    // Commodities & Crypto\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD'\n  ];\n\n  const timeframes = [\n    { key: '1m', name: '1 دقيقة' },\n    { key: '5m', name: '5 دقائق' },\n    { key: '15m', name: '15 دقيقة' },\n    { key: '30m', name: '30 دقيقة' },\n    { key: '1h', name: '1 ساعة' },\n    { key: '4h', name: '4 ساعات' },\n    { key: '1d', name: '1 يوم' },\n    { key: '1w', name: '1 أسبوع' }\n  ];\n\n  const candleOptions = [20, 30, 50, 75, 100, 150, 200];\n\n  // Manual AI Analysis Function\n  const runAIAnalysis = async () => {\n    if (selectedPairs.length === 0 || selectedTimeframes.length === 0) {\n      alert('يرجى اختيار أزواج عملات وإطارات زمنية للتحليل');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    \n    // Simulate analysis time based on complexity\n    const analysisTime = selectedPairs.length * selectedTimeframes.length * (candleCount / 50) * 500;\n    const maxTime = Math.min(analysisTime, 5000); // Max 5 seconds\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, maxTime));\n      \n      const newRecommendations: AIRecommendation[] = [];\n      \n      selectedPairs.forEach(symbol => {\n        selectedTimeframes.forEach(timeframe => {\n          const basePrice = getBasePrice(symbol);\n          \n          // Advanced AI scoring based on candle count\n          const accuracyBonus = Math.min((candleCount - 20) / 180 * 20, 20);\n          const technicalScore = 50 + Math.random() * 40 + (accuracyBonus / 4);\n          const sentimentScore = 40 + Math.random() * 40 + (accuracyBonus / 5);\n          const volumeScore = 45 + Math.random() * 35 + (accuracyBonus / 6);\n          const trendScore = 50 + Math.random() * 35 + (accuracyBonus / 4);\n          \n          const overallScore = (\n            technicalScore * 0.4 +\n            sentimentScore * 0.2 +\n            volumeScore * 0.2 +\n            trendScore * 0.2\n          );\n          \n          const action = overallScore > 70 ? 'BUY' : overallScore < 40 ? 'SELL' : 'HOLD';\n          const confidence = Math.min(70 + accuracyBonus + Math.random() * 20, 95);\n          const accuracy = Math.min(80 + accuracyBonus + Math.random() * 15, 98);\n          \n          // Calculate levels\n          const entry = basePrice + (Math.random() - 0.5) * basePrice * 0.005;\n          const stopLoss = action === 'BUY' \n            ? entry - (entry * 0.015) \n            : entry + (entry * 0.015);\n          const tp1 = action === 'BUY' \n            ? entry + (entry * 0.02) \n            : entry - (entry * 0.02);\n          const tp2 = action === 'BUY' \n            ? entry + (entry * 0.035) \n            : entry - (entry * 0.035);\n          const tp3 = action === 'BUY' \n            ? entry + (entry * 0.05) \n            : entry - (entry * 0.05);\n          \n          const riskReward = Math.abs(tp1 - entry) / Math.abs(entry - stopLoss);\n          \n          newRecommendations.push({\n            id: `${symbol}_${timeframe}_${Date.now()}`,\n            symbol,\n            timeframe,\n            action,\n            entry,\n            stopLoss,\n            takeProfit1: tp1,\n            takeProfit2: tp2,\n            takeProfit3: tp3,\n            riskReward,\n            confidence,\n            accuracy,\n            candleCount,\n            reasoning: generateAdvancedReasoning(symbol, action, technicalScore, sentimentScore, candleCount),\n            technicalScore,\n            sentimentScore,\n            volumeScore,\n            trendScore,\n            overallScore,\n            timestamp: Date.now()\n          });\n        });\n      });\n      \n      // Sort by overall score (best recommendations first)\n      newRecommendations.sort((a, b) => b.overallScore - a.overallScore);\n      \n      setRecommendations(newRecommendations);\n      setLastAnalysis(new Date());\n      \n    } catch (error) {\n      console.error('AI Analysis Error:', error);\n      alert('حدث خطأ في التحليل. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const generateAdvancedReasoning = (symbol: string, action: string, technicalScore: number, sentimentScore: number, candleCount: number): string[] => {\n    const reasons = [];\n    \n    reasons.push(`🔍 تحليل متقدم باستخدام ${candleCount} شمعة`);\n    \n    if (action === 'BUY') {\n      reasons.push(`📈 إشارة شراء قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💹 معنويات السوق إيجابية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات المقاومة مع حجم تداول مرتفع`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الصاعد`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else if (action === 'SELL') {\n      reasons.push(`📉 إشارة بيع قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💸 معنويات السوق سلبية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات الدعم مع ضغط بيع قوي`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الهابط`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else {\n      reasons.push(`⚖️ السوق في حالة توازن - انتظار إشارة واضحة`);\n      reasons.push(`📊 المؤشرات متضاربة - يُنصح بالانتظار`);\n    }\n    \n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getActionColor = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'bg-green-100 text-green-800 border-green-200';\n      case 'SELL': return 'bg-red-100 text-red-800 border-red-200';\n      case 'HOLD': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getActionText = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'شراء';\n      case 'SELL': return 'بيع';\n      case 'HOLD': return 'انتظار';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المتقدمة\n            <span className=\"mr-3 px-2 py-1 bg-purple-600 text-white rounded text-sm\">\n              تحكم يدوي\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            {lastAnalysis && (\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                آخر تحليل: {lastAnalysis.toLocaleTimeString('ar-SA')}\n              </div>\n            )}\n            <button\n              onClick={runAIAnalysis}\n              disabled={isAnalyzing}\n              className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n                isAnalyzing \n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-purple-600 text-white hover:bg-purple-700'\n              }`}\n            >\n              {isAnalyzing ? '🧠 جاري التحليل...' : '🚀 تشغيل التحليل الذكي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات ({selectedPairs.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"max-h-24 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-1\">\n              <div className=\"grid grid-cols-3 gap-1\">\n                {allForexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطارات الزمنية ({selectedTimeframes.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedTimeframes(['1h', '4h', '1d'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الأساسية\n              </button>\n              <button\n                onClick={() => setSelectedTimeframes(timeframes.map(t => t.key))}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-4 gap-1\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf.key}\n                  onClick={() => {\n                    if (selectedTimeframes.includes(tf.key)) {\n                      setSelectedTimeframes(selectedTimeframes.filter(t => t !== tf.key));\n                    } else {\n                      setSelectedTimeframes([...selectedTimeframes, tf.key]);\n                    }\n                  }}\n                  className={`px-1 py-1 rounded text-xs transition-colors ${\n                    selectedTimeframes.includes(tf.key)\n                      ? 'bg-green-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf.key}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Candle Count */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عدد الشموع للتحليل:\n            </label>\n            <select\n              value={candleCount}\n              onChange={(e) => setCandleCount(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white mb-2\"\n            >\n              {candleOptions.map(count => (\n                <option key={count} value={count}>\n                  {count} شمعة\n                </option>\n              ))}\n            </select>\n            <div className=\"text-xs text-gray-500\">\n              دقة متوقعة: {candleCount >= 150 ? '98%' : candleCount >= 100 ? '95%' : candleCount >= 50 ? '85%' : '75%'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Analysis Progress */}\n        {isAnalyzing && (\n          <div className=\"mb-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-3\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600\"></div>\n              <span className=\"font-medium text-gray-900 dark:text-white\">\n                🧠 الذكاء الاصطناعي يحلل السوق...\n              </span>\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n              <div>• تحليل {selectedPairs.length} زوج عملة</div>\n              <div>• فحص {selectedTimeframes.length} إطار زمني</div>\n              <div>• معالجة {candleCount} شمعة لكل إطار</div>\n              <div>• إجمالي نقاط البيانات: {selectedPairs.length * selectedTimeframes.length * candleCount}</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && recommendations.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🤖</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              مرحباً بك في نظام الذكاء الاصطناعي المتقدم\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اختر أزواج العملات والإطارات الزمنية وعدد الشموع، ثم اضغط على \"تشغيل التحليل الذكي\" للحصول على توصيات دقيقة\n            </p>\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">💡 نصائح للحصول على أفضل النتائج:</h4>\n              <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1 text-right\">\n                <li>• استخدم 100+ شمعة للحصول على دقة عالية</li>\n                <li>• اختر 3-5 أزواج للتحليل المفصل</li>\n                <li>• ركز على الإطارات الزمنية الأساسية (1h, 4h, 1d)</li>\n                <li>• التحليل اليدوي يعطي نتائج أكثر دقة</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Recommendations Results */}\n        {!isAnalyzing && recommendations.length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Summary Stats */}\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص التحليل\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {recommendations.filter(r => r.action === 'BUY').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات شراء</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {recommendations.filter(r => r.action === 'SELL').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات بيع</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-yellow-600\">\n                    {recommendations.filter(r => r.action === 'HOLD').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">انتظار</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {(recommendations.reduce((sum, r) => sum + r.accuracy, 0) / recommendations.length).toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">متوسط الدقة</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recommendations Grid */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n              {recommendations.slice(0, 9).map((rec) => (\n                <div key={rec.id} className={`border-2 rounded-xl p-6 ${\n                  rec.action === 'BUY' \n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                    : rec.action === 'SELL'\n                    ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n                    : 'bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200'\n                }`}>\n                  {/* Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                      <div>\n                        <h4 className=\"text-lg font-bold text-gray-900\">\n                          {getActionText(rec.action)} {rec.symbol}\n                        </h4>\n                        <p className=\"text-xs text-gray-600\">\n                          {timeframes.find(t => t.key === rec.timeframe)?.name} | {rec.candleCount} شمعة\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900\">\n                        {rec.confidence.toFixed(0)}%\n                      </div>\n                      <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(0)}%</div>\n                    </div>\n                  </div>\n\n                  {/* Action Badge */}\n                  <div className={`text-center py-2 rounded-lg mb-4 border-2 ${getActionColor(rec.action)}`}>\n                    <div className=\"font-bold text-sm\">{getActionText(rec.action)}</div>\n                    <div className=\"text-xs\">النتيجة الإجمالية: {rec.overallScore.toFixed(1)}/100</div>\n                  </div>\n\n                  {/* Price Levels */}\n                  {rec.action !== 'HOLD' && (\n                    <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">الدخول</div>\n                        <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                        <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">هدف 1</div>\n                        <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">R/R</div>\n                        <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* AI Scores */}\n                  <div className=\"bg-white rounded p-3 mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🎯 نتائج التحليل:</h5>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"flex justify-between\">\n                        <span>فني:</span>\n                        <span className=\"font-medium\">{rec.technicalScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>مشاعر:</span>\n                        <span className=\"font-medium\">{rec.sentimentScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>حجم:</span>\n                        <span className=\"font-medium\">{rec.volumeScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>اتجاه:</span>\n                        <span className=\"font-medium\">{rec.trendScore.toFixed(0)}/100</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* AI Reasoning */}\n                  <div className=\"bg-white rounded p-3\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                    <ul className=\"text-xs text-gray-600 space-y-1\">\n                      {rec.reasoning.slice(0, 3).map((reason, i) => (\n                        <li key={i} className=\"flex items-start\">\n                          <span className=\"mr-1 text-blue-500\">▶</span>\n                          <span>{reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {recommendations.length > 9 && (\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  يتم عرض أفضل 9 توصيات من أصل {recommendations.length}\n                </p>\n                <button\n                  onClick={() => {\n                    // Could implement pagination or show all\n                    alert(`إجمالي التوصيات: ${recommendations.length}\\nيتم عرض أفضل 9 توصيات بناءً على النتيجة الإجمالية`);\n                  }}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700\"\n                >\n                  عرض جميع التوصيات ({recommendations.length})\n                </button>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAM;QAAM;KAAK;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,gBAAgB;QACpB,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QACxC;QAAU;QAAU;QAAU;QAC9B;QAAU;QAAU;QACpB;QAAU;QAAU;QACpB,uBAAuB;QACvB;QAAU;QAAU;QAAS;QAAU;KACxC;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAM,MAAM;QAAS;QAC5B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAQ;QAC3B;YAAE,KAAK;YAAM,MAAM;QAAU;KAC9B;IAED,MAAM,gBAAgB;QAAC;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAErD,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI,cAAc,MAAM,KAAK,KAAK,mBAAmB,MAAM,KAAK,GAAG;YACjE,MAAM;YACN;QACF;QAEA,eAAe;QAEf,6CAA6C;QAC7C,MAAM,eAAe,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG,CAAC,cAAc,EAAE,IAAI;QAC7F,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,OAAO,gBAAgB;QAE9D,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,qBAAyC,EAAE;YAEjD,cAAc,OAAO,CAAC,CAAA;gBACpB,mBAAmB,OAAO,CAAC,CAAA;oBACzB,MAAM,YAAY,aAAa;oBAE/B,4CAA4C;oBAC5C,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,MAAM,IAAI;oBAC9D,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,cAAc,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAC/D,MAAM,aAAa,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAE9D,MAAM,eACJ,iBAAiB,MACjB,iBAAiB,MACjB,cAAc,MACd,aAAa;oBAGf,MAAM,SAAS,eAAe,KAAK,QAAQ,eAAe,KAAK,SAAS;oBACxE,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBACrE,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBAEnE,mBAAmB;oBACnB,MAAM,QAAQ,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBAErB,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;oBAE5D,mBAAmB,IAAI,CAAC;wBACtB,IAAI,AAAC,GAAY,OAAV,QAAO,KAAgB,OAAb,WAAU,KAAc,OAAX,KAAK,GAAG;wBACtC;wBACA;wBACA;wBACA;wBACA;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;wBACA;wBACA;wBACA;wBACA,WAAW,0BAA0B,QAAQ,QAAQ,gBAAgB,gBAAgB;wBACrF;wBACA;wBACA;wBACA;wBACA;wBACA,WAAW,KAAK,GAAG;oBACrB;gBACF;YACF;YAEA,qDAAqD;YACrD,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YAEjE,mBAAmB;YACnB,gBAAgB,IAAI;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,4BAA4B,CAAC,QAAgB,QAAgB,gBAAwB,gBAAwB;QACjH,MAAM,UAAU,EAAE;QAElB,QAAQ,IAAI,CAAC,AAAC,2BAAsC,OAAZ,aAAY;QAEpD,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,wCAAiE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC/E,QAAQ,IAAI,CAAC,AAAC,6BAAsD,OAA1B,eAAe,OAAO,CAAC,IAAG;YACpE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,uCAAgE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC9E,QAAQ,IAAI,CAAC,AAAC,2BAAoD,OAA1B,eAAe,OAAO,CAAC,IAAG;YAClE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO;YACL,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;QAChB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;oCACZ,8BACC,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,aAAa,kBAAkB,CAAC;;;;;;;kDAGhD,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;kDAGL,cAAc,uBAAuB;;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACjE,cAAc,MAAM;4CAAC;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,+CAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;8DAGL;mDAdI;;;;;;;;;;;;;;;;;;;;;0CAsBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CAC9D,mBAAmB,MAAM;4CAAC;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,sBAAsB;wDAAC;wDAAM;wDAAM;qDAAK;gDACvD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,sBAAsB,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gDAC9D,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS;oDACP,IAAI,mBAAmB,QAAQ,CAAC,GAAG,GAAG,GAAG;wDACvC,sBAAsB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM,GAAG,GAAG;oDACnE,OAAO;wDACL,sBAAsB;+DAAI;4DAAoB,GAAG,GAAG;yDAAC;oDACvD;gDACF;gDACA,WAAW,AAAC,+CAIX,OAHC,mBAAmB,QAAQ,CAAC,GAAG,GAAG,IAC9B,4BACA;0DAGL,GAAG,GAAG;+CAdF,GAAG,GAAG;;;;;;;;;;;;;;;;0CAqBnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;gDAAmB,OAAO;;oDACxB;oDAAM;;+CADI;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;;4CAAwB;4CACxB,eAAe,MAAM,QAAQ,eAAe,MAAM,QAAQ,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3G,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAS,cAAc,MAAM;4CAAC;;;;;;;kDACnC,6LAAC;;4CAAI;4CAAO,mBAAmB,MAAM;4CAAC;;;;;;;kDACtC,6LAAC;;4CAAI;4CAAU;4CAAY;;;;;;;kDAC3B,6LAAC;;4CAAI;4CAAyB,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oBAMtF,CAAC,eAAe,gBAAgB,MAAM,KAAK,mBAC1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,gBAAgB,MAAM,GAAG,mBACxC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,OAAO,MAAM;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,CAAC,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,gBAAgB,MAAM,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEjG,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;0CAMhE,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wCAiBrB;yDAhBX,6LAAC;wCAAiB,WAAW,AAAC,2BAM7B,OALC,IAAI,MAAM,KAAK,QACX,mEACA,IAAI,MAAM,KAAK,SACf,4DACA;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAW,YAAY,IAAI,MAAM;;;;;;0EACjD,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;4EACX,cAAc,IAAI,MAAM;4EAAE;4EAAE,IAAI,MAAM;;;;;;;kFAEzC,6LAAC;wEAAE,WAAU;;6EACV,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,IAAI,SAAS,eAA5C,uCAAA,iBAA+C,IAAI;4EAAC;4EAAI,IAAI,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;kEAI/E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE7B,6LAAC;gEAAI,WAAU;;oEAAwB;oEAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAKzE,6LAAC;gDAAI,WAAW,AAAC,6CAAuE,OAA3B,eAAe,IAAI,MAAM;;kEACpF,6LAAC;wDAAI,WAAU;kEAAqB,cAAc,IAAI,MAAM;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;;4DAAU;4DAAoB,IAAI,YAAY,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAI1E,IAAI,MAAM,KAAK,wBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAErE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;;oEAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAM1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,WAAW,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE5D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;kEACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;gEAAW,WAAU;;kFACpB,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;kFACrC,6LAAC;kFAAM;;;;;;;+DAFA;;;;;;;;;;;;;;;;;uCApFP,IAAI,EAAE;;;;;;;;;;;4BA+FnB,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgD;4CAC7B,gBAAgB,MAAM;;;;;;;kDAEtD,6LAAC;wCACC,SAAS;4CACP,yCAAyC;4CACzC,MAAM,AAAC,oBAA0C,OAAvB,gBAAgB,MAAM,EAAC;wCACnD;wCACA,WAAU;;4CACX;4CACqB,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D;GA/iBwB;KAAA", "debugId": null}}, {"offset": {"line": 4868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/FibonacciAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface FibonacciLevel {\n  level: number;\n  price: number;\n  percentage: string;\n  type: 'support' | 'resistance';\n  strength: number;\n}\n\ninterface FibonacciAnalysis {\n  symbol: string;\n  high: number;\n  low: number;\n  direction: 'bullish' | 'bearish';\n  levels: FibonacciLevel[];\n  currentPrice: number;\n  nearestLevel: FibonacciLevel;\n  recommendation: string;\n  confidence: number;\n}\n\nexport default function FibonacciAnalysis() {\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [analysis, setAnalysis] = useState<FibonacciAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [timeframe, setTimeframe] = useState<string>('1h');\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n\n  const fibonacciLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1, 1.272, 1.414, 1.618, 2.618];\n\n  const runFibonacciAnalysis = async () => {\n    setIsAnalyzing(true);\n    \n    try {\n      // Simulate analysis delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const basePrice = getBasePrice(selectedPair);\n      const volatility = getVolatility(selectedPair);\n      \n      // Generate swing high and low\n      const swingRange = basePrice * volatility * 0.05;\n      const high = basePrice + swingRange * (0.5 + Math.random() * 0.5);\n      const low = basePrice - swingRange * (0.5 + Math.random() * 0.5);\n      \n      const direction = Math.random() > 0.5 ? 'bullish' : 'bearish';\n      const currentPrice = basePrice + (Math.random() - 0.5) * swingRange * 0.3;\n      \n      // Calculate Fibonacci levels\n      const levels: FibonacciLevel[] = fibonacciLevels.map(level => {\n        const price = direction === 'bullish' \n          ? high - (high - low) * level\n          : low + (high - low) * level;\n          \n        const distanceFromCurrent = Math.abs(price - currentPrice);\n        const strength = Math.max(20, 100 - (distanceFromCurrent / basePrice) * 1000);\n        \n        return {\n          level,\n          price,\n          percentage: `${(level * 100).toFixed(1)}%`,\n          type: price > currentPrice ? 'resistance' : 'support',\n          strength: Math.min(strength, 95)\n        };\n      });\n      \n      // Find nearest level\n      const nearestLevel = levels.reduce((nearest, current) => {\n        const nearestDistance = Math.abs(nearest.price - currentPrice);\n        const currentDistance = Math.abs(current.price - currentPrice);\n        return currentDistance < nearestDistance ? current : nearest;\n      });\n      \n      // Generate recommendation\n      const distanceToNearest = Math.abs(nearestLevel.price - currentPrice) / basePrice * 100;\n      let recommendation = '';\n      let confidence = 0;\n      \n      if (distanceToNearest < 0.1) {\n        if (nearestLevel.type === 'support' && direction === 'bullish') {\n          recommendation = 'شراء قوي - السعر عند مستوى دعم فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else if (nearestLevel.type === 'resistance' && direction === 'bearish') {\n          recommendation = 'بيع قوي - السعر عند مستوى مقاومة فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else {\n          recommendation = 'انتظار - السعر عند مستوى فيبوناتشي مهم';\n          confidence = 70 + Math.random() * 15;\n        }\n      } else if (distanceToNearest < 0.2) {\n        recommendation = `مراقبة - السعر يقترب من مستوى ${nearestLevel.percentage}`;\n        confidence = 60 + Math.random() * 20;\n      } else {\n        recommendation = 'لا توجد إشارة واضحة - السعر بعيد عن مستويات فيبوناتشي';\n        confidence = 40 + Math.random() * 20;\n      }\n      \n      setAnalysis({\n        symbol: selectedPair,\n        high,\n        low,\n        direction,\n        levels,\n        currentPrice,\n        nearestLevel,\n        recommendation,\n        confidence\n      });\n      \n    } catch (error) {\n      console.error('Fibonacci Analysis Error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,\n      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,\n      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,\n      'USOIL': 3.0, 'BTCUSD': 4.0\n    };\n    return volatilities[symbol] || 1.0;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getLevelColor = (level: FibonacciLevel, currentPrice: number): string => {\n    const distance = Math.abs(level.price - currentPrice) / currentPrice * 100;\n    \n    if (distance < 0.1) return 'bg-red-100 border-red-500 text-red-800'; // Very close\n    if (distance < 0.2) return 'bg-yellow-100 border-yellow-500 text-yellow-800'; // Close\n    if (level.type === 'support') return 'bg-green-100 border-green-500 text-green-800';\n    return 'bg-blue-100 border-blue-500 text-blue-800';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📐 تحليل فيبوناتشي المتقدم\n            <span className=\"mr-3 px-2 py-1 bg-yellow-600 text-white rounded text-sm\">\n              أداة احترافية\n            </span>\n          </h3>\n          <button\n            onClick={runFibonacciAnalysis}\n            disabled={isAnalyzing}\n            className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n              isAnalyzing \n                ? 'bg-gray-400 text-white cursor-not-allowed'\n                : 'bg-yellow-600 text-white hover:bg-yellow-700'\n            }`}\n          >\n            {isAnalyzing ? '📐 جاري التحليل...' : '🚀 تحليل فيبوناتشي'}\n          </button>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              زوج العملة:\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>\n                  {getPairFlag(pair)} {pair}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <select\n              value={timeframe}\n              onChange={(e) => setTimeframe(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {timeframes.map(tf => (\n                <option key={tf} value={tf}>{tf}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isAnalyzing && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🔍 تحليل مستويات فيبوناتشي لـ {selectedPair}...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500\">\n              جاري حساب مستويات الدعم والمقاومة الذهبية\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && !analysis && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📐</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              تحليل فيبوناتشي الاحترافي\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اكتشف مستويات الدعم والمقاومة الذهبية باستخدام نسب فيبوناتشي المتقدمة\n            </p>\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                💡 ما هو تحليل فيبوناتشي؟\n              </h4>\n              <ul className=\"text-sm text-yellow-800 dark:text-yellow-200 space-y-1 text-right\">\n                <li>• مستويات رياضية دقيقة للدعم والمقاومة</li>\n                <li>• نسب ذهبية مستخدمة من قبل المحترفين</li>\n                <li>• تحديد نقاط الدخول والخروج المثلى</li>\n                <li>• دقة عالية في التنبؤ بحركة الأسعار</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isAnalyzing && analysis && (\n          <div className=\"space-y-6\">\n            {/* Summary */}\n            <div className=\"bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <span className=\"text-2xl\">{getPairFlag(analysis.symbol)}</span>\n                  <div>\n                    <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {analysis.symbol} - {timeframe}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      اتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">السعر الحالي</div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-600\">\n                    {analysis.high.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أعلى سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-red-600\">\n                    {analysis.low.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أقل سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">\n                    {analysis.confidence.toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">مستوى الثقة</div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">📋 التوصية:</h5>\n                <p className=\"text-sm text-gray-700 dark:text-gray-300\">{analysis.recommendation}</p>\n              </div>\n            </div>\n\n            {/* Fibonacci Levels */}\n            <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                📐 مستويات فيبوناتشي\n              </h4>\n              \n              <div className=\"space-y-2\">\n                {analysis.levels.map((level, index) => (\n                  <div \n                    key={index}\n                    className={`border-2 rounded-lg p-3 transition-all duration-200 ${getLevelColor(level, analysis.currentPrice)}`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"text-lg font-bold\">\n                          {level.percentage}\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">\n                            {level.price.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                          </div>\n                          <div className=\"text-xs\">\n                            {level.type === 'support' ? '🟢 دعم' : '🔴 مقاومة'}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium\">\n                          قوة: {level.strength.toFixed(0)}%\n                        </div>\n                        <div className=\"text-xs\">\n                          المسافة: {(Math.abs(level.price - analysis.currentPrice) / analysis.currentPrice * 100).toFixed(2)}%\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {level === analysis.nearestLevel && (\n                      <div className=\"mt-2 pt-2 border-t border-current\">\n                        <div className=\"text-xs font-medium\">\n                          ⭐ أقرب مستوى فيبوناتشي للسعر الحالي\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Trading Suggestions */}\n            <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                💡 اقتراحات التداول\n              </h4>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-green-600 mb-2\">🎯 نقاط الدخول المحتملة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'support' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-red-600 mb-2\">🛑 مستويات وقف الخسارة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'resistance' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            {/* Final Fibonacci Conclusion */}\n            <div className={`rounded-xl p-6 border-4 ${\n              analysis.recommendation.includes('شراء')\n                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'\n                : analysis.recommendation.includes('بيع')\n                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'\n                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'\n            }`}>\n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                  📐 خلاصة تحليل فيبوناتشي\n                </h3>\n                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${\n                  analysis.recommendation.includes('شراء') ? 'bg-green-600 text-white' :\n                  analysis.recommendation.includes('بيع') ? 'bg-red-600 text-white' :\n                  'bg-yellow-600 text-white'\n                }`}>\n                  {analysis.recommendation.includes('شراء') && '📈 '}\n                  {analysis.recommendation.includes('بيع') && '📉 '}\n                  {!analysis.recommendation.includes('شراء') && !analysis.recommendation.includes('بيع') && '➡️ '}\n                  {analysis.recommendation.includes('شراء قوي') ? 'شراء قوي' :\n                   analysis.recommendation.includes('بيع قوي') ? 'بيع قوي' :\n                   analysis.recommendation.includes('شراء') ? 'شراء' :\n                   analysis.recommendation.includes('بيع') ? 'بيع' : 'انتظار'}\n                </div>\n                <p className=\"text-lg text-gray-600 dark:text-gray-400 mt-2\">\n                  بناءً على تحليل مستويات فيبوناتشي الذهبية والنسب المقدسة\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Fibonacci Setup */}\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                    📐 إعداد فيبوناتشي المقترح\n                  </h4>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">أقرب مستوى</div>\n                        <div className=\"text-lg font-bold text-blue-600\">\n                          {analysis.nearestLevel.percentage}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {analysis.nearestLevel.price.toFixed(5)}\n                        </div>\n                      </div>\n                      <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded p-3 text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">قوة المستوى</div>\n                        <div className=\"text-lg font-bold text-purple-600\">\n                          {analysis.nearestLevel.strength.toFixed(0)}%\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة'}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded p-3\">\n                      <div className=\"text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">مستوى الثقة</div>\n                        <div className=\"text-xl font-bold text-yellow-600\">\n                          {analysis.confidence.toFixed(0)}%\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded p-3\">\n                      <h5 className=\"font-medium text-amber-800 dark:text-amber-200 mb-2\">\n                        🔢 المستويات الذهبية النشطة:\n                      </h5>\n                      <div className=\"grid grid-cols-3 gap-1 text-xs\">\n                        {analysis.levels\n                          .filter(l => l.strength > 70)\n                          .slice(0, 6)\n                          .map((level, i) => (\n                            <div key={i} className=\"text-center bg-white dark:bg-gray-600 rounded p-1\">\n                              <div className=\"font-bold\">{level.percentage}</div>\n                              <div className=\"text-gray-500\">{level.strength.toFixed(0)}%</div>\n                            </div>\n                          ))}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Fibonacci Analysis */}\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                    🌟 تحليل فيبوناتشي المتقدم\n                  </h4>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-green-50 dark:bg-green-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-green-800 dark:text-green-200 mb-2\">\n                        📊 إحصائيات المستويات:\n                      </h5>\n                      <div className=\"text-sm space-y-1\">\n                        <div>إجمالي المستويات: {analysis.levels.length}</div>\n                        <div>مستويات قوية: {analysis.levels.filter(l => l.strength > 80).length}</div>\n                        <div>مستويات دعم: {analysis.levels.filter(l => l.type === 'support').length}</div>\n                        <div>مستويات مقاومة: {analysis.levels.filter(l => l.type === 'resistance').length}</div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-blue-800 dark:text-blue-200 mb-2\">\n                        🎯 تحليل الاتجاه:\n                      </h5>\n                      <div className=\"text-sm\">\n                        الاتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}<br/>\n                        أعلى سعر: {analysis.high.toFixed(5)}<br/>\n                        أقل سعر: {analysis.low.toFixed(5)}<br/>\n                        المدى: {((analysis.high - analysis.low) / analysis.low * 100).toFixed(2)}%\n                      </div>\n                    </div>\n\n                    <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-orange-800 dark:text-orange-200 mb-2\">\n                        💡 نصائح فيبوناتشي:\n                      </h5>\n                      <ul className=\"text-sm space-y-1\">\n                        <li>• ركز على مستويات 61.8% و 78.6%</li>\n                        <li>• انتظر تأكيد الكسر أو الارتداد</li>\n                        <li>• استخدم مستويات متعددة للتأكيد</li>\n                        <li>• راقب التقارب مع مؤشرات أخرى</li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Confidence Meter */}\n              <div className=\"mt-6 bg-white dark:bg-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">مستوى الثقة في تحليل فيبوناتشي:</span>\n                  <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{analysis.confidence.toFixed(0)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div\n                    className={`h-3 rounded-full transition-all duration-500 ${\n                      analysis.confidence > 80 ? 'bg-green-500' :\n                      analysis.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'\n                    }`}\n                    style={{ width: `${analysis.confidence}%` }}\n                  ></div>\n                </div>\n                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                  <span>منخفض</span>\n                  <span>متوسط</span>\n                  <span>عالي</span>\n                </div>\n              </div>\n\n              {/* Key Recommendation */}\n              <div className=\"mt-4 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 rounded-lg p-4\">\n                <h5 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                  🎯 التوصية الرئيسية:\n                </h5>\n                <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n                  {analysis.recommendation}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAwBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IAErE,MAAM,kBAAkB;QAAC;QAAG;QAAO;QAAO;QAAK;QAAO;QAAO;QAAG;QAAO;QAAO;QAAO;KAAM;IAE3F,MAAM,uBAAuB;QAC3B,eAAe;QAEf,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,aAAa;YAC/B,MAAM,aAAa,cAAc;YAEjC,8BAA8B;YAC9B,MAAM,aAAa,YAAY,aAAa;YAC5C,MAAM,OAAO,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAChE,MAAM,MAAM,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAE/D,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,YAAY;YACpD,MAAM,eAAe,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YAEtE,6BAA6B;YAC7B,MAAM,SAA2B,gBAAgB,GAAG,CAAC,CAAA;gBACnD,MAAM,QAAQ,cAAc,YACxB,OAAO,CAAC,OAAO,GAAG,IAAI,QACtB,MAAM,CAAC,OAAO,GAAG,IAAI;gBAEzB,MAAM,sBAAsB,KAAK,GAAG,CAAC,QAAQ;gBAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,MAAM,AAAC,sBAAsB,YAAa;gBAExE,OAAO;oBACL;oBACA;oBACA,YAAY,AAAC,GAA2B,OAAzB,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,IAAG;oBACxC,MAAM,QAAQ,eAAe,eAAe;oBAC5C,UAAU,KAAK,GAAG,CAAC,UAAU;gBAC/B;YACF;YAEA,qBAAqB;YACrB,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,SAAS;gBAC3C,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,OAAO,kBAAkB,kBAAkB,UAAU;YACvD;YAEA,0BAA0B;YAC1B,MAAM,oBAAoB,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG,gBAAgB,YAAY;YACpF,IAAI,iBAAiB;YACrB,IAAI,aAAa;YAEjB,IAAI,oBAAoB,KAAK;gBAC3B,IAAI,aAAa,IAAI,KAAK,aAAa,cAAc,WAAW;oBAC9D,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO,IAAI,aAAa,IAAI,KAAK,gBAAgB,cAAc,WAAW;oBACxE,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO;oBACL,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC;YACF,OAAO,IAAI,oBAAoB,KAAK;gBAClC,iBAAiB,AAAC,iCAAwD,OAAxB,aAAa,UAAU;gBACzE,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC,OAAO;gBACL,iBAAiB;gBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC;YAEA,YAAY;gBACV,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,SAAS;YAAK,UAAU;QAC1B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,gBAAgB,CAAC,OAAuB;QAC5C,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,gBAAgB,eAAe;QAEvE,IAAI,WAAW,KAAK,OAAO,0CAA0C,aAAa;QAClF,IAAI,WAAW,KAAK,OAAO,mDAAmD,QAAQ;QACtF,IAAI,MAAM,IAAI,KAAK,WAAW,OAAO;QACrC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;0CAGL,cAAc,uBAAuB;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;gDAAkB,OAAO;;oDACvB,YAAY;oDAAM;oDAAE;;+CADV;;;;;;;;;;;;;;;;0CAOnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAAgB,OAAO;0DAAK;+CAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;4CAA2C;4CAC1B;4CAAa;;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;oBAO/C,CAAC,eAAe,CAAC,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,0BACf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,YAAY,SAAS,MAAM;;;;;;kEACvD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,SAAS,MAAM;oEAAC;oEAAI;;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;;oEAA2C;oEAC9C,SAAS,SAAS,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;;0DAI7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAEvE,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE/D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE9D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAElC,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAA4C,SAAS,cAAc;;;;;;;;;;;;;;;;;;0CAKpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;gDAEC,WAAW,AAAC,uDAAkG,OAA5C,cAAc,OAAO,SAAS,YAAY;;kEAE5G,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,MAAM,UAAU;;;;;;kFAEnB,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;0FAE7D,6LAAC;gFAAI,WAAU;0FACZ,MAAM,IAAI,KAAK,YAAY,WAAW;;;;;;;;;;;;;;;;;;0EAK7C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EAAsB;4EAC7B,MAAM,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;kFAElC,6LAAC;wEAAI,WAAU;;4EAAU;4EACb,CAAC,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,SAAS,YAAY,IAAI,SAAS,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;oDAKxG,UAAU,SAAS,YAAY,kBAC9B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;;+CA9BpC;;;;;;;;;;;;;;;;0CAyCb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,6LAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ,GAAG,IACjD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,6LAAC;;oEAAW;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAA/C;;;;;;;;;;;;;;;;0DAKjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,6LAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,gBAAgB,EAAE,QAAQ,GAAG,IACpD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,6LAAC;;oEAAW;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAA/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrB,6LAAC;gCAAI,WAAW,AAAC,2BAMhB,OALC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAC7B,gHACA,SAAS,cAAc,CAAC,QAAQ,CAAC,SACjC,oGACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,6LAAC;gDAAI,WAAW,AAAC,sEAIhB,OAHC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,4BAC3C,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,0BAC1C;;oDAEC,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW;oDAC5C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU;oDAC3C,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU;oDACzF,SAAS,cAAc,CAAC,QAAQ,CAAC,cAAc,aAC/C,SAAS,cAAc,CAAC,QAAQ,CAAC,aAAa,YAC9C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,SAC3C,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,QAAQ;;;;;;;0DAErD,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAK/D,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,6LAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,UAAU;;;;;;0FAEnC,6LAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kFAGzC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,6LAAC;gFAAI,WAAU;;oFACZ,SAAS,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;oFAAG;;;;;;;0FAE7C,6LAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,IAAI,KAAK,YAAY,QAAQ;;;;;;;;;;;;;;;;;;0EAK1D,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA2C;;;;;;sFAC1D,6LAAC;4EAAI,WAAU;;gFACZ,SAAS,UAAU,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;0EAKtC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAsD;;;;;;kFAGpE,6LAAC;wEAAI,WAAU;kFACZ,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,6LAAC;gFAAY,WAAU;;kGACrB,6LAAC;wFAAI,WAAU;kGAAa,MAAM,UAAU;;;;;;kGAC5C,6LAAC;wFAAI,WAAU;;4FAAiB,MAAM,QAAQ,CAAC,OAAO,CAAC;4FAAG;;;;;;;;+EAFlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAWtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAsD;;;;;;kFAGpE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAI;oFAAmB,SAAS,MAAM,CAAC,MAAM;;;;;;;0FAC9C,6LAAC;;oFAAI;oFAAe,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IAAI,MAAM;;;;;;;0FACvE,6LAAC;;oFAAI;oFAAc,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;0FAC3E,6LAAC;;oFAAI;oFAAiB,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,MAAM;;;;;;;;;;;;;;;;;;;0EAIrF,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAoD;;;;;;kFAGlE,6LAAC;wEAAI,WAAU;;4EAAU;4EACb,SAAS,SAAS,KAAK,YAAY,YAAY;0FAAU,6LAAC;;;;;4EAAI;4EAC7D,SAAS,IAAI,CAAC,OAAO,CAAC;0FAAG,6LAAC;;;;;4EAAI;4EAC/B,SAAS,GAAG,CAAC,OAAO,CAAC;0FAAG,6LAAC;;;;;4EAAI;4EAC/B,CAAC,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAI7E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAuD;;;;;;kEACvE,6LAAC;wDAAK,WAAU;;4DAAmD,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAEpG,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAW,AAAC,gDAGX,OAFC,SAAS,UAAU,GAAG,KAAK,iBAC3B,SAAS,UAAU,GAAG,KAAK,kBAAkB;oDAE/C,OAAO;wDAAE,OAAO,AAAC,GAAsB,OAApB,SAAS,UAAU,EAAC;oDAAG;;;;;;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,6LAAC;gDAAE,WAAU;0DACV,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;GAhiBwB;KAAA", "debugId": null}}, {"offset": {"line": 6339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ProfessionalOrderBlocks.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface OrderBlock {\n  id: string;\n  type: 'BULLISH' | 'BEARISH';\n  level: number;\n  strength: number;\n  volume: number;\n  timeframe: string;\n  age: number; // hours since formation\n  tested: number; // how many times tested\n  validity: 'FRESH' | 'TESTED' | 'BROKEN';\n  institutionalFlow: 'ACCUMULATION' | 'DISTRIBUTION' | 'NEUTRAL';\n  confluence: string[];\n  probability: number;\n}\n\ninterface SmartMoneyAnalysis {\n  symbol: string;\n  currentPrice: number;\n  orderBlocks: OrderBlock[];\n  marketStructure: 'BULLISH' | 'BEARISH' | 'RANGING';\n  institutionalSentiment: number; // -100 to +100\n  liquidityLevels: {\n    buyLiquidity: number[];\n    sellLiquidity: number[];\n  };\n  recommendation: {\n    action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';\n    confidence: number;\n    reasoning: string[];\n    entryZone: { min: number; max: number };\n    stopLoss: number;\n    targets: number[];\n    riskReward: number;\n  };\n}\n\nexport default function ProfessionalOrderBlocks() {\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [analysis, setAnalysis] = useState<SmartMoneyAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const timeframes = [\n    { key: '15m', name: '15 دقيقة', weight: 1 },\n    { key: '1h', name: '1 ساعة', weight: 2 },\n    { key: '4h', name: '4 ساعات', weight: 3 },\n    { key: '1d', name: '1 يوم', weight: 4 }\n  ];\n\n  const runProfessionalAnalysis = async () => {\n    setIsAnalyzing(true);\n    \n    try {\n      // Simulate professional analysis delay\n      await new Promise(resolve => setTimeout(resolve, 3000));\n      \n      const basePrice = getBasePrice(selectedPair);\n      const volatility = getVolatility(selectedPair);\n      \n      // Generate professional order blocks\n      const orderBlocks: OrderBlock[] = generateOrderBlocks(basePrice, volatility);\n      \n      // Analyze market structure\n      const marketStructure = analyzeMarketStructure(orderBlocks, basePrice);\n      \n      // Calculate institutional sentiment\n      const institutionalSentiment = calculateInstitutionalSentiment(orderBlocks);\n      \n      // Generate liquidity levels\n      const liquidityLevels = generateLiquidityLevels(basePrice, volatility);\n      \n      // Generate professional recommendation\n      const recommendation = generateProfessionalRecommendation(\n        orderBlocks, marketStructure, institutionalSentiment, basePrice\n      );\n      \n      setAnalysis({\n        symbol: selectedPair,\n        currentPrice: basePrice + (Math.random() - 0.5) * volatility * basePrice * 0.01,\n        orderBlocks,\n        marketStructure,\n        institutionalSentiment,\n        liquidityLevels,\n        recommendation\n      });\n      \n      setLastUpdate(new Date());\n      \n    } catch (error) {\n      console.error('Professional Analysis Error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const generateOrderBlocks = (basePrice: number, volatility: number): OrderBlock[] => {\n    const blocks: OrderBlock[] = [];\n    const timeframeWeight = timeframes.find(tf => tf.key === selectedTimeframe)?.weight || 2;\n    \n    // Generate 3-7 order blocks based on timeframe\n    const blockCount = 3 + Math.floor(Math.random() * 5);\n    \n    for (let i = 0; i < blockCount; i++) {\n      const type = Math.random() > 0.5 ? 'BULLISH' : 'BEARISH';\n      const distance = (Math.random() * 0.02 + 0.005) * basePrice; // 0.5% to 2.5% from current price\n      const level = type === 'BULLISH' \n        ? basePrice - distance \n        : basePrice + distance;\n      \n      const age = Math.floor(Math.random() * 48); // 0-48 hours\n      const tested = Math.floor(Math.random() * 4); // 0-3 times tested\n      const volume = 500000 + Math.random() * 2000000;\n      \n      // Calculate strength based on multiple factors\n      let strength = 50;\n      strength += timeframeWeight * 10; // Higher timeframe = stronger\n      strength += Math.max(0, (48 - age) / 48 * 20); // Fresher = stronger\n      strength -= tested * 15; // More tested = weaker\n      strength += (volume / 1000000) * 5; // Higher volume = stronger\n      strength = Math.max(20, Math.min(95, strength));\n      \n      // Determine validity\n      let validity: 'FRESH' | 'TESTED' | 'BROKEN' = 'FRESH';\n      if (tested > 0) validity = 'TESTED';\n      if (tested > 2 && strength < 40) validity = 'BROKEN';\n      \n      // Institutional flow analysis\n      const institutionalFlow = type === 'BULLISH' \n        ? (Math.random() > 0.3 ? 'ACCUMULATION' : 'NEUTRAL')\n        : (Math.random() > 0.3 ? 'DISTRIBUTION' : 'NEUTRAL');\n      \n      // Generate confluence factors\n      const confluence = generateConfluence(level, basePrice, type);\n      \n      // Calculate probability\n      const probability = calculateBlockProbability(strength, validity, confluence.length, timeframeWeight);\n      \n      blocks.push({\n        id: `OB_${i}_${Date.now()}`,\n        type,\n        level,\n        strength,\n        volume,\n        timeframe: selectedTimeframe,\n        age,\n        tested,\n        validity,\n        institutionalFlow,\n        confluence,\n        probability\n      });\n    }\n    \n    return blocks.sort((a, b) => b.probability - a.probability);\n  };\n\n  const generateConfluence = (level: number, basePrice: number, type: string): string[] => {\n    const confluence: string[] = [];\n    \n    // Random confluence factors\n    const factors = [\n      'Fibonacci 61.8%',\n      'Previous Structure',\n      'Volume Profile POC',\n      'Daily Pivot',\n      'Weekly Support/Resistance',\n      'Psychological Level',\n      'Trendline Confluence',\n      'Moving Average',\n      'Institutional Level'\n    ];\n    \n    const confluenceCount = 1 + Math.floor(Math.random() * 4); // 1-4 confluence factors\n    const selectedFactors = factors.sort(() => 0.5 - Math.random()).slice(0, confluenceCount);\n    \n    return selectedFactors;\n  };\n\n  const calculateBlockProbability = (strength: number, validity: string, confluenceCount: number, timeframeWeight: number): number => {\n    let probability = strength;\n    \n    // Validity adjustment\n    if (validity === 'FRESH') probability += 10;\n    else if (validity === 'TESTED') probability -= 5;\n    else probability -= 20; // BROKEN\n    \n    // Confluence adjustment\n    probability += confluenceCount * 5;\n    \n    // Timeframe adjustment\n    probability += timeframeWeight * 3;\n    \n    return Math.max(10, Math.min(95, probability));\n  };\n\n  const analyzeMarketStructure = (blocks: OrderBlock[], currentPrice: number): 'BULLISH' | 'BEARISH' | 'RANGING' => {\n    const bullishBlocks = blocks.filter(b => b.type === 'BULLISH' && b.validity !== 'BROKEN');\n    const bearishBlocks = blocks.filter(b => b.type === 'BEARISH' && b.validity !== 'BROKEN');\n    \n    const bullishStrength = bullishBlocks.reduce((sum, b) => sum + b.strength, 0);\n    const bearishStrength = bearishBlocks.reduce((sum, b) => sum + b.strength, 0);\n    \n    const difference = Math.abs(bullishStrength - bearishStrength);\n    const total = bullishStrength + bearishStrength;\n    \n    if (total === 0) return 'RANGING';\n    \n    const dominancePercentage = difference / total;\n    \n    if (dominancePercentage < 0.2) return 'RANGING';\n    return bullishStrength > bearishStrength ? 'BULLISH' : 'BEARISH';\n  };\n\n  const calculateInstitutionalSentiment = (blocks: OrderBlock[]): number => {\n    let sentiment = 0;\n    let totalWeight = 0;\n    \n    blocks.forEach(block => {\n      const weight = block.strength * (block.validity === 'FRESH' ? 1.5 : block.validity === 'TESTED' ? 1 : 0.5);\n      const blockSentiment = block.type === 'BULLISH' ? 1 : -1;\n      \n      if (block.institutionalFlow === 'ACCUMULATION') {\n        sentiment += blockSentiment * weight * 1.3;\n      } else if (block.institutionalFlow === 'DISTRIBUTION') {\n        sentiment += blockSentiment * weight * 1.3;\n      } else {\n        sentiment += blockSentiment * weight;\n      }\n      \n      totalWeight += weight;\n    });\n    \n    return totalWeight > 0 ? (sentiment / totalWeight) * 100 : 0;\n  };\n\n  const generateLiquidityLevels = (basePrice: number, volatility: number) => {\n    const buyLiquidity: number[] = [];\n    const sellLiquidity: number[] = [];\n    \n    // Generate 3-5 liquidity levels above and below current price\n    for (let i = 0; i < 4; i++) {\n      const distance = (i + 1) * 0.005 * basePrice; // 0.5%, 1%, 1.5%, 2%\n      buyLiquidity.push(basePrice - distance);\n      sellLiquidity.push(basePrice + distance);\n    }\n    \n    return { buyLiquidity, sellLiquidity };\n  };\n\n  const generateProfessionalRecommendation = (\n    blocks: OrderBlock[],\n    marketStructure: string,\n    sentiment: number,\n    currentPrice: number\n  ) => {\n    const strongBlocks = blocks.filter(b => b.strength > 70 && b.validity !== 'BROKEN');\n    const nearestBlock = blocks.reduce((nearest, current) => {\n      const nearestDistance = Math.abs(nearest.level - currentPrice);\n      const currentDistance = Math.abs(current.level - currentPrice);\n      return currentDistance < nearestDistance ? current : nearest;\n    });\n    \n    let action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' = 'HOLD';\n    let confidence = 50;\n    const reasoning: string[] = [];\n    \n    // Determine action based on multiple factors\n    if (marketStructure === 'BULLISH' && sentiment > 30) {\n      action = sentiment > 60 ? 'STRONG_BUY' : 'BUY';\n      confidence = 70 + Math.min(25, sentiment / 4);\n      reasoning.push(`هيكل السوق صاعد مع معنويات مؤسسية إيجابية (${sentiment.toFixed(1)})`);\n    } else if (marketStructure === 'BEARISH' && sentiment < -30) {\n      action = sentiment < -60 ? 'STRONG_SELL' : 'SELL';\n      confidence = 70 + Math.min(25, Math.abs(sentiment) / 4);\n      reasoning.push(`هيكل السوق هابط مع معنويات مؤسسية سلبية (${sentiment.toFixed(1)})`);\n    } else {\n      reasoning.push(`السوق في حالة توازن - انتظار إشارة واضحة`);\n    }\n    \n    // Add order block analysis\n    if (nearestBlock.strength > 80) {\n      reasoning.push(`منطقة طلبات قوية عند ${nearestBlock.level.toFixed(5)} (قوة: ${nearestBlock.strength.toFixed(0)}%)`);\n    }\n    \n    if (strongBlocks.length > 0) {\n      reasoning.push(`${strongBlocks.length} منطقة طلبات قوية تدعم التحليل`);\n    }\n    \n    // Calculate entry zone and levels\n    const entryZone = {\n      min: nearestBlock.level - Math.abs(nearestBlock.level * 0.001),\n      max: nearestBlock.level + Math.abs(nearestBlock.level * 0.001)\n    };\n    \n    const stopLoss = action.includes('BUY') \n      ? nearestBlock.level - Math.abs(nearestBlock.level * 0.015)\n      : nearestBlock.level + Math.abs(nearestBlock.level * 0.015);\n    \n    const targets = action.includes('BUY') \n      ? [\n          nearestBlock.level + Math.abs(nearestBlock.level * 0.02),\n          nearestBlock.level + Math.abs(nearestBlock.level * 0.035),\n          nearestBlock.level + Math.abs(nearestBlock.level * 0.05)\n        ]\n      : [\n          nearestBlock.level - Math.abs(nearestBlock.level * 0.02),\n          nearestBlock.level - Math.abs(nearestBlock.level * 0.035),\n          nearestBlock.level - Math.abs(nearestBlock.level * 0.05)\n        ];\n    \n    const riskReward = Math.abs(targets[0] - nearestBlock.level) / Math.abs(nearestBlock.level - stopLoss);\n    \n    return {\n      action,\n      confidence,\n      reasoning,\n      entryZone,\n      stopLoss,\n      targets,\n      riskReward\n    };\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,\n      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,\n      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,\n      'USOIL': 3.0, 'BTCUSD': 4.0\n    };\n    return volatilities[symbol] || 1.0;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getBlockColor = (block: OrderBlock): string => {\n    if (block.validity === 'BROKEN') return 'bg-gray-100 border-gray-400 text-gray-600';\n    \n    if (block.type === 'BULLISH') {\n      if (block.strength > 80) return 'bg-green-100 border-green-600 text-green-800';\n      if (block.strength > 60) return 'bg-green-50 border-green-400 text-green-700';\n      return 'bg-green-25 border-green-300 text-green-600';\n    } else {\n      if (block.strength > 80) return 'bg-red-100 border-red-600 text-red-800';\n      if (block.strength > 60) return 'bg-red-50 border-red-400 text-red-700';\n      return 'bg-red-25 border-red-300 text-red-600';\n    }\n  };\n\n  const getActionColor = (action: string): string => {\n    switch (action) {\n      case 'STRONG_BUY': return 'bg-green-600 text-white';\n      case 'BUY': return 'bg-green-500 text-white';\n      case 'HOLD': return 'bg-yellow-500 text-white';\n      case 'SELL': return 'bg-red-500 text-white';\n      case 'STRONG_SELL': return 'bg-red-600 text-white';\n      default: return 'bg-gray-500 text-white';\n    }\n  };\n\n  const getActionText = (action: string): string => {\n    switch (action) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'HOLD': return 'انتظار';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🏛️ تحليل Order Blocks الاحترافي\n            <span className=\"mr-3 px-2 py-1 bg-indigo-600 text-white rounded text-sm\">\n              ICT Pro\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n            </div>\n            <button\n              onClick={runProfessionalAnalysis}\n              disabled={isAnalyzing}\n              className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n                isAnalyzing \n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-indigo-600 text-white hover:bg-indigo-700'\n              }`}\n            >\n              {isAnalyzing ? '🔍 جاري التحليل...' : '🚀 تحليل احترافي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              زوج العملة:\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>\n                  {getPairFlag(pair)} {pair}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <select\n              value={selectedTimeframe}\n              onChange={(e) => setSelectedTimeframe(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {timeframes.map(tf => (\n                <option key={tf.key} value={tf.key}>\n                  {tf.name} (وزن: {tf.weight})\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isAnalyzing && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🔍 تحليل مناطق الطلبات المؤسسية...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500 space-y-1\">\n              <div>• فحص هيكل السوق</div>\n              <div>• تحليل تدفق الأموال الذكية</div>\n              <div>• حساب مستويات السيولة</div>\n              <div>• توليد توصيات احترافية</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && !analysis && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🏛️</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              تحليل Order Blocks الاحترافي\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اكتشف مناطق الطلبات المؤسسية وتحليل تدفق الأموال الذكية باستخدام مفاهيم ICT المتقدمة\n            </p>\n            <div className=\"bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-indigo-900 dark:text-indigo-100 mb-2\">\n                💡 ما هي Order Blocks؟\n              </h4>\n              <ul className=\"text-sm text-indigo-800 dark:text-indigo-200 space-y-1 text-right\">\n                <li>• مناطق تراكم الطلبات المؤسسية الكبيرة</li>\n                <li>• مستويات دعم ومقاومة قوية جداً</li>\n                <li>• تحليل تدفق الأموال الذكية</li>\n                <li>• دقة عالية في تحديد نقاط الانعكاس</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isAnalyzing && analysis && (\n          <div className=\"space-y-6\">\n            {/* Market Overview */}\n            <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <span className=\"text-2xl\">{getPairFlag(analysis.symbol)}</span>\n                  <div>\n                    <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {analysis.symbol} - {selectedTimeframe}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      هيكل السوق: {analysis.marketStructure === 'BULLISH' ? '📈 صاعد' : analysis.marketStructure === 'BEARISH' ? '📉 هابط' : '➡️ متذبذب'}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">السعر الحالي</div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div className=\"text-center\">\n                  <div className={`text-lg font-bold ${analysis.institutionalSentiment > 0 ? 'text-green-600' : analysis.institutionalSentiment < 0 ? 'text-red-600' : 'text-yellow-600'}`}>\n                    {analysis.institutionalSentiment.toFixed(1)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">معنويات مؤسسية</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">\n                    {analysis.orderBlocks.length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">مناطق طلبات</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-purple-600\">\n                    {analysis.orderBlocks.filter(b => b.validity === 'FRESH').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">مناطق جديدة</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Professional Recommendation */}\n            <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6 border-2 border-indigo-200\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n                🎯 التوصية الاحترافية\n                <span className={`mr-3 px-3 py-1 rounded-full text-sm font-medium ${getActionColor(analysis.recommendation.action)}`}>\n                  {getActionText(analysis.recommendation.action)}\n                </span>\n              </h4>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h5 className=\"font-medium text-gray-900 dark:text-white mb-3\">📋 تحليل الأسباب:</h5>\n                  <ul className=\"space-y-2\">\n                    {analysis.recommendation.reasoning.map((reason, i) => (\n                      <li key={i} className=\"flex items-start text-sm\">\n                        <span className=\"mr-2 text-indigo-500\">▶</span>\n                        <span className=\"text-gray-700 dark:text-gray-300\">{reason}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                <div>\n                  <h5 className=\"font-medium text-gray-900 dark:text-white mb-3\">💰 مستويات التداول:</h5>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">منطقة الدخول:</span>\n                      <span className=\"font-medium\">\n                        {analysis.recommendation.entryZone.min.toFixed(5)} - {analysis.recommendation.entryZone.max.toFixed(5)}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">وقف الخسارة:</span>\n                      <span className=\"font-medium text-red-600\">\n                        {analysis.recommendation.stopLoss.toFixed(5)}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">الهدف الأول:</span>\n                      <span className=\"font-medium text-green-600\">\n                        {analysis.recommendation.targets[0].toFixed(5)}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">نسبة R/R:</span>\n                      <span className=\"font-medium text-blue-600\">\n                        {analysis.recommendation.riskReward.toFixed(2)}:1\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">مستوى الثقة:</span>\n                      <span className=\"font-medium text-purple-600\">\n                        {analysis.recommendation.confidence.toFixed(0)}%\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Final ICT Trading Conclusion */}\n            <div className={`rounded-xl p-6 border-4 ${\n              analysis.recommendation.action.includes('BUY')\n                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'\n                : analysis.recommendation.action.includes('SELL')\n                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'\n                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'\n            }`}>\n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                  🏛️ خلاصة تحليل ICT\n                </h3>\n                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${getActionColor(analysis.recommendation.action)}`}>\n                  {analysis.recommendation.action.includes('BUY') && '📈 '}\n                  {analysis.recommendation.action.includes('SELL') && '📉 '}\n                  {analysis.recommendation.action === 'انتظار' && '➡️ '}\n                  {getActionText(analysis.recommendation.action)}\n                </div>\n                <p className=\"text-lg text-gray-600 dark:text-gray-400 mt-2\">\n                  بناءً على تحليل Order Blocks المؤسسية ومفاهيم ICT المتقدمة\n                </p>\n              </div>\n\n              {analysis.recommendation.action !== 'انتظار' && (\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  {/* ICT Setup */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                      🎯 إعداد ICT المقترح\n                    </h4>\n\n                    <div className=\"space-y-4\">\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center\">\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">منطقة الدخول</div>\n                          <div className=\"text-lg font-bold text-blue-600\">\n                            {((analysis.recommendation.entryZone.min + analysis.recommendation.entryZone.max) / 2).toFixed(5)}\n                          </div>\n                        </div>\n                        <div className=\"bg-red-50 dark:bg-red-900/20 rounded p-3 text-center\">\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">وقف الخسارة</div>\n                          <div className=\"text-lg font-bold text-red-600\">\n                            {analysis.recommendation.stopLoss.toFixed(5)}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-3 gap-2\">\n                        {analysis.recommendation.targets.map((target, i) => (\n                          <div key={i} className=\"bg-green-50 dark:bg-green-900/20 rounded p-2 text-center\">\n                            <div className=\"text-xs text-gray-600 dark:text-gray-400\">هدف {i + 1}</div>\n                            <div className=\"text-sm font-bold text-green-600\">\n                              {target.toFixed(5)}\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n\n                      <div className=\"bg-indigo-50 dark:bg-indigo-900/20 rounded p-3\">\n                        <div className=\"text-center\">\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400\">نسبة المخاطرة/العائد</div>\n                          <div className=\"text-xl font-bold text-indigo-600\">\n                            {analysis.recommendation.riskReward.toFixed(2)}:1\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* ICT Analysis */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                      🏛️ تحليل ICT المتقدم\n                    </h4>\n\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded p-4\">\n                        <h5 className=\"font-medium text-purple-800 dark:text-purple-200 mb-2\">\n                          📊 Order Blocks النشطة:\n                        </h5>\n                        <div className=\"text-sm space-y-1\">\n                          <div>مناطق جديدة: {analysis.orderBlocks.filter(b => b.validity === 'FRESH').length}</div>\n                          <div>مناطق مختبرة: {analysis.orderBlocks.filter(b => b.validity === 'TESTED').length}</div>\n                          <div>قوة متوسطة: {(analysis.orderBlocks.reduce((sum, b) => sum + b.strength, 0) / analysis.orderBlocks.length).toFixed(0)}%</div>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded p-4\">\n                        <h5 className=\"font-medium text-orange-800 dark:text-orange-200 mb-2\">\n                          💰 تدفق الأموال الذكية:\n                        </h5>\n                        <div className=\"text-sm\">\n                          معنويات مؤسسية: {analysis.institutionalSentiment > 0 ? 'إيجابية' : analysis.institutionalSentiment < 0 ? 'سلبية' : 'محايدة'}\n                          ({analysis.institutionalSentiment.toFixed(1)})\n                        </div>\n                      </div>\n\n                      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-4\">\n                        <h5 className=\"font-medium text-blue-800 dark:text-blue-200 mb-2\">\n                          🎯 مستويات السيولة:\n                        </h5>\n                        <div className=\"text-sm\">\n                          سيولة شراء: {analysis.liquidityLevels.buyLiquidity.length} مستوى<br/>\n                          سيولة بيع: {analysis.liquidityLevels.sellLiquidity.length} مستوى\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Confidence and Best Order Block */}\n              <div className=\"mt-6 grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">مستوى الثقة ICT:</span>\n                    <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{analysis.recommendation.confidence.toFixed(0)}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                    <div\n                      className={`h-3 rounded-full transition-all duration-500 ${\n                        analysis.recommendation.confidence > 80 ? 'bg-green-500' :\n                        analysis.recommendation.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'\n                      }`}\n                      style={{ width: `${analysis.recommendation.confidence}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4\">\n                  <h5 className=\"font-medium text-indigo-900 dark:text-indigo-100 mb-2\">\n                    🏆 أقوى Order Block: {analysis.nearestLevel.percentage}\n                  </h5>\n                  <div className=\"text-sm text-indigo-800 dark:text-indigo-200\">\n                    قوة: {analysis.nearestLevel.strength.toFixed(0)}% |\n                    نوع: {analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة'} |\n                    حالة: {analysis.nearestLevel.validity}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Order Blocks */}\n            <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                🏛️ مناطق الطلبات المؤسسية\n              </h4>\n              \n              <div className=\"space-y-3\">\n                {analysis.orderBlocks.map((block, index) => (\n                  <div \n                    key={block.id}\n                    className={`border-2 rounded-lg p-4 transition-all duration-200 ${getBlockColor(block)}`}\n                  >\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"text-lg font-bold\">\n                          {block.type === 'BULLISH' ? '🟢' : '🔴'} {block.type}\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">\n                            {block.level.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                          </div>\n                          <div className=\"text-xs\">\n                            {block.validity} | {block.timeframe}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium\">\n                          احتمالية: {block.probability.toFixed(0)}%\n                        </div>\n                        <div className=\"text-xs\">\n                          قوة: {block.strength.toFixed(0)}%\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2 text-xs\">\n                      <div>\n                        <span className=\"text-gray-600\">العمر:</span>\n                        <span className=\"font-medium mr-1\">{block.age}س</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">اختبار:</span>\n                        <span className=\"font-medium mr-1\">{block.tested}x</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">الحجم:</span>\n                        <span className=\"font-medium mr-1\">{(block.volume / 1000000).toFixed(1)}M</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600\">التدفق:</span>\n                        <span className=\"font-medium mr-1\">{block.institutionalFlow}</span>\n                      </div>\n                    </div>\n\n                    {block.confluence.length > 0 && (\n                      <div className=\"mt-3 pt-3 border-t border-current\">\n                        <div className=\"text-xs\">\n                          <span className=\"font-medium\">التقارب: </span>\n                          {block.confluence.join(', ')}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Liquidity Levels */}\n            <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                💧 مستويات السيولة\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-green-600 mb-2\">🟢 سيولة الشراء:</h5>\n                  <div className=\"space-y-1 text-sm\">\n                    {analysis.liquidityLevels.buyLiquidity.map((level, i) => (\n                      <div key={i} className=\"flex justify-between\">\n                        <span>مستوى {i + 1}:</span>\n                        <span className=\"font-medium\">{level.toFixed(5)}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-red-600 mb-2\">🔴 سيولة البيع:</h5>\n                  <div className=\"space-y-1 text-sm\">\n                    {analysis.liquidityLevels.sellLiquidity.map((level, i) => (\n                      <div key={i} className=\"flex justify-between\">\n                        <span>مستوى {i + 1}:</span>\n                        <span className=\"font-medium\">{level.toFixed(5)}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAwCe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAO,MAAM;YAAY,QAAQ;QAAE;QAC1C;YAAE,KAAK;YAAM,MAAM;YAAU,QAAQ;QAAE;QACvC;YAAE,KAAK;YAAM,MAAM;YAAW,QAAQ;QAAE;QACxC;YAAE,KAAK;YAAM,MAAM;YAAS,QAAQ;QAAE;KACvC;IAED,MAAM,0BAA0B;QAC9B,eAAe;QAEf,IAAI;YACF,uCAAuC;YACvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,aAAa;YAC/B,MAAM,aAAa,cAAc;YAEjC,qCAAqC;YACrC,MAAM,cAA4B,oBAAoB,WAAW;YAEjE,2BAA2B;YAC3B,MAAM,kBAAkB,uBAAuB,aAAa;YAE5D,oCAAoC;YACpC,MAAM,yBAAyB,gCAAgC;YAE/D,4BAA4B;YAC5B,MAAM,kBAAkB,wBAAwB,WAAW;YAE3D,uCAAuC;YACvC,MAAM,iBAAiB,mCACrB,aAAa,iBAAiB,wBAAwB;YAGxD,YAAY;gBACV,QAAQ;gBACR,cAAc,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa,YAAY;gBAC3E;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,cAAc,IAAI;QAEpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,sBAAsB,CAAC,WAAmB;YAEtB;QADxB,MAAM,SAAuB,EAAE;QAC/B,MAAM,kBAAkB,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,KAAM,GAAG,GAAG,KAAK,gCAAjC,uCAAA,iBAAqD,MAAM,KAAI;QAEvF,+CAA+C;QAC/C,MAAM,aAAa,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAElD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;YAC/C,MAAM,WAAW,CAAC,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,WAAW,kCAAkC;YAC/F,MAAM,QAAQ,SAAS,YACnB,YAAY,WACZ,YAAY;YAEhB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,aAAa;YACzD,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,mBAAmB;YACjE,MAAM,SAAS,SAAS,KAAK,MAAM,KAAK;YAExC,+CAA+C;YAC/C,IAAI,WAAW;YACf,YAAY,kBAAkB,IAAI,8BAA8B;YAChE,YAAY,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,KAAK,qBAAqB;YACpE,YAAY,SAAS,IAAI,uBAAuB;YAChD,YAAY,AAAC,SAAS,UAAW,GAAG,2BAA2B;YAC/D,WAAW,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YAErC,qBAAqB;YACrB,IAAI,WAA0C;YAC9C,IAAI,SAAS,GAAG,WAAW;YAC3B,IAAI,SAAS,KAAK,WAAW,IAAI,WAAW;YAE5C,8BAA8B;YAC9B,MAAM,oBAAoB,SAAS,YAC9B,KAAK,MAAM,KAAK,MAAM,iBAAiB,YACvC,KAAK,MAAM,KAAK,MAAM,iBAAiB;YAE5C,8BAA8B;YAC9B,MAAM,aAAa,mBAAmB,OAAO,WAAW;YAExD,wBAAwB;YACxB,MAAM,cAAc,0BAA0B,UAAU,UAAU,WAAW,MAAM,EAAE;YAErF,OAAO,IAAI,CAAC;gBACV,IAAI,AAAC,MAAU,OAAL,GAAE,KAAc,OAAX,KAAK,GAAG;gBACvB;gBACA;gBACA;gBACA;gBACA,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;QAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;IAC5D;IAEA,MAAM,qBAAqB,CAAC,OAAe,WAAmB;QAC5D,MAAM,aAAuB,EAAE;QAE/B,4BAA4B;QAC5B,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,yBAAyB;QACpF,MAAM,kBAAkB,QAAQ,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG;QAEzE,OAAO;IACT;IAEA,MAAM,4BAA4B,CAAC,UAAkB,UAAkB,iBAAyB;QAC9F,IAAI,cAAc;QAElB,sBAAsB;QACtB,IAAI,aAAa,SAAS,eAAe;aACpC,IAAI,aAAa,UAAU,eAAe;aAC1C,eAAe,IAAI,SAAS;QAEjC,wBAAwB;QACxB,eAAe,kBAAkB;QAEjC,uBAAuB;QACvB,eAAe,kBAAkB;QAEjC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;IACnC;IAEA,MAAM,yBAAyB,CAAC,QAAsB;QACpD,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ,KAAK;QAChF,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ,KAAK;QAEhF,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE;QAC3E,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE;QAE3E,MAAM,aAAa,KAAK,GAAG,CAAC,kBAAkB;QAC9C,MAAM,QAAQ,kBAAkB;QAEhC,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,sBAAsB,aAAa;QAEzC,IAAI,sBAAsB,KAAK,OAAO;QACtC,OAAO,kBAAkB,kBAAkB,YAAY;IACzD;IAEA,MAAM,kCAAkC,CAAC;QACvC,IAAI,YAAY;QAChB,IAAI,cAAc;QAElB,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,SAAS,MAAM,QAAQ,GAAG,CAAC,MAAM,QAAQ,KAAK,UAAU,MAAM,MAAM,QAAQ,KAAK,WAAW,IAAI,GAAG;YACzG,MAAM,iBAAiB,MAAM,IAAI,KAAK,YAAY,IAAI,CAAC;YAEvD,IAAI,MAAM,iBAAiB,KAAK,gBAAgB;gBAC9C,aAAa,iBAAiB,SAAS;YACzC,OAAO,IAAI,MAAM,iBAAiB,KAAK,gBAAgB;gBACrD,aAAa,iBAAiB,SAAS;YACzC,OAAO;gBACL,aAAa,iBAAiB;YAChC;YAEA,eAAe;QACjB;QAEA,OAAO,cAAc,IAAI,AAAC,YAAY,cAAe,MAAM;IAC7D;IAEA,MAAM,0BAA0B,CAAC,WAAmB;QAClD,MAAM,eAAyB,EAAE;QACjC,MAAM,gBAA0B,EAAE;QAElC,8DAA8D;QAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,QAAQ,WAAW,qBAAqB;YACnE,aAAa,IAAI,CAAC,YAAY;YAC9B,cAAc,IAAI,CAAC,YAAY;QACjC;QAEA,OAAO;YAAE;YAAc;QAAc;IACvC;IAEA,MAAM,qCAAqC,CACzC,QACA,iBACA,WACA;QAEA,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,MAAM,EAAE,QAAQ,KAAK;QAC1E,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,SAAS;YAC3C,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;YACjD,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;YACjD,OAAO,kBAAkB,kBAAkB,UAAU;QACvD;QAEA,IAAI,SAAiE;QACrE,IAAI,aAAa;QACjB,MAAM,YAAsB,EAAE;QAE9B,6CAA6C;QAC7C,IAAI,oBAAoB,aAAa,YAAY,IAAI;YACnD,SAAS,YAAY,KAAK,eAAe;YACzC,aAAa,KAAK,KAAK,GAAG,CAAC,IAAI,YAAY;YAC3C,UAAU,IAAI,CAAC,AAAC,8CAAkE,OAArB,UAAU,OAAO,CAAC,IAAG;QACpF,OAAO,IAAI,oBAAoB,aAAa,YAAY,CAAC,IAAI;YAC3D,SAAS,YAAY,CAAC,KAAK,gBAAgB;YAC3C,aAAa,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,aAAa;YACrD,UAAU,IAAI,CAAC,AAAC,4CAAgE,OAArB,UAAU,OAAO,CAAC,IAAG;QAClF,OAAO;YACL,UAAU,IAAI,CAAE;QAClB;QAEA,2BAA2B;QAC3B,IAAI,aAAa,QAAQ,GAAG,IAAI;YAC9B,UAAU,IAAI,CAAC,AAAC,wBAA8D,OAAvC,aAAa,KAAK,CAAC,OAAO,CAAC,IAAG,WAA0C,OAAjC,aAAa,QAAQ,CAAC,OAAO,CAAC,IAAG;QACjH;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,UAAU,IAAI,CAAC,AAAC,GAAsB,OAApB,aAAa,MAAM,EAAC;QACxC;QAEA,kCAAkC;QAClC,MAAM,YAAY;YAChB,KAAK,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;YACxD,KAAK,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;QAC1D;QAEA,MAAM,WAAW,OAAO,QAAQ,CAAC,SAC7B,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG,SACnD,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;QAEvD,MAAM,UAAU,OAAO,QAAQ,CAAC,SAC5B;YACE,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;YACnD,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;YACnD,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;SACpD,GACD;YACE,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;YACnD,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;YACnD,aAAa,KAAK,GAAG,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;SACpD;QAEL,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,aAAa,KAAK,IAAI,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG;QAE7F,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,SAAS;YAAK,UAAU;QAC1B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,QAAQ,KAAK,UAAU,OAAO;QAExC,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,IAAI,MAAM,QAAQ,GAAG,IAAI,OAAO;YAChC,IAAI,MAAM,QAAQ,GAAG,IAAI,OAAO;YAChC,OAAO;QACT,OAAO;YACL,IAAI,MAAM,QAAQ,GAAG,IAAI,OAAO;YAChC,IAAI,MAAM,QAAQ,GAAG,IAAI,OAAO;YAChC,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,WAAW,kBAAkB,CAAC;;;;;;;kDAE5C,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;kDAGL,cAAc,uBAAuB;;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;gDAAkB,OAAO;;oDACvB,YAAY;oDAAM;oDAAE;;+CADV;;;;;;;;;;;;;;;;0CAOnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACpD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAAoB,OAAO,GAAG,GAAG;;oDAC/B,GAAG,IAAI;oDAAC;oDAAQ,GAAG,MAAM;oDAAC;;+CADhB,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;;;;;;;;;;;;;oBAMV,CAAC,eAAe,CAAC,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,0BACf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,YAAY,SAAS,MAAM;;;;;;kEACvD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,SAAS,MAAM;oEAAC;oEAAI;;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;;oEAA2C;oEACzC,SAAS,eAAe,KAAK,YAAY,YAAY,SAAS,eAAe,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;;0DAI7H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAEvE,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,qBAAsJ,OAAlI,SAAS,sBAAsB,GAAG,IAAI,mBAAmB,SAAS,sBAAsB,GAAG,IAAI,iBAAiB;kEAClJ,SAAS,sBAAsB,CAAC,OAAO,CAAC;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,WAAW,CAAC,MAAM;;;;;;kEAE9B,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,MAAM;;;;;;kEAElE,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;0CAMhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA6E;0DAEzF,6LAAC;gDAAK,WAAW,AAAC,mDAAiG,OAA/C,eAAe,SAAS,cAAc,CAAC,MAAM;0DAC9G,cAAc,SAAS,cAAc,CAAC,MAAM;;;;;;;;;;;;kDAIjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,6LAAC;wDAAG,WAAU;kEACX,SAAS,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC9C,6LAAC;gEAAW,WAAU;;kFACpB,6LAAC;wEAAK,WAAU;kFAAuB;;;;;;kFACvC,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;+DAF7C;;;;;;;;;;;;;;;;0DAQf,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;;4EACb,SAAS,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;4EAAG;4EAAI,SAAS,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;0EAGxG,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFACb,SAAS,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAG9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFACb,SAAS,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAGhD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;;4EACb,SAAS,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;;4EACb,SAAS,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3D,6LAAC;gCAAI,WAAW,AAAC,2BAMhB,OALC,SAAS,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,SACpC,gHACA,SAAS,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,UACxC,oGACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,6LAAC;gDAAI,WAAW,AAAC,sEAAoH,OAA/C,eAAe,SAAS,cAAc,CAAC,MAAM;;oDAChI,SAAS,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU;oDAClD,SAAS,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW;oDACnD,SAAS,cAAc,CAAC,MAAM,KAAK,YAAY;oDAC/C,cAAc,SAAS,cAAc,CAAC,MAAM;;;;;;;0DAE/C,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;oCAK9D,SAAS,cAAc,CAAC,MAAM,KAAK,0BAClC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,6LAAC;gFAAI,WAAU;0FACZ,CAAC,CAAC,SAAS,cAAc,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;kFAGnG,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,6LAAC;gFAAI,WAAU;0FACZ,SAAS,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;0EAKhD,6LAAC;gEAAI,WAAU;0EACZ,SAAS,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC5C,6LAAC;wEAAY,WAAU;;0FACrB,6LAAC;gFAAI,WAAU;;oFAA2C;oFAAK,IAAI;;;;;;;0FACnE,6LAAC;gFAAI,WAAU;0FACZ,OAAO,OAAO,CAAC;;;;;;;uEAHV;;;;;;;;;;0EASd,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA2C;;;;;;sFAC1D,6LAAC;4EAAI,WAAU;;gFACZ,SAAS,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAI;oFAAc,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,MAAM;;;;;;;0FAClF,6LAAC;;oFAAI;oFAAe,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;;;;;;;0FACpF,6LAAC;;oFAAI;oFAAa,CAAC,SAAS,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,SAAS,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC;oFAAG;;;;;;;;;;;;;;;;;;;0EAI9H,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,6LAAC;wEAAI,WAAU;;4EAAU;4EACN,SAAS,sBAAsB,GAAG,IAAI,YAAY,SAAS,sBAAsB,GAAG,IAAI,UAAU;4EAAS;4EAC1H,SAAS,sBAAsB,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAIjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAoD;;;;;;kFAGlE,6LAAC;wEAAI,WAAU;;4EAAU;4EACV,SAAS,eAAe,CAAC,YAAY,CAAC,MAAM;4EAAC;0FAAM,6LAAC;;;;;4EAAI;4EACzD,SAAS,eAAe,CAAC,aAAa,CAAC,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAStE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAuD;;;;;;0EACvE,6LAAC;gEAAK,WAAU;;oEAAmD,SAAS,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEnH,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,AAAC,gDAGX,OAFC,SAAS,cAAc,CAAC,UAAU,GAAG,KAAK,iBAC1C,SAAS,cAAc,CAAC,UAAU,GAAG,KAAK,kBAAkB;4DAE9D,OAAO;gEAAE,OAAO,AAAC,GAAqC,OAAnC,SAAS,cAAc,CAAC,UAAU,EAAC;4DAAG;;;;;;;;;;;;;;;;;0DAK/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;4DAAwD;4DAC9C,SAAS,YAAY,CAAC,UAAU;;;;;;;kEAExD,6LAAC;wDAAI,WAAU;;4DAA+C;4DACtD,SAAS,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;4DAAG;4DAC1C,SAAS,YAAY,CAAC,IAAI,KAAK,YAAY,QAAQ;4DAAS;4DAC3D,SAAS,YAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;kDACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChC,6LAAC;gDAEC,WAAW,AAAC,uDAA2E,OAArB,cAAc;;kEAEhF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,MAAM,IAAI,KAAK,YAAY,OAAO;4EAAK;4EAAE,MAAM,IAAI;;;;;;;kFAEtD,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;0FAE7D,6LAAC;gFAAI,WAAU;;oFACZ,MAAM,QAAQ;oFAAC;oFAAI,MAAM,SAAS;;;;;;;;;;;;;;;;;;;0EAKzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EAAsB;4EACxB,MAAM,WAAW,CAAC,OAAO,CAAC;4EAAG;;;;;;;kFAE1C,6LAAC;wEAAI,WAAU;;4EAAU;4EACjB,MAAM,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;kEAKtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EAAoB,MAAM,GAAG;4EAAC;;;;;;;;;;;;;0EAEhD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EAAoB,MAAM,MAAM;4EAAC;;;;;;;;;;;;;0EAEnD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;;4EAAoB,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE1E,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAK,WAAU;kFAAoB,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;oDAI9D,MAAM,UAAU,CAAC,MAAM,GAAG,mBACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAc;;;;;;gEAC7B,MAAM,UAAU,CAAC,IAAI,CAAC;;;;;;;;;;;;;+CAnDxB,MAAM,EAAE;;;;;;;;;;;;;;;;0CA6DrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,6LAAC;wDAAI,WAAU;kEACZ,SAAS,eAAe,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,kBACjD,6LAAC;gEAAY,WAAU;;kFACrB,6LAAC;;4EAAK;4EAAO,IAAI;4EAAE;;;;;;;kFACnB,6LAAC;wEAAK,WAAU;kFAAe,MAAM,OAAO,CAAC;;;;;;;+DAFrC;;;;;;;;;;;;;;;;0DAQhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,6LAAC;wDAAI,WAAU;kEACZ,SAAS,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,kBAClD,6LAAC;gEAAY,WAAU;;kFACrB,6LAAC;;4EAAK;4EAAO,IAAI;4EAAE;;;;;;;kFACnB,6LAAC;wEAAK,WAAU;kFAAe,MAAM,OAAO,CAAC;;;;;;;+DAFrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;GAxzBwB;KAAA", "debugId": null}}, {"offset": {"line": 8322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ProfessionalRecommendationEngine.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ProfessionalSignal {\n  id: string;\n  symbol: string;\n  timeframe: string;\n  action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';\n  confidence: number;\n  accuracy: number;\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  \n  // Professional Analysis Components\n  technicalAnalysis: {\n    score: number;\n    indicators: {\n      rsi: { value: number; signal: string };\n      macd: { value: number; signal: string };\n      ema: { signal: string; alignment: boolean };\n      bollinger: { position: string; squeeze: boolean };\n      stochastic: { value: number; signal: string };\n      adx: { value: number; trend: string };\n    };\n  };\n  \n  fundamentalAnalysis: {\n    score: number;\n    economicEvents: string[];\n    newsImpact: 'HIGH' | 'MEDIUM' | 'LOW';\n    centralBankSentiment: 'HAWKISH' | 'DOVISH' | 'NEUTRAL';\n    economicStrength: number;\n  };\n  \n  sentimentAnalysis: {\n    score: number;\n    retailSentiment: number;\n    institutionalFlow: 'BUYING' | 'SELLING' | 'NEUTRAL';\n    socialMediaBuzz: number;\n    fearGreedIndex: number;\n  };\n  \n  marketStructure: {\n    trend: 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS';\n    strength: number;\n    support: number[];\n    resistance: number[];\n    keyLevels: number[];\n    liquidityZones: number[];\n  };\n  \n  riskAssessment: {\n    volatility: number;\n    correlation: number;\n    drawdownRisk: number;\n    positionSize: number;\n    maxRisk: number;\n  };\n  \n  reasoning: string[];\n  timestamp: number;\n  validUntil: number;\n}\n\nexport default function ProfessionalRecommendationEngine() {\n  const [signals, setSignals] = useState<ProfessionalSignal[]>([]);\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [analysisDepth, setAnalysisDepth] = useState<'BASIC' | 'ADVANCED' | 'PROFESSIONAL'>('PROFESSIONAL');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [lastGeneration, setLastGeneration] = useState<Date>(new Date());\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const generateProfessionalSignals = async () => {\n    setIsGenerating(true);\n    \n    try {\n      // Simulate professional analysis time\n      const analysisTime = selectedPairs.length * (analysisDepth === 'PROFESSIONAL' ? 3000 : analysisDepth === 'ADVANCED' ? 2000 : 1000);\n      await new Promise(resolve => setTimeout(resolve, Math.min(analysisTime, 8000)));\n      \n      const newSignals: ProfessionalSignal[] = [];\n      \n      for (const symbol of selectedPairs) {\n        const signal = await generateProfessionalSignal(symbol);\n        newSignals.push(signal);\n      }\n      \n      // Sort by confidence and accuracy\n      newSignals.sort((a, b) => (b.confidence * b.accuracy) - (a.confidence * a.accuracy));\n      \n      setSignals(newSignals);\n      setLastGeneration(new Date());\n      \n    } catch (error) {\n      console.error('Professional Signal Generation Error:', error);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const generateProfessionalSignal = async (symbol: string): Promise<ProfessionalSignal> => {\n    const basePrice = getBasePrice(symbol);\n    const volatility = getVolatility(symbol);\n    \n    // Technical Analysis\n    const technicalAnalysis = generateTechnicalAnalysis(basePrice, volatility);\n    \n    // Fundamental Analysis\n    const fundamentalAnalysis = generateFundamentalAnalysis(symbol);\n    \n    // Sentiment Analysis\n    const sentimentAnalysis = generateSentimentAnalysis();\n    \n    // Market Structure\n    const marketStructure = generateMarketStructure(basePrice, volatility);\n    \n    // Risk Assessment\n    const riskAssessment = generateRiskAssessment(symbol, volatility);\n    \n    // Calculate overall scores\n    const technicalWeight = analysisDepth === 'PROFESSIONAL' ? 0.3 : 0.5;\n    const fundamentalWeight = analysisDepth === 'PROFESSIONAL' ? 0.25 : 0.2;\n    const sentimentWeight = analysisDepth === 'PROFESSIONAL' ? 0.25 : 0.2;\n    const structureWeight = analysisDepth === 'PROFESSIONAL' ? 0.2 : 0.1;\n    \n    const overallScore = (\n      technicalAnalysis.score * technicalWeight +\n      fundamentalAnalysis.score * fundamentalWeight +\n      sentimentAnalysis.score * sentimentWeight +\n      (marketStructure.strength / 100 * 100) * structureWeight\n    );\n    \n    // Determine action\n    let action: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' = 'HOLD';\n    if (overallScore > 80) action = 'STRONG_BUY';\n    else if (overallScore > 65) action = 'BUY';\n    else if (overallScore < 20) action = 'STRONG_SELL';\n    else if (overallScore < 35) action = 'SELL';\n    \n    // Calculate confidence and accuracy\n    const scoreConsistency = calculateScoreConsistency([\n      technicalAnalysis.score,\n      fundamentalAnalysis.score,\n      sentimentAnalysis.score,\n      marketStructure.strength\n    ]);\n    \n    const confidence = Math.min(95, 60 + scoreConsistency * 0.35);\n    const accuracy = Math.min(98, 75 + (analysisDepth === 'PROFESSIONAL' ? 15 : analysisDepth === 'ADVANCED' ? 10 : 5) + scoreConsistency * 0.1);\n    \n    // Calculate trading levels\n    const levels = calculateTradingLevels(basePrice, action, riskAssessment, marketStructure);\n    \n    // Generate reasoning\n    const reasoning = generateProfessionalReasoning(\n      symbol, action, technicalAnalysis, fundamentalAnalysis, sentimentAnalysis, marketStructure\n    );\n    \n    return {\n      id: `PROF_${symbol}_${Date.now()}`,\n      symbol,\n      timeframe: '1h', // Default timeframe\n      action,\n      confidence,\n      accuracy,\n      entry: levels.entry,\n      stopLoss: levels.stopLoss,\n      takeProfit1: levels.takeProfit1,\n      takeProfit2: levels.takeProfit2,\n      takeProfit3: levels.takeProfit3,\n      riskReward: levels.riskReward,\n      technicalAnalysis,\n      fundamentalAnalysis,\n      sentimentAnalysis,\n      marketStructure,\n      riskAssessment,\n      reasoning,\n      timestamp: Date.now(),\n      validUntil: Date.now() + (4 * 60 * 60 * 1000) // Valid for 4 hours\n    };\n  };\n\n  const generateTechnicalAnalysis = (basePrice: number, volatility: number) => {\n    const rsi = 30 + Math.random() * 40;\n    const macd = (Math.random() - 0.5) * 0.02;\n    const emaAlignment = Math.random() > 0.4;\n    const bollingerPosition = Math.random() > 0.5 ? 'UPPER' : Math.random() > 0.5 ? 'LOWER' : 'MIDDLE';\n    const stochastic = Math.random() * 100;\n    const adx = 20 + Math.random() * 60;\n    \n    let score = 50;\n    \n    // RSI scoring\n    if (rsi < 30) score += 20;\n    else if (rsi > 70) score -= 20;\n    else if (rsi > 45 && rsi < 55) score += 5;\n    \n    // MACD scoring\n    if (macd > 0) score += 15;\n    else score -= 15;\n    \n    // EMA alignment scoring\n    if (emaAlignment) score += 15;\n    else score -= 10;\n    \n    // Bollinger Bands scoring\n    if (bollingerPosition === 'LOWER') score += 10;\n    else if (bollingerPosition === 'UPPER') score -= 10;\n    \n    // ADX scoring\n    if (adx > 25) score += 10;\n    \n    score = Math.max(0, Math.min(100, score));\n    \n    return {\n      score,\n      indicators: {\n        rsi: {\n          value: rsi,\n          signal: rsi < 30 ? 'OVERSOLD' : rsi > 70 ? 'OVERBOUGHT' : 'NEUTRAL'\n        },\n        macd: {\n          value: macd,\n          signal: macd > 0 ? 'BULLISH' : 'BEARISH'\n        },\n        ema: {\n          signal: emaAlignment ? 'BULLISH' : 'BEARISH',\n          alignment: emaAlignment\n        },\n        bollinger: {\n          position: bollingerPosition,\n          squeeze: Math.random() > 0.7\n        },\n        stochastic: {\n          value: stochastic,\n          signal: stochastic < 20 ? 'OVERSOLD' : stochastic > 80 ? 'OVERBOUGHT' : 'NEUTRAL'\n        },\n        adx: {\n          value: adx,\n          trend: adx > 25 ? 'STRONG' : 'WEAK'\n        }\n      }\n    };\n  };\n\n  const generateFundamentalAnalysis = (symbol: string) => {\n    const economicEvents = [\n      'NFP Release', 'CPI Data', 'GDP Growth', 'Interest Rate Decision',\n      'Employment Data', 'Retail Sales', 'Manufacturing PMI', 'Consumer Confidence'\n    ];\n    \n    const selectedEvents = economicEvents.sort(() => 0.5 - Math.random()).slice(0, 2 + Math.floor(Math.random() * 3));\n    const newsImpact = Math.random() > 0.6 ? 'HIGH' : Math.random() > 0.3 ? 'MEDIUM' : 'LOW';\n    const centralBankSentiment = Math.random() > 0.6 ? 'HAWKISH' : Math.random() > 0.3 ? 'DOVISH' : 'NEUTRAL';\n    const economicStrength = 40 + Math.random() * 40;\n    \n    let score = 50;\n    \n    if (newsImpact === 'HIGH') score += Math.random() > 0.5 ? 20 : -20;\n    else if (newsImpact === 'MEDIUM') score += Math.random() > 0.5 ? 10 : -10;\n    \n    if (centralBankSentiment === 'HAWKISH') score += 15;\n    else if (centralBankSentiment === 'DOVISH') score -= 15;\n    \n    score += (economicStrength - 50) * 0.5;\n    score = Math.max(0, Math.min(100, score));\n    \n    return {\n      score,\n      economicEvents: selectedEvents,\n      newsImpact,\n      centralBankSentiment,\n      economicStrength\n    };\n  };\n\n  const generateSentimentAnalysis = () => {\n    const retailSentiment = Math.random() * 100;\n    const institutionalFlow = Math.random() > 0.6 ? 'BUYING' : Math.random() > 0.3 ? 'SELLING' : 'NEUTRAL';\n    const socialMediaBuzz = Math.random() * 100;\n    const fearGreedIndex = Math.random() * 100;\n    \n    let score = 50;\n    \n    if (retailSentiment > 70) score -= 10; // Contrarian\n    else if (retailSentiment < 30) score += 10;\n    \n    if (institutionalFlow === 'BUYING') score += 20;\n    else if (institutionalFlow === 'SELLING') score -= 20;\n    \n    if (socialMediaBuzz > 70) score += 10;\n    else if (socialMediaBuzz < 30) score -= 5;\n    \n    if (fearGreedIndex > 70) score -= 15; // Extreme greed\n    else if (fearGreedIndex < 30) score += 15; // Extreme fear\n    \n    score = Math.max(0, Math.min(100, score));\n    \n    return {\n      score,\n      retailSentiment,\n      institutionalFlow,\n      socialMediaBuzz,\n      fearGreedIndex\n    };\n  };\n\n  const generateMarketStructure = (basePrice: number, volatility: number) => {\n    const trend = Math.random() > 0.6 ? 'UPTREND' : Math.random() > 0.3 ? 'DOWNTREND' : 'SIDEWAYS';\n    const strength = 40 + Math.random() * 50;\n    \n    const support = Array.from({ length: 3 }, (_, i) => \n      basePrice - (i + 1) * basePrice * volatility * 0.01\n    );\n    \n    const resistance = Array.from({ length: 3 }, (_, i) => \n      basePrice + (i + 1) * basePrice * volatility * 0.01\n    );\n    \n    const keyLevels = [\n      ...support.slice(0, 2),\n      ...resistance.slice(0, 2)\n    ].sort((a, b) => Math.abs(a - basePrice) - Math.abs(b - basePrice));\n    \n    const liquidityZones = Array.from({ length: 4 }, () => \n      basePrice + (Math.random() - 0.5) * basePrice * volatility * 0.02\n    );\n    \n    return {\n      trend,\n      strength,\n      support,\n      resistance,\n      keyLevels,\n      liquidityZones\n    };\n  };\n\n  const generateRiskAssessment = (symbol: string, volatility: number) => {\n    const correlation = Math.random() * 0.8; // 0-80% correlation\n    const drawdownRisk = volatility * 100 * (0.5 + Math.random() * 0.5); // 0.5-1x volatility\n    const positionSize = Math.max(0.5, 3 - volatility * 100); // Inverse relationship with volatility\n    const maxRisk = 2; // 2% max risk per trade\n    \n    return {\n      volatility: volatility * 100,\n      correlation,\n      drawdownRisk,\n      positionSize,\n      maxRisk\n    };\n  };\n\n  const calculateScoreConsistency = (scores: number[]): number => {\n    const avg = scores.reduce((a, b) => a + b, 0) / scores.length;\n    const variance = scores.reduce((acc, score) => acc + Math.pow(score - avg, 2), 0) / scores.length;\n    const standardDeviation = Math.sqrt(variance);\n    \n    return Math.max(0, 100 - standardDeviation * 2);\n  };\n\n  const calculateTradingLevels = (basePrice: number, action: string, risk: any, structure: any) => {\n    const entry = basePrice + (Math.random() - 0.5) * basePrice * 0.002;\n    const isBuy = action.includes('BUY');\n    \n    const riskDistance = basePrice * risk.maxRisk / 100;\n    const stopLoss = isBuy ? entry - riskDistance : entry + riskDistance;\n    \n    const takeProfit1 = isBuy ? entry + riskDistance * 1.5 : entry - riskDistance * 1.5;\n    const takeProfit2 = isBuy ? entry + riskDistance * 2.5 : entry - riskDistance * 2.5;\n    const takeProfit3 = isBuy ? entry + riskDistance * 4 : entry - riskDistance * 4;\n    \n    const riskReward = Math.abs(takeProfit1 - entry) / Math.abs(entry - stopLoss);\n    \n    return {\n      entry,\n      stopLoss,\n      takeProfit1,\n      takeProfit2,\n      takeProfit3,\n      riskReward\n    };\n  };\n\n  const generateProfessionalReasoning = (\n    symbol: string,\n    action: string,\n    technical: any,\n    fundamental: any,\n    sentiment: any,\n    structure: any\n  ): string[] => {\n    const reasoning: string[] = [];\n    \n    reasoning.push(`🔍 تحليل احترافي شامل لـ ${symbol} باستخدام ${analysisDepth} methodology`);\n    \n    // Technical reasoning\n    if (technical.score > 70) {\n      reasoning.push(`📈 التحليل الفني قوي (${technical.score.toFixed(0)}/100) - المؤشرات تدعم ${action}`);\n    } else if (technical.score < 40) {\n      reasoning.push(`📉 التحليل الفني ضعيف (${technical.score.toFixed(0)}/100) - إشارات متضاربة`);\n    }\n    \n    // Fundamental reasoning\n    if (fundamental.score > 70) {\n      reasoning.push(`🏛️ الأساسيات إيجابية (${fundamental.score.toFixed(0)}/100) - ${fundamental.centralBankSentiment} central bank stance`);\n    } else if (fundamental.score < 40) {\n      reasoning.push(`⚠️ الأساسيات سلبية (${fundamental.score.toFixed(0)}/100) - مخاطر اقتصادية`);\n    }\n    \n    // Sentiment reasoning\n    if (sentiment.institutionalFlow === 'BUYING') {\n      reasoning.push(`💰 تدفق مؤسسي إيجابي - الأموال الذكية تشتري`);\n    } else if (sentiment.institutionalFlow === 'SELLING') {\n      reasoning.push(`💸 تدفق مؤسسي سلبي - الأموال الذكية تبيع`);\n    }\n    \n    // Structure reasoning\n    reasoning.push(`🏗️ هيكل السوق: ${structure.trend} بقوة ${structure.strength.toFixed(0)}%`);\n    \n    if (action !== 'HOLD') {\n      reasoning.push(`🎯 توصية ${action} مع ثقة عالية ونسبة مخاطرة محسوبة`);\n    }\n    \n    return reasoning;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.008, 'GBPUSD': 0.012, 'USDJPY': 0.010, 'USDCHF': 0.007,\n      'AUDUSD': 0.015, 'USDCAD': 0.010, 'NZDUSD': 0.018, 'EURGBP': 0.006,\n      'EURJPY': 0.013, 'GBPJPY': 0.018, 'XAUUSD': 0.020, 'XAGUSD': 0.025,\n      'USOIL': 0.030, 'BTCUSD': 0.040\n    };\n    return volatilities[symbol] || 0.015;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getActionColor = (action: string): string => {\n    switch (action) {\n      case 'STRONG_BUY': return 'bg-green-600 text-white';\n      case 'BUY': return 'bg-green-500 text-white';\n      case 'HOLD': return 'bg-yellow-500 text-white';\n      case 'SELL': return 'bg-red-500 text-white';\n      case 'STRONG_SELL': return 'bg-red-600 text-white';\n      default: return 'bg-gray-500 text-white';\n    }\n  };\n\n  const getActionText = (action: string): string => {\n    switch (action) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'HOLD': return 'انتظار';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🎯 محرك التوصيات الاحترافي\n            <span className=\"mr-3 px-2 py-1 bg-emerald-600 text-white rounded text-sm\">\n              Professional Grade\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              آخر تحديث: {lastGeneration.toLocaleTimeString('ar-SA')}\n            </div>\n            <button\n              onClick={generateProfessionalSignals}\n              disabled={isGenerating}\n              className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n                isGenerating \n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-emerald-600 text-white hover:bg-emerald-700'\n              }`}\n            >\n              {isGenerating ? '🔍 جاري التحليل...' : '🚀 توليد توصيات احترافية'}\n            </button>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات المختارة ({selectedPairs.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-2 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية (4)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع (3)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(forexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل ({forexPairs.length})\n              </button>\n            </div>\n\n            <div className=\"max-h-20 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              <div className=\"grid grid-cols-4 gap-1\">\n                {forexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عمق التحليل:\n            </label>\n            <select\n              value={analysisDepth}\n              onChange={(e) => setAnalysisDepth(e.target.value as any)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white mb-2\"\n            >\n              <option value=\"BASIC\">أساسي - تحليل سريع</option>\n              <option value=\"ADVANCED\">متقدم - تحليل شامل</option>\n              <option value=\"PROFESSIONAL\">احترافي - تحليل متكامل</option>\n            </select>\n            <div className=\"text-xs text-gray-500\">\n              {analysisDepth === 'PROFESSIONAL' && 'دقة 95%+ | تحليل فني + أساسي + مشاعر + هيكل'}\n              {analysisDepth === 'ADVANCED' && 'دقة 90%+ | تحليل فني + أساسي + مشاعر'}\n              {analysisDepth === 'BASIC' && 'دقة 85%+ | تحليل فني أساسي'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isGenerating && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🧠 محرك التوصيات الاحترافي يعمل...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500 space-y-1\">\n              <div>• تحليل فني متقدم لـ {selectedPairs.length} أزواج</div>\n              <div>• تحليل أساسي شامل</div>\n              <div>• تحليل مشاعر السوق</div>\n              <div>• تقييم هيكل السوق</div>\n              <div>• حساب المخاطر والعوائد</div>\n              <div>• توليد توصيات احترافية</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Signals Yet */}\n        {!isGenerating && signals.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🎯</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              محرك التوصيات الاحترافي\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              احصل على توصيات تداول احترافية مدعومة بتحليل شامل متعدد الأبعاد\n            </p>\n            <div className=\"bg-emerald-50 dark:bg-emerald-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-emerald-900 dark:text-emerald-100 mb-2\">\n                🏆 ميزات التحليل الاحترافي:\n              </h4>\n              <ul className=\"text-sm text-emerald-800 dark:text-emerald-200 space-y-1 text-right\">\n                <li>• تحليل فني متقدم مع 6+ مؤشرات</li>\n                <li>• تحليل أساسي شامل للأحداث الاقتصادية</li>\n                <li>• تحليل مشاعر السوق والتدفق المؤسسي</li>\n                <li>• تحليل هيكل السوق ومستويات السيولة</li>\n                <li>• تقييم المخاطر وحساب حجم المركز</li>\n                <li>• دقة تصل إلى 95%+ مع التحليل الاحترافي</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Professional Signals */}\n        {!isGenerating && signals.length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Summary Stats */}\n            <div className=\"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص التوصيات الاحترافية\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {signals.filter(s => s.action.includes('BUY')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات شراء</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {signals.filter(s => s.action.includes('SELL')).length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات بيع</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-emerald-600\">\n                    {(signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length).toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">متوسط الثقة</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {(signals.reduce((sum, s) => sum + s.accuracy, 0) / signals.length).toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">متوسط الدقة</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Overall Trading Conclusion */}\n            {signals.length > 0 && (\n              <div className={`rounded-xl p-6 border-4 mb-6 ${\n                signals.filter(s => s.action.includes('BUY')).length > signals.filter(s => s.action.includes('SELL')).length\n                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'\n                  : signals.filter(s => s.action.includes('SELL')).length > signals.filter(s => s.action.includes('BUY')).length\n                  ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'\n                  : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'\n              }`}>\n                <div className=\"text-center mb-6\">\n                  <h3 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                    🎯 الخلاصة النهائية للمحفظة\n                  </h3>\n                  {(() => {\n                    const buySignals = signals.filter(s => s.action.includes('BUY')).length;\n                    const sellSignals = signals.filter(s => s.action.includes('SELL')).length;\n                    const neutralSignals = signals.filter(s => s.action === 'HOLD').length;\n\n                    let overallSignal = 'NEUTRAL';\n                    let signalIcon = '➡️';\n\n                    if (buySignals > sellSignals) {\n                      overallSignal = buySignals > sellSignals * 2 ? 'STRONG_BUY' : 'BUY';\n                      signalIcon = '📈';\n                    } else if (sellSignals > buySignals) {\n                      overallSignal = sellSignals > buySignals * 2 ? 'STRONG_SELL' : 'SELL';\n                      signalIcon = '📉';\n                    }\n\n                    const avgConfidence = signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length;\n                    const avgAccuracy = signals.reduce((sum, s) => sum + s.accuracy, 0) / signals.length;\n\n                    return (\n                      <>\n                        <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${\n                          overallSignal.includes('BUY') ? 'bg-green-600 text-white' :\n                          overallSignal.includes('SELL') ? 'bg-red-600 text-white' :\n                          'bg-yellow-600 text-white'\n                        }`}>\n                          {signalIcon} {\n                            overallSignal === 'STRONG_BUY' ? 'شراء قوي للمحفظة' :\n                            overallSignal === 'BUY' ? 'شراء للمحفظة' :\n                            overallSignal === 'STRONG_SELL' ? 'بيع قوي للمحفظة' :\n                            overallSignal === 'SELL' ? 'بيع للمحفظة' :\n                            'محفظة متوازنة'\n                          }\n                        </div>\n                        <p className=\"text-lg text-gray-600 dark:text-gray-400 mt-2\">\n                          متوسط الثقة: {avgConfidence.toFixed(0)}% | متوسط الدقة: {avgAccuracy.toFixed(0)}%\n                        </p>\n                      </>\n                    );\n                  })()}\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                  {/* Portfolio Summary */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <h4 className=\"font-bold text-gray-900 dark:text-white mb-3\">📊 ملخص المحفظة</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span>إشارات شراء:</span>\n                        <span className=\"font-bold text-green-600\">{signals.filter(s => s.action.includes('BUY')).length}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>إشارات بيع:</span>\n                        <span className=\"font-bold text-red-600\">{signals.filter(s => s.action.includes('SELL')).length}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>إشارات انتظار:</span>\n                        <span className=\"font-bold text-yellow-600\">{signals.filter(s => s.action === 'HOLD').length}</span>\n                      </div>\n                      <div className=\"flex justify-between border-t pt-2\">\n                        <span>إجمالي الأزواج:</span>\n                        <span className=\"font-bold\">{signals.length}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Best Opportunities */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <h4 className=\"font-bold text-gray-900 dark:text-white mb-3\">🏆 أفضل الفرص</h4>\n                    <div className=\"space-y-2\">\n                      {signals\n                        .sort((a, b) => (b.confidence * b.accuracy) - (a.confidence * a.accuracy))\n                        .slice(0, 3)\n                        .map((signal, i) => (\n                          <div key={i} className=\"flex items-center justify-between text-sm\">\n                            <span className=\"font-medium\">{signal.symbol}</span>\n                            <div className=\"flex items-center space-x-2 space-x-reverse\">\n                              <span className={`px-2 py-1 rounded text-xs ${\n                                signal.action.includes('BUY') ? 'bg-green-100 text-green-800' :\n                                signal.action.includes('SELL') ? 'bg-red-100 text-red-800' :\n                                'bg-yellow-100 text-yellow-800'\n                              }`}>\n                                {signal.action.includes('BUY') ? 'شراء' : signal.action.includes('SELL') ? 'بيع' : 'انتظار'}\n                              </span>\n                              <span className=\"text-xs text-gray-500\">\n                                {(signal.confidence * signal.accuracy / 100).toFixed(0)}%\n                              </span>\n                            </div>\n                          </div>\n                        ))}\n                    </div>\n                  </div>\n\n                  {/* Risk Assessment */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <h4 className=\"font-bold text-gray-900 dark:text-white mb-3\">⚠️ تقييم المخاطر</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      {(() => {\n                        const avgRiskReward = signals.reduce((sum, s) => sum + s.riskReward, 0) / signals.length;\n                        const highRiskPairs = signals.filter(s => s.riskReward < 1.5).length;\n                        const lowRiskPairs = signals.filter(s => s.riskReward > 2.5).length;\n\n                        return (\n                          <>\n                            <div className=\"flex justify-between\">\n                              <span>متوسط R/R:</span>\n                              <span className=\"font-bold text-blue-600\">{avgRiskReward.toFixed(2)}:1</span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>مخاطر عالية:</span>\n                              <span className=\"font-bold text-red-600\">{highRiskPairs}</span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>مخاطر منخفضة:</span>\n                              <span className=\"font-bold text-green-600\">{lowRiskPairs}</span>\n                            </div>\n                            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded p-2 mt-2\">\n                              <div className=\"text-xs text-yellow-800 dark:text-yellow-200\">\n                                💡 ركز على الأزواج ذات R/R أعلى من 2:1\n                              </div>\n                            </div>\n                          </>\n                        );\n                      })()}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Professional Signals Grid */}\n            <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n              {signals.map((signal) => (\n                <div key={signal.id} className=\"bg-white dark:bg-gray-700 rounded-xl p-6 border-2 border-emerald-200 shadow-lg\">\n                  {/* Signal Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <span className=\"text-2xl\">{getPairFlag(signal.symbol)}</span>\n                      <div>\n                        <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                          {signal.symbol}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {analysisDepth} Analysis\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className={`px-3 py-1 rounded-full text-sm font-medium ${getActionColor(signal.action)}`}>\n                        {getActionText(signal.action)}\n                      </div>\n                      <div className=\"text-xs text-gray-500 mt-1\">\n                        ثقة: {signal.confidence.toFixed(0)}% | دقة: {signal.accuracy.toFixed(0)}%\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Analysis Scores */}\n                  <div className=\"grid grid-cols-4 gap-2 mb-4\">\n                    <div className=\"text-center bg-blue-50 dark:bg-blue-900/20 rounded p-2\">\n                      <div className=\"text-lg font-bold text-blue-600\">{signal.technicalAnalysis.score.toFixed(0)}</div>\n                      <div className=\"text-xs text-gray-600\">فني</div>\n                    </div>\n                    <div className=\"text-center bg-green-50 dark:bg-green-900/20 rounded p-2\">\n                      <div className=\"text-lg font-bold text-green-600\">{signal.fundamentalAnalysis.score.toFixed(0)}</div>\n                      <div className=\"text-xs text-gray-600\">أساسي</div>\n                    </div>\n                    <div className=\"text-center bg-purple-50 dark:bg-purple-900/20 rounded p-2\">\n                      <div className=\"text-lg font-bold text-purple-600\">{signal.sentimentAnalysis.score.toFixed(0)}</div>\n                      <div className=\"text-xs text-gray-600\">مشاعر</div>\n                    </div>\n                    <div className=\"text-center bg-orange-50 dark:bg-orange-900/20 rounded p-2\">\n                      <div className=\"text-lg font-bold text-orange-600\">{signal.marketStructure.strength.toFixed(0)}</div>\n                      <div className=\"text-xs text-gray-600\">هيكل</div>\n                    </div>\n                  </div>\n\n                  {/* Trading Levels */}\n                  <div className=\"bg-gray-50 dark:bg-gray-600 rounded p-3 mb-4\">\n                    <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">💰 مستويات التداول:</h5>\n                    <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span>الدخول:</span>\n                        <span className=\"font-medium\">{signal.entry.toFixed(5)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>وقف الخسارة:</span>\n                        <span className=\"font-medium text-red-600\">{signal.stopLoss.toFixed(5)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>هدف 1:</span>\n                        <span className=\"font-medium text-green-600\">{signal.takeProfit1.toFixed(5)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>R/R:</span>\n                        <span className=\"font-medium text-blue-600\">{signal.riskReward.toFixed(2)}:1</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Professional Reasoning */}\n                  <div className=\"bg-emerald-50 dark:bg-emerald-900/20 rounded p-3\">\n                    <h5 className=\"font-medium text-emerald-900 dark:text-emerald-100 mb-2\">🧠 التحليل الاحترافي:</h5>\n                    <ul className=\"text-xs text-emerald-800 dark:text-emerald-200 space-y-1\">\n                      {signal.reasoning.slice(0, 4).map((reason, i) => (\n                        <li key={i} className=\"flex items-start\">\n                          <span className=\"mr-1 text-emerald-500\">▶</span>\n                          <span>{reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqEe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyC;IAC1F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAE/D,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,8BAA8B;QAClC,gBAAgB;QAEhB,IAAI;YACF,sCAAsC;YACtC,MAAM,eAAe,cAAc,MAAM,GAAG,CAAC,kBAAkB,iBAAiB,OAAO,kBAAkB,aAAa,OAAO,IAAI;YACjI,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,cAAc;YAExE,MAAM,aAAmC,EAAE;YAE3C,KAAK,MAAM,UAAU,cAAe;gBAClC,MAAM,SAAS,MAAM,2BAA2B;gBAChD,WAAW,IAAI,CAAC;YAClB;YAEA,kCAAkC;YAClC,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,EAAE,UAAU,GAAG,EAAE,QAAQ,GAAK,EAAE,UAAU,GAAG,EAAE,QAAQ;YAElF,WAAW;YACX,kBAAkB,IAAI;QAExB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,6BAA6B,OAAO;QACxC,MAAM,YAAY,aAAa;QAC/B,MAAM,aAAa,cAAc;QAEjC,qBAAqB;QACrB,MAAM,oBAAoB,0BAA0B,WAAW;QAE/D,uBAAuB;QACvB,MAAM,sBAAsB,4BAA4B;QAExD,qBAAqB;QACrB,MAAM,oBAAoB;QAE1B,mBAAmB;QACnB,MAAM,kBAAkB,wBAAwB,WAAW;QAE3D,kBAAkB;QAClB,MAAM,iBAAiB,uBAAuB,QAAQ;QAEtD,2BAA2B;QAC3B,MAAM,kBAAkB,kBAAkB,iBAAiB,MAAM;QACjE,MAAM,oBAAoB,kBAAkB,iBAAiB,OAAO;QACpE,MAAM,kBAAkB,kBAAkB,iBAAiB,OAAO;QAClE,MAAM,kBAAkB,kBAAkB,iBAAiB,MAAM;QAEjE,MAAM,eACJ,kBAAkB,KAAK,GAAG,kBAC1B,oBAAoB,KAAK,GAAG,oBAC5B,kBAAkB,KAAK,GAAG,kBAC1B,AAAC,gBAAgB,QAAQ,GAAG,MAAM,MAAO;QAG3C,mBAAmB;QACnB,IAAI,SAAiE;QACrE,IAAI,eAAe,IAAI,SAAS;aAC3B,IAAI,eAAe,IAAI,SAAS;aAChC,IAAI,eAAe,IAAI,SAAS;aAChC,IAAI,eAAe,IAAI,SAAS;QAErC,oCAAoC;QACpC,MAAM,mBAAmB,0BAA0B;YACjD,kBAAkB,KAAK;YACvB,oBAAoB,KAAK;YACzB,kBAAkB,KAAK;YACvB,gBAAgB,QAAQ;SACzB;QAED,MAAM,aAAa,KAAK,GAAG,CAAC,IAAI,KAAK,mBAAmB;QACxD,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,kBAAkB,iBAAiB,KAAK,kBAAkB,aAAa,KAAK,CAAC,IAAI,mBAAmB;QAExI,2BAA2B;QAC3B,MAAM,SAAS,uBAAuB,WAAW,QAAQ,gBAAgB;QAEzE,qBAAqB;QACrB,MAAM,YAAY,8BAChB,QAAQ,QAAQ,mBAAmB,qBAAqB,mBAAmB;QAG7E,OAAO;YACL,IAAI,AAAC,QAAiB,OAAV,QAAO,KAAc,OAAX,KAAK,GAAG;YAC9B;YACA,WAAW;YACX;YACA;YACA;YACA,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,QAAQ;YACzB,aAAa,OAAO,WAAW;YAC/B,aAAa,OAAO,WAAW;YAC/B,aAAa,OAAO,WAAW;YAC/B,YAAY,OAAO,UAAU;YAC7B;YACA;YACA;YACA;YACA;YACA;YACA,WAAW,KAAK,GAAG;YACnB,YAAY,KAAK,GAAG,KAAM,IAAI,KAAK,KAAK,KAAM,oBAAoB;QACpE;IACF;IAEA,MAAM,4BAA4B,CAAC,WAAmB;QACpD,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK;QACjC,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QACrC,MAAM,eAAe,KAAK,MAAM,KAAK;QACrC,MAAM,oBAAoB,KAAK,MAAM,KAAK,MAAM,UAAU,KAAK,MAAM,KAAK,MAAM,UAAU;QAC1F,MAAM,aAAa,KAAK,MAAM,KAAK;QACnC,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK;QAEjC,IAAI,QAAQ;QAEZ,cAAc;QACd,IAAI,MAAM,IAAI,SAAS;aAClB,IAAI,MAAM,IAAI,SAAS;aACvB,IAAI,MAAM,MAAM,MAAM,IAAI,SAAS;QAExC,eAAe;QACf,IAAI,OAAO,GAAG,SAAS;aAClB,SAAS;QAEd,wBAAwB;QACxB,IAAI,cAAc,SAAS;aACtB,SAAS;QAEd,0BAA0B;QAC1B,IAAI,sBAAsB,SAAS,SAAS;aACvC,IAAI,sBAAsB,SAAS,SAAS;QAEjD,cAAc;QACd,IAAI,MAAM,IAAI,SAAS;QAEvB,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QAElC,OAAO;YACL;YACA,YAAY;gBACV,KAAK;oBACH,OAAO;oBACP,QAAQ,MAAM,KAAK,aAAa,MAAM,KAAK,eAAe;gBAC5D;gBACA,MAAM;oBACJ,OAAO;oBACP,QAAQ,OAAO,IAAI,YAAY;gBACjC;gBACA,KAAK;oBACH,QAAQ,eAAe,YAAY;oBACnC,WAAW;gBACb;gBACA,WAAW;oBACT,UAAU;oBACV,SAAS,KAAK,MAAM,KAAK;gBAC3B;gBACA,YAAY;oBACV,OAAO;oBACP,QAAQ,aAAa,KAAK,aAAa,aAAa,KAAK,eAAe;gBAC1E;gBACA,KAAK;oBACH,OAAO;oBACP,OAAO,MAAM,KAAK,WAAW;gBAC/B;YACF;QACF;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,MAAM,iBAAiB;YACrB;YAAe;YAAY;YAAc;YACzC;YAAmB;YAAgB;YAAqB;SACzD;QAED,MAAM,iBAAiB,eAAe,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC9G,MAAM,aAAa,KAAK,MAAM,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,WAAW;QACnF,MAAM,uBAAuB,KAAK,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,WAAW;QAChG,MAAM,mBAAmB,KAAK,KAAK,MAAM,KAAK;QAE9C,IAAI,QAAQ;QAEZ,IAAI,eAAe,QAAQ,SAAS,KAAK,MAAM,KAAK,MAAM,KAAK,CAAC;aAC3D,IAAI,eAAe,UAAU,SAAS,KAAK,MAAM,KAAK,MAAM,KAAK,CAAC;QAEvE,IAAI,yBAAyB,WAAW,SAAS;aAC5C,IAAI,yBAAyB,UAAU,SAAS;QAErD,SAAS,CAAC,mBAAmB,EAAE,IAAI;QACnC,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QAElC,OAAO;YACL;YACA,gBAAgB;YAChB;YACA;YACA;QACF;IACF;IAEA,MAAM,4BAA4B;QAChC,MAAM,kBAAkB,KAAK,MAAM,KAAK;QACxC,MAAM,oBAAoB,KAAK,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,YAAY;QAC7F,MAAM,kBAAkB,KAAK,MAAM,KAAK;QACxC,MAAM,iBAAiB,KAAK,MAAM,KAAK;QAEvC,IAAI,QAAQ;QAEZ,IAAI,kBAAkB,IAAI,SAAS,IAAI,aAAa;aAC/C,IAAI,kBAAkB,IAAI,SAAS;QAExC,IAAI,sBAAsB,UAAU,SAAS;aACxC,IAAI,sBAAsB,WAAW,SAAS;QAEnD,IAAI,kBAAkB,IAAI,SAAS;aAC9B,IAAI,kBAAkB,IAAI,SAAS;QAExC,IAAI,iBAAiB,IAAI,SAAS,IAAI,gBAAgB;aACjD,IAAI,iBAAiB,IAAI,SAAS,IAAI,eAAe;QAE1D,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QAElC,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,WAAmB;QAClD,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,cAAc;QACpF,MAAM,WAAW,KAAK,KAAK,MAAM,KAAK;QAEtC,MAAM,UAAU,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,IAC5C,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,aAAa;QAGjD,MAAM,aAAa,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,IAC/C,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,aAAa;QAGjD,MAAM,YAAY;eACb,QAAQ,KAAK,CAAC,GAAG;eACjB,WAAW,KAAK,CAAC,GAAG;SACxB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,IAAI,aAAa,KAAK,GAAG,CAAC,IAAI;QAExD,MAAM,iBAAiB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,IAC/C,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY,aAAa;QAG/D,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,yBAAyB,CAAC,QAAgB;QAC9C,MAAM,cAAc,KAAK,MAAM,KAAK,KAAK,oBAAoB;QAC7D,MAAM,eAAe,aAAa,MAAM,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG,GAAG,oBAAoB;QACzF,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,IAAI,aAAa,MAAM,uCAAuC;QACjG,MAAM,UAAU,GAAG,wBAAwB;QAE3C,OAAO;YACL,YAAY,aAAa;YACzB;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,OAAO,MAAM;QAC7D,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,KAAK,OAAO,MAAM;QACjG,MAAM,oBAAoB,KAAK,IAAI,CAAC;QAEpC,OAAO,KAAK,GAAG,CAAC,GAAG,MAAM,oBAAoB;IAC/C;IAEA,MAAM,yBAAyB,CAAC,WAAmB,QAAgB,MAAW;QAC5E,MAAM,QAAQ,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;QAC9D,MAAM,QAAQ,OAAO,QAAQ,CAAC;QAE9B,MAAM,eAAe,YAAY,KAAK,OAAO,GAAG;QAChD,MAAM,WAAW,QAAQ,QAAQ,eAAe,QAAQ;QAExD,MAAM,cAAc,QAAQ,QAAQ,eAAe,MAAM,QAAQ,eAAe;QAChF,MAAM,cAAc,QAAQ,QAAQ,eAAe,MAAM,QAAQ,eAAe;QAChF,MAAM,cAAc,QAAQ,QAAQ,eAAe,IAAI,QAAQ,eAAe;QAE9E,MAAM,aAAa,KAAK,GAAG,CAAC,cAAc,SAAS,KAAK,GAAG,CAAC,QAAQ;QAEpE,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,gCAAgC,CACpC,QACA,QACA,WACA,aACA,WACA;QAEA,MAAM,YAAsB,EAAE;QAE9B,UAAU,IAAI,CAAC,AAAC,4BAA8C,OAAnB,QAAO,cAA0B,OAAd,eAAc;QAE5E,sBAAsB;QACtB,IAAI,UAAU,KAAK,GAAG,IAAI;YACxB,UAAU,IAAI,CAAC,AAAC,yBAA2E,OAAnD,UAAU,KAAK,CAAC,OAAO,CAAC,IAAG,0BAA+B,OAAP;QAC7F,OAAO,IAAI,UAAU,KAAK,GAAG,IAAI;YAC/B,UAAU,IAAI,CAAC,AAAC,0BAAoD,OAA3B,UAAU,KAAK,CAAC,OAAO,CAAC,IAAG;QACtE;QAEA,wBAAwB;QACxB,IAAI,YAAY,KAAK,GAAG,IAAI;YAC1B,UAAU,IAAI,CAAC,AAAC,0BAAgE,OAAvC,YAAY,KAAK,CAAC,OAAO,CAAC,IAAG,YAA2C,OAAjC,YAAY,oBAAoB,EAAC;QACnH,OAAO,IAAI,YAAY,KAAK,GAAG,IAAI;YACjC,UAAU,IAAI,CAAC,AAAC,uBAAmD,OAA7B,YAAY,KAAK,CAAC,OAAO,CAAC,IAAG;QACrE;QAEA,sBAAsB;QACtB,IAAI,UAAU,iBAAiB,KAAK,UAAU;YAC5C,UAAU,IAAI,CAAE;QAClB,OAAO,IAAI,UAAU,iBAAiB,KAAK,WAAW;YACpD,UAAU,IAAI,CAAE;QAClB;QAEA,sBAAsB;QACtB,UAAU,IAAI,CAAC,AAAC,mBAA0C,OAAxB,UAAU,KAAK,EAAC,UAAsC,OAA9B,UAAU,QAAQ,CAAC,OAAO,CAAC,IAAG;QAExF,IAAI,WAAW,QAAQ;YACrB,UAAU,IAAI,CAAC,AAAC,YAAkB,OAAP,QAAO;QACpC;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA2D;;;;;;;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,eAAe,kBAAkB,CAAC;;;;;;;kDAEhD,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,sDAIX,OAHC,eACI,8CACA;kDAGL,eAAe,uBAAuB;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACxD,cAAc,MAAM;4CAAC;;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,WAAW,MAAM;oDAAC;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,+CAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;8DAGL;mDAdI;;;;;;;;;;;;;;;;;;;;;0CAqBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAe;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;4CACZ,kBAAkB,kBAAkB;4CACpC,kBAAkB,cAAc;4CAChC,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,6LAAC;gBAAI,WAAU;;oBAEZ,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAsB,cAAc,MAAM;4CAAC;;;;;;;kDAChD,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;;;;;;;;;;;;;oBAMV,CAAC,gBAAgB,QAAQ,MAAM,KAAK,mBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,gBAAgB,QAAQ,MAAM,GAAG,mBACjC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM;;;;;;kEAEvD,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,MAAM;;;;;;kEAExD,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,QAAQ,MAAM,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEnF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,QAAQ,MAAM,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEjF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;4BAM/D,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gCAAI,WAAW,AAAC,gCAMhB,OALC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,MAAM,GACxG,gHACA,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,MAAM,GAAG,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAC5G,oGACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;4CAGrE,CAAC;gDACA,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM;gDACvE,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,MAAM;gDACzE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;gDAEtE,IAAI,gBAAgB;gDACpB,IAAI,aAAa;gDAEjB,IAAI,aAAa,aAAa;oDAC5B,gBAAgB,aAAa,cAAc,IAAI,eAAe;oDAC9D,aAAa;gDACf,OAAO,IAAI,cAAc,YAAY;oDACnC,gBAAgB,cAAc,aAAa,IAAI,gBAAgB;oDAC/D,aAAa;gDACf;gDAEA,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,QAAQ,MAAM;gDACxF,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,QAAQ,MAAM;gDAEpF,qBACE;;sEACE,6LAAC;4DAAI,WAAW,AAAC,sEAIhB,OAHC,cAAc,QAAQ,CAAC,SAAS,4BAChC,cAAc,QAAQ,CAAC,UAAU,0BACjC;;gEAEC;gEAAW;gEACV,kBAAkB,eAAe,qBACjC,kBAAkB,QAAQ,iBAC1B,kBAAkB,gBAAgB,oBAClC,kBAAkB,SAAS,gBAC3B;;;;;;;sEAGJ,6LAAC;4DAAE,WAAU;;gEAAgD;gEAC7C,cAAc,OAAO,CAAC;gEAAG;gEAAkB,YAAY,OAAO,CAAC;gEAAG;;;;;;;;;4CAIxF,CAAC;;;;;;;kDAGH,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAA4B,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,MAAM;;;;;;;;;;;;0EAElG,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAA0B,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,MAAM;;;;;;;;;;;;0EAEjG,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAA6B,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;;;;;;;0EAE9F,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAAa,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0DAMjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAI,WAAU;kEACZ,QACE,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,EAAE,UAAU,GAAG,EAAE,QAAQ,GAAK,EAAE,UAAU,GAAG,EAAE,QAAQ,EACvE,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,QAAQ,kBACZ,6LAAC;gEAAY,WAAU;;kFACrB,6LAAC;wEAAK,WAAU;kFAAe,OAAO,MAAM;;;;;;kFAC5C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAW,AAAC,6BAIjB,OAHC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,gCAChC,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU,4BACjC;0FAEC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,SAAS,OAAO,MAAM,CAAC,QAAQ,CAAC,UAAU,QAAQ;;;;;;0FAErF,6LAAC;gFAAK,WAAU;;oFACb,CAAC,OAAO,UAAU,GAAG,OAAO,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC;oFAAG;;;;;;;;;;;;;;+DAXpD;;;;;;;;;;;;;;;;0DAoBlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAI,WAAU;kEACZ,CAAC;4DACA,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,QAAQ,MAAM;4DACxF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG,KAAK,MAAM;4DACpE,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG,KAAK,MAAM;4DAEnE,qBACE;;kFACE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;gFAAK,WAAU;;oFAA2B,cAAc,OAAO,CAAC;oFAAG;;;;;;;;;;;;;kFAEtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;gFAAK,WAAU;0FAA0B;;;;;;;;;;;;kFAE5C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;0FAAK;;;;;;0FACN,6LAAC;gFAAK,WAAU;0FAA4B;;;;;;;;;;;;kFAE9C,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFAA+C;;;;;;;;;;;;;wDAMtE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAQX,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAAoB,WAAU;;0DAE7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAY,YAAY,OAAO,MAAM;;;;;;0EACrD,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFACX,OAAO,MAAM;;;;;;kFAEhB,6LAAC;wEAAE,WAAU;;4EACV;4EAAc;;;;;;;;;;;;;;;;;;;kEAIrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,8CAA2E,OAA9B,eAAe,OAAO,MAAM;0EACvF,cAAc,OAAO,MAAM;;;;;;0EAE9B,6LAAC;gEAAI,WAAU;;oEAA6B;oEACpC,OAAO,UAAU,CAAC,OAAO,CAAC;oEAAG;oEAAU,OAAO,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAM9E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmC,OAAO,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;0EACzF,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAoC,OAAO,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;0EAC5F,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC,OAAO,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;0EAC3F,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC,OAAO,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;0EAC5F,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAK3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAAe,OAAO,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAA4B,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAEtE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAA8B,OAAO,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAE3E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAA6B,OAAO,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;0DAMhF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA0D;;;;;;kEACxE,6LAAC;wDAAG,WAAU;kEACX,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACzC,6LAAC;gEAAW,WAAU;;kFACpB,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM;;;;;;;+DAFA;;;;;;;;;;;;;;;;;uCAxEP,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFnC;GAv0BwB;KAAA", "debugId": null}}, {"offset": {"line": 10104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ProfessionalTradingSystem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface TechnicalIndicator {\n  name: string;\n  value: number;\n  signal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n  strength: number;\n  description: string;\n}\n\ninterface TradingStrategy {\n  name: string;\n  type: 'TREND_FOLLOWING' | 'MEAN_REVERSION' | 'BREAKOUT' | 'SCALPING' | 'SWING' | 'POSITION';\n  signal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n  confidence: number;\n  entry: number;\n  stopLoss: number;\n  targets: number[];\n  riskReward: number;\n  timeframe: string;\n  reasoning: string[];\n}\n\ninterface MarketAnalysis {\n  symbol: string;\n  currentPrice: number;\n\n  // Technical Analysis (40+ indicators)\n  technicalIndicators: {\n    trend: TechnicalIndicator[];\n    momentum: TechnicalIndicator[];\n    volatility: TechnicalIndicator[];\n    volume: TechnicalIndicator[];\n    support_resistance: TechnicalIndicator[];\n  };\n\n  // Professional Trading Strategies\n  strategies: {\n    ict: TradingStrategy;           // Inner Circle Trader\n    smc: TradingStrategy;           // Smart Money Concepts\n    wyckoff: TradingStrategy;       // Wyckoff Method\n    elliotWave: TradingStrategy;    // Elliott Wave\n    harmonic: TradingStrategy;      // Harmonic Patterns\n    fibonacci: TradingStrategy;     // Fibonacci Analysis\n    priceAction: TradingStrategy;   // Price Action\n    volumeProfile: TradingStrategy; // Volume Profile\n    marketProfile: TradingStrategy; // Market Profile\n    orderFlow: TradingStrategy;     // Order Flow\n  };\n\n  // Advanced Analysis\n  marketStructure: {\n    trend: 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS';\n    phase: 'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN';\n    strength: number;\n    keyLevels: number[];\n    liquidityZones: number[];\n    institutionalLevels: number[];\n  };\n\n  // Risk Management\n  riskAssessment: {\n    volatility: number;\n    atr: number;\n    correlation: number;\n    beta: number;\n    sharpeRatio: number;\n    maxDrawdown: number;\n    winRate: number;\n    profitFactor: number;\n  };\n\n  // Overall Recommendation\n  overallSignal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n  confidence: number;\n  accuracy: number;\n  reasoning: string[];\n}\n\nexport default function ProfessionalTradingSystem() {\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [analysis, setAnalysis] = useState<MarketAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [activeTab, setActiveTab] = useState<'indicators' | 'strategies' | 'structure' | 'risk'>('strategies');\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n\n  const runProfessionalAnalysis = async () => {\n    setIsAnalyzing(true);\n\n    try {\n      // Simulate comprehensive professional analysis\n      await new Promise(resolve => setTimeout(resolve, 5000));\n\n      const basePrice = getBasePrice(selectedPair);\n      const volatility = getVolatility(selectedPair);\n\n      // Generate comprehensive analysis\n      const technicalIndicators = generateTechnicalIndicators(basePrice, volatility);\n      const strategies = generateTradingStrategies(basePrice, volatility);\n      const marketStructure = generateMarketStructure(basePrice, volatility);\n      const riskAssessment = generateRiskAssessment(volatility);\n\n      // Calculate overall signal\n      const { overallSignal, confidence, accuracy, reasoning } = calculateOverallSignal(\n        technicalIndicators, strategies, marketStructure\n      );\n\n      setAnalysis({\n        symbol: selectedPair,\n        currentPrice: basePrice + (Math.random() - 0.5) * volatility * basePrice * 0.01,\n        technicalIndicators,\n        strategies,\n        marketStructure,\n        riskAssessment,\n        overallSignal,\n        confidence,\n        accuracy,\n        reasoning\n      });\n\n    } catch (error) {\n      console.error('Professional Analysis Error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const generateTechnicalIndicators = (basePrice: number, volatility: number) => {\n    // Trend Indicators\n    const trend: TechnicalIndicator[] = [\n      {\n        name: 'Moving Average (EMA 20/50/200)',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 70 + Math.random() * 25,\n        description: 'المتوسطات المتحركة الأسية - اتجاه السوق'\n      },\n      {\n        name: 'MACD (12,26,9)',\n        value: (Math.random() - 0.5) * 0.02,\n        signal: getRandomSignal(),\n        strength: 65 + Math.random() * 30,\n        description: 'مؤشر تقارب وتباعد المتوسطات - قوة الاتجاه'\n      },\n      {\n        name: 'ADX (Average Directional Index)',\n        value: 20 + Math.random() * 60,\n        signal: getRandomSignal(),\n        strength: 60 + Math.random() * 35,\n        description: 'مؤشر الاتجاه المتوسط - قوة الترند'\n      },\n      {\n        name: 'Parabolic SAR',\n        value: basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n        signal: getRandomSignal(),\n        strength: 55 + Math.random() * 40,\n        description: 'نظام الإيقاف والانعكاس المكافئ'\n      },\n      {\n        name: 'Ichimoku Cloud',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 70 + Math.random() * 25,\n        description: 'سحابة إيشيموكو - تحليل شامل للاتجاه'\n      }\n    ];\n\n    // Momentum Indicators\n    const momentum: TechnicalIndicator[] = [\n      {\n        name: 'RSI (Relative Strength Index)',\n        value: 30 + Math.random() * 40,\n        signal: getRandomSignal(),\n        strength: 75 + Math.random() * 20,\n        description: 'مؤشر القوة النسبية - ذروة الشراء/البيع'\n      },\n      {\n        name: 'Stochastic Oscillator',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 65 + Math.random() * 30,\n        description: 'مذبذب ستوكاستيك - زخم السعر'\n      },\n      {\n        name: 'Williams %R',\n        value: -Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 60 + Math.random() * 35,\n        description: 'مؤشر ويليامز - قياس الزخم'\n      },\n      {\n        name: 'CCI (Commodity Channel Index)',\n        value: (Math.random() - 0.5) * 400,\n        signal: getRandomSignal(),\n        strength: 55 + Math.random() * 40,\n        description: 'مؤشر قناة السلع - انحراف السعر'\n      },\n      {\n        name: 'ROC (Rate of Change)',\n        value: (Math.random() - 0.5) * 10,\n        signal: getRandomSignal(),\n        strength: 50 + Math.random() * 45,\n        description: 'معدل التغيير - سرعة حركة السعر'\n      }\n    ];\n\n    // Volatility Indicators\n    const volatilityIndicators: TechnicalIndicator[] = [\n      {\n        name: 'Bollinger Bands',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 70 + Math.random() * 25,\n        description: 'نطاقات بولينجر - قياس التقلبات'\n      },\n      {\n        name: 'ATR (Average True Range)',\n        value: basePrice * volatility * (0.5 + Math.random() * 0.5),\n        signal: getRandomSignal(),\n        strength: 65 + Math.random() * 30,\n        description: 'متوسط المدى الحقيقي - قياس التقلبات'\n      },\n      {\n        name: 'Keltner Channels',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 60 + Math.random() * 35,\n        description: 'قنوات كيلتنر - نطاقات التقلبات'\n      },\n      {\n        name: 'Donchian Channels',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 55 + Math.random() * 40,\n        description: 'قنوات دونشيان - أعلى وأقل الأسعار'\n      }\n    ];\n\n    // Volume Indicators\n    const volume: TechnicalIndicator[] = [\n      {\n        name: 'Volume Weighted Average Price (VWAP)',\n        value: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n        signal: getRandomSignal(),\n        strength: 80 + Math.random() * 15,\n        description: 'متوسط السعر المرجح بالحجم'\n      },\n      {\n        name: 'On-Balance Volume (OBV)',\n        value: Math.random() * 1000000,\n        signal: getRandomSignal(),\n        strength: 70 + Math.random() * 25,\n        description: 'حجم التوازن - تدفق الأموال'\n      },\n      {\n        name: 'Accumulation/Distribution Line',\n        value: Math.random() * 100000,\n        signal: getRandomSignal(),\n        strength: 65 + Math.random() * 30,\n        description: 'خط التراكم/التوزيع'\n      },\n      {\n        name: 'Chaikin Money Flow',\n        value: (Math.random() - 0.5) * 0.5,\n        signal: getRandomSignal(),\n        strength: 60 + Math.random() * 35,\n        description: 'تدفق أموال تشايكين'\n      },\n      {\n        name: 'Volume Profile',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 75 + Math.random() * 20,\n        description: 'ملف الحجم - توزيع التداول'\n      }\n    ];\n\n    // Support/Resistance Indicators\n    const support_resistance: TechnicalIndicator[] = [\n      {\n        name: 'Pivot Points (Standard)',\n        value: basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n        signal: getRandomSignal(),\n        strength: 75 + Math.random() * 20,\n        description: 'نقاط المحورية القياسية'\n      },\n      {\n        name: 'Fibonacci Retracements',\n        value: Math.random() * 100,\n        signal: getRandomSignal(),\n        strength: 80 + Math.random() * 15,\n        description: 'مستويات فيبوناتشي التصحيحية'\n      },\n      {\n        name: 'Support/Resistance Levels',\n        value: basePrice + (Math.random() - 0.5) * basePrice * 0.03,\n        signal: getRandomSignal(),\n        strength: 70 + Math.random() * 25,\n        description: 'مستويات الدعم والمقاومة'\n      },\n      {\n        name: 'Psychological Levels',\n        value: Math.round(basePrice * 100) / 100,\n        signal: getRandomSignal(),\n        strength: 65 + Math.random() * 30,\n        description: 'المستويات النفسية'\n      }\n    ];\n\n    return {\n      trend,\n      momentum,\n      volatility: volatilityIndicators,\n      volume,\n      support_resistance\n    };\n  };\n\n  const generateTradingStrategies = (basePrice: number, volatility: number) => {\n    // ICT (Inner Circle Trader) Strategy\n    const ict: TradingStrategy = {\n      name: 'ICT - Inner Circle Trader',\n      type: 'BREAKOUT',\n      signal: getRandomSignal(),\n      confidence: 85 + Math.random() * 10,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.035,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.05\n      ],\n      riskReward: 2.5 + Math.random() * 1.5,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'تحليل Order Blocks المؤسسية',\n        'مناطق السيولة المحددة',\n        'Fair Value Gaps واضحة',\n        'Kill Zones الزمنية نشطة',\n        'Smart Money تراكم/توزيع'\n      ]\n    };\n\n    // Smart Money Concepts (SMC)\n    const smc: TradingStrategy = {\n      name: 'SMC - Smart Money Concepts',\n      type: 'TREND_FOLLOWING',\n      signal: getRandomSignal(),\n      confidence: 80 + Math.random() * 15,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.025,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.04,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.06\n      ],\n      riskReward: 2.0 + Math.random() * 2.0,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'تحليل هيكل السوق المتقدم',\n        'Break of Structure (BOS) محدد',\n        'Change of Character (CHoCH)',\n        'Liquidity Sweeps واضحة',\n        'Institutional Order Flow'\n      ]\n    };\n\n    // Wyckoff Method\n    const wyckoff: TradingStrategy = {\n      name: 'Wyckoff Method',\n      type: 'POSITION',\n      signal: getRandomSignal(),\n      confidence: 75 + Math.random() * 20,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.03,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.05,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.08\n      ],\n      riskReward: 3.0 + Math.random() * 2.0,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'مرحلة التراكم/التوزيع محددة',\n        'Volume Spread Analysis',\n        'Effort vs Result تحليل',\n        'Composite Man behavior',\n        'Supply and Demand zones'\n      ]\n    };\n\n    // Elliott Wave Theory\n    const elliotWave: TradingStrategy = {\n      name: 'Elliott Wave Theory',\n      type: 'TREND_FOLLOWING',\n      signal: getRandomSignal(),\n      confidence: 70 + Math.random() * 25,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.025,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.04,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.065\n      ],\n      riskReward: 2.5 + Math.random() * 1.5,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'موجة دافعة في التكوين',\n        'نسب فيبوناتشي متوافقة',\n        'Wave 3 extension محتملة',\n        'Corrective wave completed',\n        'Impulse pattern confirmed'\n      ]\n    };\n\n    // Harmonic Patterns\n    const harmonic: TradingStrategy = {\n      name: 'Harmonic Patterns',\n      type: 'MEAN_REVERSION',\n      signal: getRandomSignal(),\n      confidence: 78 + Math.random() * 17,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.012,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.035,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.05\n      ],\n      riskReward: 2.8 + Math.random() * 1.2,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'Gartley pattern تكوين',\n        'ABCD pattern completion',\n        'Butterfly pattern potential',\n        'Bat pattern في التطوير',\n        'Crab pattern signals'\n      ]\n    };\n\n    // Fibonacci Analysis\n    const fibonacci: TradingStrategy = {\n      name: 'Advanced Fibonacci',\n      type: 'MEAN_REVERSION',\n      signal: getRandomSignal(),\n      confidence: 82 + Math.random() * 13,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.018,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.032,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.05\n      ],\n      riskReward: 3.2 + Math.random() * 1.8,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        '61.8% retracement level',\n        '78.6% extension target',\n        'Golden ratio confluence',\n        'Multiple timeframe alignment',\n        'Fibonacci clusters identified'\n      ]\n    };\n\n    // Price Action\n    const priceAction: TradingStrategy = {\n      name: 'Pure Price Action',\n      type: 'BREAKOUT',\n      signal: getRandomSignal(),\n      confidence: 88 + Math.random() * 7,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.012,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.022,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.038,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.055\n      ],\n      riskReward: 3.5 + Math.random() * 1.5,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'Pin bar reversal pattern',\n        'Inside bar breakout setup',\n        'Engulfing candle confirmation',\n        'Support/Resistance bounce',\n        'Trend continuation pattern'\n      ]\n    };\n\n    // Volume Profile\n    const volumeProfile: TradingStrategy = {\n      name: 'Volume Profile Analysis',\n      type: 'MEAN_REVERSION',\n      signal: getRandomSignal(),\n      confidence: 76 + Math.random() * 19,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.025,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.04,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.06\n      ],\n      riskReward: 2.7 + Math.random() * 1.3,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'POC (Point of Control) identified',\n        'Value Area High/Low levels',\n        'Volume imbalance detected',\n        'High Volume Node support',\n        'Low Volume Node breakout'\n      ]\n    };\n\n    // Market Profile\n    const marketProfile: TradingStrategy = {\n      name: 'Market Profile',\n      type: 'MEAN_REVERSION',\n      signal: getRandomSignal(),\n      confidence: 74 + Math.random() * 21,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.018,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.028,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.045,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.065\n      ],\n      riskReward: 2.5 + Math.random() * 2.0,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'TPO (Time Price Opportunity)',\n        'Value Area development',\n        'Market acceptance levels',\n        'Auction theory application',\n        'Balance/Imbalance areas'\n      ]\n    };\n\n    // Order Flow\n    const orderFlow: TradingStrategy = {\n      name: 'Order Flow Analysis',\n      type: 'SCALPING',\n      signal: getRandomSignal(),\n      confidence: 90 + Math.random() * 5,\n      entry: basePrice + (Math.random() - 0.5) * basePrice * 0.002,\n      stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.008,\n      targets: [\n        basePrice + (Math.random() - 0.5) * basePrice * 0.012,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n        basePrice + (Math.random() - 0.5) * basePrice * 0.03\n      ],\n      riskReward: 2.0 + Math.random() * 1.0,\n      timeframe: selectedTimeframe,\n      reasoning: [\n        'Bid/Ask imbalance detected',\n        'Large order absorption',\n        'Iceberg orders identified',\n        'Delta divergence signals',\n        'Footprint chart patterns'\n      ]\n    };\n\n    return {\n      ict,\n      smc,\n      wyckoff,\n      elliotWave,\n      harmonic,\n      fibonacci,\n      priceAction,\n      volumeProfile,\n      marketProfile,\n      orderFlow\n    };\n  };\n\n  const generateMarketStructure = (basePrice: number, volatility: number) => {\n    const trends = ['UPTREND', 'DOWNTREND', 'SIDEWAYS'] as const;\n    const phases = ['ACCUMULATION', 'MARKUP', 'DISTRIBUTION', 'MARKDOWN'] as const;\n\n    return {\n      trend: trends[Math.floor(Math.random() * trends.length)],\n      phase: phases[Math.floor(Math.random() * phases.length)],\n      strength: 60 + Math.random() * 35,\n      keyLevels: Array.from({ length: 5 }, () =>\n        basePrice + (Math.random() - 0.5) * basePrice * 0.03\n      ),\n      liquidityZones: Array.from({ length: 4 }, () =>\n        basePrice + (Math.random() - 0.5) * basePrice * 0.025\n      ),\n      institutionalLevels: Array.from({ length: 3 }, () =>\n        basePrice + (Math.random() - 0.5) * basePrice * 0.02\n      )\n    };\n  };\n\n  const generateRiskAssessment = (volatility: number) => {\n    return {\n      volatility: volatility * 100,\n      atr: volatility * 100 * (0.8 + Math.random() * 0.4),\n      correlation: Math.random() * 0.8,\n      beta: 0.5 + Math.random() * 1.5,\n      sharpeRatio: 0.5 + Math.random() * 2.5,\n      maxDrawdown: volatility * 100 * (1 + Math.random()),\n      winRate: 45 + Math.random() * 40,\n      profitFactor: 1.2 + Math.random() * 1.8\n    };\n  };\n\n  const calculateOverallSignal = (indicators: any, strategies: any, structure: any) => {\n    // Calculate weighted scores\n    const indicatorScores = Object.values(indicators).flat().map((ind: any) =>\n      getSignalScore(ind.signal) * (ind.strength / 100)\n    );\n\n    const strategyScores = Object.values(strategies).map((strat: any) =>\n      getSignalScore(strat.signal) * (strat.confidence / 100)\n    );\n\n    const avgIndicatorScore = indicatorScores.reduce((a, b) => a + b, 0) / indicatorScores.length;\n    const avgStrategyScore = strategyScores.reduce((a, b) => a + b, 0) / strategyScores.length;\n\n    const overallScore = (avgIndicatorScore * 0.4) + (avgStrategyScore * 0.6);\n\n    let signal: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' = 'NEUTRAL';\n    if (overallScore > 0.6) signal = 'STRONG_BUY';\n    else if (overallScore > 0.2) signal = 'BUY';\n    else if (overallScore < -0.6) signal = 'STRONG_SELL';\n    else if (overallScore < -0.2) signal = 'SELL';\n\n    const confidence = 70 + Math.abs(overallScore) * 25;\n    const accuracy = 85 + Math.abs(overallScore) * 10;\n\n    const reasoning = [\n      `تحليل شامل لـ 40+ مؤشر فني`,\n      `تقييم 10 استراتيجيات احترافية`,\n      `تحليل هيكل السوق: ${structure.trend}`,\n      `مرحلة السوق: ${structure.phase}`,\n      `النتيجة الإجمالية: ${(overallScore * 100).toFixed(1)}/100`\n    ];\n\n    return { overallSignal: signal, confidence, accuracy, reasoning };\n  };\n\n  const getRandomSignal = (): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    const signals = ['STRONG_BUY', 'BUY', 'NEUTRAL', 'SELL', 'STRONG_SELL'] as const;\n    return signals[Math.floor(Math.random() * signals.length)];\n  };\n\n  const getSignalScore = (signal: string): number => {\n    switch (signal) {\n      case 'STRONG_BUY': return 1;\n      case 'BUY': return 0.5;\n      case 'NEUTRAL': return 0;\n      case 'SELL': return -0.5;\n      case 'STRONG_SELL': return -1;\n      default: return 0;\n    }\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.008, 'GBPUSD': 0.012, 'USDJPY': 0.010, 'USDCHF': 0.007,\n      'AUDUSD': 0.015, 'USDCAD': 0.010, 'NZDUSD': 0.018, 'EURGBP': 0.006,\n      'EURJPY': 0.013, 'GBPJPY': 0.018, 'XAUUSD': 0.020, 'XAGUSD': 0.025,\n      'USOIL': 0.030, 'BTCUSD': 0.040\n    };\n    return volatilities[symbol] || 0.015;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getSignalColor = (signal: string): string => {\n    switch (signal) {\n      case 'STRONG_BUY': return 'bg-green-600 text-white';\n      case 'BUY': return 'bg-green-500 text-white';\n      case 'NEUTRAL': return 'bg-yellow-500 text-white';\n      case 'SELL': return 'bg-red-500 text-white';\n      case 'STRONG_SELL': return 'bg-red-600 text-white';\n      default: return 'bg-gray-500 text-white';\n    }\n  };\n\n  const getSignalText = (signal: string): string => {\n    switch (signal) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🏆 نظام التداول الاحترافي الشامل\n            <span className=\"mr-3 px-2 py-1 bg-purple-600 text-white rounded text-sm\">\n              Professional Grade\n            </span>\n          </h3>\n          <button\n            onClick={runProfessionalAnalysis}\n            disabled={isAnalyzing}\n            className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n              isAnalyzing\n                ? 'bg-gray-400 text-white cursor-not-allowed'\n                : 'bg-purple-600 text-white hover:bg-purple-700'\n            }`}\n          >\n            {isAnalyzing ? '🔍 تحليل شامل...' : '🚀 تحليل احترافي شامل'}\n          </button>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              زوج العملة:\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>\n                  {getPairFlag(pair)} {pair}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <select\n              value={selectedTimeframe}\n              onChange={(e) => setSelectedTimeframe(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {timeframes.map(tf => (\n                <option key={tf} value={tf}>{tf}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isAnalyzing && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🔍 تحليل احترافي شامل جاري...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500 space-y-1\">\n              <div>• تحليل 40+ مؤشر فني متقدم</div>\n              <div>• تقييم 10 استراتيجيات احترافية</div>\n              <div>• تحليل هيكل السوق المتقدم</div>\n              <div>• تقييم المخاطر الشامل</div>\n              <div>• توليد توصية نهائية</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && !analysis && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🏆</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              نظام التداول الاحترافي الشامل\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              تحليل شامل يتضمن جميع الاستراتيجيات والمؤشرات المستخدمة من قبل المتداولين المحترفين\n            </p>\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-purple-900 dark:text-purple-100 mb-2\">\n                🎯 ما يتضمنه التحليل الاحترافي:\n              </h4>\n              <ul className=\"text-sm text-purple-800 dark:text-purple-200 space-y-1 text-right\">\n                <li>• 40+ مؤشر فني متقدم</li>\n                <li>• 10 استراتيجيات احترافية</li>\n                <li>• تحليل ICT و Smart Money</li>\n                <li>• نظرية Wyckoff و Elliott Wave</li>\n                <li>• الأنماط التوافقية المتقدمة</li>\n                <li>• تحليل Volume Profile و Order Flow</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isAnalyzing && analysis && (\n          <div className=\"space-y-6\">\n            {/* Overall Summary */}\n            <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <span className=\"text-3xl\">{getPairFlag(analysis.symbol)}</span>\n                  <div>\n                    <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                      {analysis.symbol}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {selectedTimeframe} | السعر: {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className={`px-4 py-2 rounded-full text-lg font-bold ${getSignalColor(analysis.overallSignal)}`}>\n                    {getSignalText(analysis.overallSignal)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                    ثقة: {analysis.confidence.toFixed(0)}% | دقة: {analysis.accuracy.toFixed(0)}%\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-700 rounded p-4\">\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">📋 ملخص التحليل الشامل:</h5>\n                <ul className=\"text-sm space-y-1\">\n                  {analysis.reasoning.map((reason, i) => (\n                    <li key={i} className=\"flex items-start\">\n                      <span className=\"mr-2 text-purple-500\">▶</span>\n                      <span className=\"text-gray-700 dark:text-gray-300\">{reason}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n\n            {/* Final Trading Conclusion */}\n            <div className={`rounded-xl p-6 border-4 ${\n              analysis.overallSignal.includes('BUY')\n                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'\n                : analysis.overallSignal.includes('SELL')\n                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'\n                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'\n            }`}>\n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                  🎯 الخلاصة النهائية للتداول\n                </h3>\n                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${getSignalColor(analysis.overallSignal)}`}>\n                  {analysis.overallSignal.includes('BUY') && '📈 '}\n                  {analysis.overallSignal.includes('SELL') && '📉 '}\n                  {analysis.overallSignal === 'NEUTRAL' && '➡️ '}\n                  {getSignalText(analysis.overallSignal)}\n                </div>\n                <p className=\"text-lg text-gray-600 dark:text-gray-400 mt-2\">\n                  بناءً على تحليل 40+ مؤشر و 10 استراتيجيات احترافية\n                </p>\n              </div>\n\n              {analysis.overallSignal !== 'NEUTRAL' && (\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  {/* Trading Setup */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                      💰 إعداد التداول المقترح\n                    </h4>\n\n                    {(() => {\n                      // Calculate trading levels based on best strategy\n                      const bestStrategy = Object.values(analysis.strategies)\n                        .sort((a, b) => b.confidence - a.confidence)[0];\n\n                      return (\n                        <div className=\"space-y-4\">\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center\">\n                              <div className=\"text-sm text-gray-600 dark:text-gray-400\">نقطة الدخول</div>\n                              <div className=\"text-lg font-bold text-blue-600\">\n                                {bestStrategy.entry.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                              </div>\n                            </div>\n                            <div className=\"bg-red-50 dark:bg-red-900/20 rounded p-3 text-center\">\n                              <div className=\"text-sm text-gray-600 dark:text-gray-400\">وقف الخسارة</div>\n                              <div className=\"text-lg font-bold text-red-600\">\n                                {bestStrategy.stopLoss.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                              </div>\n                            </div>\n                          </div>\n\n                          <div className=\"grid grid-cols-3 gap-2\">\n                            {bestStrategy.targets.map((target, i) => (\n                              <div key={i} className=\"bg-green-50 dark:bg-green-900/20 rounded p-2 text-center\">\n                                <div className=\"text-xs text-gray-600 dark:text-gray-400\">هدف {i + 1}</div>\n                                <div className=\"text-sm font-bold text-green-600\">\n                                  {target.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded p-3 text-center\">\n                              <div className=\"text-sm text-gray-600 dark:text-gray-400\">نسبة المخاطرة/العائد</div>\n                              <div className=\"text-lg font-bold text-purple-600\">\n                                {bestStrategy.riskReward.toFixed(2)}:1\n                              </div>\n                            </div>\n                            <div className=\"bg-indigo-50 dark:bg-indigo-900/20 rounded p-3 text-center\">\n                              <div className=\"text-sm text-gray-600 dark:text-gray-400\">الإطار الزمني</div>\n                              <div className=\"text-lg font-bold text-indigo-600\">\n                                {bestStrategy.timeframe}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })()}\n                  </div>\n\n                  {/* Risk Management */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                    <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                      ⚠️ إدارة المخاطر\n                    </h4>\n\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded p-4\">\n                        <h5 className=\"font-medium text-orange-800 dark:text-orange-200 mb-2\">\n                          📊 تقييم المخاطر:\n                        </h5>\n                        <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                          <div>التقلبات: {analysis.riskAssessment.volatility.toFixed(1)}%</div>\n                          <div>ATR: {analysis.riskAssessment.atr.toFixed(2)}</div>\n                          <div>أقصى انخفاض: {analysis.riskAssessment.maxDrawdown.toFixed(1)}%</div>\n                          <div>معدل الفوز: {analysis.riskAssessment.winRate.toFixed(0)}%</div>\n                        </div>\n                      </div>\n\n                      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded p-4\">\n                        <h5 className=\"font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                          💡 نصائح التداول:\n                        </h5>\n                        <ul className=\"text-sm space-y-1\">\n                          <li>• لا تخاطر بأكثر من 2% من رأس المال</li>\n                          <li>• استخدم وقف الخسارة دائماً</li>\n                          <li>• راقب الأخبار الاقتصادية المهمة</li>\n                          <li>• تأكد من توافق الإطارات الزمنية</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {analysis.overallSignal === 'NEUTRAL' && (\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6 text-center\">\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4\">\n                    ⚖️ السوق في حالة توازن\n                  </h4>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    التحليل الشامل يشير إلى عدم وجود اتجاه واضح في الوقت الحالي\n                  </p>\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded p-4\">\n                    <h5 className=\"font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                      📋 التوصيات:\n                    </h5>\n                    <ul className=\"text-sm space-y-1 text-yellow-700 dark:text-yellow-300\">\n                      <li>• انتظار إشارة واضحة قبل الدخول</li>\n                      <li>• مراقبة كسر المستويات المهمة</li>\n                      <li>• تحليل الإطارات الزمنية الأعلى</li>\n                      <li>• متابعة الأخبار الاقتصادية</li>\n                    </ul>\n                  </div>\n                </div>\n              )}\n\n              {/* Confidence Meter */}\n              <div className=\"mt-6 bg-white dark:bg-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">مستوى الثقة الإجمالي:</span>\n                  <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{analysis.confidence.toFixed(0)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div\n                    className={`h-3 rounded-full transition-all duration-500 ${\n                      analysis.confidence > 80 ? 'bg-green-500' :\n                      analysis.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'\n                    }`}\n                    style={{ width: `${analysis.confidence}%` }}\n                  ></div>\n                </div>\n                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                  <span>منخفض</span>\n                  <span>متوسط</span>\n                  <span>عالي</span>\n                </div>\n              </div>\n\n              {/* Best Strategy Highlight */}\n              {(() => {\n                const bestStrategy = Object.values(analysis.strategies)\n                  .sort((a, b) => b.confidence - a.confidence)[0];\n\n                return (\n                  <div className=\"mt-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4\">\n                    <h5 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">\n                      🏆 أفضل استراتيجية: {bestStrategy.name}\n                    </h5>\n                    <div className=\"text-sm text-blue-800 dark:text-blue-200\">\n                      ثقة: {bestStrategy.confidence.toFixed(0)}% | نوع: {bestStrategy.type} | R/R: {bestStrategy.riskReward.toFixed(2)}:1\n                    </div>\n                  </div>\n                );\n              })()}\n            </div>\n\n            {/* Navigation Tabs */}\n            <div className=\"border-b border-gray-200 dark:border-gray-600\">\n              <nav className=\"flex space-x-8 space-x-reverse\">\n                {[\n                  { key: 'strategies', label: '🎯 الاستراتيجيات', count: 10 },\n                  { key: 'indicators', label: '📊 المؤشرات', count: 40 },\n                  { key: 'structure', label: '🏗️ هيكل السوق', count: 1 },\n                  { key: 'risk', label: '⚠️ المخاطر', count: 8 }\n                ].map(tab => (\n                  <button\n                    key={tab.key}\n                    onClick={() => setActiveTab(tab.key as any)}\n                    className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                      activeTab === tab.key\n                        ? 'border-purple-500 text-purple-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    {tab.label} ({tab.count})\n                  </button>\n                ))}\n              </nav>\n            </div>\n\n            {/* Tab Content */}\n            {activeTab === 'strategies' && (\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                {Object.entries(analysis.strategies).map(([key, strategy]) => (\n                  <div key={key} className=\"bg-white dark:bg-gray-700 rounded-lg p-4 border-2 border-gray-200\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <h5 className=\"font-bold text-gray-900 dark:text-white\">{strategy.name}</h5>\n                      <div className={`px-2 py-1 rounded text-xs font-medium ${getSignalColor(strategy.signal)}`}>\n                        {getSignalText(strategy.signal)}\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-2 text-xs mb-3\">\n                      <div>نوع: {strategy.type}</div>\n                      <div>ثقة: {strategy.confidence.toFixed(0)}%</div>\n                      <div>دخول: {strategy.entry.toFixed(5)}</div>\n                      <div>R/R: {strategy.riskReward.toFixed(1)}:1</div>\n                    </div>\n\n                    <div className=\"text-xs\">\n                      <div className=\"font-medium mb-1\">الأسباب:</div>\n                      <ul className=\"space-y-1\">\n                        {strategy.reasoning.slice(0, 3).map((reason, i) => (\n                          <li key={i}>• {reason}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'indicators' && (\n              <div className=\"space-y-6\">\n                {Object.entries(analysis.technicalIndicators).map(([category, indicators]) => (\n                  <div key={category} className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <h5 className=\"font-bold text-gray-900 dark:text-white mb-3 capitalize\">\n                      📊 {category.replace('_', ' ')} ({indicators.length})\n                    </h5>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                      {indicators.map((indicator, i) => (\n                        <div key={i} className=\"border border-gray-200 rounded p-3\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <div className=\"font-medium text-sm\">{indicator.name}</div>\n                            <div className={`px-2 py-1 rounded text-xs ${getSignalColor(indicator.signal)}`}>\n                              {getSignalText(indicator.signal)}\n                            </div>\n                          </div>\n                          <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            <div>قيمة: {typeof indicator.value === 'number' ? indicator.value.toFixed(2) : indicator.value}</div>\n                            <div>قوة: {indicator.strength.toFixed(0)}%</div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n\n            {activeTab === 'structure' && (\n              <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                <h5 className=\"font-bold text-gray-900 dark:text-white mb-4\">🏗️ تحليل هيكل السوق</h5>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h6 className=\"font-medium mb-3\">معلومات عامة:</h6>\n                    <div className=\"space-y-2 text-sm\">\n                      <div>الاتجاه: <span className=\"font-medium\">{analysis.marketStructure.trend}</span></div>\n                      <div>المرحلة: <span className=\"font-medium\">{analysis.marketStructure.phase}</span></div>\n                      <div>القوة: <span className=\"font-medium\">{analysis.marketStructure.strength.toFixed(0)}%</span></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <h6 className=\"font-medium mb-3\">المستويات المهمة:</h6>\n                    <div className=\"space-y-1 text-xs\">\n                      <div>مستويات رئيسية: {analysis.marketStructure.keyLevels.length}</div>\n                      <div>مناطق سيولة: {analysis.marketStructure.liquidityZones.length}</div>\n                      <div>مستويات مؤسسية: {analysis.marketStructure.institutionalLevels.length}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'risk' && (\n              <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                <h5 className=\"font-bold text-gray-900 dark:text-white mb-4\">⚠️ تقييم المخاطر</h5>\n\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {Object.entries(analysis.riskAssessment).map(([key, value]) => (\n                    <div key={key} className=\"text-center bg-gray-50 dark:bg-gray-600 rounded p-3\">\n                      <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                        {typeof value === 'number' ? value.toFixed(2) : value}\n                      </div>\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 capitalize\">\n                        {key.replace(/([A-Z])/g, ' $1').trim()}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAiFe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAE/F,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IAErE,MAAM,0BAA0B;QAC9B,eAAe;QAEf,IAAI;YACF,+CAA+C;YAC/C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,aAAa;YAC/B,MAAM,aAAa,cAAc;YAEjC,kCAAkC;YAClC,MAAM,sBAAsB,4BAA4B,WAAW;YACnE,MAAM,aAAa,0BAA0B,WAAW;YACxD,MAAM,kBAAkB,wBAAwB,WAAW;YAC3D,MAAM,iBAAiB,uBAAuB;YAE9C,2BAA2B;YAC3B,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,uBACzD,qBAAqB,YAAY;YAGnC,YAAY;gBACV,QAAQ;gBACR,cAAc,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa,YAAY;gBAC3E;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,8BAA8B,CAAC,WAAmB;QACtD,mBAAmB;QACnB,MAAM,QAA8B;YAClC;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,KAAK,MAAM,KAAK;gBAC5B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBACvD,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;SACD;QAED,sBAAsB;QACtB,MAAM,WAAiC;YACrC;gBACE,MAAM;gBACN,OAAO,KAAK,KAAK,MAAM,KAAK;gBAC5B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,CAAC,KAAK,MAAM,KAAK;gBACxB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;SACD;QAED,wBAAwB;QACxB,MAAM,uBAA6C;YACjD;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;gBAC1D,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;SACD;QAED,oBAAoB;QACpB,MAAM,SAA+B;YACnC;gBACE,MAAM;gBACN,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBACvD,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/B,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;SACD;QAED,gCAAgC;QAChC,MAAM,qBAA2C;YAC/C;gBACE,MAAM;gBACN,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBACvD,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,MAAM,KAAK;gBACvB,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBACvD,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO,KAAK,KAAK,CAAC,YAAY,OAAO;gBACrC,QAAQ;gBACR,UAAU,KAAK,KAAK,MAAM,KAAK;gBAC/B,aAAa;YACf;SACD;QAED,OAAO;YACL;YACA;YACA,YAAY;YACZ;YACA;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC,WAAmB;QACpD,qCAAqC;QACrC,MAAM,MAAuB;YAC3B,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,6BAA6B;QAC7B,MAAM,MAAuB;YAC3B,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,iBAAiB;QACjB,MAAM,UAA2B;YAC/B,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,sBAAsB;QACtB,MAAM,aAA8B;YAClC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,oBAAoB;QACpB,MAAM,WAA4B;YAChC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,qBAAqB;QACrB,MAAM,YAA6B;YACjC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,eAAe;QACf,MAAM,cAA+B;YACnC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,iBAAiB;QACjB,MAAM,gBAAiC;YACrC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,iBAAiB;QACjB,MAAM,gBAAiC;YACrC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,aAAa;QACb,MAAM,YAA6B;YACjC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,YAAY,KAAK,KAAK,MAAM,KAAK;YACjC,OAAO,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACvD,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAC1D,SAAS;gBACP,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;gBAChD,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;aACjD;YACD,YAAY,MAAM,KAAK,MAAM,KAAK;YAClC,WAAW;YACX,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,WAAmB;QAClD,MAAM,SAAS;YAAC;YAAW;YAAa;SAAW;QACnD,MAAM,SAAS;YAAC;YAAgB;YAAU;YAAgB;SAAW;QAErE,OAAO;YACL,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;YACxD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;YACxD,UAAU,KAAK,KAAK,MAAM,KAAK;YAC/B,WAAW,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,IACnC,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAElD,gBAAgB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,IACxC,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YAElD,qBAAqB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,IAC7C,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;QAEpD;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO;YACL,YAAY,aAAa;YACzB,KAAK,aAAa,MAAM,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAClD,aAAa,KAAK,MAAM,KAAK;YAC7B,MAAM,MAAM,KAAK,MAAM,KAAK;YAC5B,aAAa,MAAM,KAAK,MAAM,KAAK;YACnC,aAAa,aAAa,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE;YAClD,SAAS,KAAK,KAAK,MAAM,KAAK;YAC9B,cAAc,MAAM,KAAK,MAAM,KAAK;QACtC;IACF;IAEA,MAAM,yBAAyB,CAAC,YAAiB,YAAiB;QAChE,4BAA4B;QAC5B,MAAM,kBAAkB,OAAO,MAAM,CAAC,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,MAC5D,eAAe,IAAI,MAAM,IAAI,CAAC,IAAI,QAAQ,GAAG,GAAG;QAGlD,MAAM,iBAAiB,OAAO,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,QACpD,eAAe,MAAM,MAAM,IAAI,CAAC,MAAM,UAAU,GAAG,GAAG;QAGxD,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,gBAAgB,MAAM;QAC7F,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,eAAe,MAAM;QAE1F,MAAM,eAAe,AAAC,oBAAoB,MAAQ,mBAAmB;QAErE,IAAI,SAAoE;QACxE,IAAI,eAAe,KAAK,SAAS;aAC5B,IAAI,eAAe,KAAK,SAAS;aACjC,IAAI,eAAe,CAAC,KAAK,SAAS;aAClC,IAAI,eAAe,CAAC,KAAK,SAAS;QAEvC,MAAM,aAAa,KAAK,KAAK,GAAG,CAAC,gBAAgB;QACjD,MAAM,WAAW,KAAK,KAAK,GAAG,CAAC,gBAAgB;QAE/C,MAAM,YAAY;YACf;YACA;YACA,qBAAoC,OAAhB,UAAU,KAAK;YACnC,gBAA+B,OAAhB,UAAU,KAAK;YAC9B,sBAAqD,OAAhC,CAAC,eAAe,GAAG,EAAE,OAAO,CAAC,IAAG;SACvD;QAED,OAAO;YAAE,eAAe;YAAQ;YAAY;YAAU;QAAU;IAClE;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU;YAAC;YAAc;YAAO;YAAW;YAAQ;SAAc;QACvE,OAAO,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO,CAAC;YACrB,KAAK;gBAAe,OAAO,CAAC;YAC5B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAO,UAAU;YAC7D,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;0CAGL,cAAc,qBAAqB;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;gDAAkB,OAAO;;oDACvB,YAAY;oDAAM;oDAAE;;+CADV;;;;;;;;;;;;;;;;0CAOnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACpD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAAgB,OAAO;0DAAK;+CAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;kDACL,6LAAC;kDAAI;;;;;;;;;;;;;;;;;;oBAMV,CAAC,eAAe,CAAC,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,0BACf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,YAAY,SAAS,MAAM;;;;;;kEACvD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,SAAS,MAAM;;;;;;0EAElB,6LAAC;gEAAE,WAAU;;oEACV;oEAAkB;oEAAW,SAAS,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;0DAIxG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,4CAAkF,OAAvC,eAAe,SAAS,aAAa;kEAC9F,cAAc,SAAS,aAAa;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;;4DAAgD;4DACvD,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;4DAAU,SAAS,QAAQ,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKlF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,6LAAC;gDAAG,WAAU;0DACX,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC/B,6LAAC;wDAAW,WAAU;;0EACpB,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;0EACvC,6LAAC;gEAAK,WAAU;0EAAoC;;;;;;;uDAF7C;;;;;;;;;;;;;;;;;;;;;;0CAUjB,6LAAC;gCAAI,WAAW,AAAC,2BAMhB,OALC,SAAS,aAAa,CAAC,QAAQ,CAAC,SAC5B,gHACA,SAAS,aAAa,CAAC,QAAQ,CAAC,UAChC,oGACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,6LAAC;gDAAI,WAAW,AAAC,sEAA4G,OAAvC,eAAe,SAAS,aAAa;;oDACxH,SAAS,aAAa,CAAC,QAAQ,CAAC,UAAU;oDAC1C,SAAS,aAAa,CAAC,QAAQ,CAAC,WAAW;oDAC3C,SAAS,aAAa,KAAK,aAAa;oDACxC,cAAc,SAAS,aAAa;;;;;;;0DAEvC,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;oCAK9D,SAAS,aAAa,KAAK,2BAC1B,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;oDAItF,CAAC;wDACA,kDAAkD;wDAClD,MAAM,eAAe,OAAO,MAAM,CAAC,SAAS,UAAU,EACnD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;wDAEjD,qBACE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA2C;;;;;;8FAC1D,6LAAC;oFAAI,WAAU;8FACZ,aAAa,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sFAGtE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA2C;;;;;;8FAC1D,6LAAC;oFAAI,WAAU;8FACZ,aAAa,QAAQ,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;8EAK3E,6LAAC;oEAAI,WAAU;8EACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACjC,6LAAC;4EAAY,WAAU;;8FACrB,6LAAC;oFAAI,WAAU;;wFAA2C;wFAAK,IAAI;;;;;;;8FACnE,6LAAC;oFAAI,WAAU;8FACZ,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;2EAHhD;;;;;;;;;;8EASd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA2C;;;;;;8FAC1D,6LAAC;oFAAI,WAAU;;wFACZ,aAAa,UAAU,CAAC,OAAO,CAAC;wFAAG;;;;;;;;;;;;;sFAGxC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA2C;;;;;;8FAC1D,6LAAC;oFAAI,WAAU;8FACZ,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;oDAMnC,CAAC;;;;;;;0DAIH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAI;oFAAW,SAAS,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;oFAAG;;;;;;;0FAC9D,6LAAC;;oFAAI;oFAAM,SAAS,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;0FAC/C,6LAAC;;oFAAI;oFAAc,SAAS,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC;oFAAG;;;;;;;0FAClE,6LAAC;;oFAAI;oFAAa,SAAS,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC;oFAAG;;;;;;;;;;;;;;;;;;;0EAIjE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;0FACJ,6LAAC;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQf,SAAS,aAAa,KAAK,2BAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwD;;;;;;kEAGtE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;kDAOZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAuD;;;;;;kEACvE,6LAAC;wDAAK,WAAU;;4DAAmD,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAEpG,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAW,AAAC,gDAGX,OAFC,SAAS,UAAU,GAAG,KAAK,iBAC3B,SAAS,UAAU,GAAG,KAAK,kBAAkB;oDAE/C,OAAO;wDAAE,OAAO,AAAC,GAAsB,OAApB,SAAS,UAAU,EAAC;oDAAG;;;;;;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;oCAKT,CAAC;wCACA,MAAM,eAAe,OAAO,MAAM,CAAC,SAAS,UAAU,EACnD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE;wCAEjD,qBACE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDAAoD;wDAC3C,aAAa,IAAI;;;;;;;8DAExC,6LAAC;oDAAI,WAAU;;wDAA2C;wDAClD,aAAa,UAAU,CAAC,OAAO,CAAC;wDAAG;wDAAU,aAAa,IAAI;wDAAC;wDAAS,aAAa,UAAU,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;oCAIzH,CAAC;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,KAAK;4CAAc,OAAO;4CAAoB,OAAO;wCAAG;wCAC1D;4CAAE,KAAK;4CAAc,OAAO;4CAAe,OAAO;wCAAG;wCACrD;4CAAE,KAAK;4CAAa,OAAO;4CAAkB,OAAO;wCAAE;wCACtD;4CAAE,KAAK;4CAAQ,OAAO;4CAAc,OAAO;wCAAE;qCAC9C,CAAC,GAAG,CAAC,CAAA,oBACJ,6LAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,GAAG;4CACnC,WAAW,AAAC,4CAIX,OAHC,cAAc,IAAI,GAAG,GACjB,sCACA;;gDAGL,IAAI,KAAK;gDAAC;gDAAG,IAAI,KAAK;gDAAC;;2CARnB,IAAI,GAAG;;;;;;;;;;;;;;;4BAenB,cAAc,8BACb,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,SAAS,UAAU,EAAE,GAAG,CAAC;wCAAC,CAAC,KAAK,SAAS;yDACvD,6LAAC;wCAAc,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C,SAAS,IAAI;;;;;;kEACtE,6LAAC;wDAAI,WAAW,AAAC,yCAAwE,OAAhC,eAAe,SAAS,MAAM;kEACpF,cAAc,SAAS,MAAM;;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAI;4DAAM,SAAS,IAAI;;;;;;;kEACxB,6LAAC;;4DAAI;4DAAM,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAC1C,6LAAC;;4DAAI;4DAAO,SAAS,KAAK,CAAC,OAAO,CAAC;;;;;;;kEACnC,6LAAC;;4DAAI;4DAAM,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmB;;;;;;kEAClC,6LAAC;wDAAG,WAAU;kEACX,SAAS,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBAC3C,6LAAC;;oEAAW;oEAAG;;+DAAN;;;;;;;;;;;;;;;;;uCAnBP;;;;;;;;;;;4BA4Bf,cAAc,8BACb,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,SAAS,mBAAmB,EAAE,GAAG,CAAC;wCAAC,CAAC,UAAU,WAAW;yDACvE,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAG,WAAU;;oDAA0D;oDAClE,SAAS,OAAO,CAAC,KAAK;oDAAK;oDAAG,WAAW,MAAM;oDAAC;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,WAAW,kBAC1B,6LAAC;wDAAY,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAuB,UAAU,IAAI;;;;;;kFACpD,6LAAC;wEAAI,WAAW,AAAC,6BAA6D,OAAjC,eAAe,UAAU,MAAM;kFACzE,cAAc,UAAU,MAAM;;;;;;;;;;;;0EAGnC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAI;4EAAO,OAAO,UAAU,KAAK,KAAK,WAAW,UAAU,KAAK,CAAC,OAAO,CAAC,KAAK,UAAU,KAAK;;;;;;;kFAC9F,6LAAC;;4EAAI;4EAAM,UAAU,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;uDATnC;;;;;;;;;;;uCANN;;;;;;;;;;;4BAyBf,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+C;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAI;kFAAS,6LAAC;wEAAK,WAAU;kFAAe,SAAS,eAAe,CAAC,KAAK;;;;;;;;;;;;0EAC3E,6LAAC;;oEAAI;kFAAS,6LAAC;wEAAK,WAAU;kFAAe,SAAS,eAAe,CAAC,KAAK;;;;;;;;;;;;0EAC3E,6LAAC;;oEAAI;kFAAO,6LAAC;wEAAK,WAAU;;4EAAe,SAAS,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;0DAI5F,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAI;oEAAiB,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM;;;;;;;0EAC/D,6LAAC;;oEAAI;oEAAc,SAAS,eAAe,CAAC,cAAc,CAAC,MAAM;;;;;;;0EACjE,6LAAC;;oEAAI;oEAAiB,SAAS,eAAe,CAAC,mBAAmB,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOlF,cAAc,wBACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA+C;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,SAAS,cAAc,EAAE,GAAG,CAAC;gDAAC,CAAC,KAAK,MAAM;iEACxD,6LAAC;gDAAc,WAAU;;kEACvB,6LAAC;wDAAI,WAAU;kEACZ,OAAO,UAAU,WAAW,MAAM,OAAO,CAAC,KAAK;;;;;;kEAElD,6LAAC;wDAAI,WAAU;kEACZ,IAAI,OAAO,CAAC,YAAY,OAAO,IAAI;;;;;;;+CAL9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB9B;GAxkCwB;KAAA", "debugId": null}}]}