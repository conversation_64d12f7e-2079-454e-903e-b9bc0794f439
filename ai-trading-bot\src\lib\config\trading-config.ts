// Trading Bot Configuration
export const TRADING_CONFIG = {
  // Demo Mode Settings
  DEMO_MODE: process.env.DEMO_MODE === 'true' || true,
  DEMO_ACCOUNT_BALANCE: Number(process.env.DEMO_ACCOUNT_BALANCE) || 10000,

  // Risk Management
  RISK_MANAGEMENT: {
    MAX_RISK_PER_TRADE: Number(process.env.MAX_RISK_PER_TRADE) || 2, // 2%
    MAX_DAILY_RISK: Number(process.env.MAX_DAILY_RISK) || 6, // 6%
    MAX_OPEN_TRADES: Number(process.env.MAX_OPEN_TRADES) || 3,
    MIN_RISK_REWARD_RATIO: Number(process.env.MIN_RISK_REWARD_RATIO) || 1.5,
    STOP_LOSS_TYPE: 'PERCENTAGE', // 'PERCENTAGE' | 'ATR' | 'SUPPORT_RESISTANCE'
    TRAILING_STOP_ENABLED: false,
  },

  // AI Settings
  AI: {
    CONFIDENCE_THRESHOLD: Number(process.env.AI_CONFIDENCE_THRESHOLD) || 75,
    PATTERN_RECOGNITION_ENABLED: process.env.PATTERN_RECOGNITION_ENABLED !== 'false',
    AUTO_TRADING_ENABLED: process.env.AUTO_TRADING_ENABLED === 'true' && false, // Disabled for safety
    LEARNING_ENABLED: true,
    MIN_PATTERN_CONFIDENCE: 60,
  },

  // Technical Analysis
  TECHNICAL_ANALYSIS: {
    EMA_PERIOD: 20,
    RSI_PERIOD: 14,
    MACD_FAST: 12,
    MACD_SLOW: 26,
    MACD_SIGNAL: 9,
    SUPERTREND_PERIOD: 10,
    SUPERTREND_MULTIPLIER: 3,
    ATR_PERIOD: 14,
    VOLUME_PROFILE_BINS: 50,
  },

  // Market Data
  MARKET_DATA: {
    UPDATE_INTERVAL: 1000, // 1 second
    CANDLE_UPDATE_INTERVAL: 60000, // 1 minute
    HISTORICAL_CANDLES: 500,
    SUPPORTED_TIMEFRAMES: ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
    DEFAULT_TIMEFRAME: '1h',
  },

  // Symbols Configuration
  SYMBOLS: {
    MAJOR_PAIRS: [
      'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 
      'AUDUSD', 'USDCAD', 'NZDUSD'
    ],
    MINOR_PAIRS: [
      'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 
      'EURCHF', 'AUDCAD', 'GBPAUD', 'GBPCHF'
    ],
    EXOTIC_PAIRS: [
      'USDTRY', 'USDZAR', 'USDMXN', 'USDSEK', 
      'USDNOK', 'USDPLN', 'USDHUF'
    ],
    COMMODITIES: [
      'XAUUSD', 'XAGUSD', 'USOIL', 'UKOIL', 'NATGAS'
    ],
    DEFAULT_SYMBOLS: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],
  },

  // Notifications
  NOTIFICATIONS: {
    TELEGRAM: {
      ENABLED: process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN ? true : false,
      BOT_TOKEN: process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN || '',
      CHAT_ID: process.env.NEXT_PUBLIC_TELEGRAM_CHAT_ID || '',
      RETRY_ATTEMPTS: 3,
      RETRY_DELAY: 5000, // 5 seconds
    },
    EMAIL: {
      ENABLED: process.env.EMAIL_USER ? true : false,
      HOST: process.env.EMAIL_HOST || 'smtp.gmail.com',
      PORT: Number(process.env.EMAIL_PORT) || 587,
      USER: process.env.EMAIL_USER || '',
      PASS: process.env.EMAIL_PASS || '',
      FROM: process.env.EMAIL_FROM || process.env.EMAIL_USER || '',
      TO: process.env.EMAIL_TO?.split(',') || [],
    },
    WEBHOOK: {
      ENABLED: process.env.WEBHOOK_URL ? true : false,
      URL: process.env.WEBHOOK_URL || '',
      SECRET: process.env.WEBHOOK_SECRET || '',
    },
    ALERT_TYPES: {
      TRADE_SIGNALS: true,
      MARKET_UPDATES: true,
      RISK_WARNINGS: true,
      SYSTEM_STATUS: true,
      COUNTDOWN_ALERTS: true,
    },
    COUNTDOWN_TIMES: [5, 3, 1], // Minutes before trade execution
  },

  // Trading Sessions
  TRADING_SESSIONS: {
    ASIAN: {
      START_UTC: 0, // 00:00 UTC
      END_UTC: 9 * 60, // 09:00 UTC
      VOLATILITY: 'LOW',
      LIQUIDITY: 'LOW',
    },
    LONDON: {
      START_UTC: 8 * 60, // 08:00 UTC
      END_UTC: 17 * 60, // 17:00 UTC
      VOLATILITY: 'MEDIUM',
      LIQUIDITY: 'HIGH',
    },
    NEW_YORK: {
      START_UTC: 13 * 60, // 13:00 UTC
      END_UTC: 22 * 60, // 22:00 UTC
      VOLATILITY: 'MEDIUM',
      LIQUIDITY: 'MEDIUM',
    },
    OVERLAP_LONDON_NY: {
      START_UTC: 13 * 60, // 13:00 UTC
      END_UTC: 17 * 60, // 17:00 UTC
      VOLATILITY: 'HIGH',
      LIQUIDITY: 'HIGH',
    },
  },

  // Performance Tracking
  PERFORMANCE: {
    TRACK_SIGNALS: true,
    TRACK_PATTERNS: true,
    TRACK_MARKET_CONDITIONS: true,
    HISTORY_RETENTION_DAYS: 30,
    BACKUP_INTERVAL_HOURS: 24,
  },

  // Logging
  LOGGING: {
    LEVEL: process.env.LOG_LEVEL || 'info',
    CONSOLE_ENABLED: true,
    FILE_ENABLED: false,
    MAX_FILE_SIZE: '10MB',
    MAX_FILES: 5,
  },

  // Security
  SECURITY: {
    API_RATE_LIMIT: 100, // requests per minute
    MAX_CONCURRENT_CONNECTIONS: 10,
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    CORS_ORIGINS: ['http://localhost:3000'],
  },

  // Development
  DEVELOPMENT: {
    MOCK_DATA: process.env.NODE_ENV === 'development',
    DEBUG_MODE: process.env.NODE_ENV === 'development',
    HOT_RELOAD: true,
    VERBOSE_LOGGING: process.env.NODE_ENV === 'development',
  },

  // API Endpoints
  API_ENDPOINTS: {
    TRADINGVIEW: 'https://api.tradingview.com',
    TWELVE_DATA: 'https://api.twelvedata.com',
    FOREX_FACTORY: 'https://www.forexfactory.com',
    INVESTING_COM: 'https://api.investing.com',
  },

  // API Keys
  API_KEYS: {
    TRADINGVIEW: process.env.TRADINGVIEW_API_KEY || '',
    TWELVE_DATA: process.env.TWELVE_DATA_API_KEY || '',
    ALPHA_VANTAGE: process.env.ALPHA_VANTAGE_API_KEY || '',
    POLYGON: process.env.POLYGON_API_KEY || '',
  },

  // Database (if needed)
  DATABASE: {
    URL: process.env.DATABASE_URL || '',
    MAX_CONNECTIONS: 10,
    CONNECTION_TIMEOUT: 30000,
    QUERY_TIMEOUT: 10000,
  },

  // Cache Settings
  CACHE: {
    ENABLED: true,
    TTL_QUOTES: 5000, // 5 seconds
    TTL_INDICATORS: 60000, // 1 minute
    TTL_PATTERNS: 300000, // 5 minutes
    TTL_MARKET_STATE: 60000, // 1 minute
    MAX_SIZE: 1000, // Maximum cached items
  },

  // Error Handling
  ERROR_HANDLING: {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000, // 1 second
    CIRCUIT_BREAKER_THRESHOLD: 5,
    CIRCUIT_BREAKER_TIMEOUT: 60000, // 1 minute
    FALLBACK_ENABLED: true,
  },

  // Feature Flags
  FEATURES: {
    ADVANCED_PATTERNS: true,
    SENTIMENT_ANALYSIS: true,
    NEWS_INTEGRATION: false,
    SOCIAL_TRADING: false,
    BACKTESTING: false,
    PAPER_TRADING: true,
    LIVE_TRADING: false, // Disabled for safety
  },
};

// Validation function
export function validateConfig(): boolean {
  const config = TRADING_CONFIG;
  
  // Validate risk management settings
  if (config.RISK_MANAGEMENT.MAX_RISK_PER_TRADE > 10) {
    console.warn('⚠️ MAX_RISK_PER_TRADE is set above 10%. This is very risky!');
  }
  
  if (config.RISK_MANAGEMENT.MAX_DAILY_RISK > 20) {
    console.warn('⚠️ MAX_DAILY_RISK is set above 20%. This is extremely risky!');
  }
  
  // Validate AI settings
  if (config.AI.CONFIDENCE_THRESHOLD < 50) {
    console.warn('⚠️ AI_CONFIDENCE_THRESHOLD is below 50%. This may generate too many signals.');
  }
  
  // Validate demo mode
  if (!config.DEMO_MODE && config.FEATURES.LIVE_TRADING) {
    console.error('❌ Live trading is enabled! Make sure this is intentional.');
    return false;
  }
  
  return true;
}

// Export individual configurations for easier imports
export const RISK_CONFIG = TRADING_CONFIG.RISK_MANAGEMENT;
export const AI_CONFIG = TRADING_CONFIG.AI;
export const SYMBOLS_CONFIG = TRADING_CONFIG.SYMBOLS;
export const NOTIFICATIONS_CONFIG = TRADING_CONFIG.NOTIFICATIONS;
export const TECHNICAL_CONFIG = TRADING_CONFIG.TECHNICAL_ANALYSIS;

// Initialize configuration validation
if (typeof window === 'undefined') {
  // Only run on server side
  validateConfig();
}
