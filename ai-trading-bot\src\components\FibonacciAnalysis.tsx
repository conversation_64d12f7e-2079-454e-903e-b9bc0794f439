'use client';

import { useState, useEffect } from 'react';

interface FibonacciLevel {
  level: number;
  price: number;
  percentage: string;
  type: 'support' | 'resistance';
  strength: number;
}

interface FibonacciAnalysis {
  symbol: string;
  high: number;
  low: number;
  direction: 'bullish' | 'bearish';
  levels: FibonacciLevel[];
  currentPrice: number;
  nearestLevel: FibonacciLevel;
  recommendation: string;
  confidence: number;
}

export default function FibonacciAnalysis() {
  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');
  const [analysis, setAnalysis] = useState<FibonacciAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [timeframe, setTimeframe] = useState<string>('1h');

  const forexPairs = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'
  ];

  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];

  const fibonacciLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1, 1.272, 1.414, 1.618, 2.618];

  const runFibonacciAnalysis = async () => {
    setIsAnalyzing(true);
    
    try {
      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const basePrice = getBasePrice(selectedPair);
      const volatility = getVolatility(selectedPair);
      
      // Generate swing high and low
      const swingRange = basePrice * volatility * 0.05;
      const high = basePrice + swingRange * (0.5 + Math.random() * 0.5);
      const low = basePrice - swingRange * (0.5 + Math.random() * 0.5);
      
      const direction = Math.random() > 0.5 ? 'bullish' : 'bearish';
      const currentPrice = basePrice + (Math.random() - 0.5) * swingRange * 0.3;
      
      // Calculate Fibonacci levels
      const levels: FibonacciLevel[] = fibonacciLevels.map(level => {
        const price = direction === 'bullish' 
          ? high - (high - low) * level
          : low + (high - low) * level;
          
        const distanceFromCurrent = Math.abs(price - currentPrice);
        const strength = Math.max(20, 100 - (distanceFromCurrent / basePrice) * 1000);
        
        return {
          level,
          price,
          percentage: `${(level * 100).toFixed(1)}%`,
          type: price > currentPrice ? 'resistance' : 'support',
          strength: Math.min(strength, 95)
        };
      });
      
      // Find nearest level
      const nearestLevel = levels.reduce((nearest, current) => {
        const nearestDistance = Math.abs(nearest.price - currentPrice);
        const currentDistance = Math.abs(current.price - currentPrice);
        return currentDistance < nearestDistance ? current : nearest;
      });
      
      // Generate recommendation
      const distanceToNearest = Math.abs(nearestLevel.price - currentPrice) / basePrice * 100;
      let recommendation = '';
      let confidence = 0;
      
      if (distanceToNearest < 0.1) {
        if (nearestLevel.type === 'support' && direction === 'bullish') {
          recommendation = 'شراء قوي - السعر عند مستوى دعم فيبوناتشي قوي';
          confidence = 85 + Math.random() * 10;
        } else if (nearestLevel.type === 'resistance' && direction === 'bearish') {
          recommendation = 'بيع قوي - السعر عند مستوى مقاومة فيبوناتشي قوي';
          confidence = 85 + Math.random() * 10;
        } else {
          recommendation = 'انتظار - السعر عند مستوى فيبوناتشي مهم';
          confidence = 70 + Math.random() * 15;
        }
      } else if (distanceToNearest < 0.2) {
        recommendation = `مراقبة - السعر يقترب من مستوى ${nearestLevel.percentage}`;
        confidence = 60 + Math.random() * 20;
      } else {
        recommendation = 'لا توجد إشارة واضحة - السعر بعيد عن مستويات فيبوناتشي';
        confidence = 40 + Math.random() * 20;
      }
      
      setAnalysis({
        symbol: selectedPair,
        high,
        low,
        direction,
        levels,
        currentPrice,
        nearestLevel,
        recommendation,
        confidence
      });
      
    } catch (error) {
      console.error('Fibonacci Analysis Error:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getBasePrice = (symbol: string): number => {
    const prices: { [key: string]: number } = {
      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,
      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,
      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,
      'USOIL': 78.50, 'BTCUSD': 43250.00
    };
    return prices[symbol] || 1.0000;
  };

  const getVolatility = (symbol: string): number => {
    const volatilities: { [key: string]: number } = {
      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,
      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,
      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,
      'USOIL': 3.0, 'BTCUSD': 4.0
    };
    return volatilities[symbol] || 1.0;
  };

  const getPairFlag = (symbol: string): string => {
    const flags: { [key: string]: string } = {
      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',
      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',
      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',
      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'
    };
    return flags[symbol] || '💱';
  };

  const getLevelColor = (level: FibonacciLevel, currentPrice: number): string => {
    const distance = Math.abs(level.price - currentPrice) / currentPrice * 100;
    
    if (distance < 0.1) return 'bg-red-100 border-red-500 text-red-800'; // Very close
    if (distance < 0.2) return 'bg-yellow-100 border-yellow-500 text-yellow-800'; // Close
    if (level.type === 'support') return 'bg-green-100 border-green-500 text-green-800';
    return 'bg-blue-100 border-blue-500 text-blue-800';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            📐 تحليل فيبوناتشي المتقدم
            <span className="mr-3 px-2 py-1 bg-yellow-600 text-white rounded text-sm">
              أداة احترافية
            </span>
          </h3>
          <button
            onClick={runFibonacciAnalysis}
            disabled={isAnalyzing}
            className={`px-6 py-2 rounded-lg font-medium transition-colors ${
              isAnalyzing 
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-yellow-600 text-white hover:bg-yellow-700'
            }`}
          >
            {isAnalyzing ? '📐 جاري التحليل...' : '🚀 تحليل فيبوناتشي'}
          </button>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              زوج العملة:
            </label>
            <select
              value={selectedPair}
              onChange={(e) => setSelectedPair(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {forexPairs.map(pair => (
                <option key={pair} value={pair}>
                  {getPairFlag(pair)} {pair}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الإطار الزمني:
            </label>
            <select
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {timeframes.map(tf => (
                <option key={tf} value={tf}>{tf}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Loading State */}
        {isAnalyzing && (
          <div className="text-center py-12">
            <div className="inline-flex items-center space-x-3 space-x-reverse">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
              <span className="text-lg text-gray-600 dark:text-gray-400">
                🔍 تحليل مستويات فيبوناتشي لـ {selectedPair}...
              </span>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              جاري حساب مستويات الدعم والمقاومة الذهبية
            </div>
          </div>
        )}

        {/* No Analysis Yet */}
        {!isAnalyzing && !analysis && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📐</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              تحليل فيبوناتشي الاحترافي
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              اكتشف مستويات الدعم والمقاومة الذهبية باستخدام نسب فيبوناتشي المتقدمة
            </p>
            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 max-w-lg mx-auto">
              <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                💡 ما هو تحليل فيبوناتشي؟
              </h4>
              <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1 text-right">
                <li>• مستويات رياضية دقيقة للدعم والمقاومة</li>
                <li>• نسب ذهبية مستخدمة من قبل المحترفين</li>
                <li>• تحديد نقاط الدخول والخروج المثلى</li>
                <li>• دقة عالية في التنبؤ بحركة الأسعار</li>
              </ul>
            </div>
          </div>
        )}

        {/* Analysis Results */}
        {!isAnalyzing && analysis && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <span className="text-2xl">{getPairFlag(analysis.symbol)}</span>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white">
                      {analysis.symbol} - {timeframe}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      اتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold text-gray-900 dark:text-white">
                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">السعر الحالي</div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">
                    {analysis.high.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">أعلى سعر</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-600">
                    {analysis.low.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">أقل سعر</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">
                    {analysis.confidence.toFixed(0)}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">مستوى الثقة</div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-700 rounded p-3">
                <h5 className="font-medium text-gray-900 dark:text-white mb-2">📋 التوصية:</h5>
                <p className="text-sm text-gray-700 dark:text-gray-300">{analysis.recommendation}</p>
              </div>
            </div>

            {/* Fibonacci Levels */}
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📐 مستويات فيبوناتشي
              </h4>
              
              <div className="space-y-2">
                {analysis.levels.map((level, index) => (
                  <div 
                    key={index}
                    className={`border-2 rounded-lg p-3 transition-all duration-200 ${getLevelColor(level, analysis.currentPrice)}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="text-lg font-bold">
                          {level.percentage}
                        </div>
                        <div>
                          <div className="font-medium">
                            {level.price.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}
                          </div>
                          <div className="text-xs">
                            {level.type === 'support' ? '🟢 دعم' : '🔴 مقاومة'}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          قوة: {level.strength.toFixed(0)}%
                        </div>
                        <div className="text-xs">
                          المسافة: {(Math.abs(level.price - analysis.currentPrice) / analysis.currentPrice * 100).toFixed(2)}%
                        </div>
                      </div>
                    </div>
                    
                    {level === analysis.nearestLevel && (
                      <div className="mt-2 pt-2 border-t border-current">
                        <div className="text-xs font-medium">
                          ⭐ أقرب مستوى فيبوناتشي للسعر الحالي
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Trading Suggestions */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                💡 اقتراحات التداول
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-green-600 mb-2">🎯 نقاط الدخول المحتملة:</h5>
                  <ul className="text-sm space-y-1">
                    {analysis.levels
                      .filter(l => l.type === 'support' && l.strength > 70)
                      .slice(0, 3)
                      .map((level, i) => (
                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>
                      ))}
                  </ul>
                </div>

                <div className="bg-white dark:bg-gray-700 rounded p-3">
                  <h5 className="font-medium text-red-600 mb-2">🛑 مستويات وقف الخسارة:</h5>
                  <ul className="text-sm space-y-1">
                    {analysis.levels
                      .filter(l => l.type === 'resistance' && l.strength > 70)
                      .slice(0, 3)
                      .map((level, i) => (
                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>
                      ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Final Fibonacci Conclusion */}
            <div className={`rounded-xl p-6 border-4 ${
              analysis.recommendation.includes('شراء')
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'
                : analysis.recommendation.includes('بيع')
                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'
                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'
            }`}>
              <div className="text-center mb-6">
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  📐 خلاصة تحليل فيبوناتشي
                </h3>
                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${
                  analysis.recommendation.includes('شراء') ? 'bg-green-600 text-white' :
                  analysis.recommendation.includes('بيع') ? 'bg-red-600 text-white' :
                  'bg-yellow-600 text-white'
                }`}>
                  {analysis.recommendation.includes('شراء') && '📈 '}
                  {analysis.recommendation.includes('بيع') && '📉 '}
                  {!analysis.recommendation.includes('شراء') && !analysis.recommendation.includes('بيع') && '➡️ '}
                  {analysis.recommendation.includes('شراء قوي') ? 'شراء قوي' :
                   analysis.recommendation.includes('بيع قوي') ? 'بيع قوي' :
                   analysis.recommendation.includes('شراء') ? 'شراء' :
                   analysis.recommendation.includes('بيع') ? 'بيع' : 'انتظار'}
                </div>
                <p className="text-lg text-gray-600 dark:text-gray-400 mt-2">
                  بناءً على تحليل مستويات فيبوناتشي الذهبية والنسب المقدسة
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Fibonacci Setup */}
                <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                    📐 إعداد فيبوناتشي المقترح
                  </h4>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center">
                        <div className="text-sm text-gray-600 dark:text-gray-400">أقرب مستوى</div>
                        <div className="text-lg font-bold text-blue-600">
                          {analysis.nearestLevel.percentage}
                        </div>
                        <div className="text-sm text-gray-500">
                          {analysis.nearestLevel.price.toFixed(5)}
                        </div>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-3 text-center">
                        <div className="text-sm text-gray-600 dark:text-gray-400">قوة المستوى</div>
                        <div className="text-lg font-bold text-purple-600">
                          {analysis.nearestLevel.strength.toFixed(0)}%
                        </div>
                        <div className="text-sm text-gray-500">
                          {analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة'}
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded p-3">
                      <div className="text-center">
                        <div className="text-sm text-gray-600 dark:text-gray-400">مستوى الثقة</div>
                        <div className="text-xl font-bold text-yellow-600">
                          {analysis.confidence.toFixed(0)}%
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded p-3">
                      <h5 className="font-medium text-amber-800 dark:text-amber-200 mb-2">
                        🔢 المستويات الذهبية النشطة:
                      </h5>
                      <div className="grid grid-cols-3 gap-1 text-xs">
                        {analysis.levels
                          .filter(l => l.strength > 70)
                          .slice(0, 6)
                          .map((level, i) => (
                            <div key={i} className="text-center bg-white dark:bg-gray-600 rounded p-1">
                              <div className="font-bold">{level.percentage}</div>
                              <div className="text-gray-500">{level.strength.toFixed(0)}%</div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Fibonacci Analysis */}
                <div className="bg-white dark:bg-gray-700 rounded-lg p-6">
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                    🌟 تحليل فيبوناتشي المتقدم
                  </h4>

                  <div className="space-y-4">
                    <div className="bg-green-50 dark:bg-green-900/20 rounded p-4">
                      <h5 className="font-medium text-green-800 dark:text-green-200 mb-2">
                        📊 إحصائيات المستويات:
                      </h5>
                      <div className="text-sm space-y-1">
                        <div>إجمالي المستويات: {analysis.levels.length}</div>
                        <div>مستويات قوية: {analysis.levels.filter(l => l.strength > 80).length}</div>
                        <div>مستويات دعم: {analysis.levels.filter(l => l.type === 'support').length}</div>
                        <div>مستويات مقاومة: {analysis.levels.filter(l => l.type === 'resistance').length}</div>
                      </div>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-4">
                      <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                        🎯 تحليل الاتجاه:
                      </h5>
                      <div className="text-sm">
                        الاتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}<br/>
                        أعلى سعر: {analysis.high.toFixed(5)}<br/>
                        أقل سعر: {analysis.low.toFixed(5)}<br/>
                        المدى: {((analysis.high - analysis.low) / analysis.low * 100).toFixed(2)}%
                      </div>
                    </div>

                    <div className="bg-orange-50 dark:bg-orange-900/20 rounded p-4">
                      <h5 className="font-medium text-orange-800 dark:text-orange-200 mb-2">
                        💡 نصائح فيبوناتشي:
                      </h5>
                      <ul className="text-sm space-y-1">
                        <li>• ركز على مستويات 61.8% و 78.6%</li>
                        <li>• انتظر تأكيد الكسر أو الارتداد</li>
                        <li>• استخدم مستويات متعددة للتأكيد</li>
                        <li>• راقب التقارب مع مؤشرات أخرى</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Confidence Meter */}
              <div className="mt-6 bg-white dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">مستوى الثقة في تحليل فيبوناتشي:</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">{analysis.confidence.toFixed(0)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-500 ${
                      analysis.confidence > 80 ? 'bg-green-500' :
                      analysis.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${analysis.confidence}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>منخفض</span>
                  <span>متوسط</span>
                  <span>عالي</span>
                </div>
              </div>

              {/* Key Recommendation */}
              <div className="mt-4 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 rounded-lg p-4">
                <h5 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                  🎯 التوصية الرئيسية:
                </h5>
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  {analysis.recommendation}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
