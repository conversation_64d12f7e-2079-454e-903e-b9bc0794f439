// Real Telegram Bot Integration
import { TradeSignal } from '../types/trading';
import { MarketState } from '../types/market';

export interface TelegramConfig {
  botToken: string;
  chatId: string;
  enabled: boolean;
}

export interface TelegramMessage {
  text: string;
  parseMode?: 'HTML' | 'Markdown';
  disableWebPagePreview?: boolean;
  replyMarkup?: any;
}

export class TelegramBot {
  private config: TelegramConfig;
  private apiUrl: string;

  constructor(config: TelegramConfig) {
    this.config = config;
    this.apiUrl = `https://api.telegram.org/bot${config.botToken}`;
  }

  // Send trade signal to Telegram
  async sendTradeSignal(signal: TradeSignal, marketState: MarketState): Promise<boolean> {
    if (!this.config.enabled || !this.config.botToken) {
      console.log('📱 Telegram disabled - signal not sent');
      return false;
    }

    const message = this.formatTradeSignalMessage(signal, marketState);
    return await this.sendMessage(message);
  }

  // Send countdown alert
  async sendCountdownAlert(signal: TradeSignal, minutes: number): Promise<boolean> {
    if (!this.config.enabled) return false;

    const urgencyEmoji = minutes === 1 ? '🚨' : minutes <= 3 ? '⏰' : '⏳';
    const urgency = minutes === 1 ? 'IMMEDIATE' : minutes <= 3 ? 'URGENT' : 'UPCOMING';
    
    const message: TelegramMessage = {
      text: `
${urgencyEmoji} <b>${urgency} TRADE EXECUTION</b>

🎯 <b>Signal:</b> ${signal.type} ${signal.symbol}
⏰ <b>Time:</b> ${minutes} minute${minutes > 1 ? 's' : ''} remaining
💰 <b>Entry:</b> ${signal.entry}
🛑 <b>Stop Loss:</b> ${signal.stopLoss}
🎯 <b>Take Profit:</b> ${signal.takeProfit1}
📈 <b>R/R:</b> ${signal.riskRewardRatio.toFixed(2)}:1
🎲 <b>Confidence:</b> ${signal.confidence.toFixed(1)}%

${minutes === 1 ? '🚨 <b>EXECUTE NOW!</b>' : `⏳ Prepare for execution in ${minutes} minutes`}
      `.trim(),
      parseMode: 'HTML'
    };

    return await this.sendMessage(message);
  }

  // Send risk alert
  async sendRiskAlert(message: string, level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'): Promise<boolean> {
    if (!this.config.enabled) return false;

    const emoji = {
      'LOW': '💚',
      'MEDIUM': '💛', 
      'HIGH': '🧡',
      'CRITICAL': '🔴'
    };

    const alertMessage: TelegramMessage = {
      text: `
${emoji[level]} <b>RISK ALERT - ${level}</b>

⚠️ ${message}

📊 <b>Current Status:</b>
🕐 Time: ${new Date().toLocaleString()}
🤖 Bot: Active & Monitoring
      `.trim(),
      parseMode: 'HTML'
    };

    return await this.sendMessage(alertMessage);
  }

  // Send market update
  async sendMarketUpdate(marketData: any[]): Promise<boolean> {
    if (!this.config.enabled) return false;

    let text = '📊 <b>MARKET UPDATE</b>\n\n';
    
    marketData.forEach(market => {
      const trendEmoji = market.trend === 'BULLISH' ? '📈' : market.trend === 'BEARISH' ? '📉' : '➡️';
      const changeEmoji = market.changePercent >= 0 ? '🟢' : '🔴';
      
      text += `${trendEmoji} <b>${market.symbol}</b>\n`;
      text += `💰 ${market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}\n`;
      text += `${changeEmoji} ${market.changePercent >= 0 ? '+' : ''}${market.changePercent.toFixed(2)}%\n`;
      text += `📊 ${market.session} | ${market.riskLevel} Risk\n\n`;
    });

    text += `🕐 <i>Updated: ${new Date().toLocaleString()}</i>`;

    const message: TelegramMessage = {
      text,
      parseMode: 'HTML'
    };

    return await this.sendMessage(message);
  }

  // Format trade signal message
  private formatTradeSignalMessage(signal: TradeSignal, marketState: MarketState): TelegramMessage {
    const confidenceEmoji = signal.confidence >= 85 ? '🔥' : signal.confidence >= 75 ? '⚡' : '📊';
    const trendEmoji = marketState.trend === 'BULLISH' ? '📈' : marketState.trend === 'BEARISH' ? '📉' : '➡️';
    
    return {
      text: `
${confidenceEmoji} <b>NEW TRADING SIGNAL</b> ${trendEmoji}

🎯 <b>${signal.type} ${signal.symbol}</b>
🎲 <b>Confidence:</b> ${signal.confidence.toFixed(1)}%

💰 <b>ENTRY:</b> ${signal.entry}
🛑 <b>STOP LOSS:</b> ${signal.stopLoss}
🎯 <b>TAKE PROFITS:</b>
   TP1: ${signal.takeProfit1}
   TP2: ${signal.takeProfit2 || 'N/A'}
   TP3: ${signal.takeProfit3 || 'N/A'}

📈 <b>R/R RATIO:</b> ${signal.riskRewardRatio.toFixed(2)}:1

📊 <b>MARKET CONDITIONS:</b>
📈 Trend: ${marketState.trend} (${marketState.strength}%)
⚡ Momentum: ${marketState.momentum}
🕐 Session: ${marketState.session.name}
⚠️ Risk: ${marketState.riskLevel}

🧠 <b>AI ANALYSIS:</b>
${signal.reasoning.slice(0, 3).map(reason => `• ${reason}`).join('\n')}

🤖 <i>AI Trading Bot | ${new Date().toLocaleString()}</i>
      `.trim(),
      parseMode: 'HTML',
      disableWebPagePreview: true
    };
  }

  // Send message to Telegram
  private async sendMessage(message: TelegramMessage): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: this.config.chatId,
          text: message.text,
          parse_mode: message.parseMode || 'HTML',
          disable_web_page_preview: message.disableWebPagePreview || true,
          reply_markup: message.replyMarkup
        })
      });

      const result = await response.json();
      
      if (result.ok) {
        console.log('✅ Telegram message sent successfully');
        return true;
      } else {
        console.error('❌ Telegram API error:', result.description);
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to send Telegram message:', error);
      return false;
    }
  }

  // Test connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/getMe`);
      const result = await response.json();
      
      if (result.ok) {
        console.log('✅ Telegram bot connected:', result.result.username);
        return true;
      } else {
        console.error('❌ Telegram bot connection failed:', result.description);
        return false;
      }
    } catch (error) {
      console.error('❌ Telegram connection test failed:', error);
      return false;
    }
  }

  // Send startup message
  async sendStartupMessage(): Promise<boolean> {
    const message: TelegramMessage = {
      text: `
🤖 <b>AI TRADING BOT STARTED</b>

✅ <b>Status:</b> Online & Active
🕐 <b>Started:</b> ${new Date().toLocaleString()}
📊 <b>Mode:</b> Demo Trading
🛡️ <b>Risk Management:</b> Active

🎯 <b>Monitoring:</b>
• EUR/USD, GBP/USD, USD/JPY, XAU/USD
• ICT Smart Money Concepts
• Advanced Technical Analysis
• Real-time Market Conditions

📱 <b>Alerts:</b> Enabled
🔔 You will receive notifications for:
• New trading signals
• Risk alerts
• Market updates
• System status

<i>Ready to analyze markets and generate signals!</i>
      `.trim(),
      parseMode: 'HTML'
    };

    return await this.sendMessage(message);
  }

  // Send shutdown message
  async sendShutdownMessage(): Promise<boolean> {
    const message: TelegramMessage = {
      text: `
🤖 <b>AI TRADING BOT STOPPED</b>

❌ <b>Status:</b> Offline
🕐 <b>Stopped:</b> ${new Date().toLocaleString()}

📊 <b>Session Summary:</b>
• Monitoring completed
• All positions reviewed
• Risk management applied

🔔 <b>Alerts:</b> Disabled until restart

<i>Bot will resume monitoring when restarted.</i>
      `.trim(),
      parseMode: 'HTML'
    };

    return await this.sendMessage(message);
  }
}
