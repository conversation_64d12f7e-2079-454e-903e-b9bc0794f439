{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/FibonacciAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface FibonacciLevel {\n  level: number;\n  price: number;\n  percentage: string;\n  type: 'support' | 'resistance';\n  strength: number;\n}\n\ninterface FibonacciAnalysis {\n  symbol: string;\n  high: number;\n  low: number;\n  direction: 'bullish' | 'bearish';\n  levels: FibonacciLevel[];\n  currentPrice: number;\n  nearestLevel: FibonacciLevel;\n  recommendation: string;\n  confidence: number;\n}\n\nexport default function FibonacciAnalysis() {\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [analysis, setAnalysis] = useState<FibonacciAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [timeframe, setTimeframe] = useState<string>('1h');\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n\n  const fibonacciLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1, 1.272, 1.414, 1.618, 2.618];\n\n  const runFibonacciAnalysis = async () => {\n    setIsAnalyzing(true);\n    \n    try {\n      // Simulate analysis delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const basePrice = getBasePrice(selectedPair);\n      const volatility = getVolatility(selectedPair);\n      \n      // Generate swing high and low\n      const swingRange = basePrice * volatility * 0.05;\n      const high = basePrice + swingRange * (0.5 + Math.random() * 0.5);\n      const low = basePrice - swingRange * (0.5 + Math.random() * 0.5);\n      \n      const direction = Math.random() > 0.5 ? 'bullish' : 'bearish';\n      const currentPrice = basePrice + (Math.random() - 0.5) * swingRange * 0.3;\n      \n      // Calculate Fibonacci levels\n      const levels: FibonacciLevel[] = fibonacciLevels.map(level => {\n        const price = direction === 'bullish' \n          ? high - (high - low) * level\n          : low + (high - low) * level;\n          \n        const distanceFromCurrent = Math.abs(price - currentPrice);\n        const strength = Math.max(20, 100 - (distanceFromCurrent / basePrice) * 1000);\n        \n        return {\n          level,\n          price,\n          percentage: `${(level * 100).toFixed(1)}%`,\n          type: price > currentPrice ? 'resistance' : 'support',\n          strength: Math.min(strength, 95)\n        };\n      });\n      \n      // Find nearest level\n      const nearestLevel = levels.reduce((nearest, current) => {\n        const nearestDistance = Math.abs(nearest.price - currentPrice);\n        const currentDistance = Math.abs(current.price - currentPrice);\n        return currentDistance < nearestDistance ? current : nearest;\n      });\n      \n      // Generate recommendation\n      const distanceToNearest = Math.abs(nearestLevel.price - currentPrice) / basePrice * 100;\n      let recommendation = '';\n      let confidence = 0;\n      \n      if (distanceToNearest < 0.1) {\n        if (nearestLevel.type === 'support' && direction === 'bullish') {\n          recommendation = 'شراء قوي - السعر عند مستوى دعم فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else if (nearestLevel.type === 'resistance' && direction === 'bearish') {\n          recommendation = 'بيع قوي - السعر عند مستوى مقاومة فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else {\n          recommendation = 'انتظار - السعر عند مستوى فيبوناتشي مهم';\n          confidence = 70 + Math.random() * 15;\n        }\n      } else if (distanceToNearest < 0.2) {\n        recommendation = `مراقبة - السعر يقترب من مستوى ${nearestLevel.percentage}`;\n        confidence = 60 + Math.random() * 20;\n      } else {\n        recommendation = 'لا توجد إشارة واضحة - السعر بعيد عن مستويات فيبوناتشي';\n        confidence = 40 + Math.random() * 20;\n      }\n      \n      setAnalysis({\n        symbol: selectedPair,\n        high,\n        low,\n        direction,\n        levels,\n        currentPrice,\n        nearestLevel,\n        recommendation,\n        confidence\n      });\n      \n    } catch (error) {\n      console.error('Fibonacci Analysis Error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,\n      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,\n      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,\n      'USOIL': 3.0, 'BTCUSD': 4.0\n    };\n    return volatilities[symbol] || 1.0;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getLevelColor = (level: FibonacciLevel, currentPrice: number): string => {\n    const distance = Math.abs(level.price - currentPrice) / currentPrice * 100;\n    \n    if (distance < 0.1) return 'bg-red-100 border-red-500 text-red-800'; // Very close\n    if (distance < 0.2) return 'bg-yellow-100 border-yellow-500 text-yellow-800'; // Close\n    if (level.type === 'support') return 'bg-green-100 border-green-500 text-green-800';\n    return 'bg-blue-100 border-blue-500 text-blue-800';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📐 تحليل فيبوناتشي المتقدم\n            <span className=\"mr-3 px-2 py-1 bg-yellow-600 text-white rounded text-sm\">\n              أداة احترافية\n            </span>\n          </h3>\n          <button\n            onClick={runFibonacciAnalysis}\n            disabled={isAnalyzing}\n            className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n              isAnalyzing \n                ? 'bg-gray-400 text-white cursor-not-allowed'\n                : 'bg-yellow-600 text-white hover:bg-yellow-700'\n            }`}\n          >\n            {isAnalyzing ? '📐 جاري التحليل...' : '🚀 تحليل فيبوناتشي'}\n          </button>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              زوج العملة:\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>\n                  {getPairFlag(pair)} {pair}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <select\n              value={timeframe}\n              onChange={(e) => setTimeframe(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {timeframes.map(tf => (\n                <option key={tf} value={tf}>{tf}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isAnalyzing && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🔍 تحليل مستويات فيبوناتشي لـ {selectedPair}...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500\">\n              جاري حساب مستويات الدعم والمقاومة الذهبية\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && !analysis && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📐</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              تحليل فيبوناتشي الاحترافي\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اكتشف مستويات الدعم والمقاومة الذهبية باستخدام نسب فيبوناتشي المتقدمة\n            </p>\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                💡 ما هو تحليل فيبوناتشي؟\n              </h4>\n              <ul className=\"text-sm text-yellow-800 dark:text-yellow-200 space-y-1 text-right\">\n                <li>• مستويات رياضية دقيقة للدعم والمقاومة</li>\n                <li>• نسب ذهبية مستخدمة من قبل المحترفين</li>\n                <li>• تحديد نقاط الدخول والخروج المثلى</li>\n                <li>• دقة عالية في التنبؤ بحركة الأسعار</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isAnalyzing && analysis && (\n          <div className=\"space-y-6\">\n            {/* Summary */}\n            <div className=\"bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <span className=\"text-2xl\">{getPairFlag(analysis.symbol)}</span>\n                  <div>\n                    <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {analysis.symbol} - {timeframe}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      اتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">السعر الحالي</div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-600\">\n                    {analysis.high.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أعلى سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-red-600\">\n                    {analysis.low.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أقل سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">\n                    {analysis.confidence.toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">مستوى الثقة</div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">📋 التوصية:</h5>\n                <p className=\"text-sm text-gray-700 dark:text-gray-300\">{analysis.recommendation}</p>\n              </div>\n            </div>\n\n            {/* Fibonacci Levels */}\n            <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                📐 مستويات فيبوناتشي\n              </h4>\n              \n              <div className=\"space-y-2\">\n                {analysis.levels.map((level, index) => (\n                  <div \n                    key={index}\n                    className={`border-2 rounded-lg p-3 transition-all duration-200 ${getLevelColor(level, analysis.currentPrice)}`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"text-lg font-bold\">\n                          {level.percentage}\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">\n                            {level.price.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                          </div>\n                          <div className=\"text-xs\">\n                            {level.type === 'support' ? '🟢 دعم' : '🔴 مقاومة'}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium\">\n                          قوة: {level.strength.toFixed(0)}%\n                        </div>\n                        <div className=\"text-xs\">\n                          المسافة: {(Math.abs(level.price - analysis.currentPrice) / analysis.currentPrice * 100).toFixed(2)}%\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {level === analysis.nearestLevel && (\n                      <div className=\"mt-2 pt-2 border-t border-current\">\n                        <div className=\"text-xs font-medium\">\n                          ⭐ أقرب مستوى فيبوناتشي للسعر الحالي\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Trading Suggestions */}\n            <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                💡 اقتراحات التداول\n              </h4>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-green-600 mb-2\">🎯 نقاط الدخول المحتملة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'support' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={`support-level-${level.percentage}-${i}`}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-red-600 mb-2\">🛑 مستويات وقف الخسارة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'resistance' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={`resistance-level-${level.percentage}-${i}`}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            {/* Final Fibonacci Conclusion */}\n            <div className={`rounded-xl p-6 border-4 ${\n              analysis.recommendation.includes('شراء')\n                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20'\n                : analysis.recommendation.includes('بيع')\n                ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20'\n                : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'\n            }`}>\n              <div className=\"text-center mb-6\">\n                <h3 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                  📐 خلاصة تحليل فيبوناتشي\n                </h3>\n                <div className={`inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${\n                  analysis.recommendation.includes('شراء') ? 'bg-green-600 text-white' :\n                  analysis.recommendation.includes('بيع') ? 'bg-red-600 text-white' :\n                  'bg-yellow-600 text-white'\n                }`}>\n                  {analysis.recommendation.includes('شراء') && '📈 '}\n                  {analysis.recommendation.includes('بيع') && '📉 '}\n                  {!analysis.recommendation.includes('شراء') && !analysis.recommendation.includes('بيع') && '➡️ '}\n                  {analysis.recommendation.includes('شراء قوي') ? 'شراء قوي' :\n                   analysis.recommendation.includes('بيع قوي') ? 'بيع قوي' :\n                   analysis.recommendation.includes('شراء') ? 'شراء' :\n                   analysis.recommendation.includes('بيع') ? 'بيع' : 'انتظار'}\n                </div>\n                <p className=\"text-lg text-gray-600 dark:text-gray-400 mt-2\">\n                  بناءً على تحليل مستويات فيبوناتشي الذهبية والنسب المقدسة\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Fibonacci Setup */}\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                    📐 إعداد فيبوناتشي المقترح\n                  </h4>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">أقرب مستوى</div>\n                        <div className=\"text-lg font-bold text-blue-600\">\n                          {analysis.nearestLevel.percentage}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {analysis.nearestLevel.price.toFixed(5)}\n                        </div>\n                      </div>\n                      <div className=\"bg-purple-50 dark:bg-purple-900/20 rounded p-3 text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">قوة المستوى</div>\n                        <div className=\"text-lg font-bold text-purple-600\">\n                          {analysis.nearestLevel.strength.toFixed(0)}%\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة'}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded p-3\">\n                      <div className=\"text-center\">\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">مستوى الثقة</div>\n                        <div className=\"text-xl font-bold text-yellow-600\">\n                          {analysis.confidence.toFixed(0)}%\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded p-3\">\n                      <h5 className=\"font-medium text-amber-800 dark:text-amber-200 mb-2\">\n                        🔢 المستويات الذهبية النشطة:\n                      </h5>\n                      <div className=\"grid grid-cols-3 gap-1 text-xs\">\n                        {analysis.levels\n                          .filter(l => l.strength > 70)\n                          .slice(0, 6)\n                          .map((level, i) => (\n                            <div key={`golden-level-${level.percentage}-${i}`} className=\"text-center bg-white dark:bg-gray-600 rounded p-1\">\n                              <div className=\"font-bold\">{level.percentage}</div>\n                              <div className=\"text-gray-500\">{level.strength.toFixed(0)}%</div>\n                            </div>\n                          ))}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Fibonacci Analysis */}\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg p-6\">\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center\">\n                    🌟 تحليل فيبوناتشي المتقدم\n                  </h4>\n\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-green-50 dark:bg-green-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-green-800 dark:text-green-200 mb-2\">\n                        📊 إحصائيات المستويات:\n                      </h5>\n                      <div className=\"text-sm space-y-1\">\n                        <div>إجمالي المستويات: {analysis.levels.length}</div>\n                        <div>مستويات قوية: {analysis.levels.filter(l => l.strength > 80).length}</div>\n                        <div>مستويات دعم: {analysis.levels.filter(l => l.type === 'support').length}</div>\n                        <div>مستويات مقاومة: {analysis.levels.filter(l => l.type === 'resistance').length}</div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-blue-800 dark:text-blue-200 mb-2\">\n                        🎯 تحليل الاتجاه:\n                      </h5>\n                      <div className=\"text-sm\">\n                        الاتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}<br/>\n                        أعلى سعر: {analysis.high.toFixed(5)}<br/>\n                        أقل سعر: {analysis.low.toFixed(5)}<br/>\n                        المدى: {((analysis.high - analysis.low) / analysis.low * 100).toFixed(2)}%\n                      </div>\n                    </div>\n\n                    <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded p-4\">\n                      <h5 className=\"font-medium text-orange-800 dark:text-orange-200 mb-2\">\n                        💡 نصائح فيبوناتشي:\n                      </h5>\n                      <ul className=\"text-sm space-y-1\">\n                        <li>• ركز على مستويات 61.8% و 78.6%</li>\n                        <li>• انتظر تأكيد الكسر أو الارتداد</li>\n                        <li>• استخدم مستويات متعددة للتأكيد</li>\n                        <li>• راقب التقارب مع مؤشرات أخرى</li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Confidence Meter */}\n              <div className=\"mt-6 bg-white dark:bg-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">مستوى الثقة في تحليل فيبوناتشي:</span>\n                  <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{analysis.confidence.toFixed(0)}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div\n                    className={`h-3 rounded-full transition-all duration-500 ${\n                      analysis.confidence > 80 ? 'bg-green-500' :\n                      analysis.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'\n                    }`}\n                    style={{ width: `${analysis.confidence}%` }}\n                  ></div>\n                </div>\n                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                  <span>منخفض</span>\n                  <span>متوسط</span>\n                  <span>عالي</span>\n                </div>\n              </div>\n\n              {/* Key Recommendation */}\n              <div className=\"mt-4 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 rounded-lg p-4\">\n                <h5 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                  🎯 التوصية الرئيسية:\n                </h5>\n                <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n                  {analysis.recommendation}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAwBe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IAErE,MAAM,kBAAkB;QAAC;QAAG;QAAO;QAAO;QAAK;QAAO;QAAO;QAAG;QAAO;QAAO;QAAO;KAAM;IAE3F,MAAM,uBAAuB;QAC3B,eAAe;QAEf,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,aAAa;YAC/B,MAAM,aAAa,cAAc;YAEjC,8BAA8B;YAC9B,MAAM,aAAa,YAAY,aAAa;YAC5C,MAAM,OAAO,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAChE,MAAM,MAAM,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAE/D,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,YAAY;YACpD,MAAM,eAAe,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YAEtE,6BAA6B;YAC7B,MAAM,SAA2B,gBAAgB,GAAG,CAAC,CAAA;gBACnD,MAAM,QAAQ,cAAc,YACxB,OAAO,CAAC,OAAO,GAAG,IAAI,QACtB,MAAM,CAAC,OAAO,GAAG,IAAI;gBAEzB,MAAM,sBAAsB,KAAK,GAAG,CAAC,QAAQ;gBAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,MAAM,AAAC,sBAAsB,YAAa;gBAExE,OAAO;oBACL;oBACA;oBACA,YAAY,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC1C,MAAM,QAAQ,eAAe,eAAe;oBAC5C,UAAU,KAAK,GAAG,CAAC,UAAU;gBAC/B;YACF;YAEA,qBAAqB;YACrB,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,SAAS;gBAC3C,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,OAAO,kBAAkB,kBAAkB,UAAU;YACvD;YAEA,0BAA0B;YAC1B,MAAM,oBAAoB,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG,gBAAgB,YAAY;YACpF,IAAI,iBAAiB;YACrB,IAAI,aAAa;YAEjB,IAAI,oBAAoB,KAAK;gBAC3B,IAAI,aAAa,IAAI,KAAK,aAAa,cAAc,WAAW;oBAC9D,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO,IAAI,aAAa,IAAI,KAAK,gBAAgB,cAAc,WAAW;oBACxE,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO;oBACL,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC;YACF,OAAO,IAAI,oBAAoB,KAAK;gBAClC,iBAAiB,CAAC,8BAA8B,EAAE,aAAa,UAAU,EAAE;gBAC3E,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC,OAAO;gBACL,iBAAiB;gBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC;YAEA,YAAY;gBACV,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,SAAS;YAAK,UAAU;QAC1B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,gBAAgB,CAAC,OAAuB;QAC5C,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,gBAAgB,eAAe;QAEvE,IAAI,WAAW,KAAK,OAAO,0CAA0C,aAAa;QAClF,IAAI,WAAW,KAAK,OAAO,mDAAmD,QAAQ;QACtF,IAAI,MAAM,IAAI,KAAK,WAAW,OAAO;QACrC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,8OAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,mDAAmD,EAC7D,cACI,8CACA,gDACJ;0CAED,cAAc,uBAAuB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;gDAAkB,OAAO;;oDACvB,YAAY;oDAAM;oDAAE;;+CADV;;;;;;;;;;;;;;;;0CAOnB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,mBACd,8OAAC;gDAAgB,OAAO;0DAAK;+CAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;;oBAEZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;;4CAA2C;4CAC1B;4CAAa;;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;oBAO/C,CAAC,eAAe,CAAC,0BAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,0BACf,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAY,YAAY,SAAS,MAAM;;;;;;kEACvD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;;oEACX,SAAS,MAAM;oEAAC;oEAAI;;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;;oEAA2C;oEAC9C,SAAS,SAAS,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;;0DAI7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAEvE,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE/D,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE9D,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAA4C,SAAS,cAAc;;;;;;;;;;;;;;;;;;0CAKpF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;gDAEC,WAAW,CAAC,oDAAoD,EAAE,cAAc,OAAO,SAAS,YAAY,GAAG;;kEAE/G,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,MAAM,UAAU;;;;;;kFAEnB,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;0FAE7D,8OAAC;gFAAI,WAAU;0FACZ,MAAM,IAAI,KAAK,YAAY,WAAW;;;;;;;;;;;;;;;;;;0EAK7C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAAsB;4EAC7B,MAAM,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;kFAElC,8OAAC;wEAAI,WAAU;;4EAAU;4EACb,CAAC,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,SAAS,YAAY,IAAI,SAAS,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;oDAKxG,UAAU,SAAS,YAAY,kBAC9B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;;+CA9BpC;;;;;;;;;;;;;;;;0CAyCb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,8OAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ,GAAG,IACjD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,8OAAC;;oEAAkD;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAAtF,CAAC,cAAc,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,GAAG;;;;;;;;;;;;;;;;0DAKzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,8OAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,gBAAgB,EAAE,QAAQ,GAAG,IACpD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,8OAAC;;oEAAqD;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAAzF,CAAC,iBAAiB,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhE,8OAAC;gCAAI,WAAW,CAAC,wBAAwB,EACvC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAC7B,gHACA,SAAS,cAAc,CAAC,QAAQ,CAAC,SACjC,oGACA,8GACJ;;kDACA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,8OAAC;gDAAI,WAAW,CAAC,mEAAmE,EAClF,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,4BAC3C,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,0BAC1C,4BACA;;oDACC,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW;oDAC5C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU;oDAC3C,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU;oDACzF,SAAS,cAAc,CAAC,QAAQ,CAAC,cAAc,aAC/C,SAAS,cAAc,CAAC,QAAQ,CAAC,aAAa,YAC9C,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU,SAC3C,SAAS,cAAc,CAAC,QAAQ,CAAC,SAAS,QAAQ;;;;;;;0DAErD,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAK/D,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,8OAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,UAAU;;;;;;0FAEnC,8OAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kFAGzC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAA2C;;;;;;0FAC1D,8OAAC;gFAAI,WAAU;;oFACZ,SAAS,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;oFAAG;;;;;;;0FAE7C,8OAAC;gFAAI,WAAU;0FACZ,SAAS,YAAY,CAAC,IAAI,KAAK,YAAY,QAAQ;;;;;;;;;;;;;;;;;;0EAK1D,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA2C;;;;;;sFAC1D,8OAAC;4EAAI,WAAU;;gFACZ,SAAS,UAAU,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;0EAKtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAsD;;;;;;kFAGpE,8OAAC;wEAAI,WAAU;kFACZ,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IACzB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,8OAAC;gFAAkD,WAAU;;kGAC3D,8OAAC;wFAAI,WAAU;kGAAa,MAAM,UAAU;;;;;;kGAC5C,8OAAC;wFAAI,WAAU;;4FAAiB,MAAM,QAAQ,CAAC,OAAO,CAAC;4FAAG;;;;;;;;+EAFlD,CAAC,aAAa,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAW7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyE;;;;;;kEAIvF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAsD;;;;;;kFAGpE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAI;oFAAmB,SAAS,MAAM,CAAC,MAAM;;;;;;;0FAC9C,8OAAC;;oFAAI;oFAAe,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,GAAG,IAAI,MAAM;;;;;;;0FACvE,8OAAC;;oFAAI;oFAAc,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;;0FAC3E,8OAAC;;oFAAI;oFAAiB,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc,MAAM;;;;;;;;;;;;;;;;;;;0EAIrF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAoD;;;;;;kFAGlE,8OAAC;wEAAI,WAAU;;4EAAU;4EACb,SAAS,SAAS,KAAK,YAAY,YAAY;0FAAU,8OAAC;;;;;4EAAI;4EAC7D,SAAS,IAAI,CAAC,OAAO,CAAC;0FAAG,8OAAC;;;;;4EAAI;4EAC/B,SAAS,GAAG,CAAC,OAAO,CAAC;0FAAG,8OAAC;;;;;4EAAI;4EAC/B,CAAC,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAI7E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAwD;;;;;;kFAGtE,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;0FACJ,8OAAC;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAuD;;;;;;kEACvE,8OAAC;wDAAK,WAAU;;4DAAmD,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAEpG,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAW,CAAC,6CAA6C,EACvD,SAAS,UAAU,GAAG,KAAK,iBAC3B,SAAS,UAAU,GAAG,KAAK,kBAAkB,cAC7C;oDACF,OAAO;wDAAE,OAAO,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAG9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAKV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DAGtE,8OAAC;gDAAE,WAAU;0DACV,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C", "debugId": null}}]}