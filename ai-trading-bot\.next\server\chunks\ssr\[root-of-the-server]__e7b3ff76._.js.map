{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function Home() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    // Simulate initialization\n    const timer = setTimeout(() => {\n      setIsInitialized(true);\n    }, 3000);\n\n    // Update time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => {\n      clearTimeout(timer);\n      clearInterval(timeInterval);\n    };\n  }, []);\n\n  // Enhanced real-time market data with advanced analytics\n  const [marketData, setMarketData] = useState([\n    {\n      symbol: 'EURUSD',\n      price: 1.0850,\n      change: 0.0012,\n      changePercent: 0.11,\n      trend: 'BULLISH',\n      volume: 1250000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0\n    },\n    {\n      symbol: 'GBPUSD',\n      price: 1.2650,\n      change: -0.0025,\n      changePercent: -0.20,\n      trend: 'BEARISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1\n    },\n    {\n      symbol: 'USDJPY',\n      price: 149.50,\n      change: 0.35,\n      changePercent: 0.23,\n      trend: 'BULLISH',\n      volume: 1100000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'ASIAN',\n      riskLevel: 'LOW',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 1\n    },\n    {\n      symbol: 'XAUUSD',\n      price: 2050.00,\n      change: 15.50,\n      changePercent: 0.76,\n      trend: 'BULLISH',\n      volume: 750000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 5,\n      fvg: 4,\n      choch: 2,\n      bos: 1\n    },\n  ]);\n\n  // Enhanced signals with ICT concepts\n  const [signals, setSignals] = useState([\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit1: 1.0900,\n      takeProfit2: 1.0950,\n      takeProfit3: 1.1000,\n      riskReward: 1.67,\n      confidence: 87,\n      timestamp: Date.now() - 300000,\n      reasoning: [\n        'Bullish Order Block at 1.0845-1.0855',\n        'Fair Value Gap filled and holding',\n        'CHoCH confirmed bullish structure',\n        'RSI oversold with divergence',\n        'MACD bullish crossover',\n        'Price above VWAP',\n        'London session high liquidity'\n      ],\n      patterns: ['Bullish Order Block', 'Fair Value Gap', 'CHoCH'],\n      session: 'LONDON',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit1: 1.2600,\n      takeProfit2: 1.2550,\n      takeProfit3: 1.2500,\n      riskReward: 1.67,\n      confidence: 82,\n      timestamp: Date.now() - 600000,\n      reasoning: [\n        'Bearish Breaker Block at 1.2655-1.2665',\n        'Break of Structure confirmed',\n        'Bearish engulfing pattern',\n        'RSI overbought rejection',\n        'Volume spike on breakdown',\n        'Below key support level'\n      ],\n      patterns: ['Bearish Breaker Block', 'BOS', 'Engulfing'],\n      session: 'LONDON',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    },\n    {\n      id: '3',\n      symbol: 'XAUUSD',\n      type: 'BUY',\n      entry: 2050.00,\n      stopLoss: 2035.00,\n      takeProfit1: 2075.00,\n      takeProfit2: 2100.00,\n      takeProfit3: 2125.00,\n      riskReward: 1.67,\n      confidence: 91,\n      timestamp: Date.now() - 900000,\n      reasoning: [\n        'Premium Discount Array (PDA) setup',\n        'Institutional Order Block respected',\n        'Liquidity sweep completed',\n        'Fair Value Gap acting as support',\n        'Smart money accumulation zone',\n        'Dollar weakness confluence'\n      ],\n      patterns: ['Order Block', 'Liquidity Sweep', 'PDA'],\n      session: 'NEW_YORK',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    }\n  ]);\n\n  // Real-time updates simulation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setMarketData(prevData =>\n        prevData.map(market => ({\n          ...market,\n          price: market.price + (Math.random() - 0.5) * 0.001 * market.price,\n          change: market.change + (Math.random() - 0.5) * 0.0005,\n          changePercent: market.changePercent + (Math.random() - 0.5) * 0.05,\n          volume: market.volume + Math.floor((Math.random() - 0.5) * 50000)\n        }))\n      );\n    }, 2000); // Update every 2 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const mockSignals = [\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit: 1.0900,\n      confidence: 85,\n      timestamp: Date.now() - 300000,\n      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit: 1.2600,\n      confidence: 78,\n      timestamp: Date.now() - 600000,\n      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']\n    }\n  ];\n\n  if (!isInitialized) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">🤖 AI Trading Bot</h1>\n          <p className=\"text-blue-200\">Initializing advanced trading systems...</p>\n          <div className=\"mt-4 space-y-2 text-sm text-blue-300\">\n            <p>✅ Loading technical indicators</p>\n            <p>✅ Connecting to market data</p>\n            <p>✅ Initializing AI pattern recognition</p>\n            <p>✅ Setting up risk management</p>\n            <p>🔄 Starting real-time analysis...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                🤖 AI Trading Bot\n              </h1>\n              <div className=\"ml-6 flex items-center space-x-2\">\n                <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-300\">Live</span>\n              </div>\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n              {currentTime.toLocaleTimeString()}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Dashboard */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Enhanced Market Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {marketData.map(market => (\n            <div key={market.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-l-4 border-blue-500\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {market.symbol}\n                </h3>\n                <div className=\"flex items-center space-x-2\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    market.trend === 'BULLISH' ? 'bg-green-500' : 'bg-red-500'\n                  }`}></div>\n                  <span className=\"text-xs text-gray-500\">{market.session}</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}\n                  </span>\n                  <span className={`text-sm font-medium ${\n                    market.change >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.trend === 'BULLISH'\n                      ? 'text-green-600 bg-green-50 dark:bg-green-900/20'\n                      : 'text-red-600 bg-red-50 dark:bg-red-900/20'\n                  }`}>\n                    {market.trend}\n                  </div>\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.riskLevel === 'LOW' ? 'text-green-600 bg-green-50' :\n                    market.riskLevel === 'MEDIUM' ? 'text-yellow-600 bg-yellow-50' :\n                    'text-red-600 bg-red-50'\n                  } dark:bg-opacity-20`}>\n                    {market.riskLevel}\n                  </div>\n                </div>\n\n                <div className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex justify-between\">\n                    <span>Volume:</span>\n                    <span>{(market.volume / 1000000).toFixed(1)}M</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>Volatility:</span>\n                    <span>{market.volatility}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>Liquidity:</span>\n                    <span>{market.liquidity}</span>\n                  </div>\n                </div>\n\n                {/* ICT Concepts Summary */}\n                <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"grid grid-cols-4 gap-1 text-xs\">\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-blue-600\">{market.orderBlocks}</div>\n                      <div className=\"text-gray-500\">OB</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-purple-600\">{market.fvg}</div>\n                      <div className=\"text-gray-500\">FVG</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-green-600\">{market.choch}</div>\n                      <div className=\"text-gray-500\">CHoCH</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-orange-600\">{market.bos}</div>\n                      <div className=\"text-gray-500\">BOS</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Trading Signals */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              🎯 AI Trading Signals\n            </h3>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              {mockSignals.map(signal => (\n                <div key={signal.id} className={`border rounded-lg p-4 ${\n                  signal.type === 'BUY'\n                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'\n                    : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'\n                }`}>\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-5 h-5 ${\n                        signal.type === 'BUY' ? 'text-green-500' : 'text-red-500'\n                      }`}>\n                        {signal.type === 'BUY' ? '📈' : '📉'}\n                      </div>\n                      <div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"font-semibold text-gray-900 dark:text-white\">\n                            {signal.type} {signal.symbol}\n                          </span>\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {signal.confidence}% confidence\n                          </span>\n                        </div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                          {new Date(signal.timestamp).toLocaleString()}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-4 grid grid-cols-3 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">Entry:</span>\n                      <div className=\"font-medium text-gray-900 dark:text-white\">\n                        {signal.entry.toFixed(5)}\n                      </div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">Stop Loss:</span>\n                      <div className=\"font-medium text-red-600\">\n                        {signal.stopLoss.toFixed(5)}\n                      </div>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">Take Profit:</span>\n                      <div className=\"font-medium text-green-600\">\n                        {signal.takeProfit.toFixed(5)}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-4 p-3 bg-white dark:bg-gray-700 rounded border\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                      🧠 AI Analysis:\n                    </h4>\n                    <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                      {signal.reasoning.map((reason, index) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"mr-2\">•</span>\n                          <span>{reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Technical Indicators */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">RSI (EURUSD)</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">45.2</div>\n            <div className=\"text-sm text-green-600\">Neutral</div>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">MACD</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">0.0012</div>\n            <div className=\"text-sm text-green-600\">Bullish</div>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">VWAP</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">1.0845</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">Volume Weighted</div>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">Supertrend</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">1.0820</div>\n            <div className=\"text-sm text-green-600\">Uptrend</div>\n          </div>\n        </div>\n\n        {/* Risk Management */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              🛡️ Risk Management\n            </h3>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600 mb-2\">$10,000</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Account Balance</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">2.5%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Daily Risk</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600 mb-2\">68.5%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Win Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-orange-600 mb-2\">2</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">Active Signals</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex justify-between items-center text-sm text-gray-600 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-4\">\n              <span>🤖 AI Trading Bot v1.0</span>\n              <span>•</span>\n              <span>Demo Mode Active</span>\n              <span>•</span>\n              <span>Last Update: {currentTime.toLocaleTimeString()}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span>Powered by AI</span>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,QAAQ,WAAW;YACvB,iBAAiB;QACnB,GAAG;QAEH,2BAA2B;QAC3B,MAAM,eAAe,YAAY;YAC/B,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO;YACL,aAAa;YACb,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;QACA;YACE,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;QACP;KACD;IAED,qCAAqC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAuB;gBAAkB;aAAQ;YAC5D,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAyB;gBAAO;aAAY;YACvD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAe;gBAAmB;aAAM;YACnD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAA,WACZ,SAAS,GAAG,CAAC,CAAA,SAAU,CAAC;wBACtB,GAAG,MAAM;wBACT,OAAO,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,OAAO,KAAK;wBAClE,QAAQ,OAAO,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAChD,eAAe,OAAO,aAAa,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC9D,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC7D,CAAC;QAEL,GAAG,OAAO,yBAAyB;QAEnC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAAgB;gBAA0B;aAAmB;QAC3E;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAA6B;gBAAkB;aAAmB;QAChF;KACD;IAED,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;0CACZ,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA,uBACd,8OAAC;gCAAwB,WAAU;;kDACjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,OAAO,MAAM;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,OAAO,KAAK,KAAK,YAAY,iBAAiB,cAC9C;;;;;;kEACF,8OAAC;wDAAK,WAAU;kEAAyB,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAI3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE5D,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,MAAM,IAAI,IAAI,mBAAmB,gBACxC;;4DACC,OAAO,MAAM,IAAI,IAAI,MAAM;4DAAI,OAAO,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,8BAA8B,EAC7C,OAAO,KAAK,KAAK,YACb,oDACA,6CACJ;kEACC,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAI,WAAW,CAAC,8BAA8B,EAC7C,OAAO,SAAS,KAAK,QAAQ,+BAC7B,OAAO,SAAS,KAAK,WAAW,iCAChC,yBACD,mBAAmB,CAAC;kEAClB,OAAO,SAAS;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,CAAC,OAAO,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,OAAO,UAAU;;;;;;;;;;;;kEAE1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,OAAO,SAAS;;;;;;;;;;;;;;;;;;0DAK3B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B,OAAO,WAAW;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA8B,OAAO,KAAK;;;;;;8EACzD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA1E/B,OAAO,MAAM;;;;;;;;;;kCAoF3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;;;;;;0CAItE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC;4CAAoB,WAAW,CAAC,sBAAsB,EACrD,OAAO,IAAI,KAAK,QACZ,4EACA,mEACJ;;8DACA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EACvB,OAAO,IAAI,KAAK,QAAQ,mBAAmB,gBAC3C;0EACC,OAAO,IAAI,KAAK,QAAQ,OAAO;;;;;;0EAElC,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;;oFACb,OAAO,IAAI;oFAAC;oFAAE,OAAO,MAAM;;;;;;;0FAE9B,8OAAC;gFAAK,WAAU;;oFACb,OAAO,UAAU;oFAAC;;;;;;;;;;;;;kFAGvB,8OAAC;wEAAI,WAAU;kFACZ,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;8DAMlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAG1B,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;sEAG7B,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAmC;;;;;;8EACnD,8OAAC;oEAAI,WAAU;8EACZ,OAAO,UAAU,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;8DAKjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAyD;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;sEACX,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;oEAAe,WAAU;;sFACxB,8OAAC;4EAAK,WAAU;sFAAO;;;;;;sFACvB,8OAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;;2CAvDP,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCAqE3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;;;;;;0CAItE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAK;4CAAc,YAAY,kBAAkB;;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}