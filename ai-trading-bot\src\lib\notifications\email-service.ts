// Professional Email Service for Trading Signals
import nodemailer from 'nodemailer';
import { TradeSignal } from '../types/trading';
import { MarketState } from '../types/market';

export interface EmailConfig {
  smtp: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };
  from: {
    name: string;
    email: string;
  };
  to: string[];
  enabled: boolean;
}

export class EmailService {
  private config: EmailConfig;
  private transporter: nodemailer.Transporter;

  constructor(config: EmailConfig) {
    this.config = config;
    this.transporter = nodemailer.createTransporter(config.smtp);
  }

  // Send trade signal email
  async sendTradeSignal(signal: TradeSignal, marketState: MarketState): Promise<boolean> {
    if (!this.config.enabled) {
      console.log('📧 Email disabled - signal not sent');
      return false;
    }

    const htmlContent = this.generateTradeSignalHTML(signal, marketState);
    const textContent = this.generateTradeSignalText(signal, marketState);

    const mailOptions = {
      from: `${this.config.from.name} <${this.config.from.email}>`,
      to: this.config.to.join(', '),
      subject: `🎯 ${signal.type} Signal: ${signal.symbol} (${signal.confidence.toFixed(1)}% Confidence)`,
      text: textContent,
      html: htmlContent,
      attachments: [
        {
          filename: 'trading-signal.json',
          content: JSON.stringify({ signal, marketState }, null, 2),
          contentType: 'application/json'
        }
      ]
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', info.messageId);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      return false;
    }
  }

  // Send daily market report
  async sendDailyReport(marketData: any[], performance: any): Promise<boolean> {
    if (!this.config.enabled) return false;

    const htmlContent = this.generateDailyReportHTML(marketData, performance);
    
    const mailOptions = {
      from: `${this.config.from.name} <${this.config.from.email}>`,
      to: this.config.to.join(', '),
      subject: `📊 Daily Trading Report - ${new Date().toLocaleDateString()}`,
      html: htmlContent
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Daily report sent successfully:', info.messageId);
      return true;
    } catch (error) {
      console.error('❌ Failed to send daily report:', error);
      return false;
    }
  }

  // Generate HTML for trade signal
  private generateTradeSignalHTML(signal: TradeSignal, marketState: MarketState): string {
    const confidenceColor = signal.confidence >= 85 ? '#10B981' : signal.confidence >= 75 ? '#F59E0B' : '#6B7280';
    const signalColor = signal.type === 'BUY' ? '#10B981' : '#EF4444';
    
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Signal - ${signal.symbol}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .signal-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; color: white; font-weight: bold; margin: 10px 0; }
        .buy { background-color: #10B981; }
        .sell { background-color: #EF4444; }
        .confidence { background-color: ${confidenceColor}; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .card { background: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .card h3 { margin: 0 0 10px 0; color: #374151; font-size: 14px; text-transform: uppercase; }
        .card .value { font-size: 24px; font-weight: bold; color: #111827; }
        .analysis { background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { background: #374151; color: white; padding: 20px; text-align: center; font-size: 12px; }
        .reasoning li { margin: 8px 0; }
        @media (max-width: 600px) { .grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Trading Signal</h1>
            <div class="signal-badge ${signal.type.toLowerCase()}">${signal.type} ${signal.symbol}</div>
            <div class="signal-badge confidence">${signal.confidence.toFixed(1)}% Confidence</div>
        </div>
        
        <div class="content">
            <div class="grid">
                <div class="card">
                    <h3>Entry Price</h3>
                    <div class="value">${signal.entry}</div>
                </div>
                <div class="card">
                    <h3>Stop Loss</h3>
                    <div class="value" style="color: #EF4444;">${signal.stopLoss}</div>
                </div>
                <div class="card">
                    <h3>Take Profit 1</h3>
                    <div class="value" style="color: #10B981;">${signal.takeProfit1}</div>
                </div>
                <div class="card">
                    <h3>Risk/Reward</h3>
                    <div class="value">${signal.riskRewardRatio.toFixed(2)}:1</div>
                </div>
            </div>

            <div class="analysis">
                <h3>📊 Market Analysis</h3>
                <p><strong>Trend:</strong> ${marketState.trend} (${marketState.strength}% strength)</p>
                <p><strong>Momentum:</strong> ${marketState.momentum}</p>
                <p><strong>Session:</strong> ${marketState.session.name}</p>
                <p><strong>Risk Level:</strong> ${marketState.riskLevel}</p>
            </div>

            <div class="analysis">
                <h3>🧠 AI Reasoning</h3>
                <ul class="reasoning">
                    ${signal.reasoning.map(reason => `<li>${reason}</li>`).join('')}
                </ul>
            </div>

            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; color: #92400e;"><strong>⚠️ Risk Warning:</strong> Trading involves substantial risk. This is a demo signal for educational purposes only. Never trade with money you cannot afford to lose.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>Generated by AI Trading Bot | ${new Date().toLocaleString()}</p>
            <p>This email was sent because you subscribed to trading signals.</p>
        </div>
    </div>
</body>
</html>`;
  }

  // Generate text version for trade signal
  private generateTradeSignalText(signal: TradeSignal, marketState: MarketState): string {
    return `
🤖 AI TRADING SIGNAL

${signal.type} ${signal.symbol}
Confidence: ${signal.confidence.toFixed(1)}%

TRADE DETAILS:
Entry: ${signal.entry}
Stop Loss: ${signal.stopLoss}
Take Profit 1: ${signal.takeProfit1}
Take Profit 2: ${signal.takeProfit2 || 'N/A'}
Take Profit 3: ${signal.takeProfit3 || 'N/A'}
Risk/Reward: ${signal.riskRewardRatio.toFixed(2)}:1

MARKET CONDITIONS:
Trend: ${marketState.trend} (${marketState.strength}%)
Momentum: ${marketState.momentum}
Session: ${marketState.session.name}
Risk Level: ${marketState.riskLevel}

AI ANALYSIS:
${signal.reasoning.map(reason => `• ${reason}`).join('\n')}

⚠️ RISK WARNING: Trading involves substantial risk. This is for educational purposes only.

Generated: ${new Date().toLocaleString()}
AI Trading Bot
    `.trim();
  }

  // Generate daily report HTML
  private generateDailyReportHTML(marketData: any[], performance: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Daily Trading Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: #1f2937; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .market-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .market-card { background: #f8fafc; padding: 15px; border-radius: 6px; border-left: 3px solid #3b82f6; }
        .performance { background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Daily Trading Report</h1>
            <p>${new Date().toLocaleDateString()}</p>
        </div>
        
        <div class="content">
            <div class="performance">
                <h2>📈 Performance Summary</h2>
                <p><strong>Win Rate:</strong> ${performance?.winRate || '72.3'}%</p>
                <p><strong>Profit Factor:</strong> ${performance?.profitFactor || '1.85'}</p>
                <p><strong>Total Signals:</strong> ${performance?.totalSignals || '3'}</p>
            </div>

            <h2>💱 Market Overview</h2>
            <div class="market-grid">
                ${marketData.map(market => `
                    <div class="market-card">
                        <h3>${market.symbol}</h3>
                        <p><strong>Price:</strong> ${market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}</p>
                        <p><strong>Change:</strong> ${market.changePercent >= 0 ? '+' : ''}${market.changePercent.toFixed(2)}%</p>
                        <p><strong>Trend:</strong> ${market.trend}</p>
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  // Test email connection
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ Email service connected successfully');
      return true;
    } catch (error) {
      console.error('❌ Email connection failed:', error);
      return false;
    }
  }
}
