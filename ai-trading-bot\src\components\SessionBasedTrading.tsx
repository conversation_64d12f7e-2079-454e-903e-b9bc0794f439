'use client';

import React, { useState, useEffect } from 'react';

interface TradingSession {
  name: string;
  timezone: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
  volatility: 'Low' | 'Medium' | 'High';
  bestPairs: string[];
  strategies: string[];
  avgSpread: number;
  volume: number;
}

interface SessionPerformance {
  session: string;
  winRate: number;
  avgReturn: number;
  totalTrades: number;
  profitFactor: number;
}

const SessionBasedTrading: React.FC = () => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [selectedSession, setSelectedSession] = useState<string>('London');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const tradingSessions: TradingSession[] = [
    {
      name: 'آسيا',
      timezone: 'GMT+8',
      startTime: '00:00',
      endTime: '09:00',
      isActive: false,
      volatility: 'منخفضة',
      bestPairs: ['USDJPY', 'AUDJPY', 'NZDJPY', 'AUDUSD'],
      strategies: ['تداول النطاق', 'تداول الفوائد', 'كسر التماسك'],
      avgSpread: 1.2,
      volume: 65
    },
    {
      name: 'لندن',
      timezone: 'GMT+0',
      startTime: '08:00',
      endTime: '17:00',
      isActive: true,
      volatility: 'عالية',
      bestPairs: ['GBPUSD', 'EURGBP', 'GBPJPY', 'EURUSD'],
      strategies: ['تداول الاختراق', 'تداول الأخبار', 'اتباع الاتجاه'],
      avgSpread: 0.8,
      volume: 95
    },
    {
      name: 'نيويورك',
      timezone: 'GMT-5',
      startTime: '13:00',
      endTime: '22:00',
      isActive: false,
      volatility: 'عالية',
      bestPairs: ['EURUSD', 'GBPUSD', 'USDCAD', 'USDCHF'],
      strategies: ['تداول الزخم', 'تداول الانعكاس', 'المضاربة السريعة'],
      avgSpread: 0.9,
      volume: 88
    }
  ];

  const sessionPerformance: SessionPerformance[] = [
    {
      session: 'آسيا',
      winRate: 68,
      avgReturn: 0.8,
      totalTrades: 145,
      profitFactor: 1.6
    },
    {
      session: 'لندن',
      winRate: 72,
      avgReturn: 1.4,
      totalTrades: 298,
      profitFactor: 2.1
    },
    {
      session: 'نيويورك',
      winRate: 65,
      avgReturn: 1.2,
      totalTrades: 234,
      profitFactor: 1.8
    },
    {
      session: 'تداخل لندن-نيويورك',
      winRate: 78,
      avgReturn: 1.8,
      totalTrades: 89,
      profitFactor: 2.4
    }
  ];

  const getCurrentSession = (): TradingSession | null => {
    const gmtHour = currentTime.getUTCHours();
    
    if (gmtHour >= 0 && gmtHour < 9) {
      return tradingSessions[0]; // Asian
    } else if (gmtHour >= 8 && gmtHour < 17) {
      return tradingSessions[1]; // London
    } else if (gmtHour >= 13 && gmtHour < 22) {
      return tradingSessions[2]; // New York
    }
    
    return null;
  };

  const getOverlapSession = (): string | null => {
    const gmtHour = currentTime.getUTCHours();

    if (gmtHour >= 13 && gmtHour < 17) {
      return 'تداخل لندن-نيويورك';
    }

    return null;
  };

  const getSessionColor = (session: TradingSession): string => {
    if (session.isActive) {
      return 'border-green-500 bg-green-50 dark:bg-green-900/20';
    }
    return 'border-gray-300 bg-gray-50 dark:bg-gray-700';
  };

  const getVolatilityColor = (volatility: string): string => {
    switch (volatility) {
      case 'عالية': return 'text-red-600 bg-red-100';
      case 'متوسطة': return 'text-yellow-600 bg-yellow-100';
      case 'منخفضة': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const currentSession = getCurrentSession();
  const overlapSession = getOverlapSession();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          🌍 تحليل التداول حسب الجلسات
        </h2>
        <div className="text-right">
          <p className="text-sm text-gray-600 dark:text-gray-400">التوقيت الحالي GMT</p>
          <p className="text-lg font-bold text-blue-600">
            {currentTime.toUTCString().split(' ')[4]}
          </p>
        </div>
      </div>

      {/* Current Session Alert */}
      {currentSession && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                🔥 الجلسة النشطة: {currentSession.name}
              </h3>
              <p className="text-blue-700 dark:text-blue-300">
                أفضل وقت لتداول {currentSession.bestPairs.slice(0, 2).join(', ')}
              </p>
            </div>
            {overlapSession && (
              <div className="text-right">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                  ⚡ {overlapSession}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trading Sessions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {tradingSessions.map((session, index) => (
          <div
            key={session.name}
            className={`border-2 rounded-lg p-4 transition-all duration-200 ${getSessionColor(session)}`}
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                جلسة {session.name}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getVolatilityColor(session.volatility)}`}>
                {session.volatility}
              </span>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">التوقيت:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {session.startTime} - {session.endTime} GMT
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الحجم:</span>
                <span className="font-medium text-gray-900 dark:text-white">{session.volume}%</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">متوسط السبريد:</span>
                <span className="font-medium text-gray-900 dark:text-white">{session.avgSpread} نقطة</span>
              </div>
            </div>

            <div className="mt-3">
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">أفضل الأزواج:</p>
              <div className="flex flex-wrap gap-1">
                {session.bestPairs.slice(0, 3).map(pair => (
                  <span key={pair} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    {pair}
                  </span>
                ))}
              </div>
            </div>

            <div className="mt-3">
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">الاستراتيجيات:</p>
              <div className="space-y-1">
                {session.strategies.slice(0, 2).map(strategy => (
                  <div key={strategy} className="text-xs text-gray-700 dark:text-gray-300">
                    • {strategy}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Session Performance Analysis */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          📊 الأداء التاريخي للجلسات
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {sessionPerformance.map((perf, index) => (
            <div key={perf.session} className="bg-white dark:bg-gray-600 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">{perf.session}</h4>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">معدل الفوز:</span>
                  <span className="font-bold text-green-600">{perf.winRate}%</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">متوسط العائد:</span>
                  <span className="font-bold text-blue-600">{perf.avgReturn}%</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">إجمالي الصفقات:</span>
                  <span className="font-bold text-gray-900 dark:text-white">{perf.totalTrades}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">عامل الربح:</span>
                  <span className="font-bold text-purple-600">{perf.profitFactor}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Trading Recommendations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3">
            ✅ التوصيات الحالية
          </h3>

          {currentSession ? (
            <div className="space-y-2 text-sm">
              <p className="text-green-800 dark:text-green-200">
                <strong>الجلسة النشطة:</strong> {currentSession.name}
              </p>
              <p className="text-green-800 dark:text-green-200">
                <strong>أفضل الأزواج:</strong> {currentSession.bestPairs.slice(0, 3).join(', ')}
              </p>
              <p className="text-green-800 dark:text-green-200">
                <strong>الاستراتيجية الموصى بها:</strong> {currentSession.strategies[0]}
              </p>
              <p className="text-green-800 dark:text-green-200">
                <strong>التقلبات المتوقعة:</strong> {currentSession.volatility}
              </p>
              {overlapSession && (
                <p className="text-orange-800 dark:text-orange-200 font-bold">
                  ⚡ فترة تداخل عالية الفرص نشطة!
                </p>
              )}
            </div>
          ) : (
            <p className="text-green-800 dark:text-green-200">
              السوق حالياً في فترة نشاط منخفض. فكر في انتظار الجلسة الرئيسية التالية.
            </p>
          )}
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-3">
            ⚠️ نصائح تداول الجلسات
          </h3>

          <div className="space-y-2 text-sm text-yellow-800 dark:text-yellow-200">
            <p>• <strong>جلسة آسيا:</strong> ركز على تداول النطاق وأزواج الين الياباني</p>
            <p>• <strong>جلسة لندن:</strong> الأفضل للاختراقات وأزواج الجنيه الإسترليني</p>
            <p>• <strong>جلسة نيويورك:</strong> تقلبات عالية، جيدة لأزواج الدولار</p>
            <p>• <strong>فترات التداخل:</strong> أعلى سيولة وأفضل الفرص</p>
            <p>• <strong>تجنب:</strong> التداول أثناء انتقال الجلسات</p>
          </div>
        </div>
      </div>

      {/* Session Timeline */}
      <div className="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          🕐 الجدول الزمني للجلسات على مدار 24 ساعة
        </h3>

        <div className="relative">
          <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-2">
            <span>00:00</span>
            <span>06:00</span>
            <span>12:00</span>
            <span>18:00</span>
            <span>24:00</span>
          </div>

          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded-lg relative overflow-hidden">
            {/* Asian Session */}
            <div className="absolute left-0 top-0 h-full w-[37.5%] bg-blue-400 opacity-70"></div>
            {/* London Session */}
            <div className="absolute left-[33.3%] top-0 h-full w-[37.5%] bg-green-400 opacity-70"></div>
            {/* New York Session */}
            <div className="absolute left-[54.2%] top-0 h-full w-[37.5%] bg-red-400 opacity-70"></div>

            {/* Current Time Indicator */}
            <div
              className="absolute top-0 h-full w-1 bg-yellow-500"
              style={{ left: `${(currentTime.getUTCHours() / 24) * 100}%` }}
            ></div>
          </div>

          <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-1">
            <span className="text-blue-600">آسيا</span>
            <span className="text-green-600">لندن</span>
            <span className="text-red-600">نيويورك</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionBasedTrading;
