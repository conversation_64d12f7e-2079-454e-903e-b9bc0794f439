module.exports = {

"[project]/src/components/ProfessionalTradingSystem.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ProfessionalTradingSystem
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$tradingViewAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/tradingViewAPI.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function ProfessionalTradingSystem() {
    const [selectedPair, setSelectedPair] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('EURUSD');
    const [selectedTimeframe, setSelectedTimeframe] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('1h');
    const [analysis, setAnalysis] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isAnalyzing, setIsAnalyzing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('strategies');
    const forexPairs = [
        'EURUSD',
        'GBPUSD',
        'USDJPY',
        'USDCHF',
        'AUDUSD',
        'USDCAD',
        'NZDUSD',
        'EURGBP',
        'EURJPY',
        'GBPJPY',
        'XAUUSD',
        'XAGUSD',
        'USOIL',
        'BTCUSD'
    ];
    const timeframes = [
        '1m',
        '5m',
        '15m',
        '30m',
        '1h',
        '4h',
        '1d',
        '1w'
    ];
    const runProfessionalAnalysis = async ()=>{
        setIsAnalyzing(true);
        try {
            // Fetch real-time data from TradingView
            console.log(`🔍 جاري جلب البيانات الحقيقية من TradingView لـ ${selectedPair}...`);
            const [priceData, indicatorData] = await Promise.all([
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$tradingViewAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchTradingViewPrice"])(selectedPair),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$tradingViewAPI$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchTradingViewIndicators"])(selectedPair, selectedTimeframe)
            ]);
            console.log(`📊 تم جلب البيانات: السعر ${priceData.price}, التغيير ${priceData.changePercent.toFixed(2)}%`);
            const volatility = Math.abs(priceData.changePercent) / 100 || 0.01;
            // Generate comprehensive analysis using real data
            const technicalIndicators = generateTechnicalIndicators(priceData.price, volatility, indicatorData);
            const strategies = generateTradingStrategies(priceData.price, volatility, priceData);
            const marketStructure = generateMarketStructure(priceData.price, volatility, priceData);
            const riskAssessment = generateRiskAssessment(volatility, priceData);
            // Calculate overall signal
            const { overallSignal, confidence, accuracy, reasoning } = calculateOverallSignal(technicalIndicators, strategies, marketStructure, priceData);
            setAnalysis({
                symbol: selectedPair,
                currentPrice: priceData.price,
                priceChange: priceData.change,
                priceChangePercent: priceData.changePercent,
                high24h: priceData.high24h,
                low24h: priceData.low24h,
                volume: priceData.volume,
                lastUpdate: priceData.timestamp,
                technicalIndicators,
                strategies,
                marketStructure,
                riskAssessment,
                overallSignal,
                confidence,
                accuracy,
                reasoning
            });
        } catch (error) {
            console.error('Professional Analysis Error:', error);
            // Fallback to simulated data if TradingView fails
            console.log('🔄 التبديل إلى البيانات المحاكاة...');
            const basePrice = getBasePrice(selectedPair);
            const volatility = getVolatility(selectedPair);
            const technicalIndicators = generateTechnicalIndicators(basePrice, volatility);
            const strategies = generateTradingStrategies(basePrice, volatility);
            const marketStructure = generateMarketStructure(basePrice, volatility);
            const riskAssessment = generateRiskAssessment(volatility);
            const { overallSignal, confidence, accuracy, reasoning } = calculateOverallSignal(technicalIndicators, strategies, marketStructure);
            setAnalysis({
                symbol: selectedPair,
                currentPrice: basePrice + (Math.random() - 0.5) * volatility * basePrice * 0.01,
                priceChange: (Math.random() - 0.5) * basePrice * 0.02,
                priceChangePercent: (Math.random() - 0.5) * 2,
                high24h: basePrice * 1.015,
                low24h: basePrice * 0.985,
                volume: Math.random() * 1000000 + 500000,
                lastUpdate: Date.now(),
                technicalIndicators,
                strategies,
                marketStructure,
                riskAssessment,
                overallSignal,
                confidence,
                accuracy,
                reasoning
            });
        } finally{
            setIsAnalyzing(false);
        }
    };
    const generateTechnicalIndicators = (basePrice, volatility, realIndicators)=>{
        // Use real TradingView indicators if available
        const getRealIndicator = (name)=>{
            return realIndicators?.find((ind)=>ind.name.toLowerCase().includes(name.toLowerCase()));
        };
        // Trend Indicators
        const macdReal = getRealIndicator('MACD');
        const adxReal = getRealIndicator('ADX');
        const trend = [
            {
                name: 'Moving Average (EMA 20/50/200)',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 70 + Math.random() * 25,
                description: 'المتوسطات المتحركة الأسية - اتجاه السوق'
            },
            {
                name: 'MACD (12,26,9)',
                value: macdReal?.value || (Math.random() - 0.5) * 0.02,
                signal: macdReal?.signal === 'BUY' ? 'STRONG_BUY' : macdReal?.signal === 'SELL' ? 'STRONG_SELL' : getRandomSignal(),
                strength: macdReal ? 85 + Math.random() * 10 : 65 + Math.random() * 30,
                description: 'مؤشر تقارب وتباعد المتوسطات - قوة الاتجاه (TradingView)'
            },
            {
                name: 'ADX (Average Directional Index)',
                value: adxReal?.value || 20 + Math.random() * 60,
                signal: adxReal?.signal === 'BUY' ? 'STRONG_BUY' : adxReal?.signal === 'SELL' ? 'STRONG_SELL' : getRandomSignal(),
                strength: adxReal ? 80 + Math.random() * 15 : 60 + Math.random() * 35,
                description: 'مؤشر الاتجاه المتوسط - قوة الترند (TradingView)'
            },
            {
                name: 'Parabolic SAR',
                value: basePrice + (Math.random() - 0.5) * basePrice * 0.02,
                signal: getRandomSignal(),
                strength: 55 + Math.random() * 40,
                description: 'نظام الإيقاف والانعكاس المكافئ'
            },
            {
                name: 'Ichimoku Cloud',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 70 + Math.random() * 25,
                description: 'سحابة إيشيموكو - تحليل شامل للاتجاه'
            }
        ];
        // Momentum Indicators
        const rsiReal = getRealIndicator('RSI');
        const stochReal = getRealIndicator('Stochastic');
        const williamsReal = getRealIndicator('Williams');
        const momentum = [
            {
                name: 'RSI (Relative Strength Index)',
                value: rsiReal?.value || 30 + Math.random() * 40,
                signal: rsiReal?.signal === 'BUY' ? 'STRONG_BUY' : rsiReal?.signal === 'SELL' ? 'STRONG_SELL' : getRandomSignal(),
                strength: rsiReal ? 90 + Math.random() * 5 : 75 + Math.random() * 20,
                description: 'مؤشر القوة النسبية - ذروة الشراء/البيع (TradingView)'
            },
            {
                name: 'Stochastic Oscillator',
                value: stochReal?.value || Math.random() * 100,
                signal: stochReal?.signal === 'BUY' ? 'STRONG_BUY' : stochReal?.signal === 'SELL' ? 'STRONG_SELL' : getRandomSignal(),
                strength: stochReal ? 85 + Math.random() * 10 : 65 + Math.random() * 30,
                description: 'مذبذب ستوكاستيك - زخم السعر (TradingView)'
            },
            {
                name: 'Williams %R',
                value: williamsReal?.value || -Math.random() * 100,
                signal: williamsReal?.signal === 'BUY' ? 'STRONG_BUY' : williamsReal?.signal === 'SELL' ? 'STRONG_SELL' : getRandomSignal(),
                strength: williamsReal ? 80 + Math.random() * 15 : 60 + Math.random() * 35,
                description: 'مؤشر ويليامز - قياس الزخم (TradingView)'
            },
            {
                name: 'CCI (Commodity Channel Index)',
                value: (Math.random() - 0.5) * 400,
                signal: getRandomSignal(),
                strength: 55 + Math.random() * 40,
                description: 'مؤشر قناة السلع - انحراف السعر'
            },
            {
                name: 'ROC (Rate of Change)',
                value: (Math.random() - 0.5) * 10,
                signal: getRandomSignal(),
                strength: 50 + Math.random() * 45,
                description: 'معدل التغيير - سرعة حركة السعر'
            }
        ];
        // Volatility Indicators
        const volatilityIndicators = [
            {
                name: 'Bollinger Bands',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 70 + Math.random() * 25,
                description: 'نطاقات بولينجر - قياس التقلبات'
            },
            {
                name: 'ATR (Average True Range)',
                value: basePrice * volatility * (0.5 + Math.random() * 0.5),
                signal: getRandomSignal(),
                strength: 65 + Math.random() * 30,
                description: 'متوسط المدى الحقيقي - قياس التقلبات'
            },
            {
                name: 'Keltner Channels',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 60 + Math.random() * 35,
                description: 'قنوات كيلتنر - نطاقات التقلبات'
            },
            {
                name: 'Donchian Channels',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 55 + Math.random() * 40,
                description: 'قنوات دونشيان - أعلى وأقل الأسعار'
            }
        ];
        // Volume Indicators
        const volume = [
            {
                name: 'Volume Weighted Average Price (VWAP)',
                value: basePrice + (Math.random() - 0.5) * basePrice * 0.01,
                signal: getRandomSignal(),
                strength: 80 + Math.random() * 15,
                description: 'متوسط السعر المرجح بالحجم'
            },
            {
                name: 'On-Balance Volume (OBV)',
                value: Math.random() * 1000000,
                signal: getRandomSignal(),
                strength: 70 + Math.random() * 25,
                description: 'حجم التوازن - تدفق الأموال'
            },
            {
                name: 'Accumulation/Distribution Line',
                value: Math.random() * 100000,
                signal: getRandomSignal(),
                strength: 65 + Math.random() * 30,
                description: 'خط التراكم/التوزيع'
            },
            {
                name: 'Chaikin Money Flow',
                value: (Math.random() - 0.5) * 0.5,
                signal: getRandomSignal(),
                strength: 60 + Math.random() * 35,
                description: 'تدفق أموال تشايكين'
            },
            {
                name: 'Volume Profile',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 75 + Math.random() * 20,
                description: 'ملف الحجم - توزيع التداول'
            }
        ];
        // Support/Resistance Indicators
        const support_resistance = [
            {
                name: 'Pivot Points (Standard)',
                value: basePrice + (Math.random() - 0.5) * basePrice * 0.02,
                signal: getRandomSignal(),
                strength: 75 + Math.random() * 20,
                description: 'نقاط المحورية القياسية'
            },
            {
                name: 'Fibonacci Retracements',
                value: Math.random() * 100,
                signal: getRandomSignal(),
                strength: 80 + Math.random() * 15,
                description: 'مستويات فيبوناتشي التصحيحية'
            },
            {
                name: 'Support/Resistance Levels',
                value: basePrice + (Math.random() - 0.5) * basePrice * 0.03,
                signal: getRandomSignal(),
                strength: 70 + Math.random() * 25,
                description: 'مستويات الدعم والمقاومة'
            },
            {
                name: 'Psychological Levels',
                value: Math.round(basePrice * 100) / 100,
                signal: getRandomSignal(),
                strength: 65 + Math.random() * 30,
                description: 'المستويات النفسية'
            }
        ];
        return {
            trend,
            momentum,
            volatility: volatilityIndicators,
            volume,
            support_resistance
        };
    };
    const generateTradingStrategies = (basePrice, volatility, priceData)=>{
        // ICT (Inner Circle Trader) Strategy - Enhanced with real data
        const priceMovement = priceData ? priceData.changePercent : 0;
        const isVolatile = Math.abs(priceMovement) > 1;
        const isBullish = priceMovement > 0;
        const ict = {
            name: 'ICT - Inner Circle Trader',
            type: isVolatile ? 'BREAKOUT' : 'MEAN_REVERSION',
            signal: isBullish ? isVolatile ? 'STRONG_BUY' : 'BUY' : isVolatile ? 'STRONG_SELL' : 'SELL',
            confidence: priceData ? 90 + Math.random() * 5 : 85 + Math.random() * 10,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.02,
                basePrice + (Math.random() - 0.5) * basePrice * 0.035,
                basePrice + (Math.random() - 0.5) * basePrice * 0.05
            ],
            riskReward: 2.5 + Math.random() * 1.5,
            timeframe: selectedTimeframe,
            reasoning: [
                'تحليل Order Blocks المؤسسية',
                `حركة السعر: ${priceMovement > 0 ? 'إيجابية' : 'سلبية'} ${Math.abs(priceMovement).toFixed(2)}%`,
                priceData ? `حجم التداول: ${(priceData.volume / 1000000).toFixed(1)}M` : 'مناطق السيولة المحددة',
                'Fair Value Gaps واضحة',
                isVolatile ? 'تقلبات عالية - فرصة كسر' : 'تقلبات منخفضة - عودة للمتوسط'
            ]
        };
        // Smart Money Concepts (SMC)
        const smc = {
            name: 'SMC - Smart Money Concepts',
            type: 'TREND_FOLLOWING',
            signal: getRandomSignal(),
            confidence: 80 + Math.random() * 15,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.025,
                basePrice + (Math.random() - 0.5) * basePrice * 0.04,
                basePrice + (Math.random() - 0.5) * basePrice * 0.06
            ],
            riskReward: 2.0 + Math.random() * 2.0,
            timeframe: selectedTimeframe,
            reasoning: [
                'تحليل هيكل السوق المتقدم',
                'Break of Structure (BOS) محدد',
                'Change of Character (CHoCH)',
                'Liquidity Sweeps واضحة',
                'Institutional Order Flow'
            ]
        };
        // Wyckoff Method
        const wyckoff = {
            name: 'Wyckoff Method',
            type: 'POSITION',
            signal: getRandomSignal(),
            confidence: 75 + Math.random() * 20,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.02,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.03,
                basePrice + (Math.random() - 0.5) * basePrice * 0.05,
                basePrice + (Math.random() - 0.5) * basePrice * 0.08
            ],
            riskReward: 3.0 + Math.random() * 2.0,
            timeframe: selectedTimeframe,
            reasoning: [
                'مرحلة التراكم/التوزيع محددة',
                'Volume Spread Analysis',
                'Effort vs Result تحليل',
                'Composite Man behavior',
                'Supply and Demand zones'
            ]
        };
        // Elliott Wave Theory
        const elliotWave = {
            name: 'Elliott Wave Theory',
            type: 'TREND_FOLLOWING',
            signal: getRandomSignal(),
            confidence: 70 + Math.random() * 25,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.025,
                basePrice + (Math.random() - 0.5) * basePrice * 0.04,
                basePrice + (Math.random() - 0.5) * basePrice * 0.065
            ],
            riskReward: 2.5 + Math.random() * 1.5,
            timeframe: selectedTimeframe,
            reasoning: [
                'موجة دافعة في التكوين',
                'نسب فيبوناتشي متوافقة',
                'Wave 3 extension محتملة',
                'Corrective wave completed',
                'Impulse pattern confirmed'
            ]
        };
        // Harmonic Patterns
        const harmonic = {
            name: 'Harmonic Patterns',
            type: 'MEAN_REVERSION',
            signal: getRandomSignal(),
            confidence: 78 + Math.random() * 17,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.012,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.02,
                basePrice + (Math.random() - 0.5) * basePrice * 0.035,
                basePrice + (Math.random() - 0.5) * basePrice * 0.05
            ],
            riskReward: 2.8 + Math.random() * 1.2,
            timeframe: selectedTimeframe,
            reasoning: [
                'Gartley pattern تكوين',
                'ABCD pattern completion',
                'Butterfly pattern potential',
                'Bat pattern في التطوير',
                'Crab pattern signals'
            ]
        };
        // Fibonacci Analysis
        const fibonacci = {
            name: 'Advanced Fibonacci',
            type: 'MEAN_REVERSION',
            signal: getRandomSignal(),
            confidence: 82 + Math.random() * 13,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.01,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.018,
                basePrice + (Math.random() - 0.5) * basePrice * 0.032,
                basePrice + (Math.random() - 0.5) * basePrice * 0.05
            ],
            riskReward: 3.2 + Math.random() * 1.8,
            timeframe: selectedTimeframe,
            reasoning: [
                '61.8% retracement level',
                '78.6% extension target',
                'Golden ratio confluence',
                'Multiple timeframe alignment',
                'Fibonacci clusters identified'
            ]
        };
        // Price Action
        const priceAction = {
            name: 'Pure Price Action',
            type: 'BREAKOUT',
            signal: getRandomSignal(),
            confidence: 88 + Math.random() * 7,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.012,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.022,
                basePrice + (Math.random() - 0.5) * basePrice * 0.038,
                basePrice + (Math.random() - 0.5) * basePrice * 0.055
            ],
            riskReward: 3.5 + Math.random() * 1.5,
            timeframe: selectedTimeframe,
            reasoning: [
                'Pin bar reversal pattern',
                'Inside bar breakout setup',
                'Engulfing candle confirmation',
                'Support/Resistance bounce',
                'Trend continuation pattern'
            ]
        };
        // Volume Profile
        const volumeProfile = {
            name: 'Volume Profile Analysis',
            type: 'MEAN_REVERSION',
            signal: getRandomSignal(),
            confidence: 76 + Math.random() * 19,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.015,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.025,
                basePrice + (Math.random() - 0.5) * basePrice * 0.04,
                basePrice + (Math.random() - 0.5) * basePrice * 0.06
            ],
            riskReward: 2.7 + Math.random() * 1.3,
            timeframe: selectedTimeframe,
            reasoning: [
                'POC (Point of Control) identified',
                'Value Area High/Low levels',
                'Volume imbalance detected',
                'High Volume Node support',
                'Low Volume Node breakout'
            ]
        };
        // Market Profile
        const marketProfile = {
            name: 'Market Profile',
            type: 'MEAN_REVERSION',
            signal: getRandomSignal(),
            confidence: 74 + Math.random() * 21,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.005,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.018,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.028,
                basePrice + (Math.random() - 0.5) * basePrice * 0.045,
                basePrice + (Math.random() - 0.5) * basePrice * 0.065
            ],
            riskReward: 2.5 + Math.random() * 2.0,
            timeframe: selectedTimeframe,
            reasoning: [
                'TPO (Time Price Opportunity)',
                'Value Area development',
                'Market acceptance levels',
                'Auction theory application',
                'Balance/Imbalance areas'
            ]
        };
        // Order Flow
        const orderFlow = {
            name: 'Order Flow Analysis',
            type: 'SCALPING',
            signal: getRandomSignal(),
            confidence: 90 + Math.random() * 5,
            entry: basePrice + (Math.random() - 0.5) * basePrice * 0.002,
            stopLoss: basePrice + (Math.random() - 0.5) * basePrice * 0.008,
            targets: [
                basePrice + (Math.random() - 0.5) * basePrice * 0.012,
                basePrice + (Math.random() - 0.5) * basePrice * 0.02,
                basePrice + (Math.random() - 0.5) * basePrice * 0.03
            ],
            riskReward: 2.0 + Math.random() * 1.0,
            timeframe: selectedTimeframe,
            reasoning: [
                'Bid/Ask imbalance detected',
                'Large order absorption',
                'Iceberg orders identified',
                'Delta divergence signals',
                'Footprint chart patterns'
            ]
        };
        return {
            ict,
            smc,
            wyckoff,
            elliotWave,
            harmonic,
            fibonacci,
            priceAction,
            volumeProfile,
            marketProfile,
            orderFlow
        };
    };
    const generateMarketStructure = (basePrice, volatility)=>{
        const trends = [
            'UPTREND',
            'DOWNTREND',
            'SIDEWAYS'
        ];
        const phases = [
            'ACCUMULATION',
            'MARKUP',
            'DISTRIBUTION',
            'MARKDOWN'
        ];
        return {
            trend: trends[Math.floor(Math.random() * trends.length)],
            phase: phases[Math.floor(Math.random() * phases.length)],
            strength: 60 + Math.random() * 35,
            keyLevels: Array.from({
                length: 5
            }, ()=>basePrice + (Math.random() - 0.5) * basePrice * 0.03),
            liquidityZones: Array.from({
                length: 4
            }, ()=>basePrice + (Math.random() - 0.5) * basePrice * 0.025),
            institutionalLevels: Array.from({
                length: 3
            }, ()=>basePrice + (Math.random() - 0.5) * basePrice * 0.02)
        };
    };
    const generateRiskAssessment = (volatility)=>{
        return {
            volatility: volatility * 100,
            atr: volatility * 100 * (0.8 + Math.random() * 0.4),
            correlation: Math.random() * 0.8,
            beta: 0.5 + Math.random() * 1.5,
            sharpeRatio: 0.5 + Math.random() * 2.5,
            maxDrawdown: volatility * 100 * (1 + Math.random()),
            winRate: 45 + Math.random() * 40,
            profitFactor: 1.2 + Math.random() * 1.8
        };
    };
    const calculateOverallSignal = (indicators, strategies, structure, priceData)=>{
        // Calculate weighted scores
        const indicatorScores = Object.values(indicators).flat().map((ind)=>getSignalScore(ind.signal) * (ind.strength / 100));
        const strategyScores = Object.values(strategies).map((strat)=>getSignalScore(strat.signal) * (strat.confidence / 100));
        const avgIndicatorScore = indicatorScores.reduce((a, b)=>a + b, 0) / indicatorScores.length;
        const avgStrategyScore = strategyScores.reduce((a, b)=>a + b, 0) / strategyScores.length;
        const overallScore = avgIndicatorScore * 0.4 + avgStrategyScore * 0.6;
        let signal = 'NEUTRAL';
        if (overallScore > 0.6) signal = 'STRONG_BUY';
        else if (overallScore > 0.2) signal = 'BUY';
        else if (overallScore < -0.6) signal = 'STRONG_SELL';
        else if (overallScore < -0.2) signal = 'SELL';
        const confidence = 70 + Math.abs(overallScore) * 25;
        const accuracy = 85 + Math.abs(overallScore) * 10;
        const reasoning = [
            `تحليل شامل لـ 40+ مؤشر فني`,
            `تقييم 10 استراتيجيات احترافية`,
            `تحليل هيكل السوق: ${structure.trend}`,
            `مرحلة السوق: ${structure.phase}`,
            priceData ? `بيانات حقيقية من TradingView: ${priceData.changePercent.toFixed(2)}%` : 'بيانات محاكاة',
            `النتيجة الإجمالية: ${(overallScore * 100).toFixed(1)}/100`
        ];
        return {
            overallSignal: signal,
            confidence,
            accuracy,
            reasoning
        };
    };
    const getRandomSignal = ()=>{
        const signals = [
            'STRONG_BUY',
            'BUY',
            'NEUTRAL',
            'SELL',
            'STRONG_SELL'
        ];
        return signals[Math.floor(Math.random() * signals.length)];
    };
    const getSignalScore = (signal)=>{
        switch(signal){
            case 'STRONG_BUY':
                return 1;
            case 'BUY':
                return 0.5;
            case 'NEUTRAL':
                return 0;
            case 'SELL':
                return -0.5;
            case 'STRONG_SELL':
                return -1;
            default:
                return 0;
        }
    };
    const getBasePrice = (symbol)=>{
        const prices = {
            'EURUSD': 1.0850,
            'GBPUSD': 1.2650,
            'USDJPY': 149.50,
            'USDCHF': 0.8920,
            'AUDUSD': 0.6580,
            'USDCAD': 1.3650,
            'NZDUSD': 0.6120,
            'EURGBP': 0.8580,
            'EURJPY': 162.30,
            'GBPJPY': 189.20,
            'XAUUSD': 2050.00,
            'XAGUSD': 24.50,
            'USOIL': 78.50,
            'BTCUSD': 43250.00
        };
        return prices[symbol] || 1.0000;
    };
    const getVolatility = (symbol)=>{
        const volatilities = {
            'EURUSD': 0.008,
            'GBPUSD': 0.012,
            'USDJPY': 0.010,
            'USDCHF': 0.007,
            'AUDUSD': 0.015,
            'USDCAD': 0.010,
            'NZDUSD': 0.018,
            'EURGBP': 0.006,
            'EURJPY': 0.013,
            'GBPJPY': 0.018,
            'XAUUSD': 0.020,
            'XAGUSD': 0.025,
            'USOIL': 0.030,
            'BTCUSD': 0.040
        };
        return volatilities[symbol] || 0.015;
    };
    const getPairFlag = (symbol)=>{
        const flags = {
            'EURUSD': '🇪🇺🇺🇸',
            'GBPUSD': '🇬🇧🇺🇸',
            'USDJPY': '🇺🇸🇯🇵',
            'USDCHF': '🇺🇸🇨🇭',
            'AUDUSD': '🇦🇺🇺🇸',
            'USDCAD': '🇺🇸🇨🇦',
            'NZDUSD': '🇳🇿🇺🇸',
            'EURGBP': '🇪🇺🇬🇧',
            'EURJPY': '🇪🇺🇯🇵',
            'GBPJPY': '🇬🇧🇯🇵',
            'XAUUSD': '🥇💰',
            'XAGUSD': '🥈💰',
            'USOIL': '🛢️💰',
            'BTCUSD': '₿💰'
        };
        return flags[symbol] || '💱';
    };
    const getSignalColor = (signal)=>{
        switch(signal){
            case 'STRONG_BUY':
                return 'bg-green-600 text-white';
            case 'BUY':
                return 'bg-green-500 text-white';
            case 'NEUTRAL':
                return 'bg-yellow-500 text-white';
            case 'SELL':
                return 'bg-red-500 text-white';
            case 'STRONG_SELL':
                return 'bg-red-600 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };
    const getSignalText = (signal)=>{
        switch(signal){
            case 'STRONG_BUY':
                return 'شراء قوي';
            case 'BUY':
                return 'شراء';
            case 'NEUTRAL':
                return 'محايد';
            case 'SELL':
                return 'بيع';
            case 'STRONG_SELL':
                return 'بيع قوي';
            default:
                return 'غير محدد';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white dark:bg-gray-800 rounded-lg shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-bold text-gray-900 dark:text-white flex items-center",
                                children: [
                                    "🏆 نظام التداول الاحترافي الشامل",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "mr-3 px-2 py-1 bg-purple-600 text-white rounded text-sm",
                                        children: "Professional Grade"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 795,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "mr-2 px-2 py-1 bg-blue-600 text-white rounded text-xs",
                                        children: "TradingView"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 798,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 793,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: runProfessionalAnalysis,
                                disabled: isAnalyzing,
                                className: `px-6 py-2 rounded-lg font-medium transition-colors ${isAnalyzing ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-purple-600 text-white hover:bg-purple-700'}`,
                                children: isAnalyzing ? '🔍 تحليل شامل...' : '🚀 تحليل احترافي شامل'
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 802,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                        lineNumber: 792,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "زوج العملة:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 818,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: selectedPair,
                                        onChange: (e)=>setSelectedPair(e.target.value),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",
                                        children: forexPairs.map((pair)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: pair,
                                                children: [
                                                    getPairFlag(pair),
                                                    " ",
                                                    pair
                                                ]
                                            }, pair, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 827,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 821,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 817,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "الإطار الزمني:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 835,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: selectedTimeframe,
                                        onChange: (e)=>setSelectedTimeframe(e.target.value),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",
                                        children: timeframes.map((tf)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: tf,
                                                children: tf
                                            }, tf, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 844,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 838,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 834,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                        lineNumber: 816,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                lineNumber: 791,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6",
                children: [
                    isAnalyzing && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inline-flex items-center space-x-3 space-x-reverse",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 856,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-lg text-gray-600 dark:text-gray-400",
                                        children: "🔍 تحليل احترافي شامل جاري..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 857,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 855,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-4 text-sm text-gray-500 space-y-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• جلب بيانات حقيقية من TradingView"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 862,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• تحليل 40+ مؤشر فني متقدم"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 863,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• تقييم 10 استراتيجيات احترافية"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 864,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• تحليل هيكل السوق المتقدم"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 865,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• تقييم المخاطر الشامل"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 866,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• توليد توصية نهائية"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 867,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 861,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                        lineNumber: 854,
                        columnNumber: 11
                    }, this),
                    !isAnalyzing && !analysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-6xl mb-4",
                                children: "🏆"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 875,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold text-gray-900 dark:text-white mb-2",
                                children: "نظام التداول الاحترافي الشامل"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 876,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",
                                children: "تحليل شامل يتضمن جميع الاستراتيجيات والمؤشرات المستخدمة من قبل المتداولين المحترفين"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 879,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 max-w-lg mx-auto",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-purple-900 dark:text-purple-100 mb-2",
                                        children: "🎯 ما يتضمنه التحليل الاحترافي:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 883,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "text-sm text-purple-800 dark:text-purple-200 space-y-1 text-right",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 40+ مؤشر فني متقدم"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 887,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• 10 استراتيجيات احترافية"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 888,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• تحليل ICT و Smart Money"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 889,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• نظرية Wyckoff و Elliott Wave"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 890,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• الأنماط التوافقية المتقدمة"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 891,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• تحليل Volume Profile و Order Flow"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 892,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 886,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 882,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                        lineNumber: 874,
                        columnNumber: 11
                    }, this),
                    !isAnalyzing && analysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-3 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-3xl",
                                                        children: getPairFlag(analysis.symbol)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 905,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "text-2xl font-bold text-gray-900 dark:text-white",
                                                                children: analysis.symbol
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 907,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                children: [
                                                                    selectedTimeframe,
                                                                    " | السعر: ",
                                                                    analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5),
                                                                    analysis.priceChangePercent !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: `mr-2 ${analysis.priceChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`,
                                                                        children: [
                                                                            "(",
                                                                            analysis.priceChangePercent >= 0 ? '+' : '',
                                                                            analysis.priceChangePercent.toFixed(2),
                                                                            "%)"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 913,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 910,
                                                                columnNumber: 21
                                                            }, this),
                                                            analysis.lastUpdate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-gray-500",
                                                                children: [
                                                                    "آخر تحديث: ",
                                                                    new Date(analysis.lastUpdate).toLocaleTimeString('ar-SA'),
                                                                    " |",
                                                                    analysis.volume && ` حجم: ${(analysis.volume / 1000000).toFixed(1)}M`
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 919,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 906,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 904,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-right",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `px-4 py-2 rounded-full text-lg font-bold ${getSignalColor(analysis.overallSignal)}`,
                                                        children: getSignalText(analysis.overallSignal)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 927,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400 mt-2",
                                                        children: [
                                                            "ثقة: ",
                                                            analysis.confidence.toFixed(0),
                                                            "% | دقة: ",
                                                            analysis.accuracy.toFixed(0),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 930,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 926,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 903,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white dark:bg-gray-700 rounded p-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                className: "font-medium text-gray-900 dark:text-white mb-2",
                                                children: "📋 ملخص التحليل الشامل:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 937,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: "text-sm space-y-1",
                                                children: analysis.reasoning.map((reason, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: "flex items-start",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "mr-2 text-purple-500",
                                                                children: "▶"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 941,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-gray-700 dark:text-gray-300",
                                                                children: reason
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 942,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, `analysis-reasoning-${i}`, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 940,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 938,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 936,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 902,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `rounded-xl p-6 border-4 ${analysis.overallSignal.includes('BUY') ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20' : analysis.overallSignal.includes('SELL') ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20' : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-3xl font-bold text-gray-900 dark:text-white mb-2",
                                                children: "🎯 الخلاصة النهائية للتداول"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 958,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ${getSignalColor(analysis.overallSignal)}`,
                                                children: [
                                                    analysis.overallSignal.includes('BUY') && '📈 ',
                                                    analysis.overallSignal.includes('SELL') && '📉 ',
                                                    analysis.overallSignal === 'NEUTRAL' && '➡️ ',
                                                    getSignalText(analysis.overallSignal)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 961,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-lg text-gray-600 dark:text-gray-400 mt-2",
                                                children: "بناءً على تحليل 40+ مؤشر و 10 استراتيجيات احترافية"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 967,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 957,
                                        columnNumber: 15
                                    }, this),
                                    analysis.overallSignal !== 'NEUTRAL' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center",
                                                        children: "💰 إعداد التداول المقترح"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 976,
                                                        columnNumber: 21
                                                    }, this),
                                                    (()=>{
                                                        // Calculate trading levels based on best strategy
                                                        const bestStrategy = Object.values(analysis.strategies).sort((a, b)=>b.confidence - a.confidence)[0];
                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-4",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-2 gap-4",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                    children: "نقطة الدخول"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 989,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-lg font-bold text-blue-600",
                                                                                    children: bestStrategy.entry.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 990,
                                                                                    columnNumber: 31
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                            lineNumber: 988,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "bg-red-50 dark:bg-red-900/20 rounded p-3 text-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                    children: "وقف الخسارة"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 995,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-lg font-bold text-red-600",
                                                                                    children: bestStrategy.stopLoss.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 996,
                                                                                    columnNumber: 31
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                            lineNumber: 994,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                    lineNumber: 987,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-3 gap-2",
                                                                    children: bestStrategy.targets.map((target, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "bg-green-50 dark:bg-green-900/20 rounded p-2 text-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-xs text-gray-600 dark:text-gray-400",
                                                                                    children: [
                                                                                        "هدف ",
                                                                                        i + 1
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1005,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm font-bold text-green-600",
                                                                                    children: target.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1006,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, `best-strategy-target-${i}`, true, {
                                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                            lineNumber: 1004,
                                                                            columnNumber: 31
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                    lineNumber: 1002,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-2 gap-4",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "bg-purple-50 dark:bg-purple-900/20 rounded p-3 text-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                    children: "نسبة المخاطرة/العائد"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1015,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-lg font-bold text-purple-600",
                                                                                    children: [
                                                                                        bestStrategy.riskReward.toFixed(2),
                                                                                        ":1"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1016,
                                                                                    columnNumber: 31
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                            lineNumber: 1014,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "bg-indigo-50 dark:bg-indigo-900/20 rounded p-3 text-center",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                    children: "الإطار الزمني"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1021,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-lg font-bold text-indigo-600",
                                                                                    children: bestStrategy.timeframe
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                    lineNumber: 1022,
                                                                                    columnNumber: 31
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                            lineNumber: 1020,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                    lineNumber: 1013,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                            lineNumber: 986,
                                                            columnNumber: 25
                                                        }, this);
                                                    })()
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 975,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center",
                                                        children: "⚠️ إدارة المخاطر"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1034,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-orange-50 dark:bg-orange-900/20 rounded p-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                        className: "font-medium text-orange-800 dark:text-orange-200 mb-2",
                                                                        children: "📊 تقييم المخاطر:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1040,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "grid grid-cols-2 gap-2 text-sm",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "التقلبات: ",
                                                                                    analysis.riskAssessment.volatility.toFixed(1),
                                                                                    "%"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1044,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "ATR: ",
                                                                                    analysis.riskAssessment.atr.toFixed(2)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1045,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "أقصى انخفاض: ",
                                                                                    analysis.riskAssessment.maxDrawdown.toFixed(1),
                                                                                    "%"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1046,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "معدل الفوز: ",
                                                                                    analysis.riskAssessment.winRate.toFixed(0),
                                                                                    "%"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1047,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1043,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1039,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-yellow-50 dark:bg-yellow-900/20 rounded p-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                        className: "font-medium text-yellow-800 dark:text-yellow-200 mb-2",
                                                                        children: "💡 نصائح التداول:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1052,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                        className: "text-sm space-y-1",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                children: "• لا تخاطر بأكثر من 2% من رأس المال"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1056,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                children: "• استخدم وقف الخسارة دائماً"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1057,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                children: "• راقب الأخبار الاقتصادية المهمة"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1058,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                children: "• تأكد من توافق الإطارات الزمنية"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                                lineNumber: 1059,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1055,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1051,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1038,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1033,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 973,
                                        columnNumber: 17
                                    }, this),
                                    analysis.overallSignal === 'NEUTRAL' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white dark:bg-gray-700 rounded-lg p-6 text-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-xl font-bold text-gray-900 dark:text-white mb-4",
                                                children: "⚖️ السوق في حالة توازن"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1069,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-600 dark:text-gray-400 mb-4",
                                                children: "التحليل الشامل يشير إلى عدم وجود اتجاه واضح في الوقت الحالي"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1072,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-yellow-50 dark:bg-yellow-900/20 rounded p-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-yellow-800 dark:text-yellow-200 mb-2",
                                                        children: "📋 التوصيات:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1076,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "text-sm space-y-1 text-yellow-700 dark:text-yellow-300",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: "• انتظار إشارة واضحة قبل الدخول"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1080,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: "• مراقبة كسر المستويات المهمة"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1081,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: "• تحليل الإطارات الزمنية الأعلى"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1082,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: "• متابعة الأخبار الاقتصادية"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1083,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1079,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1075,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1068,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-6 bg-white dark:bg-gray-700 rounded-lg p-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                                        children: "مستوى الثقة الإجمالي:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1092,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-bold text-gray-900 dark:text-white",
                                                        children: [
                                                            analysis.confidence.toFixed(0),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1093,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1091,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-full bg-gray-200 rounded-full h-3",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `h-3 rounded-full transition-all duration-500 ${analysis.confidence > 80 ? 'bg-green-500' : analysis.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'}`,
                                                    style: {
                                                        width: `${analysis.confidence}%`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                    lineNumber: 1096,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1095,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between text-xs text-gray-500 mt-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "منخفض"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1105,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "متوسط"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1106,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "عالي"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1107,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1104,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1090,
                                        columnNumber: 15
                                    }, this),
                                    (()=>{
                                        const bestStrategy = Object.values(analysis.strategies).sort((a, b)=>b.confidence - a.confidence)[0];
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                    className: "font-medium text-blue-900 dark:text-blue-100 mb-2",
                                                    children: [
                                                        "🏆 أفضل استراتيجية: ",
                                                        bestStrategy.name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                    lineNumber: 1118,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm text-blue-800 dark:text-blue-200",
                                                    children: [
                                                        "ثقة: ",
                                                        bestStrategy.confidence.toFixed(0),
                                                        "% | نوع: ",
                                                        bestStrategy.type,
                                                        " | R/R: ",
                                                        bestStrategy.riskReward.toFixed(2),
                                                        ":1"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                    lineNumber: 1121,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                            lineNumber: 1117,
                                            columnNumber: 19
                                        }, this);
                                    })()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 950,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-b border-gray-200 dark:border-gray-600",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                    className: "flex space-x-8 space-x-reverse",
                                    children: [
                                        {
                                            key: 'strategies',
                                            label: '🎯 الاستراتيجيات',
                                            count: 10
                                        },
                                        {
                                            key: 'indicators',
                                            label: '📊 المؤشرات',
                                            count: 40
                                        },
                                        {
                                            key: 'structure',
                                            label: '🏗️ هيكل السوق',
                                            count: 1
                                        },
                                        {
                                            key: 'risk',
                                            label: '⚠️ المخاطر',
                                            count: 8
                                        }
                                    ].map((tab)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setActiveTab(tab.key),
                                            className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.key ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,
                                            children: [
                                                tab.label,
                                                " (",
                                                tab.count,
                                                ")"
                                            ]
                                        }, tab.key, true, {
                                            fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                            lineNumber: 1138,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                    lineNumber: 1131,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 1130,
                                columnNumber: 13
                            }, this),
                            activeTab === 'strategies' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 lg:grid-cols-2 gap-4",
                                children: Object.entries(analysis.strategies).map(([key, strategy])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white dark:bg-gray-700 rounded-lg p-4 border-2 border-gray-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-bold text-gray-900 dark:text-white",
                                                        children: strategy.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1159,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `px-2 py-1 rounded text-xs font-medium ${getSignalColor(strategy.signal)}`,
                                                        children: getSignalText(strategy.signal)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1160,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1158,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-2 gap-2 text-xs mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "نوع: ",
                                                            strategy.type
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1166,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "ثقة: ",
                                                            strategy.confidence.toFixed(0),
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1167,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "دخول: ",
                                                            strategy.entry.toFixed(5)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1168,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "R/R: ",
                                                            strategy.riskReward.toFixed(1),
                                                            ":1"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1169,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1165,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-xs",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "font-medium mb-1",
                                                        children: "الأسباب:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1173,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "space-y-1",
                                                        children: strategy.reasoning.slice(0, 3).map((reason, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    "• ",
                                                                    reason
                                                                ]
                                                            }, `strategy-reasoning-${strategy.name}-${i}`, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1176,
                                                                columnNumber: 27
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1174,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1172,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, key, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1157,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 1155,
                                columnNumber: 15
                            }, this),
                            activeTab === 'indicators' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-6",
                                children: Object.entries(analysis.technicalIndicators).map(([category, indicators])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white dark:bg-gray-700 rounded-lg p-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                className: "font-bold text-gray-900 dark:text-white mb-3 capitalize",
                                                children: [
                                                    "📊 ",
                                                    category.replace('_', ' '),
                                                    " (",
                                                    indicators.length,
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1189,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",
                                                children: indicators.map((indicator, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "border border-gray-200 rounded p-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center justify-between mb-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "font-medium text-sm",
                                                                        children: indicator.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1196,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: `px-2 py-1 rounded text-xs ${getSignalColor(indicator.signal)}`,
                                                                        children: getSignalText(indicator.signal)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1197,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1195,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-xs text-gray-600 dark:text-gray-400",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            "قيمة: ",
                                                                            typeof indicator.value === 'number' ? indicator.value.toFixed(2) : indicator.value
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1202,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            "قوة: ",
                                                                            indicator.strength.toFixed(0),
                                                                            "%"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1203,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1201,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, `indicator-${category}-${indicator.name}-${i}`, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1194,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1192,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, category, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1188,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 1186,
                                columnNumber: 15
                            }, this),
                            activeTab === 'structure' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                        className: "font-bold text-gray-900 dark:text-white mb-4",
                                        children: "🏗️ تحليل هيكل السوق"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1215,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h6", {
                                                        className: "font-medium mb-3",
                                                        children: "معلومات عامة:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1219,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-2 text-sm",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "الاتجاه: ",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: analysis.marketStructure.trend
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1221,
                                                                        columnNumber: 37
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1221,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "المرحلة: ",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: analysis.marketStructure.phase
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1222,
                                                                        columnNumber: 37
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1222,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "القوة: ",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: [
                                                                            analysis.marketStructure.strength.toFixed(0),
                                                                            "%"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                        lineNumber: 1223,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1223,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1220,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1218,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h6", {
                                                        className: "font-medium mb-3",
                                                        children: "المستويات المهمة:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1228,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-1 text-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "مستويات رئيسية: ",
                                                                    analysis.marketStructure.keyLevels.length
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1230,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "مناطق سيولة: ",
                                                                    analysis.marketStructure.liquidityZones.length
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1231,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    "مستويات مؤسسية: ",
                                                                    analysis.marketStructure.institutionalLevels.length
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                                lineNumber: 1232,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1229,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1227,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1217,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 1214,
                                columnNumber: 15
                            }, this),
                            activeTab === 'risk' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                        className: "font-bold text-gray-900 dark:text-white mb-4",
                                        children: "⚠️ تقييم المخاطر"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1241,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 md:grid-cols-4 gap-4",
                                        children: Object.entries(analysis.riskAssessment).map(([key, value])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center bg-gray-50 dark:bg-gray-600 rounded p-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-bold text-gray-900 dark:text-white",
                                                        children: typeof value === 'number' ? value.toFixed(2) : value
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1246,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs text-gray-600 dark:text-gray-400 capitalize",
                                                        children: key.replace(/([A-Z])/g, ' $1').trim()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                        lineNumber: 1249,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, `risk-assessment-${key}`, true, {
                                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                                lineNumber: 1245,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                        lineNumber: 1243,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                                lineNumber: 1240,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                        lineNumber: 900,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
                lineNumber: 851,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ProfessionalTradingSystem.tsx",
        lineNumber: 789,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=src_components_ProfessionalTradingSystem_tsx_d4a48626._.js.map