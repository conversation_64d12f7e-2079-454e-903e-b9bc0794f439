{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AutoTradingRobot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface RobotSettings {\n  enabled: boolean;\n  maxRiskPerTrade: number;\n  maxDailyRisk: number;\n  minConfidence: number;\n  allowedPairs: string[];\n  tradingHours: {\n    start: string;\n    end: string;\n  };\n  maxOpenTrades: number;\n}\n\ninterface RobotStats {\n  totalTrades: number;\n  winningTrades: number;\n  losingTrades: number;\n  totalProfit: number;\n  winRate: number;\n  profitFactor: number;\n  currentDrawdown: number;\n}\n\nexport default function AutoTradingRobot() {\n  const [robotSettings, setRobotSettings] = useState<RobotSettings>({\n    enabled: false,\n    maxRiskPerTrade: 2,\n    maxDailyRisk: 6,\n    minConfidence: 80,\n    allowedPairs: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],\n    tradingHours: {\n      start: '08:00',\n      end: '18:00'\n    },\n    maxOpenTrades: 3\n  });\n\n  const [robotStats, setRobotStats] = useState<RobotStats>({\n    totalTrades: 47,\n    winningTrades: 34,\n    losingTrades: 13,\n    totalProfit: 2850.50,\n    winRate: 72.3,\n    profitFactor: 1.85,\n    currentDrawdown: 3.2\n  });\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [lastAction, setLastAction] = useState('');\n\n  // Simulate robot activity\n  useEffect(() => {\n    if (robotSettings.enabled && isRunning) {\n      const interval = setInterval(() => {\n        const actions = [\n          'تحليل إشارة EUR/USD...',\n          'فحص مستويات الدعم والمقاومة...',\n          'تقييم مخاطر الصفقة...',\n          'مراقبة Order Blocks...',\n          'تحليل Fair Value Gaps...',\n          'فحص تدفق الأموال الذكية...',\n          'تنفيذ صفقة شراء EUR/USD',\n          'إغلاق صفقة بربح +45 نقطة',\n          'وضع وقف خسارة متحرك...',\n          'مراقبة الجلسة الآسيوية...'\n        ];\n        \n        const randomAction = actions[Math.floor(Math.random() * actions.length)];\n        setLastAction(randomAction);\n      }, 3000);\n\n      return () => clearInterval(interval);\n    }\n  }, [robotSettings.enabled, isRunning]);\n\n  const toggleRobot = () => {\n    setRobotSettings(prev => ({ ...prev, enabled: !prev.enabled }));\n    setIsRunning(!isRunning);\n    if (!robotSettings.enabled) {\n      setLastAction('تم تشغيل الروبوت - بدء المراقبة...');\n    } else {\n      setLastAction('تم إيقاف الروبوت');\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n            🤖 روبوت التداول الآلي\n            <span className={`mr-2 text-xs px-2 py-1 rounded-full ${\n              robotSettings.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n            }`}>\n              {robotSettings.enabled ? 'نشط' : 'متوقف'}\n            </span>\n          </h3>\n          <button\n            onClick={toggleRobot}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              robotSettings.enabled\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-green-600 hover:bg-green-700 text-white'\n            }`}\n          >\n            {robotSettings.enabled ? '⏹️ إيقاف الروبوت' : '▶️ تشغيل الروبوت'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Robot Settings */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              ⚙️ إعدادات الروبوت\n            </h4>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للمخاطرة لكل صفقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"5\"\n                  step=\"0.5\"\n                  value={robotSettings.maxRiskPerTrade}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxRiskPerTrade: parseFloat(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.maxRiskPerTrade}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأدنى لمستوى الثقة (%)\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"60\"\n                  max=\"95\"\n                  step=\"5\"\n                  value={robotSettings.minConfidence}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    minConfidence: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full\"\n                />\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                  {robotSettings.minConfidence}%\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  الحد الأقصى للصفقات المفتوحة\n                </label>\n                <select\n                  value={robotSettings.maxOpenTrades}\n                  onChange={(e) => setRobotSettings(prev => ({\n                    ...prev,\n                    maxOpenTrades: parseInt(e.target.value)\n                  }))}\n                  className=\"w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                >\n                  <option value={1}>1 صفقة</option>\n                  <option value={2}>2 صفقة</option>\n                  <option value={3}>3 صفقات</option>\n                  <option value={5}>5 صفقات</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  ساعات التداول\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.start}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, start: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <input\n                    type=\"time\"\n                    value={robotSettings.tradingHours.end}\n                    onChange={(e) => setRobotSettings(prev => ({\n                      ...prev,\n                      tradingHours: { ...prev.tradingHours, end: e.target.value }\n                    }))}\n                    className=\"p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Robot Stats */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n              📊 إحصائيات الأداء\n            </h4>\n            \n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{robotStats.totalTrades}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الصفقات</div>\n              </div>\n              \n              <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{robotStats.winRate.toFixed(1)}%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">معدل النجاح</div>\n              </div>\n              \n              <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">${robotStats.totalProfit.toFixed(2)}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إجمالي الربح</div>\n              </div>\n              \n              <div className=\"bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600\">{robotStats.profitFactor}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">عامل الربح</div>\n              </div>\n            </div>\n\n            {/* Robot Activity */}\n            <div className=\"mt-6\">\n              <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                🔄 النشاط الحالي\n              </h5>\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  {robotSettings.enabled && (\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  )}\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {lastAction || 'الروبوت في وضع الانتظار...'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Warning */}\n        <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n              <span className=\"text-white text-sm\">⚠</span>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                تحذير مهم - الروبوت التجريبي\n              </h4>\n              <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                هذا روبوت تجريبي للأغراض التعليمية فقط. لا يتم تنفيذ صفقات حقيقية. \n                اختبر جميع الإعدادات بعناية قبل استخدام أي نظام تداول آلي حقيقي.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,cAAc;YAAC;YAAU;YAAU;YAAU;SAAS;QACtD,cAAc;YACZ,OAAO;YACP,KAAK;QACP;QACA,eAAe;IACjB;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,SAAS;QACT,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,OAAO,IAAI,WAAW;gBACtC,MAAM,WAAW;2DAAY;wBAC3B,MAAM,UAAU;4BACd;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;wBAED,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;wBACxE,cAAc;oBAChB;0DAAG;gBAEH;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC,cAAc,OAAO;QAAE;KAAU;IAErC,MAAM,cAAc;QAClB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS,CAAC,KAAK,OAAO;YAAC,CAAC;QAC7D,aAAa,CAAC;QACd,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,cAAc;QAChB,OAAO;YACL,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAwE;8CAEpF,6LAAC;oCAAK,WAAW,AAAC,uCAEjB,OADC,cAAc,OAAO,GAAG,gCAAgC;8CAEvD,cAAc,OAAO,GAAG,QAAQ;;;;;;;;;;;;sCAGrC,6LAAC;4BACC,SAAS;4BACT,WAAW,AAAC,sDAIX,OAHC,cAAc,OAAO,GACjB,2CACA;sCAGL,cAAc,OAAO,GAAG,qBAAqB;;;;;;;;;;;;;;;;;0BAKpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,eAAe;wDACpC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5C,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,eAAe;4DAAC;;;;;;;;;;;;;0DAInC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,aAAa;4DAAC;;;;;;;;;;;;;0DAIjC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,OAAO,cAAc,aAAa;wDAClC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEACzC,GAAG,IAAI;oEACP,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,CAAC;wDACD,WAAU;;0EAEV,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;0EAClB,6LAAC;gEAAO,OAAO;0EAAG;;;;;;;;;;;;;;;;;;0DAItB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,KAAK;gEACvC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC9D,CAAC;gEACD,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,YAAY,CAAC,GAAG;gEACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EACzC,GAAG,IAAI;4EACP,cAAc;gFAAE,GAAG,KAAK,YAAY;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAC;wEAC5D,CAAC;gEACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,WAAW,WAAW;;;;;;kEAC1E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAoC,WAAW,OAAO,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEACjF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAqC;4DAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;kEACrF,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,WAAW,YAAY;;;;;;kEAC5E,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,OAAO,kBACpB,6LAAC;4DAAI,WAAU;;;;;;sEAEjB,6LAAC;4DAAK,WAAU;sEACb,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgE;;;;;;sDAG9E,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1E;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AdvancedMarketAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport TimeframeAnalysis from './TimeframeAnalysis';\n\ninterface MarketCondition {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  buyPressure: number;\n  sellPressure: number;\n  trend: {\n    direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    strength: number;\n    timeframes: {\n      '1m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '5m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '15m': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '4h': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n      '1d': 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n    };\n  };\n  technicalAnalysis: {\n    rsi: number;\n    macd: number;\n    ema: number;\n    support: number;\n    resistance: number;\n    recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n    confidence: number;\n  };\n  flag: string;\n}\n\ninterface AIRecommendation {\n  symbol: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  timeframe: string;\n  reasoning: string[];\n  accuracy: number;\n}\n\nexport default function AdvancedMarketAnalysis() {\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']);\n  const [marketConditions, setMarketConditions] = useState<MarketCondition[]>([]);\n  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n  const allPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'EURCHF', 'GBPCHF', 'AUDCHF', 'CADJPY',\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD', 'USDJPY'\n  ];\n\n  // محاكاة الاتصال بـ TradingView\n  useEffect(() => {\n    const connectToTradingView = async () => {\n      setIsConnected(true);\n      // محاكاة تحميل البيانات\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      generateMarketData();\n    };\n\n    connectToTradingView();\n    \n    // تحديث البيانات كل 5 ثوانٍ\n    const interval = setInterval(() => {\n      generateMarketData();\n      setLastUpdate(new Date());\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [selectedTimeframe, selectedPairs]);\n\n  const generateMarketData = () => {\n    const conditions: MarketCondition[] = selectedPairs.map(symbol => {\n      const basePrice = getBasePrice(symbol);\n      const change = (Math.random() - 0.5) * basePrice * 0.02;\n      const buyPressure = Math.random() * 100;\n      const sellPressure = 100 - buyPressure;\n      \n      return {\n        symbol,\n        name: getPairName(symbol),\n        price: basePrice + change,\n        change,\n        changePercent: (change / basePrice) * 100,\n        volume: Math.floor(Math.random() * 2000000) + 500000,\n        buyPressure,\n        sellPressure,\n        trend: {\n          direction: buyPressure > 60 ? 'BULLISH' : buyPressure < 40 ? 'BEARISH' : 'NEUTRAL',\n          strength: Math.abs(buyPressure - 50) * 2,\n          timeframes: {\n            '1m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '5m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '15m': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1h': buyPressure > 50 ? 'BULLISH' : 'BEARISH',\n            '4h': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n            '1d': Math.random() > 0.5 ? 'BULLISH' : 'BEARISH',\n          }\n        },\n        technicalAnalysis: {\n          rsi: Math.random() * 100,\n          macd: (Math.random() - 0.5) * 0.01,\n          ema: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n          support: basePrice - Math.random() * basePrice * 0.02,\n          resistance: basePrice + Math.random() * basePrice * 0.02,\n          recommendation: getRecommendation(buyPressure),\n          confidence: 70 + Math.random() * 25\n        },\n        flag: getPairFlag(symbol)\n      };\n    });\n\n    setMarketConditions(conditions);\n    generateAIRecommendations(conditions);\n  };\n\n  const generateAIRecommendations = (conditions: MarketCondition[]) => {\n    const recommendations: AIRecommendation[] = conditions\n      .filter(condition => condition.technicalAnalysis.confidence > 80)\n      .slice(0, 3)\n      .map(condition => {\n        const action = condition.buyPressure > 65 ? 'BUY' : condition.buyPressure < 35 ? 'SELL' : 'HOLD';\n        const entry = condition.price;\n        const stopLoss = action === 'BUY' \n          ? entry - (entry * 0.015) \n          : entry + (entry * 0.015);\n        const tp1 = action === 'BUY' \n          ? entry + (entry * 0.02) \n          : entry - (entry * 0.02);\n        const tp2 = action === 'BUY' \n          ? entry + (entry * 0.035) \n          : entry - (entry * 0.035);\n        const tp3 = action === 'BUY' \n          ? entry + (entry * 0.05) \n          : entry - (entry * 0.05);\n\n        return {\n          symbol: condition.symbol,\n          action,\n          entry,\n          stopLoss,\n          takeProfit1: tp1,\n          takeProfit2: tp2,\n          takeProfit3: tp3,\n          riskReward: Math.abs(tp1 - entry) / Math.abs(entry - stopLoss),\n          confidence: condition.technicalAnalysis.confidence,\n          timeframe: selectedTimeframe,\n          reasoning: generateReasoning(condition, action),\n          accuracy: 85 + Math.random() * 10\n        };\n      });\n\n    setAIRecommendations(recommendations);\n  };\n\n  const generateReasoning = (condition: MarketCondition, action: string): string[] => {\n    const reasons = [];\n    \n    if (action === 'BUY') {\n      reasons.push(`ضغط الشراء قوي: ${condition.buyPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم صاعد: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر فوق EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى المقاومة: ${condition.technicalAnalysis.resistance.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    } else if (action === 'SELL') {\n      reasons.push(`ضغط البيع قوي: ${condition.sellPressure.toFixed(1)}%`);\n      reasons.push(`RSI يظهر زخم هابط: ${condition.technicalAnalysis.rsi.toFixed(1)}`);\n      reasons.push(`السعر تحت EMA: ${condition.technicalAnalysis.ema.toFixed(5)}`);\n      reasons.push(`كسر مستوى الدعم: ${condition.technicalAnalysis.support.toFixed(5)}`);\n      reasons.push(`حجم التداول مرتفع: ${(condition.volume / 1000000).toFixed(1)}M`);\n    }\n\n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getRecommendation = (buyPressure: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    if (buyPressure > 80) return 'STRONG_BUY';\n    if (buyPressure > 60) return 'BUY';\n    if (buyPressure > 40) return 'NEUTRAL';\n    if (buyPressure > 20) return 'SELL';\n    return 'STRONG_SELL';\n  };\n\n  const getRecommendationColor = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'text-green-600 bg-green-100';\n      case 'BUY': return 'text-green-500 bg-green-50';\n      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';\n      case 'SELL': return 'text-red-500 bg-red-50';\n      case 'STRONG_SELL': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getRecommendationText = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Controls */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📊 تحليل السوق المتقدم\n            <span className={`mr-3 w-3 h-3 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>\n            <span className=\"text-sm font-normal text-gray-600 dark:text-gray-400\">\n              {isConnected ? 'متصل بـ TradingView' : 'غير متصل'}\n            </span>\n          </h2>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf}\n                  onClick={() => setSelectedTimeframe(tf)}\n                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                    selectedTimeframe === tf\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Enhanced Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات المختارة ({selectedPairs.length}):\n            </label>\n\n            {/* Quick Selection Buttons */}\n            <div className=\"flex flex-wrap gap-2 mb-3\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700\"\n              >\n                العملات الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['BTCUSD', 'ETHUSD'])}\n                className=\"px-3 py-1 bg-purple-600 text-white rounded text-xs font-medium hover:bg-purple-700\"\n              >\n                العملات الرقمية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allPairs)}\n                className=\"px-3 py-1 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700\"\n              >\n                الكل ({allPairs.length})\n              </button>\n              <button\n                onClick={() => setSelectedPairs([])}\n                className=\"px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700\"\n              >\n                مسح الكل\n              </button>\n            </div>\n\n            {/* Individual Pair Selection */}\n            <div className=\"grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              {allPairs.map(pair => (\n                <button\n                  key={pair}\n                  onClick={() => {\n                    if (selectedPairs.includes(pair)) {\n                      setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                    } else {\n                      setSelectedPairs([...selectedPairs, pair]);\n                    }\n                  }}\n                  className={`px-2 py-1 rounded text-xs font-medium transition-colors ${\n                    selectedPairs.includes(pair)\n                      ? 'bg-green-600 text-white shadow-md'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                  title={getPairName(pair)}\n                >\n                  {pair}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market Conditions Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {marketConditions.map(condition => (\n          <div key={condition.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-lg\">{condition.flag}</span>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {condition.symbol}\n                  </h3>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {condition.name}\n                  </p>\n                </div>\n              </div>\n              <div className={`px-2 py-1 rounded text-xs font-medium ${getRecommendationColor(condition.technicalAnalysis.recommendation)}`}>\n                {getRecommendationText(condition.technicalAnalysis.recommendation)}\n              </div>\n            </div>\n\n            {/* Price and Change */}\n            <div className=\"mb-4\">\n              <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {condition.price.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n              </div>\n              <div className={`text-sm font-medium ${condition.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {condition.changePercent >= 0 ? '+' : ''}{condition.changePercent.toFixed(2)}%\n              </div>\n            </div>\n\n            {/* Buy/Sell Pressure */}\n            <div className=\"mb-4\">\n              <div className=\"flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1\">\n                <span>ضغط الشراء: {condition.buyPressure.toFixed(1)}%</span>\n                <span>ضغط البيع: {condition.sellPressure.toFixed(1)}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-green-500 h-2 rounded-full transition-all duration-500\"\n                  style={{ width: `${condition.buyPressure}%` }}\n                ></div>\n              </div>\n            </div>\n\n            {/* Multi-timeframe Trend */}\n            <div className=\"mb-4\">\n              <h4 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">الاتجاه متعدد الإطارات:</h4>\n              <div className=\"grid grid-cols-3 gap-1 text-xs\">\n                {Object.entries(condition.trend.timeframes).map(([tf, trend]) => (\n                  <div key={tf} className={`text-center py-1 rounded ${\n                    trend === 'BULLISH' ? 'bg-green-100 text-green-800' :\n                    trend === 'BEARISH' ? 'bg-red-100 text-red-800' :\n                    'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    <div className=\"font-medium\">{tf}</div>\n                    <div>{trend === 'BULLISH' ? '↗' : trend === 'BEARISH' ? '↘' : '→'}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Technical Indicators */}\n            <div className=\"space-y-2 text-xs\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">RSI:</span>\n                <span className={`font-medium ${\n                  condition.technicalAnalysis.rsi > 70 ? 'text-red-600' :\n                  condition.technicalAnalysis.rsi < 30 ? 'text-green-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {condition.technicalAnalysis.rsi.toFixed(1)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الدعم:</span>\n                <span className=\"font-medium text-green-600\">\n                  {condition.technicalAnalysis.support.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">المقاومة:</span>\n                <span className=\"font-medium text-red-600\">\n                  {condition.technicalAnalysis.resistance.toFixed(condition.symbol.includes('JPY') ? 3 : 5)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الثقة:</span>\n                <span className=\"font-medium text-blue-600\">\n                  {condition.technicalAnalysis.confidence.toFixed(1)}%\n                </span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* AI Recommendations */}\n      {aiRecommendations.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المربحة\n            <span className=\"mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n              دقة عالية\n            </span>\n          </h3>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n            {aiRecommendations.map((rec, index) => (\n              <div key={index} className={`border-2 rounded-xl p-6 ${\n                rec.action === 'BUY' \n                  ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                  : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n              }`}>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                    <div>\n                      <h4 className=\"text-lg font-bold text-gray-900\">\n                        {rec.action} {rec.symbol}\n                      </h4>\n                      <p className=\"text-xs text-gray-600\">{rec.timeframe}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900\">\n                      {rec.confidence.toFixed(1)}%\n                    </div>\n                    <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(1)}%</div>\n                  </div>\n                </div>\n\n                {/* Price Levels */}\n                <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">الدخول</div>\n                    <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                    <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">هدف 1</div>\n                    <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                  </div>\n                  <div className=\"bg-white rounded p-2 text-center\">\n                    <div className=\"text-xs text-gray-600\">R/R</div>\n                    <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                  </div>\n                </div>\n\n                {/* AI Reasoning */}\n                <div className=\"bg-white rounded p-3\">\n                  <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                  <ul className=\"text-xs text-gray-600 space-y-1\">\n                    {rec.reasoning.slice(0, 3).map((reason, i) => (\n                      <li key={i} className=\"flex items-start\">\n                        <span className=\"mr-1 text-blue-500\">▶</span>\n                        <span>{reason}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqDe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;QAAU;KAAS;IACrG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IACrE,MAAM,WAAW;QACf;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAS;QAAU;QAAU;KAClD;IAED,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;yEAAuB;oBAC3B,eAAe;oBACf,wBAAwB;oBACxB,MAAM,IAAI;iFAAQ,CAAA,UAAW,WAAW,SAAS;;oBACjD;gBACF;;YAEA;YAEA,4BAA4B;YAC5B,MAAM,WAAW;6DAAY;oBAC3B;oBACA,cAAc,IAAI;gBACpB;4DAAG;YAEH;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAmB;KAAc;IAErC,MAAM,qBAAqB;QACzB,MAAM,aAAgC,cAAc,GAAG,CAAC,CAAA;YACtD,MAAM,YAAY,aAAa;YAC/B,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;YACnD,MAAM,cAAc,KAAK,MAAM,KAAK;YACpC,MAAM,eAAe,MAAM;YAE3B,OAAO;gBACL;gBACA,MAAM,YAAY;gBAClB,OAAO,YAAY;gBACnB;gBACA,eAAe,AAAC,SAAS,YAAa;gBACtC,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;gBAC9C;gBACA;gBACA,OAAO;oBACL,WAAW,cAAc,KAAK,YAAY,cAAc,KAAK,YAAY;oBACzE,UAAU,KAAK,GAAG,CAAC,cAAc,MAAM;oBACvC,YAAY;wBACV,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;wBACzC,MAAM,cAAc,KAAK,YAAY;wBACrC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;wBACxC,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY;oBAC1C;gBACF;gBACA,mBAAmB;oBACjB,KAAK,KAAK,MAAM,KAAK;oBACrB,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC9B,KAAK,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBACrD,SAAS,YAAY,KAAK,MAAM,KAAK,YAAY;oBACjD,YAAY,YAAY,KAAK,MAAM,KAAK,YAAY;oBACpD,gBAAgB,kBAAkB;oBAClC,YAAY,KAAK,KAAK,MAAM,KAAK;gBACnC;gBACA,MAAM,YAAY;YACpB;QACF;QAEA,oBAAoB;QACpB,0BAA0B;IAC5B;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,kBAAsC,WACzC,MAAM,CAAC,CAAA,YAAa,UAAU,iBAAiB,CAAC,UAAU,GAAG,IAC7D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA;YACH,MAAM,SAAS,UAAU,WAAW,GAAG,KAAK,QAAQ,UAAU,WAAW,GAAG,KAAK,SAAS;YAC1F,MAAM,QAAQ,UAAU,KAAK;YAC7B,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;YACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;YAErB,OAAO;gBACL,QAAQ,UAAU,MAAM;gBACxB;gBACA;gBACA;gBACA,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,YAAY,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;gBACrD,YAAY,UAAU,iBAAiB,CAAC,UAAU;gBAClD,WAAW;gBACX,WAAW,kBAAkB,WAAW;gBACxC,UAAU,KAAK,KAAK,MAAM,KAAK;YACjC;QACF;QAEF,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC,WAA4B;QACrD,MAAM,UAAU,EAAE;QAElB,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,mBAAmD,OAAjC,UAAU,WAAW,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,uBAAwE,OAAlD,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;YACnF,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,kBAAmD,OAAlC,UAAU,YAAY,CAAC,OAAO,CAAC,IAAG;YACjE,QAAQ,IAAI,CAAC,AAAC,sBAAgE,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YAC3E,QAAQ,IAAI,CAAC,AAAC,kBAA4D,OAA3C,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;YACvE,QAAQ,IAAI,CAAC,AAAC,oBAAkE,OAA/C,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC;YAC7E,QAAQ,IAAI,CAAC,AAAC,sBAA6D,OAAxC,CAAC,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC,IAAG;QAC7E;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAqE;kDAEjF,6LAAC;wCAAK,WAAW,AAAC,6BAAsF,OAA1D,cAAc,+BAA+B;;;;;;kDAC3F,6LAAC;wCAAK,WAAU;kDACb,cAAc,wBAAwB;;;;;;;;;;;;0CAG3C,6LAAC;gCAAI,WAAU;;oCAA2C;oCAC5C,WAAW,kBAAkB,CAAC;;;;;;;;;;;;;kCAK9C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,AAAC,8DAIX,OAHC,sBAAsB,KAClB,2BACA;0DAGL;+CARI;;;;;;;;;;;;;;;;0CAeb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACxD,cAAc,MAAM;4CAAC;;;;;;;kDAIhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;qDAAS;gDACpD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,SAAS,MAAM;oDAAC;;;;;;;0DAEzB,6LAAC;gDACC,SAAS,IAAM,iBAAiB,EAAE;gDAClC,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAA,qBACZ,6LAAC;gDAEC,SAAS;oDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;wDAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;oDACnD,OAAO;wDACL,iBAAiB;+DAAI;4DAAe;yDAAK;oDAC3C;gDACF;gDACA,WAAW,AAAC,2DAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,sCACA;gDAEN,OAAO,YAAY;0DAElB;+CAfI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwBjB,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAA,0BACpB,6LAAC;wBAA2B,WAAU;;0CAEpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAW,UAAU,IAAI;;;;;;0DACzC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,UAAU,MAAM;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,UAAU,IAAI;;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAW,AAAC,yCAA2G,OAAnE,uBAAuB,UAAU,iBAAiB,CAAC,cAAc;kDACvH,sBAAsB,UAAU,iBAAiB,CAAC,cAAc;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,UAAU,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kDAElE,6LAAC;wCAAI,WAAW,AAAC,uBAAuF,OAAjE,UAAU,aAAa,IAAI,IAAI,mBAAmB;;4CACtF,UAAU,aAAa,IAAI,IAAI,MAAM;4CAAI,UAAU,aAAa,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAKjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAa,UAAU,WAAW,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DACpD,6LAAC;;oDAAK;oDAAY,UAAU,YAAY,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAwB,OAAtB,UAAU,WAAW,EAAC;4CAAG;;;;;;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,UAAU,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC;gDAAC,CAAC,IAAI,MAAM;iEAC1D,6LAAC;gDAAa,WAAW,AAAC,4BAIzB,OAHC,UAAU,YAAY,gCACtB,UAAU,YAAY,4BACtB;;kEAEA,6LAAC;wDAAI,WAAU;kEAAe;;;;;;kEAC9B,6LAAC;kEAAK,UAAU,YAAY,MAAM,UAAU,YAAY,MAAM;;;;;;;+CANtD;;;;;;;;;;;;;;;;;0CAahB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAW,AAAC,eAIjB,OAHC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,iBACvC,UAAU,iBAAiB,CAAC,GAAG,GAAG,KAAK,mBACvC;0DAEC,UAAU,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAGxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;kDAG3F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDACb,UAAU,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;uBAvFjD,UAAU,MAAM;;;;;;;;;;YAgG7B,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAyE;0CAErF,6LAAC;gCAAK,WAAU;0CAAkE;;;;;;;;;;;;kCAKpF,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;gCAAgB,WAAW,AAAC,2BAI5B,OAHC,IAAI,MAAM,KAAK,QACX,mEACA;;kDAEJ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,YAAY,IAAI,MAAM;;;;;;kEACjD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,IAAI,MAAM;oEAAC;oEAAE,IAAI,MAAM;;;;;;;0EAE1B,6LAAC;gEAAE,WAAU;0EAAyB,IAAI,SAAS;;;;;;;;;;;;;;;;;;0DAGvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;4DAAwB;4DAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAErE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;;4DAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;kDAKxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;wDAAW,WAAU;;0EACpB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;0EACrC,6LAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;;;;;;;;+BAhDP;;;;;;;;;;;;;;;;;;;;;;AAgExB;GArewB;KAAA", "debugId": null}}, {"offset": {"line": 1749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/TimeframeAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface TimeframeData {\n  timeframe: string;\n  trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n  strength: number;\n  rsi: number;\n  macd: number;\n  volume: number;\n  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';\n  confidence: number;\n  keyLevel: number;\n  nextTarget: number;\n}\n\ninterface MultiTimeframeAnalysis {\n  symbol: string;\n  currentPrice: number;\n  overallTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n  timeframes: TimeframeData[];\n  consensus: {\n    buy: number;\n    sell: number;\n    neutral: number;\n    recommendation: string;\n    confidence: number;\n  };\n}\n\nexport default function TimeframeAnalysis() {\n  const [analysis, setAnalysis] = useState<{ [key: string]: MultiTimeframeAnalysis }>({});\n  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('1h');\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [candleCount, setCandleCount] = useState<number>(50);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isAutoUpdate, setIsAutoUpdate] = useState(false);\n  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());\n\n  const timeframes = [\n    { key: '1m', name: '1 دقيقة', color: 'bg-red-100 text-red-800' },\n    { key: '5m', name: '5 دقائق', color: 'bg-orange-100 text-orange-800' },\n    { key: '15m', name: '15 دقيقة', color: 'bg-yellow-100 text-yellow-800' },\n    { key: '30m', name: '30 دقيقة', color: 'bg-green-100 text-green-800' },\n    { key: '1h', name: '1 ساعة', color: 'bg-blue-100 text-blue-800' },\n    { key: '4h', name: '4 ساعات', color: 'bg-indigo-100 text-indigo-800' },\n    { key: '1d', name: '1 يوم', color: 'bg-purple-100 text-purple-800' },\n    { key: '1w', name: '1 أسبوع', color: 'bg-pink-100 text-pink-800' }\n  ];\n\n  // All Forex pairs available\n  const allForexPairs = [\n    // Major Pairs\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    // Minor Pairs (Cross Currencies)\n    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',\n    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',\n    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',\n    'NZDJPY', 'NZDCHF', 'NZDCAD',\n    'CADJPY', 'CADCHF', 'CHFJPY',\n    // Exotic Pairs\n    'USDSEK', 'USDNOK', 'USDDKK', 'USDPLN', 'USDHUF', 'USDCZK',\n    'EURSEK', 'EURNOK', 'EURDKK', 'EURPLN', 'EURHUF', 'EURCZK',\n    'GBPSEK', 'GBPNOK', 'GBPDKK', 'GBPPLN',\n    // Commodities\n    'XAUUSD', 'XAGUSD', 'USOIL', 'UKOIL',\n    // Crypto\n    'BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD'\n  ];\n\n  const candleOptions = [20, 30, 50, 75, 100, 150, 200];\n\n  useEffect(() => {\n    // Only auto-update if enabled\n    if (isAutoUpdate) {\n      const interval = setInterval(() => {\n        handleManualUpdate();\n      }, 10000); // Update every 10 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [isAutoUpdate, selectedPairs, selectedTimeframe, candleCount]);\n\n  // Manual update function\n  const handleManualUpdate = () => {\n    if (selectedPairs.length === 0) {\n      alert('يرجى اختيار زوج عملة واحد على الأقل');\n      return;\n    }\n    generateMultiTimeframeAnalysis();\n  };\n\n  const generateMultiTimeframeAnalysis = () => {\n    setIsLoading(true);\n\n    // Simulate API call delay based on number of pairs and candles\n    const analysisDelay = Math.min(selectedPairs.length * 200 + candleCount * 10, 3000);\n\n    setTimeout(() => {\n      const newAnalysis: { [key: string]: MultiTimeframeAnalysis } = {};\n\n      selectedPairs.forEach(symbol => {\n        const basePrice = getBasePrice(symbol);\n\n        // Generate analysis based on candle count (more candles = more accurate)\n        const accuracyBonus = Math.min((candleCount - 20) / 180 * 15, 15); // Up to 15% bonus\n\n        const timeframeData: TimeframeData[] = timeframes.map(tf => {\n          // Simulate more sophisticated analysis with more candles\n          const trendStrength = Math.random() * 100;\n          const candleInfluence = (candleCount / 200) * 20; // More candles = better trend detection\n\n          const trend = Math.random() > 0.5 ? 'BULLISH' : Math.random() > 0.5 ? 'BEARISH' : 'NEUTRAL';\n          const strength = Math.min(trendStrength + candleInfluence, 100);\n          const rsi = Math.random() * 100;\n          const macd = (Math.random() - 0.5) * 0.02;\n          const volume = Math.random() * 2000000 + 500000;\n          const baseConfidence = 70 + Math.random() * 25;\n          const confidence = Math.min(baseConfidence + accuracyBonus, 95);\n\n          return {\n            timeframe: tf.key,\n            trend,\n            strength,\n            rsi,\n            macd,\n            volume,\n            recommendation: getRecommendation(trend, rsi, strength),\n            confidence,\n            keyLevel: basePrice + (Math.random() - 0.5) * basePrice * 0.02,\n            nextTarget: basePrice + (Math.random() - 0.5) * basePrice * 0.03\n          };\n        });\n\n        // Calculate consensus\n        const buyCount = timeframeData.filter(tf => tf.recommendation.includes('BUY')).length;\n        const sellCount = timeframeData.filter(tf => tf.recommendation.includes('SELL')).length;\n        const neutralCount = timeframeData.filter(tf => tf.recommendation === 'NEUTRAL').length;\n\n        const total = timeframeData.length;\n        const buyPercent = (buyCount / total) * 100;\n        const sellPercent = (sellCount / total) * 100;\n        const neutralPercent = (neutralCount / total) * 100;\n\n        let overallRecommendation = 'NEUTRAL';\n        if (buyPercent > 60) overallRecommendation = 'STRONG_BUY';\n        else if (buyPercent > 40) overallRecommendation = 'BUY';\n        else if (sellPercent > 60) overallRecommendation = 'STRONG_SELL';\n        else if (sellPercent > 40) overallRecommendation = 'SELL';\n\n        const overallTrend = buyPercent > sellPercent ? 'BULLISH' : sellPercent > buyPercent ? 'BEARISH' : 'NEUTRAL';\n        const consensusConfidence = Math.abs(buyPercent - sellPercent);\n\n        newAnalysis[symbol] = {\n          symbol,\n          currentPrice: basePrice + (Math.random() - 0.5) * basePrice * 0.01,\n          overallTrend,\n          timeframes: timeframeData,\n          consensus: {\n            buy: buyPercent,\n            sell: sellPercent,\n            neutral: neutralPercent,\n            recommendation: overallRecommendation,\n            confidence: consensusConfidence\n          }\n        };\n      });\n\n      setAnalysis(newAnalysis);\n      setLastUpdate(new Date());\n      setIsLoading(false);\n    }, analysisDelay);\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getRecommendation = (trend: string, rsi: number, strength: number): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' => {\n    if (trend === 'BULLISH' && rsi < 70 && strength > 70) return 'STRONG_BUY';\n    if (trend === 'BULLISH' && rsi < 80) return 'BUY';\n    if (trend === 'BEARISH' && rsi > 30 && strength > 70) return 'STRONG_SELL';\n    if (trend === 'BEARISH' && rsi > 20) return 'SELL';\n    return 'NEUTRAL';\n  };\n\n  const getRecommendationColor = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'text-green-700 bg-green-100';\n      case 'BUY': return 'text-green-600 bg-green-50';\n      case 'NEUTRAL': return 'text-yellow-600 bg-yellow-100';\n      case 'SELL': return 'text-red-600 bg-red-50';\n      case 'STRONG_SELL': return 'text-red-700 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getRecommendationText = (rec: string): string => {\n    switch (rec) {\n      case 'STRONG_BUY': return 'شراء قوي';\n      case 'BUY': return 'شراء';\n      case 'NEUTRAL': return 'محايد';\n      case 'SELL': return 'بيع';\n      case 'STRONG_SELL': return 'بيع قوي';\n      default: return 'غير محدد';\n    }\n  };\n\n  const getTrendIcon = (trend: string): string => {\n    switch (trend) {\n      case 'BULLISH': return '📈';\n      case 'BEARISH': return '📉';\n      default: return '➡️';\n    }\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'EURCHF': '🇪🇺🇨🇭', 'EURAUD': '🇪🇺🇦🇺',\n      'EURCAD': '🇪🇺🇨🇦', 'EURNZD': '🇪🇺🇳🇿', 'GBPCHF': '🇬🇧🇨🇭', 'GBPAUD': '🇬🇧🇦🇺',\n      'GBPCAD': '🇬🇧🇨🇦', 'GBPNZD': '🇬🇧🇳🇿', 'AUDJPY': '🇦🇺🇯🇵', 'AUDCHF': '🇦🇺🇨🇭',\n      'AUDCAD': '🇦🇺🇨🇦', 'AUDNZD': '🇦🇺🇳🇿', 'NZDJPY': '🇳🇿🇯🇵', 'NZDCHF': '🇳🇿🇨🇭',\n      'NZDCAD': '🇳🇿🇨🇦', 'CADJPY': '🇨🇦🇯🇵', 'CADCHF': '🇨🇦🇨🇭', 'CHFJPY': '🇨🇭🇯🇵',\n      'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰', 'USOIL': '🛢️💰', 'UKOIL': '🛢️🇬🇧',\n      'BTCUSD': '₿💰', 'ETHUSD': '⟠💰', 'LTCUSD': '🪙💰', 'XRPUSD': '💎💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Enhanced Header with Controls */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            ⏰ تحليل متعدد الإطارات الزمنية المتقدم\n            <span className=\"mr-3 px-2 py-1 bg-blue-600 text-white rounded text-sm\">\n              {selectedPairs.length} أزواج\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              آخر تحديث: {lastUpdate.toLocaleTimeString('ar-SA')}\n            </div>\n            <button\n              onClick={handleManualUpdate}\n              disabled={isLoading}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                isLoading\n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-green-600 text-white hover:bg-green-700'\n              }`}\n            >\n              {isLoading ? '🔄 جاري التحديث...' : '🔄 تحديث يدوي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n          {/* Pair Selection */}\n          <div className=\"lg:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              اختيار أزواج العملات ({selectedPairs.length}):\n            </label>\n\n            {/* Quick Selection Buttons */}\n            <div className=\"flex flex-wrap gap-2 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'].includes(p)))}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية (4)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => p.startsWith('EUR') || p.startsWith('GBP')))}\n                className=\"px-2 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700\"\n              >\n                الثانوية (14)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs.filter(p => ['XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'].includes(p)))}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع (4)\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل ({allForexPairs.length})\n              </button>\n              <button\n                onClick={() => setSelectedPairs([])}\n                className=\"px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700\"\n              >\n                مسح\n              </button>\n            </div>\n\n            {/* Individual Pair Selection */}\n            <div className=\"max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-2\">\n              <div className=\"grid grid-cols-4 md:grid-cols-6 gap-1\">\n                {allForexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs font-medium transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                    title={getPairName(pair)}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Candle Count Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عدد الشموع للتحليل:\n            </label>\n            <select\n              value={candleCount}\n              onChange={(e) => setCandleCount(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {candleOptions.map(count => (\n                <option key={count} value={count}>\n                  {count} شمعة {count >= 100 ? '(دقة عالية)' : count >= 50 ? '(دقة متوسطة)' : '(دقة أساسية)'}\n                </option>\n              ))}\n            </select>\n            <div className=\"text-xs text-gray-500 mt-1\">\n              المزيد من الشموع = دقة أعلى\n            </div>\n          </div>\n\n          {/* Auto Update Toggle */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              إعدادات التحديث:\n            </label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={isAutoUpdate}\n                  onChange={(e) => setIsAutoUpdate(e.target.checked)}\n                  className=\"mr-2 rounded\"\n                />\n                <span className=\"text-sm\">تحديث تلقائي (كل 10 ثوانٍ)</span>\n              </label>\n              <div className=\"text-xs text-gray-500\">\n                {isAutoUpdate ? '🟢 التحديث التلقائي مفعل' : '🔴 التحديث اليدوي فقط'}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isLoading && (\n          <div className=\"text-center py-8\">\n            <div className=\"inline-flex items-center space-x-2 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n              <span className=\"text-gray-600 dark:text-gray-400\">\n                جاري تحليل {selectedPairs.length} زوج باستخدام {candleCount} شمعة...\n              </span>\n            </div>\n            <div className=\"mt-2 text-sm text-gray-500\">\n              الوقت المتوقع: {Math.ceil((selectedPairs.length * 200 + candleCount * 10) / 1000)} ثانية\n            </div>\n          </div>\n        )}\n\n        {/* No Pairs Selected */}\n        {!isLoading && selectedPairs.length === 0 && (\n          <div className=\"text-center py-8\">\n            <div className=\"text-6xl mb-4\">📊</div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              اختر أزواج العملات للتحليل\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              يمكنك اختيار من {allForexPairs.length} زوج عملة متاح\n            </p>\n            <button\n              onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY'])}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n            >\n              اختيار الأزواج الرئيسية\n            </button>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Overall Market Summary */}\n            <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص السوق العام ({selectedPairs.length} أزواج)\n              </h4>\n\n              {(() => {\n                const allRecommendations = Object.values(analysis).flatMap(a => a.timeframes.map(tf => tf.recommendation));\n                const buyCount = allRecommendations.filter(r => r.includes('BUY')).length;\n                const sellCount = allRecommendations.filter(r => r.includes('SELL')).length;\n                const neutralCount = allRecommendations.filter(r => r === 'NEUTRAL').length;\n                const total = allRecommendations.length;\n\n                const buyPercent = (buyCount / total) * 100;\n                const sellPercent = (sellCount / total) * 100;\n                const neutralPercent = (neutralCount / total) * 100;\n\n                return (\n                  <>\n                    <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-green-600\">{buyPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">شراء ({buyCount})</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-yellow-600\">{neutralPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">محايد ({neutralCount})</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl font-bold text-red-600\">{sellPercent.toFixed(0)}%</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">بيع ({sellCount})</div>\n                      </div>\n                    </div>\n\n                    <div className=\"w-full bg-gray-200 rounded-full h-4 mb-2\">\n                      <div className=\"flex h-4 rounded-full overflow-hidden\">\n                        <div className=\"bg-green-500\" style={{ width: `${buyPercent}%` }}></div>\n                        <div className=\"bg-yellow-500\" style={{ width: `${neutralPercent}%` }}></div>\n                        <div className=\"bg-red-500\" style={{ width: `${sellPercent}%` }}></div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-center text-sm text-gray-600 dark:text-gray-400\">\n                      إجمالي التحليلات: {total} | عدد الشموع: {candleCount} |\n                      اتجاه السوق: {buyPercent > sellPercent ? '📈 صاعد' : sellPercent > buyPercent ? '📉 هابط' : '➡️ محايد'}\n                    </div>\n                  </>\n                );\n              })()}\n            </div>\n\n            {/* Individual Pair Analysis */}\n            {selectedPairs.map(symbol => {\n              const pairAnalysis = analysis[symbol];\n              if (!pairAnalysis) return null;\n\n              return (\n                <div key={symbol} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  {/* Pair Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <span className=\"text-2xl\">{getPairFlag(symbol)}</span>\n                      <div>\n                        <h5 className=\"text-lg font-bold text-gray-900 dark:text-white\">{symbol}</h5>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">{getPairName(symbol)}</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                        {pairAnalysis.currentPrice.toFixed(symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                      <div className={`text-sm font-medium px-2 py-1 rounded ${getRecommendationColor(pairAnalysis.consensus.recommendation)}`}>\n                        {getRecommendationText(pairAnalysis.consensus.recommendation)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Pair Consensus */}\n                  <div className=\"mb-4 bg-gray-50 dark:bg-gray-700 rounded p-3\">\n                    <div className=\"grid grid-cols-3 gap-2 text-center text-sm\">\n                      <div>\n                        <div className=\"font-bold text-green-600\">{pairAnalysis.consensus.buy.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">شراء</div>\n                      </div>\n                      <div>\n                        <div className=\"font-bold text-yellow-600\">{pairAnalysis.consensus.neutral.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">محايد</div>\n                      </div>\n                      <div>\n                        <div className=\"font-bold text-red-600\">{pairAnalysis.consensus.sell.toFixed(0)}%</div>\n                        <div className=\"text-gray-600 dark:text-gray-400\">بيع</div>\n                      </div>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                      <div className=\"flex h-2 rounded-full overflow-hidden\">\n                        <div className=\"bg-green-500\" style={{ width: `${pairAnalysis.consensus.buy}%` }}></div>\n                        <div className=\"bg-yellow-500\" style={{ width: `${pairAnalysis.consensus.neutral}%` }}></div>\n                        <div className=\"bg-red-500\" style={{ width: `${pairAnalysis.consensus.sell}%` }}></div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Timeframe Grid */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2\">\n                    {pairAnalysis.timeframes.map((tf, index) => {\n                      const timeframeInfo = timeframes.find(t => t.key === tf.timeframe);\n                      return (\n                        <div\n                          key={tf.timeframe}\n                          className={`border rounded p-2 cursor-pointer transition-all duration-200 ${\n                            selectedTimeframe === tf.timeframe\n                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                              : 'border-gray-200 hover:border-gray-300 dark:border-gray-600'\n                          }`}\n                          onClick={() => setSelectedTimeframe(tf.timeframe)}\n                        >\n                          <div className={`text-center py-1 rounded text-xs font-medium mb-2 ${timeframeInfo?.color || 'bg-gray-100 text-gray-800'}`}>\n                            {timeframeInfo?.name || tf.timeframe}\n                          </div>\n\n                          <div className={`text-center py-1 rounded mb-2 text-xs ${getRecommendationColor(tf.recommendation)}`}>\n                            {getRecommendationText(tf.recommendation)}\n                          </div>\n\n                          <div className=\"space-y-1 text-xs\">\n                            <div className=\"flex justify-between\">\n                              <span>RSI:</span>\n                              <span className={tf.rsi > 70 ? 'text-red-600' : tf.rsi < 30 ? 'text-green-600' : 'text-gray-600'}>\n                                {tf.rsi.toFixed(0)}\n                              </span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>القوة:</span>\n                              <span className=\"text-blue-600\">{tf.strength.toFixed(0)}%</span>\n                            </div>\n                            <div className=\"flex justify-between\">\n                              <span>الثقة:</span>\n                              <span className=\"text-purple-600\">{tf.confidence.toFixed(0)}%</span>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        )}\n\n        {/* AI Recommendation Section */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"mt-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                🤖 توصيات الذكاء الاصطناعي المتقدمة\n              </h4>\n              <button\n                onClick={() => {\n                  // Manual AI analysis trigger\n                  alert(`🤖 تحليل الذكاء الاصطناعي:\\n\\n` +\n                    `• عدد الأزواج: ${selectedPairs.length}\\n` +\n                    `• عدد الشموع: ${candleCount}\\n` +\n                    `• دقة التحليل: ${candleCount >= 100 ? 'عالية جداً' : candleCount >= 50 ? 'عالية' : 'متوسطة'}\\n\\n` +\n                    `سيتم تحليل ${selectedPairs.length * timeframes.length} إطار زمني باستخدام ${candleCount} شمعة لكل إطار.`\n                  );\n                }}\n                className=\"px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700\"\n              >\n                🧠 تحليل ذكي متقدم\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {selectedPairs.slice(0, 3).map(symbol => {\n                const pairAnalysis = analysis[symbol];\n                if (!pairAnalysis) return null;\n\n                const strongSignals = pairAnalysis.timeframes.filter(tf =>\n                  tf.recommendation.includes('STRONG') && tf.confidence > 80\n                );\n\n                return (\n                  <div key={symbol} className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse mb-3\">\n                      <span className=\"text-lg\">{getPairFlag(symbol)}</span>\n                      <div>\n                        <h5 className=\"font-bold text-gray-900 dark:text-white\">{symbol}</h5>\n                        <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                          {candleCount} شمعة | {strongSignals.length} إشارة قوية\n                        </p>\n                      </div>\n                    </div>\n\n                    {strongSignals.length > 0 ? (\n                      <div className=\"space-y-2\">\n                        <div className={`p-2 rounded text-center ${getRecommendationColor(strongSignals[0].recommendation)}`}>\n                          <div className=\"font-bold text-sm\">{getRecommendationText(strongSignals[0].recommendation)}</div>\n                          <div className=\"text-xs\">ثقة: {strongSignals[0].confidence.toFixed(0)}%</div>\n                        </div>\n                        <div className=\"text-xs space-y-1\">\n                          <div>🎯 الإطار: {timeframes.find(t => t.key === strongSignals[0].timeframe)?.name}</div>\n                          <div>📊 RSI: {strongSignals[0].rsi.toFixed(1)}</div>\n                          <div>💪 القوة: {strongSignals[0].strength.toFixed(0)}%</div>\n                          <div>🎯 الهدف: {strongSignals[0].nextTarget.toFixed(symbol.includes('JPY') ? 3 : 5)}</div>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-4\">\n                        <div className=\"text-yellow-600 font-medium text-sm\">⚠️ لا توجد إشارات قوية</div>\n                        <div className=\"text-xs text-gray-500 mt-1\">جرب زيادة عدد الشموع للحصول على دقة أعلى</div>\n                      </div>\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {selectedPairs.length > 3 && (\n              <div className=\"mt-4 text-center\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  يتم عرض أول 3 أزواج فقط في توصيات الذكاء الاصطناعي\n                </p>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Analysis Statistics */}\n        {!isLoading && selectedPairs.length > 0 && Object.keys(analysis).length > 0 && (\n          <div className=\"mt-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n            <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n              📈 إحصائيات التحليل المتقدم\n            </h4>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-blue-600\">{selectedPairs.length}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">أزواج محللة</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-purple-600\">{selectedPairs.length * timeframes.length}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">إطار زمني</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-green-600\">{candleCount}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">شمعة لكل إطار</div>\n              </div>\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <div className=\"text-2xl font-bold text-orange-600\">\n                  {candleCount >= 100 ? '95%' : candleCount >= 50 ? '85%' : '75%'}\n                </div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">دقة متوقعة</div>\n              </div>\n            </div>\n\n            <div className=\"mt-4 text-center text-sm text-gray-600 dark:text-gray-400\">\n              💡 نصيحة: استخدم 100+ شمعة للحصول على أعلى دقة في التحليل\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA+Be,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C,CAAC;IACrF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEvD,MAAM,aAAa;QACjB;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAA0B;QAC/D;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAAgC;QACrE;YAAE,KAAK;YAAO,MAAM;YAAY,OAAO;QAAgC;QACvE;YAAE,KAAK;YAAO,MAAM;YAAY,OAAO;QAA8B;QACrE;YAAE,KAAK;YAAM,MAAM;YAAU,OAAO;QAA4B;QAChE;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAAgC;QACrE;YAAE,KAAK;YAAM,MAAM;YAAS,OAAO;QAAgC;QACnE;YAAE,KAAK;YAAM,MAAM;YAAW,OAAO;QAA4B;KAClE;IAED,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D,iCAAiC;QACjC;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QACxC;QAAU;QAAU;QAAU;QAC9B;QAAU;QAAU;QACpB;QAAU;QAAU;QACpB,eAAe;QACf;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAC9B,cAAc;QACd;QAAU;QAAU;QAAS;QAC7B,SAAS;QACT;QAAU;QAAU;QAAU;KAC/B;IAED,MAAM,gBAAgB;QAAC;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,8BAA8B;YAC9B,IAAI,cAAc;gBAChB,MAAM,WAAW;4DAAY;wBAC3B;oBACF;2DAAG,QAAQ,0BAA0B;gBAErC;mDAAO,IAAM,cAAc;;YAC7B;QACF;sCAAG;QAAC;QAAc;QAAe;QAAmB;KAAY;IAEhE,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QACA;IACF;IAEA,MAAM,iCAAiC;QACrC,aAAa;QAEb,+DAA+D;QAC/D,MAAM,gBAAgB,KAAK,GAAG,CAAC,cAAc,MAAM,GAAG,MAAM,cAAc,IAAI;QAE9E,WAAW;YACT,MAAM,cAAyD,CAAC;YAEhE,cAAc,OAAO,CAAC,CAAA;gBACpB,MAAM,YAAY,aAAa;gBAE/B,yEAAyE;gBACzE,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,MAAM,IAAI,KAAK,kBAAkB;gBAErF,MAAM,gBAAiC,WAAW,GAAG,CAAC,CAAA;oBACpD,yDAAyD;oBACzD,MAAM,gBAAgB,KAAK,MAAM,KAAK;oBACtC,MAAM,kBAAkB,AAAC,cAAc,MAAO,IAAI,wCAAwC;oBAE1F,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,YAAY;oBAClF,MAAM,WAAW,KAAK,GAAG,CAAC,gBAAgB,iBAAiB;oBAC3D,MAAM,MAAM,KAAK,MAAM,KAAK;oBAC5B,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACrC,MAAM,SAAS,KAAK,MAAM,KAAK,UAAU;oBACzC,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK;oBAC5C,MAAM,aAAa,KAAK,GAAG,CAAC,iBAAiB,eAAe;oBAE5D,OAAO;wBACL,WAAW,GAAG,GAAG;wBACjB;wBACA;wBACA;wBACA;wBACA;wBACA,gBAAgB,kBAAkB,OAAO,KAAK;wBAC9C;wBACA,UAAU,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;wBAC1D,YAAY,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D;gBACF;gBAEA,sBAAsB;gBACtB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,QAAQ,MAAM;gBACrF,MAAM,YAAY,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,CAAC,QAAQ,CAAC,SAAS,MAAM;gBACvF,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,cAAc,KAAK,WAAW,MAAM;gBAEvF,MAAM,QAAQ,cAAc,MAAM;gBAClC,MAAM,aAAa,AAAC,WAAW,QAAS;gBACxC,MAAM,cAAc,AAAC,YAAY,QAAS;gBAC1C,MAAM,iBAAiB,AAAC,eAAe,QAAS;gBAEhD,IAAI,wBAAwB;gBAC5B,IAAI,aAAa,IAAI,wBAAwB;qBACxC,IAAI,aAAa,IAAI,wBAAwB;qBAC7C,IAAI,cAAc,IAAI,wBAAwB;qBAC9C,IAAI,cAAc,IAAI,wBAAwB;gBAEnD,MAAM,eAAe,aAAa,cAAc,YAAY,cAAc,aAAa,YAAY;gBACnG,MAAM,sBAAsB,KAAK,GAAG,CAAC,aAAa;gBAElD,WAAW,CAAC,OAAO,GAAG;oBACpB;oBACA,cAAc,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D;oBACA,YAAY;oBACZ,WAAW;wBACT,KAAK;wBACL,MAAM;wBACN,SAAS;wBACT,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF;YAEA,YAAY;YACZ,cAAc,IAAI;YAClB,aAAa;QACf,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,oBAAoB,CAAC,OAAe,KAAa;QACrD,IAAI,UAAU,aAAa,MAAM,MAAM,WAAW,IAAI,OAAO;QAC7D,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO;QAC5C,IAAI,UAAU,aAAa,MAAM,MAAM,WAAW,IAAI,OAAO;QAC7D,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO;QAC5C,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAQ,UAAU;YAAQ,SAAS;YAAS,SAAS;YAC/D,UAAU;YAAO,UAAU;YAAO,UAAU;YAAQ,UAAU;QAChE;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;;4CACb,cAAc,MAAM;4CAAC;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,WAAW,kBAAkB,CAAC;;;;;;;kDAE5C,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,8DAIX,OAHC,YACI,8CACA;kDAGL,YAAY,uBAAuB;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;4CAAkE;4CAC1D,cAAc,MAAM;4CAAC;;;;;;;kDAI9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK;4DAAC;4DAAU;4DAAU;4DAAU;yDAAS,CAAC,QAAQ,CAAC;gDAC5G,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC;gDAC9F,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK;4DAAC;4DAAU;4DAAU;4DAAS;yDAAS,CAAC,QAAQ,CAAC;gDAC3G,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;;oDACX;oDACQ,cAAc,MAAM;oDAAC;;;;;;;0DAE9B,6LAAC;gDACC,SAAS,IAAM,iBAAiB,EAAE;gDAClC,WAAU;0DACX;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,2DAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;oDAEN,OAAO,YAAY;8DAElB;mDAfI;;;;;;;;;;;;;;;;;;;;;0CAuBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;gDAAmB,OAAO;;oDACxB;oDAAM;oDAAO,SAAS,MAAM,gBAAgB,SAAS,KAAK,iBAAiB;;+CADjE;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;0DACZ,eAAe,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvD,6LAAC;gBAAI,WAAU;;oBAEZ,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;4CAAmC;4CACrC,cAAc,MAAM;4CAAC;4CAAe;4CAAY;;;;;;;;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;oCAA6B;oCAC1B,KAAK,IAAI,CAAC,CAAC,cAAc,MAAM,GAAG,MAAM,cAAc,EAAE,IAAI;oCAAM;;;;;;;;;;;;;oBAMvF,CAAC,aAAa,cAAc,MAAM,KAAK,mBACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;;oCAAwC;oCAClC,cAAc,MAAM;oCAAC;;;;;;;0CAExC,6LAAC;gCACC,SAAS,IAAM,iBAAiB;wCAAC;wCAAU;wCAAU;qCAAS;gCAC9D,WAAU;0CACX;;;;;;;;;;;;oBAOJ,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA2D;4CACjD,cAAc,MAAM;4CAAC;;;;;;;oCAG5C,CAAC;wCACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,UAAU,OAAO,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,cAAc;wCACxG,MAAM,WAAW,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,MAAM;wCACzE,MAAM,YAAY,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,SAAS,MAAM;wCAC3E,MAAM,eAAe,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM,WAAW,MAAM;wCAC3E,MAAM,QAAQ,mBAAmB,MAAM;wCAEvC,MAAM,aAAa,AAAC,WAAW,QAAS;wCACxC,MAAM,cAAc,AAAC,YAAY,QAAS;wCAC1C,MAAM,iBAAiB,AAAC,eAAe,QAAS;wCAEhD,qBACE;;8DACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAqC,WAAW,OAAO,CAAC;wEAAG;;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAO;wEAAS;;;;;;;;;;;;;sEAE5E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAsC,eAAe,OAAO,CAAC;wEAAG;;;;;;;8EAC/E,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAQ;wEAAa;;;;;;;;;;;;;sEAEjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAmC,YAAY,OAAO,CAAC;wEAAG;;;;;;;8EACzE,6LAAC;oEAAI,WAAU;;wEAA2C;wEAAM;wEAAU;;;;;;;;;;;;;;;;;;;8DAI9E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,OAAO;oEAAE,OAAO,AAAC,GAAa,OAAX,YAAW;gEAAG;;;;;;0EAC/D,6LAAC;gEAAI,WAAU;gEAAgB,OAAO;oEAAE,OAAO,AAAC,GAAiB,OAAf,gBAAe;gEAAG;;;;;;0EACpE,6LAAC;gEAAI,WAAU;gEAAa,OAAO;oEAAE,OAAO,AAAC,GAAc,OAAZ,aAAY;gEAAG;;;;;;;;;;;;;;;;;8DAIlE,6LAAC;oDAAI,WAAU;;wDAAuD;wDACjD;wDAAM;wDAAgB;wDAAY;wDACvC,aAAa,cAAc,YAAY,cAAc,aAAa,YAAY;;;;;;;;;oCAIpG,CAAC;;;;;;;4BAIF,cAAc,GAAG,CAAC,CAAA;gCACjB,MAAM,eAAe,QAAQ,CAAC,OAAO;gCACrC,IAAI,CAAC,cAAc,OAAO;gCAE1B,qBACE,6LAAC;oCAAiB,WAAU;;sDAE1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,YAAY;;;;;;sEACxC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAmD;;;;;;8EACjE,6LAAC;oEAAE,WAAU;8EAA4C,YAAY;;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,aAAa,YAAY,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;sEAElE,6LAAC;4DAAI,WAAW,AAAC,yCAAsG,OAA9D,uBAAuB,aAAa,SAAS,CAAC,cAAc;sEAClH,sBAAsB,aAAa,SAAS,CAAC,cAAc;;;;;;;;;;;;;;;;;;sDAMlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA4B,aAAa,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EACjF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;sEAEpD,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA6B,aAAa,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EACtF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;sEAEpD,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAA0B,aAAa,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAChF,6LAAC;oEAAI,WAAU;8EAAmC;;;;;;;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,OAAO;oEAAE,OAAO,AAAC,GAA6B,OAA3B,aAAa,SAAS,CAAC,GAAG,EAAC;gEAAG;;;;;;0EAC/E,6LAAC;gEAAI,WAAU;gEAAgB,OAAO;oEAAE,OAAO,AAAC,GAAiC,OAA/B,aAAa,SAAS,CAAC,OAAO,EAAC;gEAAG;;;;;;0EACpF,6LAAC;gEAAI,WAAU;gEAAa,OAAO;oEAAE,OAAO,AAAC,GAA8B,OAA5B,aAAa,SAAS,CAAC,IAAI,EAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;sDAMpF,6LAAC;4CAAI,WAAU;sDACZ,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI;gDAChC,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,GAAG,SAAS;gDACjE,qBACE,6LAAC;oDAEC,WAAW,AAAC,iEAIX,OAHC,sBAAsB,GAAG,SAAS,GAC9B,mDACA;oDAEN,SAAS,IAAM,qBAAqB,GAAG,SAAS;;sEAEhD,6LAAC;4DAAI,WAAW,AAAC,qDAAwG,OAApD,CAAA,0BAAA,oCAAA,cAAe,KAAK,KAAI;sEAC1F,CAAA,0BAAA,oCAAA,cAAe,IAAI,KAAI,GAAG,SAAS;;;;;;sEAGtC,6LAAC;4DAAI,WAAW,AAAC,yCAAkF,OAA1C,uBAAuB,GAAG,cAAc;sEAC9F,sBAAsB,GAAG,cAAc;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAW,GAAG,GAAG,GAAG,KAAK,iBAAiB,GAAG,GAAG,GAAG,KAAK,mBAAmB;sFAC9E,GAAG,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;8EAGpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;;gFAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;8EAE1D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,WAAU;;gFAAmB,GAAG,UAAU,CAAC,OAAO,CAAC;gFAAG;;;;;;;;;;;;;;;;;;;;mDA7B3D,GAAG,SAAS;;;;;4CAkCvB;;;;;;;mCArFM;;;;;4BAyFd;;;;;;;oBAKH,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDAGpE,6LAAC;wCACC,SAAS;4CACP,6BAA6B;4CAC7B,MAAM,AAAC,mCACL,AAAC,kBAAsC,OAArB,cAAc,MAAM,EAAC,QACvC,AAAC,iBAA4B,OAAZ,aAAY,QAC7B,AAAC,kBAA4F,OAA3E,eAAe,MAAM,eAAe,eAAe,KAAK,UAAU,UAAS,UAC7F,AAAC,cAA4E,OAA/D,cAAc,MAAM,GAAG,WAAW,MAAM,EAAC,wBAAkC,OAAZ,aAAY;wCAE7F;wCACA,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;0CACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;wCA2BF;oCA1B3B,MAAM,eAAe,QAAQ,CAAC,OAAO;oCACrC,IAAI,CAAC,cAAc,OAAO;oCAE1B,MAAM,gBAAgB,aAAa,UAAU,CAAC,MAAM,CAAC,CAAA,KACnD,GAAG,cAAc,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,GAAG;oCAG1D,qBACE,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,YAAY;;;;;;kEACvC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;;oEACV;oEAAY;oEAAS,cAAc,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;4CAKhD,cAAc,MAAM,GAAG,kBACtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,2BAAkF,OAAxD,uBAAuB,aAAa,CAAC,EAAE,CAAC,cAAc;;0EAC/F,6LAAC;gEAAI,WAAU;0EAAqB,sBAAsB,aAAa,CAAC,EAAE,CAAC,cAAc;;;;;;0EACzF,6LAAC;gEAAI,WAAU;;oEAAU;oEAAM,aAAa,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAI;qEAAY,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,aAAa,CAAC,EAAE,CAAC,SAAS,eAAzD,uCAAA,iBAA4D,IAAI;;;;;;;0EACjF,6LAAC;;oEAAI;oEAAS,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;0EAC3C,6LAAC;;oEAAI;oEAAW,aAAa,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EACrD,6LAAC;;oEAAI;oEAAW,aAAa,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;qEAIrF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;;;;;;;;uCA3BxC;;;;;gCAgCd;;;;;;4BAGD,cAAc,MAAM,GAAG,mBACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;oBAS/D,CAAC,aAAa,cAAc,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,mBACxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAIzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,cAAc,MAAM;;;;;;0DACvE,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsC,cAAc,MAAM,GAAG,WAAW,MAAM;;;;;;0DAC7F,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,QAAQ,eAAe,KAAK,QAAQ;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;0CAA4D;;;;;;;;;;;;;;;;;;;;;;;;AAQvF;GAzpBwB;KAAA", "debugId": null}}, {"offset": {"line": 3479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/ManualAIRecommendations.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface AIRecommendation {\n  id: string;\n  symbol: string;\n  timeframe: string;\n  action: 'BUY' | 'SELL' | 'HOLD';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskReward: number;\n  confidence: number;\n  accuracy: number;\n  candleCount: number;\n  reasoning: string[];\n  technicalScore: number;\n  sentimentScore: number;\n  volumeScore: number;\n  trendScore: number;\n  overallScore: number;\n  timestamp: number;\n}\n\nexport default function ManualAIRecommendations() {\n  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);\n  const [selectedPairs, setSelectedPairs] = useState<string[]>(['EURUSD', 'GBPUSD', 'USDJPY']);\n  const [selectedTimeframes, setSelectedTimeframes] = useState<string[]>(['1h', '4h', '1d']);\n  const [candleCount, setCandleCount] = useState<number>(100);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [lastAnalysis, setLastAnalysis] = useState<Date | null>(null);\n\n  const allForexPairs = [\n    // Major Pairs\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    // Minor Pairs\n    'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',\n    'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD', 'GBPNZD',\n    'AUDJPY', 'AUDCHF', 'AUDCAD', 'AUDNZD',\n    'NZDJPY', 'NZDCHF', 'NZDCAD',\n    'CADJPY', 'CADCHF', 'CHFJPY',\n    // Commodities & Crypto\n    'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD', 'ETHUSD'\n  ];\n\n  const timeframes = [\n    { key: '1m', name: '1 دقيقة' },\n    { key: '5m', name: '5 دقائق' },\n    { key: '15m', name: '15 دقيقة' },\n    { key: '30m', name: '30 دقيقة' },\n    { key: '1h', name: '1 ساعة' },\n    { key: '4h', name: '4 ساعات' },\n    { key: '1d', name: '1 يوم' },\n    { key: '1w', name: '1 أسبوع' }\n  ];\n\n  const candleOptions = [20, 30, 50, 75, 100, 150, 200];\n\n  // Manual AI Analysis Function\n  const runAIAnalysis = async () => {\n    if (selectedPairs.length === 0 || selectedTimeframes.length === 0) {\n      alert('يرجى اختيار أزواج عملات وإطارات زمنية للتحليل');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    \n    // Simulate analysis time based on complexity\n    const analysisTime = selectedPairs.length * selectedTimeframes.length * (candleCount / 50) * 500;\n    const maxTime = Math.min(analysisTime, 5000); // Max 5 seconds\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, maxTime));\n      \n      const newRecommendations: AIRecommendation[] = [];\n      \n      selectedPairs.forEach(symbol => {\n        selectedTimeframes.forEach(timeframe => {\n          const basePrice = getBasePrice(symbol);\n          \n          // Advanced AI scoring based on candle count\n          const accuracyBonus = Math.min((candleCount - 20) / 180 * 20, 20);\n          const technicalScore = 50 + Math.random() * 40 + (accuracyBonus / 4);\n          const sentimentScore = 40 + Math.random() * 40 + (accuracyBonus / 5);\n          const volumeScore = 45 + Math.random() * 35 + (accuracyBonus / 6);\n          const trendScore = 50 + Math.random() * 35 + (accuracyBonus / 4);\n          \n          const overallScore = (\n            technicalScore * 0.4 +\n            sentimentScore * 0.2 +\n            volumeScore * 0.2 +\n            trendScore * 0.2\n          );\n          \n          const action = overallScore > 70 ? 'BUY' : overallScore < 40 ? 'SELL' : 'HOLD';\n          const confidence = Math.min(70 + accuracyBonus + Math.random() * 20, 95);\n          const accuracy = Math.min(80 + accuracyBonus + Math.random() * 15, 98);\n          \n          // Calculate levels\n          const entry = basePrice + (Math.random() - 0.5) * basePrice * 0.005;\n          const stopLoss = action === 'BUY' \n            ? entry - (entry * 0.015) \n            : entry + (entry * 0.015);\n          const tp1 = action === 'BUY' \n            ? entry + (entry * 0.02) \n            : entry - (entry * 0.02);\n          const tp2 = action === 'BUY' \n            ? entry + (entry * 0.035) \n            : entry - (entry * 0.035);\n          const tp3 = action === 'BUY' \n            ? entry + (entry * 0.05) \n            : entry - (entry * 0.05);\n          \n          const riskReward = Math.abs(tp1 - entry) / Math.abs(entry - stopLoss);\n          \n          newRecommendations.push({\n            id: `${symbol}_${timeframe}_${Date.now()}`,\n            symbol,\n            timeframe,\n            action,\n            entry,\n            stopLoss,\n            takeProfit1: tp1,\n            takeProfit2: tp2,\n            takeProfit3: tp3,\n            riskReward,\n            confidence,\n            accuracy,\n            candleCount,\n            reasoning: generateAdvancedReasoning(symbol, action, technicalScore, sentimentScore, candleCount),\n            technicalScore,\n            sentimentScore,\n            volumeScore,\n            trendScore,\n            overallScore,\n            timestamp: Date.now()\n          });\n        });\n      });\n      \n      // Sort by overall score (best recommendations first)\n      newRecommendations.sort((a, b) => b.overallScore - a.overallScore);\n      \n      setRecommendations(newRecommendations);\n      setLastAnalysis(new Date());\n      \n    } catch (error) {\n      console.error('AI Analysis Error:', error);\n      alert('حدث خطأ في التحليل. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const generateAdvancedReasoning = (symbol: string, action: string, technicalScore: number, sentimentScore: number, candleCount: number): string[] => {\n    const reasons = [];\n    \n    reasons.push(`🔍 تحليل متقدم باستخدام ${candleCount} شمعة`);\n    \n    if (action === 'BUY') {\n      reasons.push(`📈 إشارة شراء قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💹 معنويات السوق إيجابية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات المقاومة مع حجم تداول مرتفع`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الصاعد`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else if (action === 'SELL') {\n      reasons.push(`📉 إشارة بيع قوية - النتيجة الفنية: ${technicalScore.toFixed(1)}/100`);\n      reasons.push(`💸 معنويات السوق سلبية: ${sentimentScore.toFixed(1)}/100`);\n      reasons.push(`🎯 كسر مستويات الدعم مع ضغط بيع قوي`);\n      reasons.push(`📊 المؤشرات الفنية تؤكد الاتجاه الهابط`);\n      if (candleCount >= 100) {\n        reasons.push(`⭐ تحليل عالي الدقة: ${candleCount} شمعة تؤكد الإشارة`);\n      }\n    } else {\n      reasons.push(`⚖️ السوق في حالة توازن - انتظار إشارة واضحة`);\n      reasons.push(`📊 المؤشرات متضاربة - يُنصح بالانتظار`);\n    }\n    \n    return reasons;\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00, 'ETHUSD': 2650.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getPairName = (symbol: string): string => {\n    const names: { [key: string]: string } = {\n      'EURUSD': 'يورو/دولار أمريكي', 'GBPUSD': 'جنيه إسترليني/دولار أمريكي',\n      'USDJPY': 'دولار أمريكي/ين ياباني', 'USDCHF': 'دولار أمريكي/فرنك سويسري',\n      'AUDUSD': 'دولار أسترالي/دولار أمريكي', 'USDCAD': 'دولار أمريكي/دولار كندي',\n      'NZDUSD': 'دولار نيوزيلندي/دولار أمريكي', 'EURGBP': 'يورو/جنيه إسترليني',\n      'EURJPY': 'يورو/ين ياباني', 'GBPJPY': 'جنيه إسترليني/ين ياباني',\n      'XAUUSD': 'الذهب/دولار أمريكي', 'XAGUSD': 'الفضة/دولار أمريكي',\n      'USOIL': 'النفط الخام/دولار أمريكي', 'BTCUSD': 'بيتكوين/دولار أمريكي',\n      'ETHUSD': 'إيثريوم/دولار أمريكي'\n    };\n    return names[symbol] || symbol;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰', 'ETHUSD': '⟠💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getActionColor = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'bg-green-100 text-green-800 border-green-200';\n      case 'SELL': return 'bg-red-100 text-red-800 border-red-200';\n      case 'HOLD': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getActionText = (action: string): string => {\n    switch (action) {\n      case 'BUY': return 'شراء';\n      case 'SELL': return 'بيع';\n      case 'HOLD': return 'انتظار';\n      default: return 'غير محدد';\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            🤖 توصيات الذكاء الاصطناعي المتقدمة\n            <span className=\"mr-3 px-2 py-1 bg-purple-600 text-white rounded text-sm\">\n              تحكم يدوي\n            </span>\n          </h3>\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            {lastAnalysis && (\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                آخر تحليل: {lastAnalysis.toLocaleTimeString('ar-SA')}\n              </div>\n            )}\n            <button\n              onClick={runAIAnalysis}\n              disabled={isAnalyzing}\n              className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n                isAnalyzing \n                  ? 'bg-gray-400 text-white cursor-not-allowed'\n                  : 'bg-purple-600 text-white hover:bg-purple-700'\n              }`}\n            >\n              {isAnalyzing ? '🧠 جاري التحليل...' : '🚀 تشغيل التحليل الذكي'}\n            </button>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n          {/* Pair Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              أزواج العملات ({selectedPairs.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedPairs(['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الرئيسية\n              </button>\n              <button\n                onClick={() => setSelectedPairs(['XAUUSD', 'XAGUSD', 'USOIL'])}\n                className=\"px-2 py-1 bg-yellow-600 text-white rounded text-xs hover:bg-yellow-700\"\n              >\n                السلع\n              </button>\n              <button\n                onClick={() => setSelectedPairs(allForexPairs)}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"max-h-24 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded p-1\">\n              <div className=\"grid grid-cols-3 gap-1\">\n                {allForexPairs.map(pair => (\n                  <button\n                    key={pair}\n                    onClick={() => {\n                      if (selectedPairs.includes(pair)) {\n                        setSelectedPairs(selectedPairs.filter(p => p !== pair));\n                      } else {\n                        setSelectedPairs([...selectedPairs, pair]);\n                      }\n                    }}\n                    className={`px-1 py-1 rounded text-xs transition-colors ${\n                      selectedPairs.includes(pair)\n                        ? 'bg-green-600 text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    {pair}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Timeframe Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطارات الزمنية ({selectedTimeframes.length}):\n            </label>\n            \n            <div className=\"flex flex-wrap gap-1 mb-2\">\n              <button\n                onClick={() => setSelectedTimeframes(['1h', '4h', '1d'])}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\"\n              >\n                الأساسية\n              </button>\n              <button\n                onClick={() => setSelectedTimeframes(timeframes.map(t => t.key))}\n                className=\"px-2 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700\"\n              >\n                الكل\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-4 gap-1\">\n              {timeframes.map(tf => (\n                <button\n                  key={tf.key}\n                  onClick={() => {\n                    if (selectedTimeframes.includes(tf.key)) {\n                      setSelectedTimeframes(selectedTimeframes.filter(t => t !== tf.key));\n                    } else {\n                      setSelectedTimeframes([...selectedTimeframes, tf.key]);\n                    }\n                  }}\n                  className={`px-1 py-1 rounded text-xs transition-colors ${\n                    selectedTimeframes.includes(tf.key)\n                      ? 'bg-green-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'\n                  }`}\n                >\n                  {tf.key}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Candle Count */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              عدد الشموع للتحليل:\n            </label>\n            <select\n              value={candleCount}\n              onChange={(e) => setCandleCount(Number(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white mb-2\"\n            >\n              {candleOptions.map(count => (\n                <option key={count} value={count}>\n                  {count} شمعة\n                </option>\n              ))}\n            </select>\n            <div className=\"text-xs text-gray-500\">\n              دقة متوقعة: {candleCount >= 150 ? '98%' : candleCount >= 100 ? '95%' : candleCount >= 50 ? '85%' : '75%'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Analysis Progress */}\n        {isAnalyzing && (\n          <div className=\"mb-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-3\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600\"></div>\n              <span className=\"font-medium text-gray-900 dark:text-white\">\n                🧠 الذكاء الاصطناعي يحلل السوق...\n              </span>\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n              <div>• تحليل {selectedPairs.length} زوج عملة</div>\n              <div>• فحص {selectedTimeframes.length} إطار زمني</div>\n              <div>• معالجة {candleCount} شمعة لكل إطار</div>\n              <div>• إجمالي نقاط البيانات: {selectedPairs.length * selectedTimeframes.length * candleCount}</div>\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && recommendations.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🤖</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              مرحباً بك في نظام الذكاء الاصطناعي المتقدم\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اختر أزواج العملات والإطارات الزمنية وعدد الشموع، ثم اضغط على \"تشغيل التحليل الذكي\" للحصول على توصيات دقيقة\n            </p>\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">💡 نصائح للحصول على أفضل النتائج:</h4>\n              <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1 text-right\">\n                <li>• استخدم 100+ شمعة للحصول على دقة عالية</li>\n                <li>• اختر 3-5 أزواج للتحليل المفصل</li>\n                <li>• ركز على الإطارات الزمنية الأساسية (1h, 4h, 1d)</li>\n                <li>• التحليل اليدوي يعطي نتائج أكثر دقة</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Recommendations Results */}\n        {!isAnalyzing && recommendations.length > 0 && (\n          <div className=\"space-y-6\">\n            {/* Summary Stats */}\n            <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                📊 ملخص التحليل\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {recommendations.filter(r => r.action === 'BUY').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات شراء</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {recommendations.filter(r => r.action === 'SELL').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">إشارات بيع</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-yellow-600\">\n                    {recommendations.filter(r => r.action === 'HOLD').length}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">انتظار</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {(recommendations.reduce((sum, r) => sum + r.accuracy, 0) / recommendations.length).toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">متوسط الدقة</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recommendations Grid */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n              {recommendations.slice(0, 9).map((rec) => (\n                <div key={rec.id} className={`border-2 rounded-xl p-6 ${\n                  rec.action === 'BUY' \n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'\n                    : rec.action === 'SELL'\n                    ? 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'\n                    : 'bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200'\n                }`}>\n                  {/* Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-2 space-x-reverse\">\n                      <span className=\"text-lg\">{getPairFlag(rec.symbol)}</span>\n                      <div>\n                        <h4 className=\"text-lg font-bold text-gray-900\">\n                          {getActionText(rec.action)} {rec.symbol}\n                        </h4>\n                        <p className=\"text-xs text-gray-600\">\n                          {timeframes.find(t => t.key === rec.timeframe)?.name} | {rec.candleCount} شمعة\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900\">\n                        {rec.confidence.toFixed(0)}%\n                      </div>\n                      <div className=\"text-xs text-gray-600\">دقة: {rec.accuracy.toFixed(0)}%</div>\n                    </div>\n                  </div>\n\n                  {/* Action Badge */}\n                  <div className={`text-center py-2 rounded-lg mb-4 border-2 ${getActionColor(rec.action)}`}>\n                    <div className=\"font-bold text-sm\">{getActionText(rec.action)}</div>\n                    <div className=\"text-xs\">النتيجة الإجمالية: {rec.overallScore.toFixed(1)}/100</div>\n                  </div>\n\n                  {/* Price Levels */}\n                  {rec.action !== 'HOLD' && (\n                    <div className=\"grid grid-cols-2 gap-2 mb-4 text-sm\">\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">الدخول</div>\n                        <div className=\"font-bold\">{rec.entry.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">وقف الخسارة</div>\n                        <div className=\"font-bold text-red-600\">{rec.stopLoss.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">هدف 1</div>\n                        <div className=\"font-bold text-green-600\">{rec.takeProfit1.toFixed(5)}</div>\n                      </div>\n                      <div className=\"bg-white rounded p-2 text-center\">\n                        <div className=\"text-xs text-gray-600\">R/R</div>\n                        <div className=\"font-bold text-blue-600\">{rec.riskReward.toFixed(2)}:1</div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* AI Scores */}\n                  <div className=\"bg-white rounded p-3 mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🎯 نتائج التحليل:</h5>\n                    <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                      <div className=\"flex justify-between\">\n                        <span>فني:</span>\n                        <span className=\"font-medium\">{rec.technicalScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>مشاعر:</span>\n                        <span className=\"font-medium\">{rec.sentimentScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>حجم:</span>\n                        <span className=\"font-medium\">{rec.volumeScore.toFixed(0)}/100</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span>اتجاه:</span>\n                        <span className=\"font-medium\">{rec.trendScore.toFixed(0)}/100</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* AI Reasoning */}\n                  <div className=\"bg-white rounded p-3\">\n                    <h5 className=\"text-sm font-medium text-gray-900 mb-2\">🧠 تحليل الذكاء الاصطناعي:</h5>\n                    <ul className=\"text-xs text-gray-600 space-y-1\">\n                      {rec.reasoning.slice(0, 3).map((reason, i) => (\n                        <li key={i} className=\"flex items-start\">\n                          <span className=\"mr-1 text-blue-500\">▶</span>\n                          <span>{reason}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {recommendations.length > 9 && (\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  يتم عرض أفضل 9 توصيات من أصل {recommendations.length}\n                </p>\n                <button\n                  onClick={() => {\n                    // Could implement pagination or show all\n                    alert(`إجمالي التوصيات: ${recommendations.length}\\nيتم عرض أفضل 9 توصيات بناءً على النتيجة الإجمالية`);\n                  }}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700\"\n                >\n                  عرض جميع التوصيات ({recommendations.length})\n                </button>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AA2Be,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAU;QAAU;KAAS;IAC3F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAM;QAAM;KAAK;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,gBAAgB;QACpB,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D,cAAc;QACd;QAAU;QAAU;QAAU;QAAU;QAAU;QAClD;QAAU;QAAU;QAAU;QAAU;QACxC;QAAU;QAAU;QAAU;QAC9B;QAAU;QAAU;QACpB;QAAU;QAAU;QACpB,uBAAuB;QACvB;QAAU;QAAU;QAAS;QAAU;KACxC;IAED,MAAM,aAAa;QACjB;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAO,MAAM;QAAW;QAC/B;YAAE,KAAK;YAAM,MAAM;QAAS;QAC5B;YAAE,KAAK;YAAM,MAAM;QAAU;QAC7B;YAAE,KAAK;YAAM,MAAM;QAAQ;QAC3B;YAAE,KAAK;YAAM,MAAM;QAAU;KAC9B;IAED,MAAM,gBAAgB;QAAC;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IAErD,8BAA8B;IAC9B,MAAM,gBAAgB;QACpB,IAAI,cAAc,MAAM,KAAK,KAAK,mBAAmB,MAAM,KAAK,GAAG;YACjE,MAAM;YACN;QACF;QAEA,eAAe;QAEf,6CAA6C;QAC7C,MAAM,eAAe,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG,CAAC,cAAc,EAAE,IAAI;QAC7F,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,OAAO,gBAAgB;QAE9D,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,qBAAyC,EAAE;YAEjD,cAAc,OAAO,CAAC,CAAA;gBACpB,mBAAmB,OAAO,CAAC,CAAA;oBACzB,MAAM,YAAY,aAAa;oBAE/B,4CAA4C;oBAC5C,MAAM,gBAAgB,KAAK,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,MAAM,IAAI;oBAC9D,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,iBAAiB,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAClE,MAAM,cAAc,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAC/D,MAAM,aAAa,KAAK,KAAK,MAAM,KAAK,KAAM,gBAAgB;oBAE9D,MAAM,eACJ,iBAAiB,MACjB,iBAAiB,MACjB,cAAc,MACd,aAAa;oBAGf,MAAM,SAAS,eAAe,KAAK,QAAQ,eAAe,KAAK,SAAS;oBACxE,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBACrE,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,gBAAgB,KAAK,MAAM,KAAK,IAAI;oBAEnE,mBAAmB;oBACnB,MAAM,QAAQ,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,YAAY;oBAC9D,MAAM,WAAW,WAAW,QACxB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,QACjB,QAAS,QAAQ;oBACrB,MAAM,MAAM,WAAW,QACnB,QAAS,QAAQ,OACjB,QAAS,QAAQ;oBAErB,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,QAAQ;oBAE5D,mBAAmB,IAAI,CAAC;wBACtB,IAAI,AAAC,GAAY,OAAV,QAAO,KAAgB,OAAb,WAAU,KAAc,OAAX,KAAK,GAAG;wBACtC;wBACA;wBACA;wBACA;wBACA;wBACA,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb;wBACA;wBACA;wBACA;wBACA,WAAW,0BAA0B,QAAQ,QAAQ,gBAAgB,gBAAgB;wBACrF;wBACA;wBACA;wBACA;wBACA;wBACA,WAAW,KAAK,GAAG;oBACrB;gBACF;YACF;YAEA,qDAAqD;YACrD,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YAEjE,mBAAmB;YACnB,gBAAgB,IAAI;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,4BAA4B,CAAC,QAAgB,QAAgB,gBAAwB,gBAAwB;QACjH,MAAM,UAAU,EAAE;QAElB,QAAQ,IAAI,CAAC,AAAC,2BAAsC,OAAZ,aAAY;QAEpD,IAAI,WAAW,OAAO;YACpB,QAAQ,IAAI,CAAC,AAAC,wCAAiE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC/E,QAAQ,IAAI,CAAC,AAAC,6BAAsD,OAA1B,eAAe,OAAO,CAAC,IAAG;YACpE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO,IAAI,WAAW,QAAQ;YAC5B,QAAQ,IAAI,CAAC,AAAC,uCAAgE,OAA1B,eAAe,OAAO,CAAC,IAAG;YAC9E,QAAQ,IAAI,CAAC,AAAC,2BAAoD,OAA1B,eAAe,OAAO,CAAC,IAAG;YAClE,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;YACd,IAAI,eAAe,KAAK;gBACtB,QAAQ,IAAI,CAAC,AAAC,uBAAkC,OAAZ,aAAY;YAClD;QACF,OAAO;YACL,QAAQ,IAAI,CAAE;YACd,QAAQ,IAAI,CAAE;QAChB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;YAAU,UAAU;QAChD;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAqB,UAAU;YACzC,UAAU;YAA0B,UAAU;YAC9C,UAAU;YAA8B,UAAU;YAClD,UAAU;YAAgC,UAAU;YACpD,UAAU;YAAkB,UAAU;YACtC,UAAU;YAAsB,UAAU;YAC1C,SAAS;YAA4B,UAAU;YAC/C,UAAU;QACZ;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;YAAO,UAAU;QAC/C;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;oCACZ,8BACC,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC5C,aAAa,kBAAkB,CAAC;;;;;;;kDAGhD,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;kDAGL,cAAc,uBAAuB;;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CACjE,cAAc,MAAM;4CAAC;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;wDAAU;qDAAS;gDACxE,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;wDAAC;wDAAU;wDAAU;qDAAQ;gDAC7D,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oDAEC,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,OAAO;4DAChC,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;wDACnD,OAAO;4DACL,iBAAiB;mEAAI;gEAAe;6DAAK;wDAC3C;oDACF;oDACA,WAAW,AAAC,+CAIX,OAHC,cAAc,QAAQ,CAAC,QACnB,4BACA;8DAGL;mDAdI;;;;;;;;;;;;;;;;;;;;;0CAsBf,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAkE;4CAC9D,mBAAmB,MAAM;4CAAC;;;;;;;kDAG/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,sBAAsB;wDAAC;wDAAM;wDAAM;qDAAK;gDACvD,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,sBAAsB,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gDAC9D,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAEC,SAAS;oDACP,IAAI,mBAAmB,QAAQ,CAAC,GAAG,GAAG,GAAG;wDACvC,sBAAsB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM,GAAG,GAAG;oDACnE,OAAO;wDACL,sBAAsB;+DAAI;4DAAoB,GAAG,GAAG;yDAAC;oDACvD;gDACF;gDACA,WAAW,AAAC,+CAIX,OAHC,mBAAmB,QAAQ,CAAC,GAAG,GAAG,IAC9B,4BACA;0DAGL,GAAG,GAAG;+CAdF,GAAG,GAAG;;;;;;;;;;;;;;;;0CAqBnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;kDAET,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;gDAAmB,OAAO;;oDACxB;oDAAM;;+CADI;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;;4CAAwB;4CACxB,eAAe,MAAM,QAAQ,eAAe,MAAM,QAAQ,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3G,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAS,cAAc,MAAM;4CAAC;;;;;;;kDACnC,6LAAC;;4CAAI;4CAAO,mBAAmB,MAAM;4CAAC;;;;;;;kDACtC,6LAAC;;4CAAI;4CAAU;4CAAY;;;;;;;kDAC3B,6LAAC;;4CAAI;4CAAyB,cAAc,MAAM,GAAG,mBAAmB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oBAMtF,CAAC,eAAe,gBAAgB,MAAM,KAAK,mBAC1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,gBAAgB,MAAM,GAAG,mBACxC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,OAAO,MAAM;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,CAAC,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE,KAAK,gBAAgB,MAAM,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAEjG,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;0CAMhE,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wCAiBrB;yDAhBX,6LAAC;wCAAiB,WAAW,AAAC,2BAM7B,OALC,IAAI,MAAM,KAAK,QACX,mEACA,IAAI,MAAM,KAAK,SACf,4DACA;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAW,YAAY,IAAI,MAAM;;;;;;0EACjD,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;;4EACX,cAAc,IAAI,MAAM;4EAAE;4EAAE,IAAI,MAAM;;;;;;;kFAEzC,6LAAC;wEAAE,WAAU;;6EACV,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,IAAI,SAAS,eAA5C,uCAAA,iBAA+C,IAAI;4EAAC;4EAAI,IAAI,WAAW;4EAAC;;;;;;;;;;;;;;;;;;;kEAI/E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAE7B,6LAAC;gEAAI,WAAU;;oEAAwB;oEAAM,IAAI,QAAQ,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAKzE,6LAAC;gDAAI,WAAW,AAAC,6CAAuE,OAA3B,eAAe,IAAI,MAAM;;kEACpF,6LAAC;wDAAI,WAAU;kEAAqB,cAAc,IAAI,MAAM;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;;4DAAU;4DAAoB,IAAI,YAAY,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAI1E,IAAI,MAAM,KAAK,wBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAAa,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA0B,IAAI,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA4B,IAAI,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;kEAErE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;;oEAA2B,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;0DAM1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,cAAc,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE/D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,WAAW,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAE5D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;;4EAAe,IAAI,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;kEACX,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,kBACtC,6LAAC;gEAAW,WAAU;;kFACpB,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;kFACrC,6LAAC;kFAAM;;;;;;;+DAFA;;;;;;;;;;;;;;;;;uCApFP,IAAI,EAAE;;;;;;;;;;;4BA+FnB,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgD;4CAC7B,gBAAgB,MAAM;;;;;;;kDAEtD,6LAAC;wCACC,SAAS;4CACP,yCAAyC;4CACzC,MAAM,AAAC,oBAA0C,OAAvB,gBAAgB,MAAM,EAAC;wCACnD;wCACA,WAAU;;4CACX;4CACqB,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D;GA/iBwB;KAAA", "debugId": null}}, {"offset": {"line": 4868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/FibonacciAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface FibonacciLevel {\n  level: number;\n  price: number;\n  percentage: string;\n  type: 'support' | 'resistance';\n  strength: number;\n}\n\ninterface FibonacciAnalysis {\n  symbol: string;\n  high: number;\n  low: number;\n  direction: 'bullish' | 'bearish';\n  levels: FibonacciLevel[];\n  currentPrice: number;\n  nearestLevel: FibonacciLevel;\n  recommendation: string;\n  confidence: number;\n}\n\nexport default function FibonacciAnalysis() {\n  const [selectedPair, setSelectedPair] = useState<string>('EURUSD');\n  const [analysis, setAnalysis] = useState<FibonacciAnalysis | null>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [timeframe, setTimeframe] = useState<string>('1h');\n\n  const forexPairs = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',\n    'EURGBP', 'EURJPY', 'GBPJPY', 'XAUUSD', 'XAGUSD', 'USOIL', 'BTCUSD'\n  ];\n\n  const timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];\n\n  const fibonacciLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1, 1.272, 1.414, 1.618, 2.618];\n\n  const runFibonacciAnalysis = async () => {\n    setIsAnalyzing(true);\n    \n    try {\n      // Simulate analysis delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const basePrice = getBasePrice(selectedPair);\n      const volatility = getVolatility(selectedPair);\n      \n      // Generate swing high and low\n      const swingRange = basePrice * volatility * 0.05;\n      const high = basePrice + swingRange * (0.5 + Math.random() * 0.5);\n      const low = basePrice - swingRange * (0.5 + Math.random() * 0.5);\n      \n      const direction = Math.random() > 0.5 ? 'bullish' : 'bearish';\n      const currentPrice = basePrice + (Math.random() - 0.5) * swingRange * 0.3;\n      \n      // Calculate Fibonacci levels\n      const levels: FibonacciLevel[] = fibonacciLevels.map(level => {\n        const price = direction === 'bullish' \n          ? high - (high - low) * level\n          : low + (high - low) * level;\n          \n        const distanceFromCurrent = Math.abs(price - currentPrice);\n        const strength = Math.max(20, 100 - (distanceFromCurrent / basePrice) * 1000);\n        \n        return {\n          level,\n          price,\n          percentage: `${(level * 100).toFixed(1)}%`,\n          type: price > currentPrice ? 'resistance' : 'support',\n          strength: Math.min(strength, 95)\n        };\n      });\n      \n      // Find nearest level\n      const nearestLevel = levels.reduce((nearest, current) => {\n        const nearestDistance = Math.abs(nearest.price - currentPrice);\n        const currentDistance = Math.abs(current.price - currentPrice);\n        return currentDistance < nearestDistance ? current : nearest;\n      });\n      \n      // Generate recommendation\n      const distanceToNearest = Math.abs(nearestLevel.price - currentPrice) / basePrice * 100;\n      let recommendation = '';\n      let confidence = 0;\n      \n      if (distanceToNearest < 0.1) {\n        if (nearestLevel.type === 'support' && direction === 'bullish') {\n          recommendation = 'شراء قوي - السعر عند مستوى دعم فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else if (nearestLevel.type === 'resistance' && direction === 'bearish') {\n          recommendation = 'بيع قوي - السعر عند مستوى مقاومة فيبوناتشي قوي';\n          confidence = 85 + Math.random() * 10;\n        } else {\n          recommendation = 'انتظار - السعر عند مستوى فيبوناتشي مهم';\n          confidence = 70 + Math.random() * 15;\n        }\n      } else if (distanceToNearest < 0.2) {\n        recommendation = `مراقبة - السعر يقترب من مستوى ${nearestLevel.percentage}`;\n        confidence = 60 + Math.random() * 20;\n      } else {\n        recommendation = 'لا توجد إشارة واضحة - السعر بعيد عن مستويات فيبوناتشي';\n        confidence = 40 + Math.random() * 20;\n      }\n      \n      setAnalysis({\n        symbol: selectedPair,\n        high,\n        low,\n        direction,\n        levels,\n        currentPrice,\n        nearestLevel,\n        recommendation,\n        confidence\n      });\n      \n    } catch (error) {\n      console.error('Fibonacci Analysis Error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const getBasePrice = (symbol: string): number => {\n    const prices: { [key: string]: number } = {\n      'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n      'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n      'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n      'USOIL': 78.50, 'BTCUSD': 43250.00\n    };\n    return prices[symbol] || 1.0000;\n  };\n\n  const getVolatility = (symbol: string): number => {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.8, 'GBPUSD': 1.2, 'USDJPY': 1.0, 'USDCHF': 0.7,\n      'AUDUSD': 1.5, 'USDCAD': 1.0, 'NZDUSD': 1.8, 'EURGBP': 0.6,\n      'EURJPY': 1.3, 'GBPJPY': 1.8, 'XAUUSD': 2.0, 'XAGUSD': 2.5,\n      'USOIL': 3.0, 'BTCUSD': 4.0\n    };\n    return volatilities[symbol] || 1.0;\n  };\n\n  const getPairFlag = (symbol: string): string => {\n    const flags: { [key: string]: string } = {\n      'EURUSD': '🇪🇺🇺🇸', 'GBPUSD': '🇬🇧🇺🇸', 'USDJPY': '🇺🇸🇯🇵', 'USDCHF': '🇺🇸🇨🇭',\n      'AUDUSD': '🇦🇺🇺🇸', 'USDCAD': '🇺🇸🇨🇦', 'NZDUSD': '🇳🇿🇺🇸', 'EURGBP': '🇪🇺🇬🇧',\n      'EURJPY': '🇪🇺🇯🇵', 'GBPJPY': '🇬🇧🇯🇵', 'XAUUSD': '🥇💰', 'XAGUSD': '🥈💰',\n      'USOIL': '🛢️💰', 'BTCUSD': '₿💰'\n    };\n    return flags[symbol] || '💱';\n  };\n\n  const getLevelColor = (level: FibonacciLevel, currentPrice: number): string => {\n    const distance = Math.abs(level.price - currentPrice) / currentPrice * 100;\n    \n    if (distance < 0.1) return 'bg-red-100 border-red-500 text-red-800'; // Very close\n    if (distance < 0.2) return 'bg-yellow-100 border-yellow-500 text-yellow-800'; // Close\n    if (level.type === 'support') return 'bg-green-100 border-green-500 text-green-800';\n    return 'bg-blue-100 border-blue-500 text-blue-800';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-gray-900 dark:text-white flex items-center\">\n            📐 تحليل فيبوناتشي المتقدم\n            <span className=\"mr-3 px-2 py-1 bg-yellow-600 text-white rounded text-sm\">\n              أداة احترافية\n            </span>\n          </h3>\n          <button\n            onClick={runFibonacciAnalysis}\n            disabled={isAnalyzing}\n            className={`px-6 py-2 rounded-lg font-medium transition-colors ${\n              isAnalyzing \n                ? 'bg-gray-400 text-white cursor-not-allowed'\n                : 'bg-yellow-600 text-white hover:bg-yellow-700'\n            }`}\n          >\n            {isAnalyzing ? '📐 جاري التحليل...' : '🚀 تحليل فيبوناتشي'}\n          </button>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              زوج العملة:\n            </label>\n            <select\n              value={selectedPair}\n              onChange={(e) => setSelectedPair(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {forexPairs.map(pair => (\n                <option key={pair} value={pair}>\n                  {getPairFlag(pair)} {pair}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              الإطار الزمني:\n            </label>\n            <select\n              value={timeframe}\n              onChange={(e) => setTimeframe(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {timeframes.map(tf => (\n                <option key={tf} value={tf}>{tf}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Loading State */}\n        {isAnalyzing && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-flex items-center space-x-3 space-x-reverse\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600\"></div>\n              <span className=\"text-lg text-gray-600 dark:text-gray-400\">\n                🔍 تحليل مستويات فيبوناتشي لـ {selectedPair}...\n              </span>\n            </div>\n            <div className=\"mt-4 text-sm text-gray-500\">\n              جاري حساب مستويات الدعم والمقاومة الذهبية\n            </div>\n          </div>\n        )}\n\n        {/* No Analysis Yet */}\n        {!isAnalyzing && !analysis && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📐</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              تحليل فيبوناتشي الاحترافي\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto\">\n              اكتشف مستويات الدعم والمقاومة الذهبية باستخدام نسب فيبوناتشي المتقدمة\n            </p>\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 max-w-lg mx-auto\">\n              <h4 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                💡 ما هو تحليل فيبوناتشي؟\n              </h4>\n              <ul className=\"text-sm text-yellow-800 dark:text-yellow-200 space-y-1 text-right\">\n                <li>• مستويات رياضية دقيقة للدعم والمقاومة</li>\n                <li>• نسب ذهبية مستخدمة من قبل المحترفين</li>\n                <li>• تحديد نقاط الدخول والخروج المثلى</li>\n                <li>• دقة عالية في التنبؤ بحركة الأسعار</li>\n              </ul>\n            </div>\n          </div>\n        )}\n\n        {/* Analysis Results */}\n        {!isAnalyzing && analysis && (\n          <div className=\"space-y-6\">\n            {/* Summary */}\n            <div className=\"bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  <span className=\"text-2xl\">{getPairFlag(analysis.symbol)}</span>\n                  <div>\n                    <h4 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {analysis.symbol} - {timeframe}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      اتجاه: {analysis.direction === 'bullish' ? '📈 صاعد' : '📉 هابط'}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                    {analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">السعر الحالي</div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-600\">\n                    {analysis.high.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أعلى سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-red-600\">\n                    {analysis.low.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">أقل سعر</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">\n                    {analysis.confidence.toFixed(0)}%\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">مستوى الثقة</div>\n                </div>\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">📋 التوصية:</h5>\n                <p className=\"text-sm text-gray-700 dark:text-gray-300\">{analysis.recommendation}</p>\n              </div>\n            </div>\n\n            {/* Fibonacci Levels */}\n            <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                📐 مستويات فيبوناتشي\n              </h4>\n              \n              <div className=\"space-y-2\">\n                {analysis.levels.map((level, index) => (\n                  <div \n                    key={index}\n                    className={`border-2 rounded-lg p-3 transition-all duration-200 ${getLevelColor(level, analysis.currentPrice)}`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3 space-x-reverse\">\n                        <div className=\"text-lg font-bold\">\n                          {level.percentage}\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">\n                            {level.price.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)}\n                          </div>\n                          <div className=\"text-xs\">\n                            {level.type === 'support' ? '🟢 دعم' : '🔴 مقاومة'}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium\">\n                          قوة: {level.strength.toFixed(0)}%\n                        </div>\n                        <div className=\"text-xs\">\n                          المسافة: {(Math.abs(level.price - analysis.currentPrice) / analysis.currentPrice * 100).toFixed(2)}%\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {level === analysis.nearestLevel && (\n                      <div className=\"mt-2 pt-2 border-t border-current\">\n                        <div className=\"text-xs font-medium\">\n                          ⭐ أقرب مستوى فيبوناتشي للسعر الحالي\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Trading Suggestions */}\n            <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                💡 اقتراحات التداول\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-green-600 mb-2\">🎯 نقاط الدخول المحتملة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'support' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n                \n                <div className=\"bg-white dark:bg-gray-700 rounded p-3\">\n                  <h5 className=\"font-medium text-red-600 mb-2\">🛑 مستويات وقف الخسارة:</h5>\n                  <ul className=\"text-sm space-y-1\">\n                    {analysis.levels\n                      .filter(l => l.type === 'resistance' && l.strength > 70)\n                      .slice(0, 3)\n                      .map((level, i) => (\n                        <li key={i}>• {level.percentage} - {level.price.toFixed(5)}</li>\n                      ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAwBe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,aAAa;QACjB;QAAU;QAAU;QAAU;QAAU;QAAU;QAAU;QAC5D;QAAU;QAAU;QAAU;QAAU;QAAU;QAAS;KAC5D;IAED,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAO;QAAM;QAAM;QAAM;KAAK;IAErE,MAAM,kBAAkB;QAAC;QAAG;QAAO;QAAO;QAAK;QAAO;QAAO;QAAG;QAAO;QAAO;QAAO;KAAM;IAE3F,MAAM,uBAAuB;QAC3B,eAAe;QAEf,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,aAAa;YAC/B,MAAM,aAAa,cAAc;YAEjC,8BAA8B;YAC9B,MAAM,aAAa,YAAY,aAAa;YAC5C,MAAM,OAAO,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAChE,MAAM,MAAM,YAAY,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;YAE/D,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,YAAY;YACpD,MAAM,eAAe,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YAEtE,6BAA6B;YAC7B,MAAM,SAA2B,gBAAgB,GAAG,CAAC,CAAA;gBACnD,MAAM,QAAQ,cAAc,YACxB,OAAO,CAAC,OAAO,GAAG,IAAI,QACtB,MAAM,CAAC,OAAO,GAAG,IAAI;gBAEzB,MAAM,sBAAsB,KAAK,GAAG,CAAC,QAAQ;gBAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,MAAM,AAAC,sBAAsB,YAAa;gBAExE,OAAO;oBACL;oBACA;oBACA,YAAY,AAAC,GAA2B,OAAzB,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,IAAG;oBACxC,MAAM,QAAQ,eAAe,eAAe;oBAC5C,UAAU,KAAK,GAAG,CAAC,UAAU;gBAC/B;YACF;YAEA,qBAAqB;YACrB,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,SAAS;gBAC3C,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,MAAM,kBAAkB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;gBACjD,OAAO,kBAAkB,kBAAkB,UAAU;YACvD;YAEA,0BAA0B;YAC1B,MAAM,oBAAoB,KAAK,GAAG,CAAC,aAAa,KAAK,GAAG,gBAAgB,YAAY;YACpF,IAAI,iBAAiB;YACrB,IAAI,aAAa;YAEjB,IAAI,oBAAoB,KAAK;gBAC3B,IAAI,aAAa,IAAI,KAAK,aAAa,cAAc,WAAW;oBAC9D,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO,IAAI,aAAa,IAAI,KAAK,gBAAgB,cAAc,WAAW;oBACxE,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC,OAAO;oBACL,iBAAiB;oBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;gBACpC;YACF,OAAO,IAAI,oBAAoB,KAAK;gBAClC,iBAAiB,AAAC,iCAAwD,OAAxB,aAAa,UAAU;gBACzE,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC,OAAO;gBACL,iBAAiB;gBACjB,aAAa,KAAK,KAAK,MAAM,KAAK;YACpC;YAEA,YAAY;gBACV,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAoC;YACxC,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAChE,UAAU;YAAQ,UAAU;YAAQ,UAAU;YAAS,UAAU;YACjE,SAAS;YAAO,UAAU;QAC5B;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAA0C;YAC9C,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,UAAU;YAAK,UAAU;YAAK,UAAU;YAAK,UAAU;YACvD,SAAS;YAAK,UAAU;QAC1B;QACA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAmC;YACvC,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAY,UAAU;YAC5E,UAAU;YAAY,UAAU;YAAY,UAAU;YAAQ,UAAU;YACxE,SAAS;YAAS,UAAU;QAC9B;QACA,OAAO,KAAK,CAAC,OAAO,IAAI;IAC1B;IAEA,MAAM,gBAAgB,CAAC,OAAuB;QAC5C,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,gBAAgB,eAAe;QAEvE,IAAI,WAAW,KAAK,OAAO,0CAA0C,aAAa;QAClF,IAAI,WAAW,KAAK,OAAO,mDAAmD,QAAQ;QACtF,IAAI,MAAM,IAAI,KAAK,WAAW,OAAO;QACrC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoE;kDAEhF,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;;;;;;;0CAI5E,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,AAAC,sDAIX,OAHC,cACI,8CACA;0CAGL,cAAc,uBAAuB;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;gDAAkB,OAAO;;oDACvB,YAAY;oDAAM;oDAAE;;+CADV;;;;;;;;;;;;;;;;0CAOnB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gDAAgB,OAAO;0DAAK;+CAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;4CAA2C;4CAC1B;4CAAa;;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;oBAO/C,CAAC,eAAe,CAAC,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;0CAGtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,eAAe,0BACf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,YAAY,SAAS,MAAM;;;;;;kEACvD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEACX,SAAS,MAAM;oEAAC;oEAAI;;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;;oEAA2C;oEAC9C,SAAS,SAAS,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;;0DAI7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,YAAY,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAEvE,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE/D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,SAAS,GAAG,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE9D,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,SAAS,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;kEAElC,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAA4C,SAAS,cAAc;;;;;;;;;;;;;;;;;;0CAKpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;gDAEC,WAAW,AAAC,uDAAkG,OAA5C,cAAc,OAAO,SAAS,YAAY;;kEAE5G,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,MAAM,UAAU;;;;;;kFAEnB,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;0FAE7D,6LAAC;gFAAI,WAAU;0FACZ,MAAM,IAAI,KAAK,YAAY,WAAW;;;;;;;;;;;;;;;;;;0EAK7C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;4EAAsB;4EAC7B,MAAM,QAAQ,CAAC,OAAO,CAAC;4EAAG;;;;;;;kFAElC,6LAAC;wEAAI,WAAU;;4EAAU;4EACb,CAAC,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,SAAS,YAAY,IAAI,SAAS,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;oDAKxG,UAAU,SAAS,YAAY,kBAC9B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;;+CA9BpC;;;;;;;;;;;;;;;;0CAyCb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,6LAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ,GAAG,IACjD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,6LAAC;;oEAAW;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAA/C;;;;;;;;;;;;;;;;0DAKjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,6LAAC;wDAAG,WAAU;kEACX,SAAS,MAAM,CACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,gBAAgB,EAAE,QAAQ,GAAG,IACpD,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,OAAO,kBACX,6LAAC;;oEAAW;oEAAG,MAAM,UAAU;oEAAC;oEAAI,MAAM,KAAK,CAAC,OAAO,CAAC;;+DAA/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjC;GA1XwB;KAAA", "debugId": null}}, {"offset": {"line": 5787, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AutoTradingRobot from '../components/AutoTradingRobot';\nimport AdvancedMarketAnalysis from '../components/AdvancedMarketAnalysis';\nimport TimeframeAnalysis from '../components/TimeframeAnalysis';\nimport ManualAIRecommendations from '../components/ManualAIRecommendations';\nimport FibonacciAnalysis from '../components/FibonacciAnalysis';\n\nexport default function Home() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    // Simulate initialization\n    const timer = setTimeout(() => {\n      setIsInitialized(true);\n    }, 3000);\n\n    // Update time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => {\n      clearTimeout(timer);\n      clearInterval(timeInterval);\n    };\n  }, []);\n\n  // Enhanced real-time market data with all major currencies and commodities\n  const [marketData, setMarketData] = useState([\n    // Major Forex Pairs\n    {\n      symbol: 'EURUSD',\n      name: 'يورو/دولار أمريكي',\n      price: 1.0850,\n      change: 0.0012,\n      changePercent: 0.11,\n      trend: 'BULLISH',\n      volume: 1250000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇪🇺🇺🇸'\n    },\n    {\n      symbol: 'GBPUSD',\n      name: 'جنيه إسترليني/دولار أمريكي',\n      price: 1.2650,\n      change: -0.0025,\n      changePercent: -0.20,\n      trend: 'BEARISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1,\n      flag: '🇬🇧🇺🇸'\n    },\n    {\n      symbol: 'USDJPY',\n      name: 'دولار أمريكي/ين ياباني',\n      price: 149.50,\n      change: 0.35,\n      changePercent: 0.23,\n      trend: 'BULLISH',\n      volume: 1100000,\n      volatility: 'MEDIUM',\n      liquidity: 'HIGH',\n      session: 'ASIAN',\n      riskLevel: 'LOW',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 1,\n      flag: '🇺🇸🇯🇵'\n    },\n    {\n      symbol: 'USDCHF',\n      name: 'دولار أمريكي/فرنك سويسري',\n      price: 0.8920,\n      change: 0.0008,\n      changePercent: 0.09,\n      trend: 'BULLISH',\n      volume: 650000,\n      volatility: 'LOW',\n      liquidity: 'HIGH',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇺🇸🇨🇭'\n    },\n    {\n      symbol: 'AUDUSD',\n      name: 'دولار أسترالي/دولار أمريكي',\n      price: 0.6580,\n      change: -0.0015,\n      changePercent: -0.23,\n      trend: 'BEARISH',\n      volume: 720000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'ASIAN',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇦🇺🇺🇸'\n    },\n    {\n      symbol: 'USDCAD',\n      name: 'دولار أمريكي/دولار كندي',\n      price: 1.3650,\n      change: 0.0020,\n      changePercent: 0.15,\n      trend: 'BULLISH',\n      volume: 580000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 1,\n      flag: '🇺🇸🇨🇦'\n    },\n    {\n      symbol: 'NZDUSD',\n      name: 'دولار نيوزيلندي/دولار أمريكي',\n      price: 0.6120,\n      change: -0.0012,\n      changePercent: -0.19,\n      trend: 'BEARISH',\n      volume: 420000,\n      volatility: 'HIGH',\n      liquidity: 'LOW',\n      session: 'ASIAN',\n      riskLevel: 'HIGH',\n      orderBlocks: 1,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇳🇿🇺🇸'\n    },\n    {\n      symbol: 'EURGBP',\n      name: 'يورو/جنيه إسترليني',\n      price: 0.8580,\n      change: 0.0005,\n      changePercent: 0.06,\n      trend: 'NEUTRAL',\n      volume: 480000,\n      volatility: 'LOW',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'LOW',\n      orderBlocks: 2,\n      fvg: 1,\n      choch: 0,\n      bos: 0,\n      flag: '🇪🇺🇬🇧'\n    },\n    // Commodities\n    {\n      symbol: 'XAUUSD',\n      name: 'الذهب/دولار أمريكي',\n      price: 2050.00,\n      change: 15.50,\n      changePercent: 0.76,\n      trend: 'BULLISH',\n      volume: 750000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'NEW_YORK',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 5,\n      fvg: 4,\n      choch: 2,\n      bos: 1,\n      flag: '🥇💰'\n    },\n    {\n      symbol: 'XAGUSD',\n      name: 'الفضة/دولار أمريكي',\n      price: 24.50,\n      change: 0.35,\n      changePercent: 1.45,\n      trend: 'BULLISH',\n      volume: 320000,\n      volatility: 'HIGH',\n      liquidity: 'LOW',\n      session: 'NEW_YORK',\n      riskLevel: 'HIGH',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 1,\n      flag: '🥈💰'\n    },\n    {\n      symbol: 'USOIL',\n      name: 'النفط الخام/دولار أمريكي',\n      price: 78.50,\n      change: -1.20,\n      changePercent: -1.51,\n      trend: 'BEARISH',\n      volume: 890000,\n      volatility: 'HIGH',\n      liquidity: 'HIGH',\n      session: 'NEW_YORK',\n      riskLevel: 'HIGH',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 2,\n      flag: '🛢️💰'\n    },\n    // Additional Major Pairs\n    {\n      symbol: 'EURJPY',\n      name: 'يورو/ين ياباني',\n      price: 162.30,\n      change: 0.45,\n      changePercent: 0.28,\n      trend: 'BULLISH',\n      volume: 680000,\n      volatility: 'MEDIUM',\n      liquidity: 'MEDIUM',\n      session: 'ASIAN',\n      riskLevel: 'MEDIUM',\n      orderBlocks: 3,\n      fvg: 2,\n      choch: 1,\n      bos: 0,\n      flag: '🇪🇺🇯🇵'\n    },\n    {\n      symbol: 'GBPJPY',\n      name: 'جنيه إسترليني/ين ياباني',\n      price: 189.20,\n      change: -0.80,\n      changePercent: -0.42,\n      trend: 'BEARISH',\n      volume: 520000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'LONDON',\n      riskLevel: 'HIGH',\n      orderBlocks: 2,\n      fvg: 3,\n      choch: 0,\n      bos: 1,\n      flag: '🇬🇧🇯🇵'\n    },\n    // Crypto (if enabled)\n    {\n      symbol: 'BTCUSD',\n      name: 'بيتكوين/دولار أمريكي',\n      price: 43250.00,\n      change: 850.00,\n      changePercent: 2.01,\n      trend: 'BULLISH',\n      volume: 1200000,\n      volatility: 'HIGH',\n      liquidity: 'HIGH',\n      session: 'GLOBAL',\n      riskLevel: 'HIGH',\n      orderBlocks: 6,\n      fvg: 5,\n      choch: 2,\n      bos: 3,\n      flag: '₿💰'\n    },\n    {\n      symbol: 'ETHUSD',\n      name: 'إيثريوم/دولار أمريكي',\n      price: 2650.00,\n      change: 45.50,\n      changePercent: 1.75,\n      trend: 'BULLISH',\n      volume: 980000,\n      volatility: 'HIGH',\n      liquidity: 'MEDIUM',\n      session: 'GLOBAL',\n      riskLevel: 'HIGH',\n      orderBlocks: 4,\n      fvg: 3,\n      choch: 1,\n      bos: 2,\n      flag: '⟠💰'\n    },\n  ]);\n\n  // Enhanced signals with ICT concepts\n  const [signals, setSignals] = useState([\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit1: 1.0900,\n      takeProfit2: 1.0950,\n      takeProfit3: 1.1000,\n      riskReward: 1.67,\n      confidence: 87,\n      timestamp: Date.now() - 300000,\n      reasoning: [\n        'Bullish Order Block at 1.0845-1.0855',\n        'Fair Value Gap filled and holding',\n        'CHoCH confirmed bullish structure',\n        'RSI oversold with divergence',\n        'MACD bullish crossover',\n        'Price above VWAP',\n        'London session high liquidity'\n      ],\n      patterns: ['Bullish Order Block', 'Fair Value Gap', 'CHoCH'],\n      session: 'LONDON',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit1: 1.2600,\n      takeProfit2: 1.2550,\n      takeProfit3: 1.2500,\n      riskReward: 1.67,\n      confidence: 82,\n      timestamp: Date.now() - 600000,\n      reasoning: [\n        'Bearish Breaker Block at 1.2655-1.2665',\n        'Break of Structure confirmed',\n        'Bearish engulfing pattern',\n        'RSI overbought rejection',\n        'Volume spike on breakdown',\n        'Below key support level'\n      ],\n      patterns: ['Bearish Breaker Block', 'BOS', 'Engulfing'],\n      session: 'LONDON',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    },\n    {\n      id: '3',\n      symbol: 'XAUUSD',\n      type: 'BUY',\n      entry: 2050.00,\n      stopLoss: 2035.00,\n      takeProfit1: 2075.00,\n      takeProfit2: 2100.00,\n      takeProfit3: 2125.00,\n      riskReward: 1.67,\n      confidence: 91,\n      timestamp: Date.now() - 900000,\n      reasoning: [\n        'Premium Discount Array (PDA) setup',\n        'Institutional Order Block respected',\n        'Liquidity sweep completed',\n        'Fair Value Gap acting as support',\n        'Smart money accumulation zone',\n        'Dollar weakness confluence'\n      ],\n      patterns: ['Order Block', 'Liquidity Sweep', 'PDA'],\n      session: 'NEW_YORK',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '4',\n      symbol: 'BTCUSD',\n      type: 'BUY',\n      entry: 43250.00,\n      stopLoss: 42500.00,\n      takeProfit1: 44500.00,\n      takeProfit2: 45500.00,\n      takeProfit3: 46500.00,\n      riskReward: 1.67,\n      confidence: 89,\n      timestamp: Date.now() - 1200000,\n      reasoning: [\n        'Crypto market showing strong bullish momentum',\n        'Breaking above key resistance at 43000',\n        'Volume surge indicating institutional interest',\n        'Fair Value Gap acting as strong support',\n        'Smart money accumulation pattern detected',\n        'Global adoption news driving sentiment'\n      ],\n      patterns: ['Volume Surge', 'Resistance Break', 'Institutional Flow'],\n      session: 'GLOBAL',\n      marketStructure: 'BULLISH',\n      smartMoney: 'ACCUMULATION'\n    },\n    {\n      id: '5',\n      symbol: 'USOIL',\n      type: 'SELL',\n      entry: 78.50,\n      stopLoss: 80.00,\n      takeProfit1: 76.50,\n      takeProfit2: 75.00,\n      takeProfit3: 73.50,\n      riskReward: 1.33,\n      confidence: 78,\n      timestamp: Date.now() - 1500000,\n      reasoning: [\n        'Oil showing bearish divergence on RSI',\n        'Inventory data showing oversupply',\n        'Geopolitical tensions easing',\n        'Dollar strength pressuring commodities',\n        'Technical breakdown below support',\n        'OPEC production concerns'\n      ],\n      patterns: ['Bearish Divergence', 'Support Break', 'Supply Pressure'],\n      session: 'NEW_YORK',\n      marketStructure: 'BEARISH',\n      smartMoney: 'DISTRIBUTION'\n    }\n  ]);\n\n  // Real-time updates simulation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setMarketData(prevData =>\n        prevData.map(market => ({\n          ...market,\n          price: market.price + (Math.random() - 0.5) * 0.001 * market.price,\n          change: market.change + (Math.random() - 0.5) * 0.0005,\n          changePercent: market.changePercent + (Math.random() - 0.5) * 0.05,\n          volume: market.volume + Math.floor((Math.random() - 0.5) * 50000)\n        }))\n      );\n    }, 2000); // Update every 2 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const mockSignals = [\n    {\n      id: '1',\n      symbol: 'EURUSD',\n      type: 'BUY',\n      entry: 1.0850,\n      stopLoss: 1.0820,\n      takeProfit: 1.0900,\n      confidence: 85,\n      timestamp: Date.now() - 300000,\n      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']\n    },\n    {\n      id: '2',\n      symbol: 'GBPUSD',\n      type: 'SELL',\n      entry: 1.2650,\n      stopLoss: 1.2680,\n      takeProfit: 1.2600,\n      confidence: 78,\n      timestamp: Date.now() - 600000,\n      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']\n    }\n  ];\n\n  if (!isInitialized) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">🤖 AI Trading Bot</h1>\n          <p className=\"text-blue-200\">Initializing advanced trading systems...</p>\n          <div className=\"mt-4 space-y-2 text-sm text-blue-300\">\n            <p>✅ Loading technical indicators</p>\n            <p>✅ Connecting to market data</p>\n            <p>✅ Initializing AI pattern recognition</p>\n            <p>✅ Setting up risk management</p>\n            <p>🔄 Starting real-time analysis...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                🤖 بوت التداول الذكي الاحترافي\n              </h1>\n              <div className=\"mr-6 flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 rounded-full bg-green-500 animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-300\">مباشر</span>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                {currentTime.toLocaleTimeString('ar-SA')}\n              </div>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">الوضع التجريبي</span>\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Dashboard */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* نظرة عامة محسنة على السوق */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8\">\n          {marketData.map(market => (\n            <div key={market.symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span className=\"text-lg\">{market.flag}</span>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {market.symbol}\n                    </h3>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {market.name}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    market.trend === 'BULLISH' ? 'bg-green-500' :\n                    market.trend === 'BEARISH' ? 'bg-red-500' : 'bg-yellow-500'\n                  }`}></div>\n                  <span className=\"text-xs text-gray-500\">{\n                    market.session === 'LONDON' ? 'لندن' :\n                    market.session === 'NEW_YORK' ? 'نيويورك' :\n                    market.session === 'ASIAN' ? 'آسيا' : market.session\n                  }</span>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}\n                  </span>\n                  <span className={`text-sm font-medium ${\n                    market.change >= 0 ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.trend === 'BULLISH'\n                      ? 'text-green-600 bg-green-50 dark:bg-green-900/20'\n                      : market.trend === 'BEARISH'\n                      ? 'text-red-600 bg-red-50 dark:bg-red-900/20'\n                      : 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'\n                  }`}>\n                    {market.trend === 'BULLISH' ? 'صاعد' :\n                     market.trend === 'BEARISH' ? 'هابط' : 'محايد'}\n                  </div>\n                  <div className={`px-2 py-1 rounded text-center ${\n                    market.riskLevel === 'LOW' ? 'text-green-600 bg-green-50' :\n                    market.riskLevel === 'MEDIUM' ? 'text-yellow-600 bg-yellow-50' :\n                    'text-red-600 bg-red-50'\n                  } dark:bg-opacity-20`}>\n                    {market.riskLevel === 'LOW' ? 'منخفض' :\n                     market.riskLevel === 'MEDIUM' ? 'متوسط' : 'عالي'}\n                  </div>\n                </div>\n\n                <div className=\"space-y-1 text-xs text-gray-600 dark:text-gray-400\">\n                  <div className=\"flex justify-between\">\n                    <span>الحجم:</span>\n                    <span>{(market.volume / 1000000).toFixed(1)} مليون</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>التقلب:</span>\n                    <span>{market.volatility === 'HIGH' ? 'عالي' :\n                           market.volatility === 'MEDIUM' ? 'متوسط' : 'منخفض'}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>السيولة:</span>\n                    <span>{market.liquidity === 'HIGH' ? 'عالية' :\n                           market.liquidity === 'MEDIUM' ? 'متوسطة' : 'منخفضة'}</span>\n                  </div>\n                </div>\n\n                {/* ICT Concepts Summary */}\n                <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  <div className=\"grid grid-cols-4 gap-1 text-xs\">\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-blue-600\">{market.orderBlocks}</div>\n                      <div className=\"text-gray-500\">OB</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-purple-600\">{market.fvg}</div>\n                      <div className=\"text-gray-500\">FVG</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-green-600\">{market.choch}</div>\n                      <div className=\"text-gray-500\">CHoCH</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"font-medium text-orange-600\">{market.bos}</div>\n                      <div className=\"text-gray-500\">BOS</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Advanced Market Analysis with TradingView Integration */}\n        <AdvancedMarketAnalysis />\n\n        {/* Manual AI Recommendations */}\n        <ManualAIRecommendations />\n\n        {/* Enhanced Multi-Timeframe Analysis */}\n        <TimeframeAnalysis />\n\n        {/* Advanced Fibonacci Analysis */}\n        <FibonacciAnalysis />\n\n        {/* Auto Trading Robot */}\n        <AutoTradingRobot />\n\n        {/* إشارات التداول المحسنة مع تحليل ICT */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🎯 إشارات التداول الذكية\n                <span className=\"mr-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                  مفاهيم ICT\n                </span>\n              </h3>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {signals.length} إشارة نشطة\n              </div>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-6\">\n              {signals.map(signal => (\n                <div key={signal.id} className={`border-2 rounded-xl p-6 transition-all hover:shadow-lg ${\n                  signal.type === 'BUY'\n                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800'\n                    : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200 dark:from-red-900/20 dark:to-rose-900/20 dark:border-red-800'\n                }`}>\n                  {/* Signal Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\n                        signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'\n                      }`}>\n                        <span className=\"text-white text-xl\">\n                          {signal.type === 'BUY' ? '📈' : '📉'}\n                        </span>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                            {signal.type} {signal.symbol}\n                          </span>\n                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                            signal.confidence >= 85 ? 'bg-green-100 text-green-800' :\n                            signal.confidence >= 75 ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-orange-100 text-orange-800'\n                          }`}>\n                            {signal.confidence}% Confidence\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-4 space-x-reverse mt-1 text-sm text-gray-600 dark:text-gray-400\">\n                          <span>{new Date(signal.timestamp).toLocaleString('ar-SA')}</span>\n                          <span>•</span>\n                          <span>جلسة {signal.session === 'LONDON' ? 'لندن' :\n                                      signal.session === 'NEW_YORK' ? 'نيويورك' :\n                                      signal.session === 'ASIAN' ? 'آسيا' : signal.session}</span>\n                          <span>•</span>\n                          <span>هيكل {signal.marketStructure === 'BULLISH' ? 'صاعد' :\n                                     signal.marketStructure === 'BEARISH' ? 'هابط' : 'محايد'}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                        R/R: {signal.riskReward}:1\n                      </div>\n                      <div className={`text-sm px-2 py-1 rounded ${\n                        signal.smartMoney === 'ACCUMULATION' ? 'bg-green-100 text-green-800' :\n                        'bg-red-100 text-red-800'\n                      }`}>\n                        {signal.smartMoney}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* مستويات الأسعار */}\n                  <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mb-4\">\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">الدخول</div>\n                      <div className=\"font-bold text-gray-900 dark:text-white\">\n                        {signal.entry.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">وقف الخسارة</div>\n                      <div className=\"font-bold text-red-600\">\n                        {signal.stopLoss.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 1</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit1.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 2</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit2.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                    <div className=\"bg-white dark:bg-gray-700 rounded-lg p-3 text-center\">\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">هدف 3</div>\n                      <div className=\"font-bold text-green-600\">\n                        {signal.takeProfit3.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* أنماط ICT */}\n                  <div className=\"mb-4\">\n                    <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      🔍 الأنماط المكتشفة:\n                    </h5>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {signal.patterns.map((pattern, index) => (\n                        <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\n                          {pattern === 'Bullish Order Block' ? 'منطقة طلبات صاعدة' :\n                           pattern === 'Fair Value Gap' ? 'فجوة القيمة العادلة' :\n                           pattern === 'CHoCH' ? 'تغيير الطبيعة' :\n                           pattern === 'Bearish Breaker Block' ? 'كتلة كسر هابطة' :\n                           pattern === 'BOS' ? 'كسر الهيكل' :\n                           pattern === 'Engulfing' ? 'نمط الابتلاع' :\n                           pattern === 'Order Block' ? 'منطقة الطلبات' :\n                           pattern === 'Liquidity Sweep' ? 'اكتساح السيولة' :\n                           pattern === 'PDA' ? 'مصفوفة العرض والطلب' : pattern}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* التحليل المتقدم */}\n                  <div className=\"bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n                      🧠 تحليل ICT المتقدم:\n                      <span className=\"mr-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">\n                        مفاهيم الأموال الذكية\n                      </span>\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(0, Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-blue-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\n                        {signal.reasoning.slice(Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2 text-purple-500\">▶</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n\n                  {/* أزرار الإجراءات */}\n                  <div className=\"mt-4 flex items-center justify-between\">\n                    <div className=\"flex space-x-2 space-x-reverse\">\n                      <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                        📊 عرض الرسم البياني\n                      </button>\n                      <button className=\"px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors\">\n                        📋 نسخ الإشارة\n                      </button>\n                    </div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      رقم الإشارة: {signal.id}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* لوحة المؤشرات الفنية المتقدمة */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n              📊 التحليل الفني المتقدم\n              <span className=\"mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                مباشر\n              </span>\n            </h3>\n          </div>\n\n          <div className=\"p-6\">\n            {/* المؤشرات التقليدية */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🔢 المؤشرات التقليدية\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-blue-700 dark:text-blue-300\">RSI (14)</h3>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">EURUSD</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">45.2</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Neutral</div>\n                    <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '45%'}}></div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-purple-700 dark:text-purple-300\">MACD</h3>\n                    <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded\">12,26,9</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">0.0012</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Bullish Cross</div>\n                    <div className=\"text-xs text-gray-500\">Signal: 0.0008</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-green-700 dark:text-green-300\">VWAP</h3>\n                    <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">Volume</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0845</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-blue-600\">Above Price</div>\n                    <div className=\"text-xs text-gray-500\">Support</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"text-sm font-medium text-orange-700 dark:text-orange-300\">Supertrend</h3>\n                    <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">10,3</span>\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">1.0820</div>\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-green-600\">Uptrend</div>\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* مفاهيم ICT */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🎯 مفاهيم الأموال الذكية ICT\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n                <div className=\"bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-cyan-200 dark:border-cyan-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-cyan-700 dark:text-cyan-300 mb-2\">Order Blocks</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">14</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Active Zones</div>\n                    <div className=\"mt-2 flex justify-center space-x-1\">\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full\"></span>\n                      <span className=\"w-2 h-2 bg-red-500 rounded-full\"></span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-violet-200 dark:border-violet-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-violet-700 dark:text-violet-300 mb-2\">Fair Value Gaps</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">7</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Unfilled</div>\n                    <div className=\"mt-2 text-xs text-violet-600\">3 Bullish, 4 Bearish</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-emerald-700 dark:text-emerald-300 mb-2\">CHoCH</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">2</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Recent</div>\n                    <div className=\"mt-2 text-xs text-emerald-600\">Bullish Structure</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-amber-700 dark:text-amber-300 mb-2\">BOS</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">3</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">Confirmed</div>\n                    <div className=\"mt-2 text-xs text-amber-600\">Strong Momentum</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-rose-200 dark:border-rose-800\">\n                  <div className=\"text-center\">\n                    <h3 className=\"text-sm font-medium text-rose-700 dark:text-rose-300 mb-2\">Liquidity</h3>\n                    <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-1\">HIGH</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">London Session</div>\n                    <div className=\"mt-2 text-xs text-rose-600\">Optimal Trading</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Market Structure Analysis */}\n            <div>\n              <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                🏗️ Market Structure Analysis\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Trend Analysis</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Highs:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Higher Lows:</span>\n                      <span className=\"text-xs font-medium text-green-600\">✓ Confirmed</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Structure:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Volume Profile</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">POC:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0842</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAH:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0865</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">VAL:</span>\n                      <span className=\"text-xs font-medium text-gray-900 dark:text-white\">1.0825</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                  <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Smart Money</h5>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Flow:</span>\n                      <span className=\"text-xs font-medium text-green-600\">ACCUMULATION</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Sentiment:</span>\n                      <span className=\"text-xs font-medium text-blue-600\">BULLISH</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">Confidence:</span>\n                      <span className=\"text-xs font-medium text-purple-600\">87%</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* لوحة إدارة المخاطر المتقدمة */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n                🛡️ إدارة المخاطر المتقدمة\n                <span className=\"mr-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full\">\n                  مراقبة مباشرة\n                </span>\n              </h3>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">محرك المخاطر نشط</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Key Metrics */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n              <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">💰</span>\n                  </div>\n                  <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">Demo</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">$10,000</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Account Balance</div>\n                <div className=\"flex items-center text-xs text-green-600\">\n                  <span className=\"mr-1\">↗</span>\n                  <span>+2.5% This Month</span>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">⚡</span>\n                  </div>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">Safe</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">2.5%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Daily Risk Exposure</div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">🎯</span>\n                  </div>\n                  <span className=\"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full\">Excellent</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">72.3%</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Win Rate (30 Days)</div>\n                <div className=\"text-xs text-purple-600\">23 Wins / 9 Losses</div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xl\">📊</span>\n                  </div>\n                  <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full\">Active</span>\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">3</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Active Signals</div>\n                <div className=\"text-xs text-orange-600\">2 BUY / 1 SELL</div>\n              </div>\n            </div>\n\n            {/* Risk Analysis */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  📈 Performance Metrics\n                </h4>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Profit Factor:</span>\n                    <span className=\"text-sm font-bold text-green-600\">1.85</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sharpe Ratio:</span>\n                    <span className=\"text-sm font-bold text-blue-600\">1.42</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Drawdown:</span>\n                    <span className=\"text-sm font-bold text-red-600\">-3.2%</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Avg R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.67:1</span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Recovery Factor:</span>\n                    <span className=\"text-sm font-bold text-indigo-600\">2.1</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n                <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-4\">\n                  ⚙️ Risk Parameters\n                </h4>\n                <div className=\"space-y-4\">\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Risk Per Trade:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">2.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-green-500 h-2 rounded-full\" style={{width: '40%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Daily Risk:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">6.0%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-blue-500 h-2 rounded-full\" style={{width: '42%'}}></div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Max Open Trades:</span>\n                      <span className=\"text-sm font-bold text-gray-900 dark:text-white\">3 / 5</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div className=\"bg-orange-500 h-2 rounded-full\" style={{width: '60%'}}></div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">Min R/R Ratio:</span>\n                    <span className=\"text-sm font-bold text-purple-600\">1.5:1</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Risk Alerts */}\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <div className=\"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                  <span className=\"text-white text-sm\">⚠</span>\n                </div>\n                <div>\n                  <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                    Risk Management Active\n                  </h4>\n                  <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                    All trades are automatically monitored for risk compliance. Current exposure is within safe limits.\n                    Demo mode ensures no real money is at risk.\n                  </p>\n                  <div className=\"mt-2 flex items-center space-x-4 text-xs text-yellow-600 dark:text-yellow-400\">\n                    <span>✓ Position sizing active</span>\n                    <span>✓ Stop loss enforcement</span>\n                    <span>✓ Daily limit monitoring</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Enhanced Footer */}\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white mt-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            {/* معلومات البوت */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 flex items-center\">\n                🤖 بوت التداول الذكي\n                <span className=\"mr-2 text-xs bg-blue-600 px-2 py-1 rounded-full\">الإصدار 2.0</span>\n              </h4>\n              <p className=\"text-sm text-gray-300 mb-4\">\n                نظام تداول احترافي مدعوم بالذكاء الاصطناعي مع مفاهيم ICT المتقدمة وتحليل الأموال الذكية.\n              </p>\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm\">مباشر ونشط</span>\n              </div>\n            </div>\n\n            {/* الميزات */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">🎯 الميزات</h4>\n              <ul className=\"text-sm text-gray-300 space-y-2\">\n                <li>• مفاهيم الأموال الذكية ICT</li>\n                <li>• مناطق الطلبات وفجوات القيمة العادلة</li>\n                <li>• كشف CHoCH وBOS</li>\n                <li>• إدارة مخاطر متقدمة</li>\n                <li>• تحليل السوق المباشر</li>\n                <li>• تحليل متعدد الإطارات الزمنية</li>\n                <li>• روبوت التداول الآلي</li>\n                <li>• دعم جميع العملات الرئيسية</li>\n              </ul>\n            </div>\n\n            {/* حالة النظام */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">📊 حالة النظام</h4>\n              <div className=\"space-y-3 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">بيانات السوق:</span>\n                  <span className=\"text-green-400\">✓ متصل</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">محرك الذكاء الاصطناعي:</span>\n                  <span className=\"text-green-400\">✓ نشط</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">مدير المخاطر:</span>\n                  <span className=\"text-green-400\">✓ يراقب</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">الروبوت الآلي:</span>\n                  <span className=\"text-blue-400\">✓ جاهز</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">الإشارات المولدة:</span>\n                  <span className=\"text-blue-400\">{signals.length} اليوم</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">آخر تحديث:</span>\n                  <span className=\"text-yellow-400\">{currentTime.toLocaleTimeString('ar-SA')}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* إخلاء المسؤولية */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">⚠️ مهم</h4>\n              <div className=\"text-xs text-gray-300 space-y-2\">\n                <p className=\"bg-red-900/30 border border-red-700 rounded p-2\">\n                  <strong>الوضع التجريبي:</strong> هذا عرض توضيحي. لا توجد أموال حقيقية متضمنة.\n                </p>\n                <p className=\"bg-yellow-900/30 border border-yellow-700 rounded p-2\">\n                  <strong>تحذير المخاطر:</strong> التداول ينطوي على مخاطر كبيرة. لا تتداول بأموال لا تستطيع تحمل خسارتها.\n                </p>\n                <p className=\"bg-blue-900/30 border border-blue-700 rounded p-2\">\n                  <strong>تعليمي:</strong> هذا البرنامج للأغراض التعليمية فقط.\n                </p>\n                <p className=\"bg-green-900/30 border border-green-700 rounded p-2\">\n                  <strong>الروبوت الآلي:</strong> ميزة تجريبية - اختبر جميع الإعدادات بعناية.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* الشريط السفلي */}\n          <div className=\"border-t border-gray-700 mt-8 pt-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <div className=\"flex items-center space-x-6 space-x-reverse text-sm text-gray-300 mb-4 md:mb-0\">\n                <span>© 2024 بوت التداول الذكي</span>\n                <span>•</span>\n                <span>مطور بـ Next.js و TypeScript</span>\n                <span>•</span>\n                <span>مدعوم بالذكاء الاصطناعي المتقدم</span>\n              </div>\n\n              <div className=\"flex items-center space-x-4 space-x-reverse\">\n                <div className=\"flex items-center space-x-2 space-x-reverse text-sm\">\n                  <span className=\"text-gray-300\">الأداء:</span>\n                  <span className=\"text-green-400 font-medium\">+2.5% شهرياً</span>\n                </div>\n                <div className=\"flex items-center space-x-2 space-x-reverse text-sm\">\n                  <span className=\"text-gray-300\">وقت التشغيل:</span>\n                  <span className=\"text-blue-400 font-medium\">99.9%</span>\n                </div>\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,0BAA0B;YAC1B,MAAM,QAAQ;wCAAW;oBACvB,iBAAiB;gBACnB;uCAAG;YAEH,2BAA2B;YAC3B,MAAM,eAAe;+CAAY;oBAC/B,eAAe,IAAI;gBACrB;8CAAG;YAEH;kCAAO;oBACL,aAAa;oBACb,cAAc;gBAChB;;QACF;yBAAG,EAAE;IAEL,2EAA2E;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,oBAAoB;QACpB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,cAAc;QACd;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,yBAAyB;QACzB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ,CAAC;YACT,eAAe,CAAC;YAChB,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,sBAAsB;QACtB;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,WAAW;YACX,SAAS;YACT,WAAW;YACX,aAAa;YACb,KAAK;YACL,OAAO;YACP,KAAK;YACL,MAAM;QACR;KACD;IAED,qCAAqC;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAuB;gBAAkB;aAAQ;YAC5D,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAyB;gBAAO;aAAY;YACvD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAe;gBAAmB;aAAM;YACnD,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAgB;gBAAoB;aAAqB;YACpE,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBACT;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBAAC;gBAAsB;gBAAiB;aAAkB;YACpE,SAAS;YACT,iBAAiB;YACjB,YAAY;QACd;KACD;IAED,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,WAAW;2CAAY;oBAC3B;mDAAc,CAAA,WACZ,SAAS,GAAG;2DAAC,CAAA,SAAU,CAAC;wCACtB,GAAG,MAAM;wCACT,OAAO,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,OAAO,KAAK;wCAClE,QAAQ,OAAO,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wCAChD,eAAe,OAAO,aAAa,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wCAC9D,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oCAC7D,CAAC;;;gBAEL;0CAAG,OAAO,yBAAyB;YAEnC;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAAgB;gBAA0B;aAAmB;QAC3E;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM;YACN,OAAO;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,WAAW,KAAK,GAAG,KAAK;YACxB,WAAW;gBAAC;gBAA6B;gBAAkB;aAAmB;QAChF;KACD;IAED,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA2C,KAAI;;0BAE5D,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,YAAY,kBAAkB,CAAC;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA,uBACd,6LAAC;gCAAwB,WAAU;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAW,OAAO,IAAI;;;;;;kEACtC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,OAAO,MAAM;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,OAAO,IAAI;;;;;;;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAGhB,OAFC,OAAO,KAAK,KAAK,YAAY,iBAC7B,OAAO,KAAK,KAAK,YAAY,eAAe;;;;;;kEAE9C,6LAAC;wDAAK,WAAU;kEACd,OAAO,OAAO,KAAK,WAAW,SAC9B,OAAO,OAAO,KAAK,aAAa,YAChC,OAAO,OAAO,KAAK,UAAU,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAK1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;kEAE5D,6LAAC;wDAAK,WAAW,AAAC,uBAEjB,OADC,OAAO,MAAM,IAAI,IAAI,mBAAmB;;4DAEvC,OAAO,MAAM,IAAI,IAAI,MAAM;4DAAI,OAAO,aAAa,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAIpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,iCAMhB,OALC,OAAO,KAAK,KAAK,YACb,oDACA,OAAO,KAAK,KAAK,YACjB,8CACA;kEAEH,OAAO,KAAK,KAAK,YAAY,SAC7B,OAAO,KAAK,KAAK,YAAY,SAAS;;;;;;kEAEzC,6LAAC;wDAAI,WAAW,AAAC,iCAIhB,OAHC,OAAO,SAAS,KAAK,QAAQ,+BAC7B,OAAO,SAAS,KAAK,WAAW,iCAChC,0BACD;kEACE,OAAO,SAAS,KAAK,QAAQ,UAC7B,OAAO,SAAS,KAAK,WAAW,UAAU;;;;;;;;;;;;0DAI/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAM,CAAC,OAAO,MAAM,GAAG,OAAO,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAE9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,OAAO,UAAU,KAAK,SAAS,SAC/B,OAAO,UAAU,KAAK,WAAW,UAAU;;;;;;;;;;;;kEAEpD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,OAAO,SAAS,KAAK,SAAS,UAC9B,OAAO,SAAS,KAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;0DAKtD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA6B,OAAO,WAAW;;;;;;8EAC9D,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA8B,OAAO,KAAK;;;;;;8EACzD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,OAAO,GAAG;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7F/B,OAAO,MAAM;;;;;;;;;;kCAuG3B,6LAAC,+IAAA,CAAA,UAAsB;;;;;kCAGvB,6LAAC,gJAAA,CAAA,UAAuB;;;;;kCAGxB,6LAAC,0IAAA,CAAA,UAAiB;;;;;kCAGlB,6LAAC,0IAAA,CAAA,UAAiB;;;;;kCAGlB,6LAAC,yIAAA,CAAA,UAAgB;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,6LAAC;oDAAK,WAAU;8DAAgE;;;;;;;;;;;;sDAIlF,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;4CAAoB,WAAW,AAAC,0DAIhC,OAHC,OAAO,IAAI,KAAK,QACZ,uIACA;;8DAGJ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,AAAC,2DAEhB,OADC,OAAO,IAAI,KAAK,QAAQ,iBAAiB;8EAEzC,cAAA,6LAAC;wEAAK,WAAU;kFACb,OAAO,IAAI,KAAK,QAAQ,OAAO;;;;;;;;;;;8EAGpC,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;;wFACb,OAAO,IAAI;wFAAC;wFAAE,OAAO,MAAM;;;;;;;8FAE9B,6LAAC;oFAAK,WAAW,AAAC,8CAIjB,OAHC,OAAO,UAAU,IAAI,KAAK,gCAC1B,OAAO,UAAU,IAAI,KAAK,kCAC1B;;wFAEC,OAAO,UAAU;wFAAC;;;;;;;;;;;;;sFAGvB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;8FAAM,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,CAAC;;;;;;8FACjD,6LAAC;8FAAK;;;;;;8FACN,6LAAC;;wFAAK;wFAAM,OAAO,OAAO,KAAK,WAAW,SAC9B,OAAO,OAAO,KAAK,aAAa,YAChC,OAAO,OAAO,KAAK,UAAU,SAAS,OAAO,OAAO;;;;;;;8FAChE,6LAAC;8FAAK;;;;;;8FACN,6LAAC;;wFAAK;wFAAM,OAAO,eAAe,KAAK,YAAY,SACxC,OAAO,eAAe,KAAK,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;sEAIjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAkD;wEACzD,OAAO,UAAU;wEAAC;;;;;;;8EAE1B,6LAAC;oEAAI,WAAW,AAAC,6BAGhB,OAFC,OAAO,UAAU,KAAK,iBAAiB,gCACvC;8EAEC,OAAO,UAAU;;;;;;;;;;;;;;;;;;8DAMxB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAG9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGjE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAGpE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAgD;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EACZ,OAAO,WAAW,CAAC,OAAO,CAAC,OAAO,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;8DAMtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAG1E,6LAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC;oEAAiB,WAAU;8EACzB,YAAY,wBAAwB,sBACpC,YAAY,mBAAmB,wBAC/B,YAAY,UAAU,kBACtB,YAAY,0BAA0B,mBACtC,YAAY,QAAQ,eACpB,YAAY,cAAc,iBAC1B,YAAY,gBAAgB,kBAC5B,YAAY,oBAAoB,mBAChC,YAAY,QAAQ,wBAAwB;mEATpC;;;;;;;;;;;;;;;;8DAgBjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;;gEAA2E;8EAEvF,6LAAC;oEAAK,WAAU;8EAA+D;;;;;;;;;;;;sEAIjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC9E,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC;oFAAK,WAAU;8FAAqB;;;;;;8FACrC,6LAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;8EAMb,6LAAC;oEAAG,WAAU;8EACX,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,sBAC3E,6LAAC;4EAAe,WAAU;;8FACxB,6LAAC;oFAAK,WAAU;8FAAuB;;;;;;8FACvC,6LAAC;8FAAM;;;;;;;2EAFA;;;;;;;;;;;;;;;;;;;;;;8DAUjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAAsG;;;;;;8EAGxH,6LAAC;oEAAO,WAAU;8EAAsG;;;;;;;;;;;;sEAI1H,6LAAC;4DAAI,WAAU;;gEAA2C;gEAC1C,OAAO,EAAE;;;;;;;;;;;;;;2CApJnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8J3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAAwE;sDAEpF,6LAAC;4CAAK,WAAU;sDAAkE;;;;;;;;;;;;;;;;;0CAMtF,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAK,WAAU;kFAAsD;;;;;;;;;;;;0EAExE,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;;;;;;;kEAKxE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,6LAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAyD;;;;;;kFACvE,6LAAC;wEAAK,WAAU;kFAAwD;;;;;;;;;;;;0EAE1E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;kFACvC,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAI3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA2D;;;;;;kFACzE,6LAAC;wEAAK,WAAU;kFAA0D;;;;;;;;;;;;0EAE5E,6LAAC;gEAAI,WAAU;0EAAwD;;;;;;0EACvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAyB;;;;;;kFACxC,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAgE;;;;;;8EAC9E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA+B;;;;;;;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAkE;;;;;;8EAChF,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA8D;;;;;;8EAC5E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA8B;;;;;;;;;;;;;;;;;kEAIjD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D;;;;;;8EAC1E,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;;;;;;;;;;;;;kEAK1D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;kFAEtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoD;;;;;;;;;;;;;;;;;;;;;;;;kEAK1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAqC;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAoC;;;;;;;;;;;;kFAEtD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAwE;8DAEpF,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;;;;;;;sDAIhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;0CAKjE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA6D;;;;;;;;;;;;kEAE/E,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA2D;;;;;;;;;;;;kEAE7E,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAC,OAAO;4DAAK;;;;;;;;;;;;;;;;;0DAItE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;0EAEvC,6LAAC;gEAAK,WAAU;0EAA+D;;;;;;;;;;;;kEAEjF,6LAAC;wDAAI,WAAU;kEAAwD;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAgD;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;kDAK7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;;0EAEpD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAiC;;;;;;;;;;;;0EAEnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;0DAK1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAgC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIvE,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA+B,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAItE,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAA2C;;;;;;0FAC3D,6LAAC;gFAAK,WAAU;0FAAkD;;;;;;;;;;;;kFAEpE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAiC,OAAO;gFAAC,OAAO;4EAAK;;;;;;;;;;;;;;;;;0EAIxE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;;;;;;8DAEvC,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAE,WAAU;sEAA+C;;;;;;sEAI5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAA+C;8DAE3D,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;;sDAEpE,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAiB,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAmB,YAAY,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAMxE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAwB;;;;;;;8DAElC,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAuB;;;;;;;8DAEjC,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAgB;;;;;;;8DAE1B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GA5wCwB;KAAA", "debugId": null}}]}