{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/utils/tradingViewAPI.ts"], "sourcesContent": ["// TradingView API Integration for Real-time Price Data\n\nexport interface TradingViewPrice {\n  symbol: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  high24h: number;\n  low24h: number;\n  volume: number;\n  timestamp: number;\n}\n\nexport interface TradingViewCandle {\n  time: number;\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n  volume: number;\n}\n\nexport interface TradingViewIndicator {\n  name: string;\n  value: number;\n  signal: 'BUY' | 'SELL' | 'NEUTRAL';\n  timeframe: string;\n}\n\n// TradingView Symbol Mapping\nconst SYMBOL_MAPPING: { [key: string]: string } = {\n  'EURUSD': 'FX:EURUSD',\n  'GBPUSD': 'FX:GBPUSD',\n  'USDJPY': 'FX:USDJPY',\n  'USDCHF': 'FX:USDCHF',\n  'AUDUSD': 'FX:AUDUSD',\n  'USDCAD': 'FX:USDCAD',\n  'NZDUSD': 'FX:NZDUSD',\n  'EURGBP': 'FX:EURGBP',\n  'EURJPY': 'FX:EURJPY',\n  'GBPJPY': 'FX:GBPJPY',\n  'XAUUSD': 'TVC:GOLD',\n  'XAGUSD': 'TVC:SILVER',\n  'USOIL': 'TVC:USOIL',\n  'BTCUSD': 'BINANCE:BTCUSDT'\n};\n\n// Real-time price fetching using TradingView's public API\nexport async function fetchTradingViewPrice(symbol: string): Promise<TradingViewPrice> {\n  try {\n    const tvSymbol = SYMBOL_MAPPING[symbol] || symbol;\n    \n    // Using TradingView's public API endpoint\n    const response = await fetch(`https://scanner.tradingview.com/symbol`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        symbols: {\n          tickers: [tvSymbol],\n          query: {\n            types: []\n          }\n        },\n        columns: [\n          'name', 'close', 'change', 'change_abs', 'high', 'low', 'volume',\n          'market_cap_basic', 'price_earnings_ttm', 'earnings_per_share_basic_ttm'\n        ]\n      })\n    });\n\n    if (!response.ok) {\n      throw new Error(`TradingView API error: ${response.status}`);\n    }\n\n    const data = await response.json();\n    \n    if (data.data && data.data.length > 0) {\n      const symbolData = data.data[0];\n      \n      return {\n        symbol: symbol,\n        price: symbolData.d[1] || 0,\n        change: symbolData.d[3] || 0,\n        changePercent: symbolData.d[2] || 0,\n        high24h: symbolData.d[4] || 0,\n        low24h: symbolData.d[5] || 0,\n        volume: symbolData.d[6] || 0,\n        timestamp: Date.now()\n      };\n    }\n    \n    throw new Error('No data received from TradingView');\n    \n  } catch (error) {\n    console.error(`Error fetching TradingView price for ${symbol}:`, error);\n    \n    // Fallback to simulated realistic prices\n    return getFallbackPrice(symbol);\n  }\n}\n\n// Fetch historical candle data\nexport async function fetchTradingViewCandles(\n  symbol: string, \n  timeframe: string = '1H', \n  limit: number = 100\n): Promise<TradingViewCandle[]> {\n  try {\n    const tvSymbol = SYMBOL_MAPPING[symbol] || symbol;\n    \n    // Convert timeframe to TradingView format\n    const tvTimeframe = convertTimeframe(timeframe);\n    \n    const response = await fetch(`https://api.tradingview.com/v1/history`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // For now, return simulated candle data based on current price\n    const currentPrice = await fetchTradingViewPrice(symbol);\n    return generateSimulatedCandles(currentPrice.price, limit);\n    \n  } catch (error) {\n    console.error(`Error fetching TradingView candles for ${symbol}:`, error);\n    \n    // Fallback to simulated data\n    const currentPrice = await fetchTradingViewPrice(symbol);\n    return generateSimulatedCandles(currentPrice.price, limit);\n  }\n}\n\n// Fetch technical indicators from TradingView\nexport async function fetchTradingViewIndicators(\n  symbol: string, \n  timeframe: string = '1H'\n): Promise<TradingViewIndicator[]> {\n  try {\n    const tvSymbol = SYMBOL_MAPPING[symbol] || symbol;\n    \n    const response = await fetch(`https://scanner.tradingview.com/symbol`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        symbols: {\n          tickers: [tvSymbol],\n          query: {\n            types: []\n          }\n        },\n        columns: [\n          'RSI', 'RSI[1]', 'Stoch.K', 'Stoch.D', 'MACD.macd', 'MACD.signal',\n          'ADX', 'Williams.R', 'CCI20', 'ATR', 'SMA20', 'EMA20', 'SMA50', 'EMA50',\n          'SMA200', 'EMA200', 'Ichimoku.BLine', 'VWAP', 'HullMA9'\n        ]\n      })\n    });\n\n    if (!response.ok) {\n      throw new Error(`TradingView Indicators API error: ${response.status}`);\n    }\n\n    const data = await response.json();\n    \n    if (data.data && data.data.length > 0) {\n      const indicatorData = data.data[0].d;\n      \n      return [\n        {\n          name: 'RSI',\n          value: indicatorData[0] || 50,\n          signal: getSignalFromRSI(indicatorData[0] || 50),\n          timeframe\n        },\n        {\n          name: 'Stochastic',\n          value: indicatorData[2] || 50,\n          signal: getSignalFromStochastic(indicatorData[2] || 50),\n          timeframe\n        },\n        {\n          name: 'MACD',\n          value: indicatorData[4] || 0,\n          signal: getSignalFromMACD(indicatorData[4] || 0, indicatorData[5] || 0),\n          timeframe\n        },\n        {\n          name: 'ADX',\n          value: indicatorData[6] || 25,\n          signal: getSignalFromADX(indicatorData[6] || 25),\n          timeframe\n        },\n        {\n          name: 'Williams %R',\n          value: indicatorData[7] || -50,\n          signal: getSignalFromWilliams(indicatorData[7] || -50),\n          timeframe\n        }\n      ];\n    }\n    \n    throw new Error('No indicator data received from TradingView');\n    \n  } catch (error) {\n    console.error(`Error fetching TradingView indicators for ${symbol}:`, error);\n    \n    // Fallback to simulated indicators\n    return generateFallbackIndicators(symbol, timeframe);\n  }\n}\n\n// Utility functions\nfunction convertTimeframe(timeframe: string): string {\n  const mapping: { [key: string]: string } = {\n    '1m': '1',\n    '5m': '5',\n    '15m': '15',\n    '30m': '30',\n    '1h': '60',\n    '4h': '240',\n    '1d': '1D',\n    '1w': '1W'\n  };\n  \n  return mapping[timeframe] || '60';\n}\n\nfunction getFallbackPrice(symbol: string): TradingViewPrice {\n  const basePrices: { [key: string]: number } = {\n    'EURUSD': 1.0850, 'GBPUSD': 1.2650, 'USDJPY': 149.50, 'USDCHF': 0.8920,\n    'AUDUSD': 0.6580, 'USDCAD': 1.3650, 'NZDUSD': 0.6120, 'EURGBP': 0.8580,\n    'EURJPY': 162.30, 'GBPJPY': 189.20, 'XAUUSD': 2050.00, 'XAGUSD': 24.50,\n    'USOIL': 78.50, 'BTCUSD': 43250.00\n  };\n  \n  const basePrice = basePrices[symbol] || 1.0000;\n  const volatility = Math.random() * 0.02 - 0.01; // ±1% random movement\n  const currentPrice = basePrice * (1 + volatility);\n  \n  return {\n    symbol,\n    price: currentPrice,\n    change: basePrice * volatility,\n    changePercent: volatility * 100,\n    high24h: currentPrice * 1.015,\n    low24h: currentPrice * 0.985,\n    volume: Math.random() * 1000000 + 500000,\n    timestamp: Date.now()\n  };\n}\n\nfunction generateSimulatedCandles(currentPrice: number, count: number): TradingViewCandle[] {\n  const candles: TradingViewCandle[] = [];\n  let price = currentPrice;\n  const now = Date.now();\n  \n  for (let i = count - 1; i >= 0; i--) {\n    const volatility = (Math.random() - 0.5) * 0.02; // ±1% movement\n    const open = price;\n    const close = price * (1 + volatility);\n    const high = Math.max(open, close) * (1 + Math.random() * 0.005);\n    const low = Math.min(open, close) * (1 - Math.random() * 0.005);\n    \n    candles.unshift({\n      time: now - (i * 60 * 60 * 1000), // 1 hour intervals\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 100000 + 50000\n    });\n    \n    price = close;\n  }\n  \n  return candles;\n}\n\nfunction generateFallbackIndicators(symbol: string, timeframe: string): TradingViewIndicator[] {\n  return [\n    {\n      name: 'RSI',\n      value: 30 + Math.random() * 40,\n      signal: Math.random() > 0.5 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'NEUTRAL',\n      timeframe\n    },\n    {\n      name: 'Stochastic',\n      value: Math.random() * 100,\n      signal: Math.random() > 0.5 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'NEUTRAL',\n      timeframe\n    },\n    {\n      name: 'MACD',\n      value: (Math.random() - 0.5) * 0.02,\n      signal: Math.random() > 0.5 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'NEUTRAL',\n      timeframe\n    },\n    {\n      name: 'ADX',\n      value: 20 + Math.random() * 60,\n      signal: Math.random() > 0.5 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'NEUTRAL',\n      timeframe\n    },\n    {\n      name: 'Williams %R',\n      value: -Math.random() * 100,\n      signal: Math.random() > 0.5 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'NEUTRAL',\n      timeframe\n    }\n  ];\n}\n\n// Signal interpretation functions\nfunction getSignalFromRSI(rsi: number): 'BUY' | 'SELL' | 'NEUTRAL' {\n  if (rsi < 30) return 'BUY';\n  if (rsi > 70) return 'SELL';\n  return 'NEUTRAL';\n}\n\nfunction getSignalFromStochastic(stoch: number): 'BUY' | 'SELL' | 'NEUTRAL' {\n  if (stoch < 20) return 'BUY';\n  if (stoch > 80) return 'SELL';\n  return 'NEUTRAL';\n}\n\nfunction getSignalFromMACD(macd: number, signal: number): 'BUY' | 'SELL' | 'NEUTRAL' {\n  if (macd > signal) return 'BUY';\n  if (macd < signal) return 'SELL';\n  return 'NEUTRAL';\n}\n\nfunction getSignalFromADX(adx: number): 'BUY' | 'SELL' | 'NEUTRAL' {\n  if (adx > 25) return 'BUY'; // Strong trend\n  return 'NEUTRAL';\n}\n\nfunction getSignalFromWilliams(williams: number): 'BUY' | 'SELL' | 'NEUTRAL' {\n  if (williams < -80) return 'BUY';\n  if (williams > -20) return 'SELL';\n  return 'NEUTRAL';\n}\n\n// Batch fetch multiple symbols\nexport async function fetchMultipleTradingViewPrices(symbols: string[]): Promise<TradingViewPrice[]> {\n  const promises = symbols.map(symbol => fetchTradingViewPrice(symbol));\n  return Promise.all(promises);\n}\n\n// Real-time price updates with WebSocket (for future implementation)\nexport class TradingViewWebSocket {\n  private ws: WebSocket | null = null;\n  private subscribers: Map<string, (price: TradingViewPrice) => void> = new Map();\n  \n  connect() {\n    // WebSocket implementation for real-time updates\n    // This would connect to TradingView's WebSocket API\n    console.log('TradingView WebSocket connection would be established here');\n  }\n  \n  subscribe(symbol: string, callback: (price: TradingViewPrice) => void) {\n    this.subscribers.set(symbol, callback);\n  }\n  \n  unsubscribe(symbol: string) {\n    this.subscribers.delete(symbol);\n  }\n  \n  disconnect() {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;AA6BvD,6BAA6B;AAC7B,MAAM,iBAA4C;IAChD,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;AACZ;AAGO,eAAe,sBAAsB,MAAc;IACxD,IAAI;QACF,MAAM,WAAW,cAAc,CAAC,OAAO,IAAI;QAE3C,0CAA0C;QAC1C,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;oBACP,SAAS;wBAAC;qBAAS;oBACnB,OAAO;wBACL,OAAO,EAAE;oBACX;gBACF;gBACA,SAAS;oBACP;oBAAQ;oBAAS;oBAAU;oBAAc;oBAAQ;oBAAO;oBACxD;oBAAoB;oBAAsB;iBAC3C;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;YACrC,MAAM,aAAa,KAAK,IAAI,CAAC,EAAE;YAE/B,OAAO;gBACL,QAAQ;gBACR,OAAO,WAAW,CAAC,CAAC,EAAE,IAAI;gBAC1B,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI;gBAC3B,eAAe,WAAW,CAAC,CAAC,EAAE,IAAI;gBAClC,SAAS,WAAW,CAAC,CAAC,EAAE,IAAI;gBAC5B,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI;gBAC3B,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI;gBAC3B,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,MAAM,IAAI,MAAM;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,EAAE;QAEjE,yCAAyC;QACzC,OAAO,iBAAiB;IAC1B;AACF;AAGO,eAAe,wBACpB,MAAc,EACd,YAAoB,IAAI,EACxB,QAAgB,GAAG;IAEnB,IAAI;QACF,MAAM,WAAW,cAAc,CAAC,OAAO,IAAI;QAE3C,0CAA0C;QAC1C,MAAM,cAAc,iBAAiB;QAErC,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,+DAA+D;QAC/D,MAAM,eAAe,MAAM,sBAAsB;QACjD,OAAO,yBAAyB,aAAa,KAAK,EAAE;IAEtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC,EAAE;QAEnE,6BAA6B;QAC7B,MAAM,eAAe,MAAM,sBAAsB;QACjD,OAAO,yBAAyB,aAAa,KAAK,EAAE;IACtD;AACF;AAGO,eAAe,2BACpB,MAAc,EACd,YAAoB,IAAI;IAExB,IAAI;QACF,MAAM,WAAW,cAAc,CAAC,OAAO,IAAI;QAE3C,MAAM,WAAW,MAAM,MAAM,CAAC,sCAAsC,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;oBACP,SAAS;wBAAC;qBAAS;oBACnB,OAAO;wBACL,OAAO,EAAE;oBACX;gBACF;gBACA,SAAS;oBACP;oBAAO;oBAAU;oBAAW;oBAAW;oBAAa;oBACpD;oBAAO;oBAAc;oBAAS;oBAAO;oBAAS;oBAAS;oBAAS;oBAChE;oBAAU;oBAAU;oBAAkB;oBAAQ;iBAC/C;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,MAAM,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;YACrC,MAAM,gBAAgB,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;YAEpC,OAAO;gBACL;oBACE,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE,IAAI;oBAC3B,QAAQ,iBAAiB,aAAa,CAAC,EAAE,IAAI;oBAC7C;gBACF;gBACA;oBACE,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE,IAAI;oBAC3B,QAAQ,wBAAwB,aAAa,CAAC,EAAE,IAAI;oBACpD;gBACF;gBACA;oBACE,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE,IAAI;oBAC3B,QAAQ,kBAAkB,aAAa,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC,EAAE,IAAI;oBACrE;gBACF;gBACA;oBACE,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE,IAAI;oBAC3B,QAAQ,iBAAiB,aAAa,CAAC,EAAE,IAAI;oBAC7C;gBACF;gBACA;oBACE,MAAM;oBACN,OAAO,aAAa,CAAC,EAAE,IAAI,CAAC;oBAC5B,QAAQ,sBAAsB,aAAa,CAAC,EAAE,IAAI,CAAC;oBACnD;gBACF;aACD;QACH;QAEA,MAAM,IAAI,MAAM;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,OAAO,CAAC,CAAC,EAAE;QAEtE,mCAAmC;QACnC,OAAO,2BAA2B,QAAQ;IAC5C;AACF;AAEA,oBAAoB;AACpB,SAAS,iBAAiB,SAAiB;IACzC,MAAM,UAAqC;QACzC,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,OAAO,OAAO,CAAC,UAAU,IAAI;AAC/B;AAEA,SAAS,iBAAiB,MAAc;IACtC,MAAM,aAAwC;QAC5C,UAAU;QAAQ,UAAU;QAAQ,UAAU;QAAQ,UAAU;QAChE,UAAU;QAAQ,UAAU;QAAQ,UAAU;QAAQ,UAAU;QAChE,UAAU;QAAQ,UAAU;QAAQ,UAAU;QAAS,UAAU;QACjE,SAAS;QAAO,UAAU;IAC5B;IAEA,MAAM,YAAY,UAAU,CAAC,OAAO,IAAI;IACxC,MAAM,aAAa,KAAK,MAAM,KAAK,OAAO,MAAM,sBAAsB;IACtE,MAAM,eAAe,YAAY,CAAC,IAAI,UAAU;IAEhD,OAAO;QACL;QACA,OAAO;QACP,QAAQ,YAAY;QACpB,eAAe,aAAa;QAC5B,SAAS,eAAe;QACxB,QAAQ,eAAe;QACvB,QAAQ,KAAK,MAAM,KAAK,UAAU;QAClC,WAAW,KAAK,GAAG;IACrB;AACF;AAEA,SAAS,yBAAyB,YAAoB,EAAE,KAAa;IACnE,MAAM,UAA+B,EAAE;IACvC,IAAI,QAAQ;IACZ,MAAM,MAAM,KAAK,GAAG;IAEpB,IAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAK;QACnC,MAAM,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,MAAM,eAAe;QAChE,MAAM,OAAO;QACb,MAAM,QAAQ,QAAQ,CAAC,IAAI,UAAU;QACrC,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/D,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK;QAE9D,QAAQ,OAAO,CAAC;YACd,MAAM,MAAO,IAAI,KAAK,KAAK;YAC3B;YACA;YACA;YACA;YACA,QAAQ,KAAK,MAAM,KAAK,SAAS;QACnC;QAEA,QAAQ;IACV;IAEA,OAAO;AACT;AAEA,SAAS,2BAA2B,MAAc,EAAE,SAAiB;IACnE,OAAO;QACL;YACE,MAAM;YACN,OAAO,KAAK,KAAK,MAAM,KAAK;YAC5B,QAAQ,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,SAAS;YACrE;QACF;QACA;YACE,MAAM;YACN,OAAO,KAAK,MAAM,KAAK;YACvB,QAAQ,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,SAAS;YACrE;QACF;QACA;YACE,MAAM;YACN,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC/B,QAAQ,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,SAAS;YACrE;QACF;QACA;YACE,MAAM;YACN,OAAO,KAAK,KAAK,MAAM,KAAK;YAC5B,QAAQ,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,SAAS;YACrE;QACF;QACA;YACE,MAAM;YACN,OAAO,CAAC,KAAK,MAAM,KAAK;YACxB,QAAQ,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,SAAS;YACrE;QACF;KACD;AACH;AAEA,kCAAkC;AAClC,SAAS,iBAAiB,GAAW;IACnC,IAAI,MAAM,IAAI,OAAO;IACrB,IAAI,MAAM,IAAI,OAAO;IACrB,OAAO;AACT;AAEA,SAAS,wBAAwB,KAAa;IAC5C,IAAI,QAAQ,IAAI,OAAO;IACvB,IAAI,QAAQ,IAAI,OAAO;IACvB,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAY,EAAE,MAAc;IACrD,IAAI,OAAO,QAAQ,OAAO;IAC1B,IAAI,OAAO,QAAQ,OAAO;IAC1B,OAAO;AACT;AAEA,SAAS,iBAAiB,GAAW;IACnC,IAAI,MAAM,IAAI,OAAO,OAAO,eAAe;IAC3C,OAAO;AACT;AAEA,SAAS,sBAAsB,QAAgB;IAC7C,IAAI,WAAW,CAAC,IAAI,OAAO;IAC3B,IAAI,WAAW,CAAC,IAAI,OAAO;IAC3B,OAAO;AACT;AAGO,eAAe,+BAA+B,OAAiB;IACpE,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU,sBAAsB;IAC7D,OAAO,QAAQ,GAAG,CAAC;AACrB;AAGO,MAAM;IACH,KAAuB,KAAK;IAC5B,cAA8D,IAAI,MAAM;IAEhF,UAAU;QACR,iDAAiD;QACjD,oDAAoD;QACpD,QAAQ,GAAG,CAAC;IACd;IAEA,UAAU,MAAc,EAAE,QAA2C,EAAE;QACrE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ;IAC/B;IAEA,YAAY,MAAc,EAAE;QAC1B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAC1B;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF", "debugId": null}}]}