// TradingView Webhook Integration
import { NextRequest, NextResponse } from 'next/server';
import { TradeSignal } from '../types/trading';

export interface TradingViewWebhookData {
  symbol: string;
  action: 'BUY' | 'SELL' | 'CLOSE';
  price: number;
  time: string;
  strategy: string;
  message?: string;
  // TradingView specific fields
  exchange?: string;
  ticker?: string;
  interval?: string;
  volume?: number;
  // Custom fields for our bot
  confidence?: number;
  stopLoss?: number;
  takeProfit1?: number;
  takeProfit2?: number;
  takeProfit3?: number;
  riskReward?: number;
}

export interface WebhookConfig {
  enabled: boolean;
  secretKey: string;
  allowedIPs: string[];
  maxRequestsPerMinute: number;
}

export class TradingViewWebhookHandler {
  private config: WebhookConfig;
  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(config: WebhookConfig) {
    this.config = config;
  }

  // Main webhook handler
  async handleWebhook(request: NextRequest): Promise<NextResponse> {
    try {
      // Security checks
      if (!this.config.enabled) {
        return NextResponse.json({ error: 'Webhook disabled' }, { status: 403 });
      }

      // Rate limiting
      const clientIP = this.getClientIP(request);
      if (!this.checkRateLimit(clientIP)) {
        return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
      }

      // IP whitelist check
      if (this.config.allowedIPs.length > 0 && !this.config.allowedIPs.includes(clientIP)) {
        return NextResponse.json({ error: 'IP not allowed' }, { status: 403 });
      }

      // Verify secret key
      const authHeader = request.headers.get('authorization');
      const providedKey = authHeader?.replace('Bearer ', '');
      
      if (providedKey !== this.config.secretKey) {
        return NextResponse.json({ error: 'Invalid secret key' }, { status: 401 });
      }

      // Parse webhook data
      const webhookData: TradingViewWebhookData = await request.json();
      
      // Validate required fields
      if (!this.validateWebhookData(webhookData)) {
        return NextResponse.json({ error: 'Invalid webhook data' }, { status: 400 });
      }

      // Process the webhook
      const result = await this.processWebhookData(webhookData);
      
      return NextResponse.json({ 
        success: true, 
        message: 'Webhook processed successfully',
        signalId: result.signalId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Webhook processing error:', error);
      return NextResponse.json({ 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
  }

  // Process webhook data and generate signal
  private async processWebhookData(data: TradingViewWebhookData): Promise<{ signalId: string }> {
    console.log('📡 Processing TradingView webhook:', data);

    // Convert webhook data to our signal format
    const signal: TradeSignal = {
      id: this.generateSignalId(),
      symbol: data.symbol,
      type: data.action,
      entry: data.price,
      stopLoss: data.stopLoss || this.calculateStopLoss(data),
      takeProfit1: data.takeProfit1 || this.calculateTakeProfit(data, 1),
      takeProfit2: data.takeProfit2 || this.calculateTakeProfit(data, 2),
      takeProfit3: data.takeProfit3 || this.calculateTakeProfit(data, 3),
      riskRewardRatio: data.riskReward || this.calculateRiskReward(data),
      confidence: data.confidence || 75, // Default confidence
      timestamp: Date.now(),
      reasoning: [
        `TradingView ${data.strategy} strategy signal`,
        `Price action at ${data.price}`,
        `${data.interval || '1H'} timeframe analysis`,
        data.message || 'Automated signal from TradingView'
      ],
      indicators: {} as any // Will be populated by technical analysis
    };

    // Emit signal to the system
    this.emitSignal(signal);

    return { signalId: signal.id };
  }

  // Validate webhook data
  private validateWebhookData(data: TradingViewWebhookData): boolean {
    if (!data.symbol || !data.action || !data.price) {
      console.error('❌ Missing required webhook fields');
      return false;
    }

    if (!['BUY', 'SELL', 'CLOSE'].includes(data.action)) {
      console.error('❌ Invalid action:', data.action);
      return false;
    }

    if (typeof data.price !== 'number' || data.price <= 0) {
      console.error('❌ Invalid price:', data.price);
      return false;
    }

    return true;
  }

  // Calculate stop loss if not provided
  private calculateStopLoss(data: TradingViewWebhookData): number {
    const atrMultiplier = 1.5;
    const estimatedATR = data.price * 0.01; // 1% as rough ATR estimate
    
    if (data.action === 'BUY') {
      return data.price - (estimatedATR * atrMultiplier);
    } else {
      return data.price + (estimatedATR * atrMultiplier);
    }
  }

  // Calculate take profit levels
  private calculateTakeProfit(data: TradingViewWebhookData, level: number): number {
    const stopLoss = data.stopLoss || this.calculateStopLoss(data);
    const riskDistance = Math.abs(data.price - stopLoss);
    
    const multipliers = [1.5, 2.5, 4.0]; // Different R:R ratios for TP levels
    const multiplier = multipliers[level - 1] || 1.5;
    
    if (data.action === 'BUY') {
      return data.price + (riskDistance * multiplier);
    } else {
      return data.price - (riskDistance * multiplier);
    }
  }

  // Calculate risk/reward ratio
  private calculateRiskReward(data: TradingViewWebhookData): number {
    const stopLoss = data.stopLoss || this.calculateStopLoss(data);
    const takeProfit = data.takeProfit1 || this.calculateTakeProfit(data, 1);
    
    const risk = Math.abs(data.price - stopLoss);
    const reward = Math.abs(takeProfit - data.price);
    
    return reward / risk;
  }

  // Rate limiting
  private checkRateLimit(ip: string): boolean {
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute
    
    const record = this.requestCounts.get(ip);
    
    if (!record || now > record.resetTime) {
      this.requestCounts.set(ip, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (record.count >= this.config.maxRequestsPerMinute) {
      return false;
    }
    
    record.count++;
    return true;
  }

  // Get client IP
  private getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    return 'unknown';
  }

  // Generate unique signal ID
  private generateSignalId(): string {
    return `tv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Emit signal to the system
  private emitSignal(signal: TradeSignal): void {
    // In a real implementation, this would emit to the main trading system
    console.log('📡 Emitting TradingView signal:', signal);
    
    // You can integrate this with your existing signal processing system
    // For example: this.tradingSystem.processSignal(signal);
  }

  // Generate webhook URL for TradingView
  static generateWebhookURL(baseUrl: string, secretKey: string): string {
    return `${baseUrl}/api/webhook/tradingview?key=${secretKey}`;
  }

  // Generate TradingView alert message template
  static generateAlertTemplate(): string {
    return `{
  "symbol": "{{ticker}}",
  "action": "{{strategy.order.action}}",
  "price": {{close}},
  "time": "{{time}}",
  "strategy": "{{strategy.order.comment}}",
  "exchange": "{{exchange}}",
  "interval": "{{interval}}",
  "volume": {{volume}},
  "message": "Signal generated at {{time}} for {{ticker}} at price {{close}}"
}`;
  }

  // Webhook setup instructions
  static getSetupInstructions(webhookUrl: string): string {
    return `
🔗 TradingView Webhook Setup Instructions:

1. Open your TradingView chart
2. Create or edit your trading strategy/indicator
3. Add an alert with these settings:
   
   📡 Webhook URL: ${webhookUrl}
   
   📝 Message (JSON format):
   ${this.generateAlertTemplate()}
   
   🔐 Headers:
   Authorization: Bearer YOUR_SECRET_KEY
   Content-Type: application/json

4. Test the webhook by triggering an alert

⚠️ Security Notes:
• Keep your secret key private
• Use HTTPS only
• Whitelist TradingView IPs if needed
• Monitor webhook logs for suspicious activity

📊 Supported Actions:
• BUY - Open long position
• SELL - Open short position  
• CLOSE - Close position

🎯 The bot will automatically:
• Calculate stop loss and take profit levels
• Apply risk management rules
• Send notifications via Telegram/Email
• Log all signals for analysis
    `;
  }
}
