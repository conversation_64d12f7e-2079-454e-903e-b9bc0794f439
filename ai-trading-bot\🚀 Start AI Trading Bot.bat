@echo off
title AI Trading Bot - Professional Trading System
color 0A

echo.
echo ========================================
echo    🤖 AI Trading Bot Starting...
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Node.js is not installed!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: npm is not available!
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js detected
echo ✅ npm detected
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    echo This may take a few minutes...
    echo.
    npm install
    if errorlevel 1 (
        echo.
        echo ❌ Failed to install dependencies!
        pause
        exit /b 1
    )
    echo.
    echo ✅ Dependencies installed successfully!
    echo.
)

echo 🚀 Starting AI Trading Bot...
echo.
echo ⏳ Please wait while the server starts...
echo.
echo 🌐 The application will open at: http://localhost:3000
echo.
echo 💡 To stop the server, press Ctrl+C
echo.
echo ========================================

REM Start the development server
npm run dev

echo.
echo 👋 AI Trading Bot has stopped.
echo.
pause
