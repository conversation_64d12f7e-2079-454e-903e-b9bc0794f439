@echo off
cd /d "%~dp0"
title AI Trading Bot
cls

echo ========================================
echo    AI Trading Bot Starting...
echo ========================================
echo.

echo Checking requirements...
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js not found! Please install from https://nodejs.org/
    pause
    exit
)

echo Node.js found!

if not exist "node_modules" (
    echo.
    echo Installing dependencies...
    echo Please wait...
    npm install
    if errorlevel 1 (
        echo Installation failed!
        pause
        exit
    )
)

echo.
echo Starting server...
echo.
echo Open your browser to: http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

echo.
echo Server stopped.
pause
