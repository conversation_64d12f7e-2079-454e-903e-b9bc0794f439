{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/technical-analysis/indicators.ts"], "sourcesContent": ["// Technical Analysis Indicators Library\nimport { \n  EMA, \n  RSI, \n  MACD, \n  BollingerBands,\n  SMA,\n  StochasticOscillator,\n  ATR,\n  ADX\n} from 'technicalindicators';\n\nexport interface CandleData {\n  open: number;\n  high: number;\n  low: number;\n  close: number;\n  volume: number;\n  timestamp: number;\n}\n\nexport interface TechnicalIndicators {\n  ema: number[];\n  rsi: number;\n  macd: {\n    MACD: number;\n    signal: number;\n    histogram: number;\n  };\n  vwap: number;\n  supertrend: {\n    value: number;\n    trend: 'up' | 'down';\n  };\n  volumeProfile: VolumeProfileData;\n  fvg: FairValueGap[];\n  choch: ChangeOfCharacter[];\n  bos: BreakOfStructure[];\n  supportResistance: SupportResistanceLevel[];\n  orderBlocks: OrderBlock[];\n  breakerBlocks: BreakerBlock[];\n}\n\nexport interface VolumeProfileData {\n  levels: {\n    price: number;\n    volume: number;\n    percentage: number;\n  }[];\n  poc: number; // Point of Control\n  vah: number; // Value Area High\n  val: number; // Value Area Low\n}\n\nexport interface FairValueGap {\n  start: number;\n  end: number;\n  price: number;\n  type: 'bullish' | 'bearish';\n  filled: boolean;\n}\n\nexport interface ChangeOfCharacter {\n  timestamp: number;\n  price: number;\n  type: 'bullish' | 'bearish';\n  strength: number;\n}\n\nexport interface BreakOfStructure {\n  timestamp: number;\n  price: number;\n  type: 'bullish' | 'bearish';\n  previousHigh: number;\n  previousLow: number;\n}\n\nexport interface SupportResistanceLevel {\n  price: number;\n  strength: number;\n  type: 'support' | 'resistance';\n  touches: number;\n  lastTouch: number;\n}\n\nexport interface OrderBlock {\n  start: number;\n  end: number;\n  high: number;\n  low: number;\n  type: 'bullish' | 'bearish';\n  mitigation: boolean;\n}\n\nexport interface BreakerBlock {\n  start: number;\n  end: number;\n  high: number;\n  low: number;\n  type: 'bullish' | 'bearish';\n  broken: boolean;\n}\n\nexport class TechnicalAnalysisEngine {\n  private data: CandleData[] = [];\n  \n  constructor(data: CandleData[]) {\n    this.data = data;\n  }\n\n  // Calculate EMA (Exponential Moving Average)\n  calculateEMA(period: number = 20): number[] {\n    const closes = this.data.map(d => d.close);\n    return EMA.calculate({ period, values: closes });\n  }\n\n  // Calculate RSI (Relative Strength Index)\n  calculateRSI(period: number = 14): number {\n    const closes = this.data.map(d => d.close);\n    const rsiValues = RSI.calculate({ period, values: closes });\n    return rsiValues[rsiValues.length - 1] || 0;\n  }\n\n  // Calculate MACD\n  calculateMACD(fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {\n    const closes = this.data.map(d => d.close);\n    const macdValues = MACD.calculate({\n      fastPeriod,\n      slowPeriod,\n      signalPeriod,\n      values: closes\n    });\n    \n    const latest = macdValues[macdValues.length - 1];\n    return {\n      MACD: latest?.MACD || 0,\n      signal: latest?.signal || 0,\n      histogram: latest?.histogram || 0\n    };\n  }\n\n  // Calculate VWAP (Volume Weighted Average Price)\n  calculateVWAP(): number {\n    let totalVolume = 0;\n    let totalVolumePrice = 0;\n\n    for (const candle of this.data) {\n      const typicalPrice = (candle.high + candle.low + candle.close) / 3;\n      totalVolumePrice += typicalPrice * candle.volume;\n      totalVolume += candle.volume;\n    }\n\n    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;\n  }\n\n  // Calculate Supertrend\n  calculateSupertrend(period: number = 10, multiplier: number = 3) {\n    const atrValues = this.calculateATR(period);\n    const closes = this.data.map(d => d.close);\n    const highs = this.data.map(d => d.high);\n    const lows = this.data.map(d => d.low);\n    \n    let trend = 'up';\n    let supertrend = 0;\n    \n    if (atrValues.length > 0 && this.data.length > 0) {\n      const hl2 = (highs[highs.length - 1] + lows[lows.length - 1]) / 2;\n      const atr = atrValues[atrValues.length - 1];\n      \n      const upperBand = hl2 + (multiplier * atr);\n      const lowerBand = hl2 - (multiplier * atr);\n      const close = closes[closes.length - 1];\n      \n      if (close > upperBand) {\n        trend = 'up';\n        supertrend = lowerBand;\n      } else if (close < lowerBand) {\n        trend = 'down';\n        supertrend = upperBand;\n      }\n    }\n    \n    return {\n      value: supertrend,\n      trend: trend as 'up' | 'down'\n    };\n  }\n\n  // Calculate ATR (Average True Range)\n  private calculateATR(period: number = 14): number[] {\n    const input = {\n      high: this.data.map(d => d.high),\n      low: this.data.map(d => d.low),\n      close: this.data.map(d => d.close),\n      period\n    };\n    \n    return ATR.calculate(input);\n  }\n\n  // Calculate Volume Profile\n  calculateVolumeProfile(bins: number = 50): VolumeProfileData {\n    if (this.data.length === 0) {\n      return {\n        levels: [],\n        poc: 0,\n        vah: 0,\n        val: 0\n      };\n    }\n\n    const minPrice = Math.min(...this.data.map(d => d.low));\n    const maxPrice = Math.max(...this.data.map(d => d.high));\n    const priceStep = (maxPrice - minPrice) / bins;\n    \n    const volumeLevels: { price: number; volume: number }[] = [];\n    \n    for (let i = 0; i < bins; i++) {\n      const priceLevel = minPrice + (i * priceStep);\n      let volumeAtLevel = 0;\n      \n      for (const candle of this.data) {\n        if (candle.low <= priceLevel && candle.high >= priceLevel) {\n          volumeAtLevel += candle.volume;\n        }\n      }\n      \n      volumeLevels.push({ price: priceLevel, volume: volumeAtLevel });\n    }\n    \n    const totalVolume = volumeLevels.reduce((sum, level) => sum + level.volume, 0);\n    const levels = volumeLevels.map(level => ({\n      ...level,\n      percentage: totalVolume > 0 ? (level.volume / totalVolume) * 100 : 0\n    }));\n    \n    // Find Point of Control (highest volume level)\n    const poc = levels.reduce((max, level) => \n      level.volume > max.volume ? level : max\n    ).price;\n    \n    // Calculate Value Area (70% of volume)\n    const sortedByVolume = [...levels].sort((a, b) => b.volume - a.volume);\n    let valueAreaVolume = 0;\n    const valueAreaLevels = [];\n    \n    for (const level of sortedByVolume) {\n      valueAreaLevels.push(level);\n      valueAreaVolume += level.volume;\n      if (valueAreaVolume >= totalVolume * 0.7) break;\n    }\n    \n    const valueAreaPrices = valueAreaLevels.map(l => l.price);\n    const vah = Math.max(...valueAreaPrices);\n    const val = Math.min(...valueAreaPrices);\n    \n    return {\n      levels,\n      poc,\n      vah,\n      val\n    };\n  }\n\n  // Detect Fair Value Gaps\n  detectFairValueGaps(): FairValueGap[] {\n    const gaps: FairValueGap[] = [];\n    \n    for (let i = 2; i < this.data.length; i++) {\n      const prev = this.data[i - 2];\n      const current = this.data[i - 1];\n      const next = this.data[i];\n      \n      // Bullish FVG: previous high < next low\n      if (prev.high < next.low) {\n        gaps.push({\n          start: i - 2,\n          end: i,\n          price: (prev.high + next.low) / 2,\n          type: 'bullish',\n          filled: false\n        });\n      }\n      \n      // Bearish FVG: previous low > next high\n      if (prev.low > next.high) {\n        gaps.push({\n          start: i - 2,\n          end: i,\n          price: (prev.low + next.high) / 2,\n          type: 'bearish',\n          filled: false\n        });\n      }\n    }\n    \n    return gaps;\n  }\n\n  // Detect Change of Character (CHoCH)\n  detectChangeOfCharacter(): ChangeOfCharacter[] {\n    const chochs: ChangeOfCharacter[] = [];\n    let currentTrend: 'bullish' | 'bearish' | null = null;\n    \n    for (let i = 20; i < this.data.length; i++) {\n      const recentData = this.data.slice(i - 20, i);\n      const highs = recentData.map(d => d.high);\n      const lows = recentData.map(d => d.low);\n      \n      const recentHigh = Math.max(...highs);\n      const recentLow = Math.min(...lows);\n      const current = this.data[i];\n      \n      // Detect trend change\n      if (currentTrend === 'bullish' && current.close < recentLow) {\n        chochs.push({\n          timestamp: current.timestamp,\n          price: current.close,\n          type: 'bearish',\n          strength: Math.abs(current.close - recentHigh) / recentHigh\n        });\n        currentTrend = 'bearish';\n      } else if (currentTrend === 'bearish' && current.close > recentHigh) {\n        chochs.push({\n          timestamp: current.timestamp,\n          price: current.close,\n          type: 'bullish',\n          strength: Math.abs(current.close - recentLow) / recentLow\n        });\n        currentTrend = 'bullish';\n      } else if (currentTrend === null) {\n        currentTrend = current.close > (recentHigh + recentLow) / 2 ? 'bullish' : 'bearish';\n      }\n    }\n    \n    return chochs;\n  }\n\n  // Detect Break of Structure (BOS)\n  detectBreakOfStructure(): BreakOfStructure[] {\n    const bosEvents: BreakOfStructure[] = [];\n    \n    for (let i = 50; i < this.data.length; i++) {\n      const lookback = this.data.slice(i - 50, i);\n      const current = this.data[i];\n      \n      const previousHigh = Math.max(...lookback.map(d => d.high));\n      const previousLow = Math.min(...lookback.map(d => d.low));\n      \n      // Bullish BOS: break above previous high\n      if (current.high > previousHigh) {\n        bosEvents.push({\n          timestamp: current.timestamp,\n          price: current.high,\n          type: 'bullish',\n          previousHigh,\n          previousLow\n        });\n      }\n      \n      // Bearish BOS: break below previous low\n      if (current.low < previousLow) {\n        bosEvents.push({\n          timestamp: current.timestamp,\n          price: current.low,\n          type: 'bearish',\n          previousHigh,\n          previousLow\n        });\n      }\n    }\n    \n    return bosEvents;\n  }\n\n  // Calculate all indicators\n  calculateAllIndicators(): TechnicalIndicators {\n    return {\n      ema: this.calculateEMA(),\n      rsi: this.calculateRSI(),\n      macd: this.calculateMACD(),\n      vwap: this.calculateVWAP(),\n      supertrend: this.calculateSupertrend(),\n      volumeProfile: this.calculateVolumeProfile(),\n      fvg: this.detectFairValueGaps(),\n      choch: this.detectChangeOfCharacter(),\n      bos: this.detectBreakOfStructure(),\n      supportResistance: this.detectSupportResistance(),\n      orderBlocks: this.detectOrderBlocks(),\n      breakerBlocks: this.detectBreakerBlocks()\n    };\n  }\n\n  // Detect Support and Resistance levels\n  private detectSupportResistance(): SupportResistanceLevel[] {\n    const levels: SupportResistanceLevel[] = [];\n    const tolerance = 0.001; // 0.1% tolerance\n    \n    for (let i = 2; i < this.data.length - 2; i++) {\n      const current = this.data[i];\n      const prev2 = this.data[i - 2];\n      const prev1 = this.data[i - 1];\n      const next1 = this.data[i + 1];\n      const next2 = this.data[i + 2];\n      \n      // Resistance: local high\n      if (current.high > prev2.high && current.high > prev1.high && \n          current.high > next1.high && current.high > next2.high) {\n        levels.push({\n          price: current.high,\n          strength: this.calculateLevelStrength(current.high, 'resistance'),\n          type: 'resistance',\n          touches: 1,\n          lastTouch: current.timestamp\n        });\n      }\n      \n      // Support: local low\n      if (current.low < prev2.low && current.low < prev1.low && \n          current.low < next1.low && current.low < next2.low) {\n        levels.push({\n          price: current.low,\n          strength: this.calculateLevelStrength(current.low, 'support'),\n          type: 'support',\n          touches: 1,\n          lastTouch: current.timestamp\n        });\n      }\n    }\n    \n    return this.consolidateLevels(levels, tolerance);\n  }\n\n  private calculateLevelStrength(price: number, type: 'support' | 'resistance'): number {\n    let touches = 0;\n    const tolerance = price * 0.001; // 0.1% tolerance\n    \n    for (const candle of this.data) {\n      if (type === 'resistance' && Math.abs(candle.high - price) <= tolerance) {\n        touches++;\n      } else if (type === 'support' && Math.abs(candle.low - price) <= tolerance) {\n        touches++;\n      }\n    }\n    \n    return Math.min(touches / 5, 1); // Normalize to 0-1\n  }\n\n  private consolidateLevels(levels: SupportResistanceLevel[], tolerance: number): SupportResistanceLevel[] {\n    const consolidated: SupportResistanceLevel[] = [];\n    \n    for (const level of levels) {\n      const existing = consolidated.find(l => \n        Math.abs(l.price - level.price) / level.price <= tolerance && l.type === level.type\n      );\n      \n      if (existing) {\n        existing.touches += level.touches;\n        existing.strength = Math.max(existing.strength, level.strength);\n        existing.lastTouch = Math.max(existing.lastTouch, level.lastTouch);\n      } else {\n        consolidated.push(level);\n      }\n    }\n    \n    return consolidated.sort((a, b) => b.strength - a.strength);\n  }\n\n  // Detect Order Blocks\n  private detectOrderBlocks(): OrderBlock[] {\n    const orderBlocks: OrderBlock[] = [];\n    \n    for (let i = 10; i < this.data.length - 5; i++) {\n      const current = this.data[i];\n      const next5 = this.data.slice(i + 1, i + 6);\n      \n      // Bullish Order Block: strong buying after consolidation\n      const strongBullishMove = next5.some(candle => \n        (candle.close - current.close) / current.close > 0.02 // 2% move\n      );\n      \n      if (strongBullishMove) {\n        orderBlocks.push({\n          start: i,\n          end: i + 5,\n          high: current.high,\n          low: current.low,\n          type: 'bullish',\n          mitigation: false\n        });\n      }\n      \n      // Bearish Order Block: strong selling after consolidation\n      const strongBearishMove = next5.some(candle => \n        (current.close - candle.close) / current.close > 0.02 // 2% move\n      );\n      \n      if (strongBearishMove) {\n        orderBlocks.push({\n          start: i,\n          end: i + 5,\n          high: current.high,\n          low: current.low,\n          type: 'bearish',\n          mitigation: false\n        });\n      }\n    }\n    \n    return orderBlocks;\n  }\n\n  // Detect Breaker Blocks\n  private detectBreakerBlocks(): BreakerBlock[] {\n    const breakerBlocks: BreakerBlock[] = [];\n    const orderBlocks = this.detectOrderBlocks();\n    \n    for (const orderBlock of orderBlocks) {\n      // Check if order block was broken and then retested\n      for (let i = orderBlock.end + 1; i < this.data.length; i++) {\n        const candle = this.data[i];\n        \n        // Check if order block was broken\n        const wasBroken = orderBlock.type === 'bullish' \n          ? candle.low < orderBlock.low\n          : candle.high > orderBlock.high;\n        \n        if (wasBroken) {\n          breakerBlocks.push({\n            start: orderBlock.start,\n            end: orderBlock.end,\n            high: orderBlock.high,\n            low: orderBlock.low,\n            type: orderBlock.type === 'bullish' ? 'bearish' : 'bullish', // Inverse type\n            broken: true\n          });\n          break;\n        }\n      }\n    }\n    \n    return breakerBlocks;\n  }\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;AAuGjC,MAAM;IAOX,6CAA6C;IAC7C,eAA4C;YAA/B,SAAA,iEAAiB;QAC5B,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACzC,OAAO,IAAI,SAAS,CAAC;YAAE;YAAQ,QAAQ;QAAO;IAChD;IAEA,0CAA0C;IAC1C,eAA0C;YAA7B,SAAA,iEAAiB;QAC5B,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACzC,MAAM,YAAY,IAAI,SAAS,CAAC;YAAE;YAAQ,QAAQ;QAAO;QACzD,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,IAAI;IAC5C;IAEA,iBAAiB;IACjB,gBAA0F;YAA5E,aAAA,iEAAqB,IAAI,aAAA,iEAAqB,IAAI,eAAA,iEAAuB;QACrF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACzC,MAAM,aAAa,KAAK,SAAS,CAAC;YAChC;YACA;YACA;YACA,QAAQ;QACV;QAEA,MAAM,SAAS,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QAChD,OAAO;YACL,MAAM,CAAA,mBAAA,6BAAA,OAAQ,IAAI,KAAI;YACtB,QAAQ,CAAA,mBAAA,6BAAA,OAAQ,MAAM,KAAI;YAC1B,WAAW,CAAA,mBAAA,6BAAA,OAAQ,SAAS,KAAI;QAClC;IACF;IAEA,iDAAiD;IACjD,gBAAwB;QACtB,IAAI,cAAc;QAClB,IAAI,mBAAmB;QAEvB,KAAK,MAAM,UAAU,IAAI,CAAC,IAAI,CAAE;YAC9B,MAAM,eAAe,CAAC,OAAO,IAAI,GAAG,OAAO,GAAG,GAAG,OAAO,KAAK,IAAI;YACjE,oBAAoB,eAAe,OAAO,MAAM;YAChD,eAAe,OAAO,MAAM;QAC9B;QAEA,OAAO,cAAc,IAAI,mBAAmB,cAAc;IAC5D;IAEA,uBAAuB;IACvB,sBAAiE;YAA7C,SAAA,iEAAiB,IAAI,aAAA,iEAAqB;QAC5D,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;QACpC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACzC,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAErC,IAAI,QAAQ;QACZ,IAAI,aAAa;QAEjB,IAAI,UAAU,MAAM,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YAChD,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI;YAChE,MAAM,MAAM,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;YAE3C,MAAM,YAAY,MAAO,aAAa;YACtC,MAAM,YAAY,MAAO,aAAa;YACtC,MAAM,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YAEvC,IAAI,QAAQ,WAAW;gBACrB,QAAQ;gBACR,aAAa;YACf,OAAO,IAAI,QAAQ,WAAW;gBAC5B,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,qCAAqC;IAC7B,eAA4C;YAA/B,SAAA,iEAAiB;QACpC,MAAM,QAAQ;YACZ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAC/B,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;YAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;YACjC;QACF;QAEA,OAAO,IAAI,SAAS,CAAC;IACvB;IAEA,2BAA2B;IAC3B,yBAA6D;YAAtC,OAAA,iEAAe;QACpC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG;YAC1B,OAAO;gBACL,QAAQ,EAAE;gBACV,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;QACF;QAEA,MAAM,WAAW,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACrD,MAAM,WAAW,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACtD,MAAM,YAAY,CAAC,WAAW,QAAQ,IAAI;QAE1C,MAAM,eAAoD,EAAE;QAE5D,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,MAAM,aAAa,WAAY,IAAI;YACnC,IAAI,gBAAgB;YAEpB,KAAK,MAAM,UAAU,IAAI,CAAC,IAAI,CAAE;gBAC9B,IAAI,OAAO,GAAG,IAAI,cAAc,OAAO,IAAI,IAAI,YAAY;oBACzD,iBAAiB,OAAO,MAAM;gBAChC;YACF;YAEA,aAAa,IAAI,CAAC;gBAAE,OAAO;gBAAY,QAAQ;YAAc;QAC/D;QAEA,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,MAAM,EAAE;QAC5E,MAAM,SAAS,aAAa,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,GAAG,KAAK;gBACR,YAAY,cAAc,IAAI,AAAC,MAAM,MAAM,GAAG,cAAe,MAAM;YACrE,CAAC;QAED,+CAA+C;QAC/C,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,KAAK,QAC9B,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,QAAQ,KACpC,KAAK;QAEP,uCAAuC;QACvC,MAAM,iBAAiB;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;QACrE,IAAI,kBAAkB;QACtB,MAAM,kBAAkB,EAAE;QAE1B,KAAK,MAAM,SAAS,eAAgB;YAClC,gBAAgB,IAAI,CAAC;YACrB,mBAAmB,MAAM,MAAM;YAC/B,IAAI,mBAAmB,cAAc,KAAK;QAC5C;QAEA,MAAM,kBAAkB,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACxD,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,MAAM,MAAM,KAAK,GAAG,IAAI;QAExB,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,yBAAyB;IACzB,sBAAsC;QACpC,MAAM,OAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YACzC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC7B,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;YAEzB,wCAAwC;YACxC,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,EAAE;gBACxB,KAAK,IAAI,CAAC;oBACR,OAAO,IAAI;oBACX,KAAK;oBACL,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;oBAChC,MAAM;oBACN,QAAQ;gBACV;YACF;YAEA,wCAAwC;YACxC,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,EAAE;gBACxB,KAAK,IAAI,CAAC;oBACR,OAAO,IAAI;oBACX,KAAK;oBACL,OAAO,CAAC,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI;oBAChC,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;QAEA,OAAO;IACT;IAEA,qCAAqC;IACrC,0BAA+C;QAC7C,MAAM,SAA8B,EAAE;QACtC,IAAI,eAA6C;QAEjD,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YAC1C,MAAM,aAAa,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI;YAC3C,MAAM,QAAQ,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YACxC,MAAM,OAAO,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;YAEtC,MAAM,aAAa,KAAK,GAAG,IAAI;YAC/B,MAAM,YAAY,KAAK,GAAG,IAAI;YAC9B,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE;YAE5B,sBAAsB;YACtB,IAAI,iBAAiB,aAAa,QAAQ,KAAK,GAAG,WAAW;gBAC3D,OAAO,IAAI,CAAC;oBACV,WAAW,QAAQ,SAAS;oBAC5B,OAAO,QAAQ,KAAK;oBACpB,MAAM;oBACN,UAAU,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,cAAc;gBACnD;gBACA,eAAe;YACjB,OAAO,IAAI,iBAAiB,aAAa,QAAQ,KAAK,GAAG,YAAY;gBACnE,OAAO,IAAI,CAAC;oBACV,WAAW,QAAQ,SAAS;oBAC5B,OAAO,QAAQ,KAAK;oBACpB,MAAM;oBACN,UAAU,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,aAAa;gBAClD;gBACA,eAAe;YACjB,OAAO,IAAI,iBAAiB,MAAM;gBAChC,eAAe,QAAQ,KAAK,GAAG,CAAC,aAAa,SAAS,IAAI,IAAI,YAAY;YAC5E;QACF;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,yBAA6C;QAC3C,MAAM,YAAgC,EAAE;QAExC,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YAC1C,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI;YACzC,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE;YAE5B,MAAM,eAAe,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YACzD,MAAM,cAAc,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;YAEvD,yCAAyC;YACzC,IAAI,QAAQ,IAAI,GAAG,cAAc;gBAC/B,UAAU,IAAI,CAAC;oBACb,WAAW,QAAQ,SAAS;oBAC5B,OAAO,QAAQ,IAAI;oBACnB,MAAM;oBACN;oBACA;gBACF;YACF;YAEA,wCAAwC;YACxC,IAAI,QAAQ,GAAG,GAAG,aAAa;gBAC7B,UAAU,IAAI,CAAC;oBACb,WAAW,QAAQ,SAAS;oBAC5B,OAAO,QAAQ,GAAG;oBAClB,MAAM;oBACN;oBACA;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,2BAA2B;IAC3B,yBAA8C;QAC5C,OAAO;YACL,KAAK,IAAI,CAAC,YAAY;YACtB,KAAK,IAAI,CAAC,YAAY;YACtB,MAAM,IAAI,CAAC,aAAa;YACxB,MAAM,IAAI,CAAC,aAAa;YACxB,YAAY,IAAI,CAAC,mBAAmB;YACpC,eAAe,IAAI,CAAC,sBAAsB;YAC1C,KAAK,IAAI,CAAC,mBAAmB;YAC7B,OAAO,IAAI,CAAC,uBAAuB;YACnC,KAAK,IAAI,CAAC,sBAAsB;YAChC,mBAAmB,IAAI,CAAC,uBAAuB;YAC/C,aAAa,IAAI,CAAC,iBAAiB;YACnC,eAAe,IAAI,CAAC,mBAAmB;QACzC;IACF;IAEA,uCAAuC;IAC/B,0BAAoD;QAC1D,MAAM,SAAmC,EAAE;QAC3C,MAAM,YAAY,OAAO,iBAAiB;QAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,IAAK;YAC7C,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAE9B,yBAAyB;YACzB,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,IACtD,QAAQ,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,EAAE;gBAC1D,OAAO,IAAI,CAAC;oBACV,OAAO,QAAQ,IAAI;oBACnB,UAAU,IAAI,CAAC,sBAAsB,CAAC,QAAQ,IAAI,EAAE;oBACpD,MAAM;oBACN,SAAS;oBACT,WAAW,QAAQ,SAAS;gBAC9B;YACF;YAEA,qBAAqB;YACrB,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,IAClD,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,EAAE;gBACtD,OAAO,IAAI,CAAC;oBACV,OAAO,QAAQ,GAAG;oBAClB,UAAU,IAAI,CAAC,sBAAsB,CAAC,QAAQ,GAAG,EAAE;oBACnD,MAAM;oBACN,SAAS;oBACT,WAAW,QAAQ,SAAS;gBAC9B;YACF;QACF;QAEA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ;IACxC;IAEQ,uBAAuB,KAAa,EAAE,IAA8B,EAAU;QACpF,IAAI,UAAU;QACd,MAAM,YAAY,QAAQ,OAAO,iBAAiB;QAElD,KAAK,MAAM,UAAU,IAAI,CAAC,IAAI,CAAE;YAC9B,IAAI,SAAS,gBAAgB,KAAK,GAAG,CAAC,OAAO,IAAI,GAAG,UAAU,WAAW;gBACvE;YACF,OAAO,IAAI,SAAS,aAAa,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,UAAU,WAAW;gBAC1E;YACF;QACF;QAEA,OAAO,KAAK,GAAG,CAAC,UAAU,GAAG,IAAI,mBAAmB;IACtD;IAEQ,kBAAkB,MAAgC,EAAE,SAAiB,EAA4B;QACvG,MAAM,eAAyC,EAAE;QAEjD,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAA,IACjC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,aAAa,EAAE,IAAI,KAAK,MAAM,IAAI;YAGrF,IAAI,UAAU;gBACZ,SAAS,OAAO,IAAI,MAAM,OAAO;gBACjC,SAAS,QAAQ,GAAG,KAAK,GAAG,CAAC,SAAS,QAAQ,EAAE,MAAM,QAAQ;gBAC9D,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC,SAAS,SAAS,EAAE,MAAM,SAAS;YACnE,OAAO;gBACL,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC5D;IAEA,sBAAsB;IACd,oBAAkC;QACxC,MAAM,cAA4B,EAAE;QAEpC,IAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,IAAK;YAC9C,MAAM,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI;YAEzC,yDAAyD;YACzD,MAAM,oBAAoB,MAAM,IAAI,CAAC,CAAA,SACnC,CAAC,OAAO,KAAK,GAAG,QAAQ,KAAK,IAAI,QAAQ,KAAK,GAAG,KAAK,UAAU;;YAGlE,IAAI,mBAAmB;gBACrB,YAAY,IAAI,CAAC;oBACf,OAAO;oBACP,KAAK,IAAI;oBACT,MAAM,QAAQ,IAAI;oBAClB,KAAK,QAAQ,GAAG;oBAChB,MAAM;oBACN,YAAY;gBACd;YACF;YAEA,0DAA0D;YAC1D,MAAM,oBAAoB,MAAM,IAAI,CAAC,CAAA,SACnC,CAAC,QAAQ,KAAK,GAAG,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,KAAK,UAAU;;YAGlE,IAAI,mBAAmB;gBACrB,YAAY,IAAI,CAAC;oBACf,OAAO;oBACP,KAAK,IAAI;oBACT,MAAM,QAAQ,IAAI;oBAClB,KAAK,QAAQ,GAAG;oBAChB,MAAM;oBACN,YAAY;gBACd;YACF;QACF;QAEA,OAAO;IACT;IAEA,wBAAwB;IAChB,sBAAsC;QAC5C,MAAM,gBAAgC,EAAE;QACxC,MAAM,cAAc,IAAI,CAAC,iBAAiB;QAE1C,KAAK,MAAM,cAAc,YAAa;YACpC,oDAAoD;YACpD,IAAK,IAAI,IAAI,WAAW,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBAC1D,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE;gBAE3B,kCAAkC;gBAClC,MAAM,YAAY,WAAW,IAAI,KAAK,YAClC,OAAO,GAAG,GAAG,WAAW,GAAG,GAC3B,OAAO,IAAI,GAAG,WAAW,IAAI;gBAEjC,IAAI,WAAW;oBACb,cAAc,IAAI,CAAC;wBACjB,OAAO,WAAW,KAAK;wBACvB,KAAK,WAAW,GAAG;wBACnB,MAAM,WAAW,IAAI;wBACrB,KAAK,WAAW,GAAG;wBACnB,MAAM,WAAW,IAAI,KAAK,YAAY,YAAY;wBAClD,QAAQ;oBACV;oBACA;gBACF;YACF;QACF;QAEA,OAAO;IACT;IApbA,YAAY,IAAkB,CAAE;QAFhC,+KAAQ,QAAqB,EAAE;QAG7B,IAAI,CAAC,IAAI,GAAG;IACd;AAmbF", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/TradingChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef } from 'react';\nimport { TradingViewProvider } from '@/lib/data-providers/tradingview';\nimport { TechnicalIndicators } from '@/lib/technical-analysis/indicators';\nimport { MarketState } from '@/lib/market-analysis/market-state';\nimport { AIDecision } from '@/lib/ai/pattern-recognition';\n\ninterface TradingChartProps {\n  symbol: string;\n  tradingProvider: TradingViewProvider;\n  indicators?: TechnicalIndicators;\n  marketState?: MarketState;\n  aiDecision?: AIDecision;\n}\n\nconst TradingChart: React.FC<TradingChartProps> = ({\n  symbol,\n  tradingProvider,\n  indicators,\n  marketState,\n  aiDecision\n}) => {\n  const chartContainerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (chartContainerRef.current) {\n      // In a real implementation, you would integrate with TradingView's charting library\n      // For now, we'll create a placeholder chart\n      renderChart();\n    }\n  }, [symbol, indicators]);\n\n  const renderChart = () => {\n    if (!chartContainerRef.current) return;\n\n    const chartData = tradingProvider.getChartData(symbol);\n    if (!chartData) return;\n\n    // Clear previous chart\n    chartContainerRef.current.innerHTML = '';\n\n    // Create a simple SVG chart as placeholder\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('height', '400');\n    svg.setAttribute('viewBox', '0 0 800 400');\n    svg.style.background = '#1f2937';\n\n    // Generate sample candlestick data\n    const candles = chartData.data.slice(-50); // Last 50 candles\n    const maxPrice = Math.max(...candles.map(c => c.high));\n    const minPrice = Math.min(...candles.map(c => c.low));\n    const priceRange = maxPrice - minPrice;\n    const candleWidth = 800 / candles.length;\n\n    candles.forEach((candle, index) => {\n      const x = index * candleWidth + candleWidth / 2;\n      const openY = 350 - ((candle.open - minPrice) / priceRange) * 300;\n      const closeY = 350 - ((candle.close - minPrice) / priceRange) * 300;\n      const highY = 350 - ((candle.high - minPrice) / priceRange) * 300;\n      const lowY = 350 - ((candle.low - minPrice) / priceRange) * 300;\n\n      // High-Low line\n      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');\n      line.setAttribute('x1', x.toString());\n      line.setAttribute('y1', highY.toString());\n      line.setAttribute('x2', x.toString());\n      line.setAttribute('y2', lowY.toString());\n      line.setAttribute('stroke', '#6b7280');\n      line.setAttribute('stroke-width', '1');\n      svg.appendChild(line);\n\n      // Candle body\n      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');\n      const bodyHeight = Math.abs(closeY - openY);\n      const bodyY = Math.min(openY, closeY);\n      \n      rect.setAttribute('x', (x - candleWidth * 0.3).toString());\n      rect.setAttribute('y', bodyY.toString());\n      rect.setAttribute('width', (candleWidth * 0.6).toString());\n      rect.setAttribute('height', bodyHeight.toString());\n      rect.setAttribute('fill', candle.close > candle.open ? '#10b981' : '#ef4444');\n      svg.appendChild(rect);\n    });\n\n    chartContainerRef.current.appendChild(svg);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Chart Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {symbol} Chart\n          </h2>\n          <div className=\"flex items-center space-x-4\">\n            {marketState && (\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Trend:</span>\n                <span className={`text-sm font-medium px-2 py-1 rounded ${\n                  marketState.trend === 'BULLISH' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                  marketState.trend === 'BEARISH' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :\n                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n                }`}>\n                  {marketState.trend}\n                </span>\n              </div>\n            )}\n            \n            {aiDecision && (\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">AI Signal:</span>\n                <span className={`text-sm font-medium px-2 py-1 rounded ${\n                  aiDecision.action === 'BUY' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                  aiDecision.action === 'SELL' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :\n                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n                }`}>\n                  {aiDecision.action} ({aiDecision.confidence.toFixed(1)}%)\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Chart Container */}\n        <div \n          ref={chartContainerRef}\n          className=\"w-full h-96 bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700\"\n        />\n\n        {/* Chart Controls */}\n        <div className=\"mt-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">Timeframe:</span>\n            <select className=\"bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-sm\">\n              <option>1m</option>\n              <option>5m</option>\n              <option>15m</option>\n              <option>1h</option>\n              <option>4h</option>\n              <option>1d</option>\n            </select>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <button className=\"px-3 py-1 text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded\">\n              Indicators\n            </button>\n            <button className=\"px-3 py-1 text-sm bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 rounded\">\n              Patterns\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Technical Indicators Panel */}\n      {indicators && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">RSI</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {indicators.rsi.toFixed(1)}\n            </div>\n            <div className={`text-sm ${\n              indicators.rsi > 70 ? 'text-red-600' :\n              indicators.rsi < 30 ? 'text-green-600' : 'text-gray-600'\n            }`}>\n              {indicators.rsi > 70 ? 'Overbought' :\n               indicators.rsi < 30 ? 'Oversold' : 'Neutral'}\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">MACD</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {indicators.macd.MACD.toFixed(4)}\n            </div>\n            <div className={`text-sm ${\n              indicators.macd.MACD > indicators.macd.signal ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {indicators.macd.MACD > indicators.macd.signal ? 'Bullish' : 'Bearish'}\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">VWAP</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {indicators.vwap.toFixed(5)}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Volume Weighted\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\n            <h3 className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2\">Supertrend</h3>\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {indicators.supertrend.value.toFixed(5)}\n            </div>\n            <div className={`text-sm ${\n              indicators.supertrend.trend === 'up' ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {indicators.supertrend.trend === 'up' ? 'Uptrend' : 'Downtrend'}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Pattern Recognition */}\n      {aiDecision && aiDecision.patterns.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            Detected Patterns\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {aiDecision.patterns.slice(0, 4).map((pattern, index) => (\n              <div key={index} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {pattern.name}\n                  </h4>\n                  <span className={`text-sm px-2 py-1 rounded ${\n                    pattern.type === 'BULLISH' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                    pattern.type === 'BEARISH' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :\n                    'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n                  }`}>\n                    {pattern.type}\n                  </span>\n                </div>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {pattern.description}\n                </p>\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">Confidence: </span>\n                  <span className=\"font-medium text-gray-900 dark:text-white\">\n                    {pattern.confidence.toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TradingChart;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBA,MAAM,eAA4C;QAAC,EACjD,MAAM,EACN,eAAe,EACf,UAAU,EACV,WAAW,EACX,UAAU,EACX;;IACC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,oFAAoF;gBACpF,4CAA4C;gBAC5C;YACF;QACF;iCAAG;QAAC;QAAQ;KAAW;IAEvB,MAAM,cAAc;QAClB,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAEhC,MAAM,YAAY,gBAAgB,YAAY,CAAC;QAC/C,IAAI,CAAC,WAAW;QAEhB,uBAAuB;QACvB,kBAAkB,OAAO,CAAC,SAAS,GAAG;QAEtC,2CAA2C;QAC3C,MAAM,MAAM,SAAS,eAAe,CAAC,8BAA8B;QACnE,IAAI,YAAY,CAAC,SAAS;QAC1B,IAAI,YAAY,CAAC,UAAU;QAC3B,IAAI,YAAY,CAAC,WAAW;QAC5B,IAAI,KAAK,CAAC,UAAU,GAAG;QAEvB,mCAAmC;QACnC,MAAM,UAAU,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,kBAAkB;QAC7D,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACpD,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACnD,MAAM,aAAa,WAAW;QAC9B,MAAM,cAAc,MAAM,QAAQ,MAAM;QAExC,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,MAAM,IAAI,QAAQ,cAAc,cAAc;YAC9C,MAAM,QAAQ,MAAM,AAAC,CAAC,OAAO,IAAI,GAAG,QAAQ,IAAI,aAAc;YAC9D,MAAM,SAAS,MAAM,AAAC,CAAC,OAAO,KAAK,GAAG,QAAQ,IAAI,aAAc;YAChE,MAAM,QAAQ,MAAM,AAAC,CAAC,OAAO,IAAI,GAAG,QAAQ,IAAI,aAAc;YAC9D,MAAM,OAAO,MAAM,AAAC,CAAC,OAAO,GAAG,GAAG,QAAQ,IAAI,aAAc;YAE5D,gBAAgB;YAChB,MAAM,OAAO,SAAS,eAAe,CAAC,8BAA8B;YACpE,KAAK,YAAY,CAAC,MAAM,EAAE,QAAQ;YAClC,KAAK,YAAY,CAAC,MAAM,MAAM,QAAQ;YACtC,KAAK,YAAY,CAAC,MAAM,EAAE,QAAQ;YAClC,KAAK,YAAY,CAAC,MAAM,KAAK,QAAQ;YACrC,KAAK,YAAY,CAAC,UAAU;YAC5B,KAAK,YAAY,CAAC,gBAAgB;YAClC,IAAI,WAAW,CAAC;YAEhB,cAAc;YACd,MAAM,OAAO,SAAS,eAAe,CAAC,8BAA8B;YACpE,MAAM,aAAa,KAAK,GAAG,CAAC,SAAS;YACrC,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO;YAE9B,KAAK,YAAY,CAAC,KAAK,CAAC,IAAI,cAAc,GAAG,EAAE,QAAQ;YACvD,KAAK,YAAY,CAAC,KAAK,MAAM,QAAQ;YACrC,KAAK,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,EAAE,QAAQ;YACvD,KAAK,YAAY,CAAC,UAAU,WAAW,QAAQ;YAC/C,KAAK,YAAY,CAAC,QAAQ,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,YAAY;YACnE,IAAI,WAAW,CAAC;QAClB;QAEA,kBAAkB,OAAO,CAAC,WAAW,CAAC;IACxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCACX;oCAAO;;;;;;;0CAEV,6LAAC;gCAAI,WAAU;;oCACZ,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAW,AAAC,yCAIjB,OAHC,YAAY,KAAK,KAAK,YAAY,yEAClC,YAAY,KAAK,KAAK,YAAY,iEAClC;0DAEC,YAAY,KAAK;;;;;;;;;;;;oCAKvB,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAW,AAAC,yCAIjB,OAHC,WAAW,MAAM,KAAK,QAAQ,yEAC9B,WAAW,MAAM,KAAK,SAAS,iEAC/B;;oDAEC,WAAW,MAAM;oDAAC;oDAAG,WAAW,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjE,6LAAC;wBACC,KAAK;wBACL,WAAU;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC;0DAAO;;;;;;0DACR,6LAAC;0DAAO;;;;;;0DACR,6LAAC;0DAAO;;;;;;0DACR,6LAAC;0DAAO;;;;;;0DACR,6LAAC;0DAAO;;;;;;0DACR,6LAAC;0DAAO;;;;;;;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAA6F;;;;;;kDAG/G,6LAAC;wCAAO,WAAU;kDAA0F;;;;;;;;;;;;;;;;;;;;;;;;YAQjH,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,OAAO,CAAC;;;;;;0CAE1B,6LAAC;gCAAI,WAAW,AAAC,WAGhB,OAFC,WAAW,GAAG,GAAG,KAAK,iBACtB,WAAW,GAAG,GAAG,KAAK,mBAAmB;0CAExC,WAAW,GAAG,GAAG,KAAK,eACtB,WAAW,GAAG,GAAG,KAAK,aAAa;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;;;;0CAEhC,6LAAC;gCAAI,WAAW,AAAC,WAEhB,OADC,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,GAAG,mBAAmB;0CAElE,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,GAAG,YAAY;;;;;;;;;;;;kCAIjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,IAAI,CAAC,OAAO,CAAC;;;;;;0CAE3B,6LAAC;gCAAI,WAAU;0CAA2C;;;;;;;;;;;;kCAK5D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;0CAEvC,6LAAC;gCAAI,WAAW,AAAC,WAEhB,OADC,WAAW,UAAU,CAAC,KAAK,KAAK,OAAO,mBAAmB;0CAEzD,WAAW,UAAU,CAAC,KAAK,KAAK,OAAO,YAAY;;;;;;;;;;;;;;;;;;YAO3D,cAAc,WAAW,QAAQ,CAAC,MAAM,GAAG,mBAC1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC7C,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;0DAEf,6LAAC;gDAAK,WAAW,AAAC,6BAIjB,OAHC,QAAQ,IAAI,KAAK,YAAY,yEAC7B,QAAQ,IAAI,KAAK,YAAY,iEAC7B;0DAEC,QAAQ,IAAI;;;;;;;;;;;;kDAGjB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;;oDACb,QAAQ,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;+BAnB3B;;;;;;;;;;;;;;;;;;;;;;AA6BxB;GAvOM;KAAA;uCAyOS", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/MarketOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { RealTimeQuote } from '@/lib/data-providers/tradingview';\nimport { MarketState } from '@/lib/market-analysis/market-state';\nimport { AIDecision } from '@/lib/ai/pattern-recognition';\nimport { TradeSignal } from '@/lib/trading/risk-management';\nimport { TrendingUp, TrendingDown, Minus, Activity, Clock, AlertTriangle } from 'lucide-react';\n\ninterface MarketOverviewProps {\n  symbols: string[];\n  quotes: Map<string, RealTimeQuote>;\n  marketStates: Map<string, MarketState>;\n  aiDecisions: Map<string, AIDecision>;\n  signals: TradeSignal[];\n}\n\nconst MarketOverview: React.FC<MarketOverviewProps> = ({\n  symbols,\n  quotes,\n  marketStates,\n  aiDecisions,\n  signals\n}) => {\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return <TrendingUp className=\"w-4 h-4 text-green-500\" />;\n      case 'BEARISH':\n        return <TrendingDown className=\"w-4 h-4 text-red-500\" />;\n      default:\n        return <Minus className=\"w-4 h-4 text-gray-500\" />;\n    }\n  };\n\n  const getTrendColor = (trend: string) => {\n    switch (trend) {\n      case 'BULLISH':\n        return 'text-green-600 bg-green-50 dark:bg-green-900/20';\n      case 'BEARISH':\n        return 'text-red-600 bg-red-50 dark:bg-red-900/20';\n      default:\n        return 'text-gray-600 bg-gray-50 dark:bg-gray-800';\n    }\n  };\n\n  const getMomentumColor = (momentum: string) => {\n    if (momentum.includes('STRONG_BULLISH')) return 'text-green-700 bg-green-100 dark:bg-green-900/30';\n    if (momentum.includes('WEAK_BULLISH')) return 'text-green-600 bg-green-50 dark:bg-green-900/20';\n    if (momentum.includes('STRONG_BEARISH')) return 'text-red-700 bg-red-100 dark:bg-red-900/30';\n    if (momentum.includes('WEAK_BEARISH')) return 'text-red-600 bg-red-50 dark:bg-red-900/20';\n    return 'text-gray-600 bg-gray-50 dark:bg-gray-800';\n  };\n\n  const getConfidenceColor = (confidence: number) => {\n    if (confidence >= 80) return 'text-green-600';\n    if (confidence >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const recentSignals = signals.slice(0, 5);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Market Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {symbols.map(symbol => {\n          const quote = quotes.get(symbol);\n          const marketState = marketStates.get(symbol);\n          const aiDecision = aiDecisions.get(symbol);\n\n          return (\n            <div key={symbol} className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {symbol}\n                </h3>\n                {marketState && getTrendIcon(marketState.trend)}\n              </div>\n\n              {quote && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                      {quote.price.toFixed(symbol.includes('JPY') ? 3 : 5)}\n                    </span>\n                    <span className={`text-sm font-medium ${\n                      quote.change >= 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {quote.change >= 0 ? '+' : ''}{quote.changePercent.toFixed(2)}%\n                    </span>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Bid: {quote.bid.toFixed(symbol.includes('JPY') ? 3 : 5)} | \n                    Ask: {quote.ask.toFixed(symbol.includes('JPY') ? 3 : 5)}\n                  </div>\n\n                  {marketState && (\n                    <div className=\"space-y-1\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">Trend:</span>\n                        <span className={`text-xs px-2 py-1 rounded-full ${getTrendColor(marketState.trend)}`}>\n                          {marketState.trend}\n                        </span>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">Momentum:</span>\n                        <span className={`text-xs px-2 py-1 rounded-full ${getMomentumColor(marketState.momentum)}`}>\n                          {marketState.momentum.replace('_', ' ')}\n                        </span>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">Volatility:</span>\n                        <span className=\"text-xs text-gray-900 dark:text-white\">\n                          {marketState.volatility}\n                        </span>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">Session:</span>\n                        <span className=\"text-xs text-gray-900 dark:text-white\">\n                          {marketState.session.name}\n                        </span>\n                      </div>\n                    </div>\n                  )}\n\n                  {aiDecision && (\n                    <div className=\"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">AI Signal:</span>\n                        <span className={`text-xs font-medium ${\n                          aiDecision.action === 'BUY' ? 'text-green-600' :\n                          aiDecision.action === 'SELL' ? 'text-red-600' : 'text-gray-600'\n                        }`}>\n                          {aiDecision.action}\n                        </span>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-600 dark:text-gray-400\">Confidence:</span>\n                        <span className={`text-xs font-medium ${getConfidenceColor(aiDecision.confidence)}`}>\n                          {aiDecision.confidence.toFixed(1)}%\n                        </span>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Market Statistics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Trading Session Info */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center mb-4\">\n            <Clock className=\"w-5 h-5 text-blue-500 mr-2\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Trading Sessions\n            </h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {['ASIAN', 'LONDON', 'NEW_YORK'].map(session => {\n              const isActive = symbols.some(symbol => {\n                const state = marketStates.get(symbol);\n                return state?.session.name === session;\n              });\n              \n              return (\n                <div key={session} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {session.replace('_', ' ')}\n                  </span>\n                  <div className=\"flex items-center\">\n                    <div className={`w-2 h-2 rounded-full mr-2 ${\n                      isActive ? 'bg-green-500' : 'bg-gray-300'\n                    }`}></div>\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {isActive ? 'Active' : 'Closed'}\n                    </span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Market Sentiment */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center mb-4\">\n            <Activity className=\"w-5 h-5 text-purple-500 mr-2\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Market Sentiment\n            </h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {symbols.map(symbol => {\n              const marketState = marketStates.get(symbol);\n              if (!marketState) return null;\n              \n              const sentimentScore = marketState.confidence;\n              const sentimentColor = sentimentScore >= 70 ? 'bg-green-500' :\n                                   sentimentScore >= 50 ? 'bg-yellow-500' : 'bg-red-500';\n              \n              return (\n                <div key={symbol}>\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {symbol}\n                    </span>\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {sentimentScore.toFixed(0)}%\n                    </span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${sentimentColor}`}\n                      style={{ width: `${sentimentScore}%` }}\n                    ></div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Risk Overview */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center mb-4\">\n            <AlertTriangle className=\"w-5 h-5 text-orange-500 mr-2\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              Risk Overview\n            </h3>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {symbols.map(symbol => {\n              const marketState = marketStates.get(symbol);\n              if (!marketState) return null;\n              \n              const riskColor = marketState.riskLevel === 'HIGH' ? 'text-red-600' :\n                               marketState.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-green-600';\n              \n              return (\n                <div key={symbol} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {symbol}\n                  </span>\n                  <span className={`text-sm font-medium ${riskColor}`}>\n                    {marketState.riskLevel}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Signals */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Recent Trade Signals\n          </h3>\n        </div>\n        \n        <div className=\"p-6\">\n          {recentSignals.length > 0 ? (\n            <div className=\"space-y-4\">\n              {recentSignals.map(signal => (\n                <div key={signal.id} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className={`w-3 h-3 rounded-full ${\n                      signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'\n                    }`}></div>\n                    <div>\n                      <div className=\"font-medium text-gray-900 dark:text-white\">\n                        {signal.type} {signal.symbol}\n                      </div>\n                      <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        Entry: {signal.entry} | SL: {signal.stopLoss} | TP: {signal.takeProfit1}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {signal.confidence.toFixed(1)}% confidence\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      R/R: {signal.riskRewardRatio.toFixed(2)}:1\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <Activity className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                No recent signals. The AI is analyzing market conditions...\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MarketOverview;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAiBA,MAAM,iBAAgD;QAAC,EACrD,OAAO,EACP,MAAM,EACN,YAAY,EACZ,WAAW,EACX,OAAO,EACR;IACC,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAW,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC;oBAAa,WAAU;;;;;;YACjC;gBACE,qBAAO,6LAAC;oBAAM,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,QAAQ,CAAC,mBAAmB,OAAO;QAChD,IAAI,SAAS,QAAQ,CAAC,iBAAiB,OAAO;QAC9C,IAAI,SAAS,QAAQ,CAAC,mBAAmB,OAAO;QAChD,IAAI,SAAS,QAAQ,CAAC,iBAAiB,OAAO;QAC9C,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,gBAAgB,QAAQ,KAAK,CAAC,GAAG;IAEvC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAA;oBACX,MAAM,QAAQ,OAAO,GAAG,CAAC;oBACzB,MAAM,cAAc,aAAa,GAAG,CAAC;oBACrC,MAAM,aAAa,YAAY,GAAG,CAAC;oBAEnC,qBACE,6LAAC;wBAAiB,WAAU;;0CAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,eAAe,aAAa,YAAY,KAAK;;;;;;;4BAG/C,uBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,MAAM,KAAK,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;0DAEpD,6LAAC;gDAAK,WAAW,AAAC,uBAEjB,OADC,MAAM,MAAM,IAAI,IAAI,mBAAmB;;oDAEtC,MAAM,MAAM,IAAI,IAAI,MAAM;oDAAI,MAAM,aAAa,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAIlE,6LAAC;wCAAI,WAAU;;4CAA2C;4CAClD,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;4CAAG;4CAClD,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI;;;;;;;oCAGtD,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAW,AAAC,kCAAkE,OAAjC,cAAc,YAAY,KAAK;kEAC/E,YAAY,KAAK;;;;;;;;;;;;0DAItB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAW,AAAC,kCAAwE,OAAvC,iBAAiB,YAAY,QAAQ;kEACrF,YAAY,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAIvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;kEACb,YAAY,UAAU;;;;;;;;;;;;0DAI3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;kEACb,YAAY,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;oCAMhC,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAW,AAAC,uBAGjB,OAFC,WAAW,MAAM,KAAK,QAAQ,mBAC9B,WAAW,MAAM,KAAK,SAAS,iBAAiB;kEAE/C,WAAW,MAAM;;;;;;;;;;;;0DAItB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAK,WAAW,AAAC,uBAAgE,OAA1C,mBAAmB,WAAW,UAAU;;4DAC7E,WAAW,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;uBAzEtC;;;;;gBAkFd;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;0CAKtE,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAS;oCAAU;iCAAW,CAAC,GAAG,CAAC,CAAA;oCACnC,MAAM,WAAW,QAAQ,IAAI,CAAC,CAAA;wCAC5B,MAAM,QAAQ,aAAa,GAAG,CAAC;wCAC/B,OAAO,CAAA,kBAAA,4BAAA,MAAO,OAAO,CAAC,IAAI,MAAK;oCACjC;oCAEA,qBACE,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAK,WAAU;0DACb,QAAQ,OAAO,CAAC,KAAK;;;;;;0DAExB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,6BAEhB,OADC,WAAW,iBAAiB;;;;;;kEAE9B,6LAAC;wDAAK,WAAU;kEACb,WAAW,WAAW;;;;;;;;;;;;;uCATnB;;;;;gCAcd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAS,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;0CAKtE,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAA;oCACX,MAAM,cAAc,aAAa,GAAG,CAAC;oCACrC,IAAI,CAAC,aAAa,OAAO;oCAEzB,MAAM,iBAAiB,YAAY,UAAU;oCAC7C,MAAM,iBAAiB,kBAAkB,KAAK,iBACzB,kBAAkB,KAAK,kBAAkB;oCAE9D,qBACE,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb;;;;;;kEAEH,6LAAC;wDAAK,WAAU;;4DACb,eAAe,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAG/B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAW,AAAC,oBAAkC,OAAf;oDAC/B,OAAO;wDAAE,OAAO,AAAC,GAAiB,OAAf,gBAAe;oDAAG;;;;;;;;;;;;uCAZjC;;;;;gCAiBd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAc,WAAU;;;;;;kDACzB,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;;;;;;;0CAKtE,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAA;oCACX,MAAM,cAAc,aAAa,GAAG,CAAC;oCACrC,IAAI,CAAC,aAAa,OAAO;oCAEzB,MAAM,YAAY,YAAY,SAAS,KAAK,SAAS,iBACpC,YAAY,SAAS,KAAK,WAAW,oBAAoB;oCAE1E,qBACE,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAK,WAAU;0DACb;;;;;;0DAEH,6LAAC;gDAAK,WAAW,AAAC,uBAAgC,OAAV;0DACrC,YAAY,SAAS;;;;;;;uCALhB;;;;;gCASd;;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,wBAEhB,OADC,OAAO,IAAI,KAAK,QAAQ,iBAAiB;;;;;;8DAE3C,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,IAAI;gEAAC;gEAAE,OAAO,MAAM;;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;gEAA2C;gEAChD,OAAO,KAAK;gEAAC;gEAAQ,OAAO,QAAQ;gEAAC;gEAAQ,OAAO,WAAW;;;;;;;;;;;;;;;;;;;sDAK7E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,OAAO,UAAU,CAAC,OAAO,CAAC;wDAAG;;;;;;;8DAEhC,6LAAC;oDAAI,WAAU;;wDAA2C;wDAClD,OAAO,eAAe,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;mCApBpC,OAAO,EAAE;;;;;;;;;qFA2BvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAS,WAAU;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D;KA3SM;uCA6SS", "debugId": null}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/SignalPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { TradeSignal, RiskManagementEngine } from '@/lib/trading/risk-management';\nimport { RealTimeQuote } from '@/lib/data-providers/tradingview';\nimport { TrendingUp, TrendingDown, Clock, Target, Shield } from 'lucide-react';\n\ninterface SignalPanelProps {\n  signals: TradeSignal[];\n  selectedSymbol: string;\n  currentQuote?: RealTimeQuote;\n  riskManager: RiskManagementEngine;\n}\n\nconst SignalPanel: React.FC<SignalPanelProps> = ({\n  signals,\n  selectedSymbol,\n  currentQuote,\n  riskManager\n}) => {\n  const filteredSignals = signals.filter(signal => signal.symbol === selectedSymbol);\n  const allSignals = signals.slice(0, 10); // Show last 10 signals\n\n  const getSignalIcon = (type: string) => {\n    return type === 'BUY' ? \n      <TrendingUp className=\"w-5 h-5 text-green-500\" /> : \n      <TrendingDown className=\"w-5 h-5 text-red-500\" />;\n  };\n\n  const getSignalColor = (type: string) => {\n    return type === 'BUY' ? \n      'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' : \n      'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';\n  };\n\n  const formatTime = (timestamp: number) => {\n    return new Date(timestamp).toLocaleString();\n  };\n\n  const calculatePnL = (signal: TradeSignal, currentPrice?: number) => {\n    if (!currentPrice) return null;\n    \n    const priceDiff = signal.type === 'BUY' ? \n      currentPrice - signal.entry : \n      signal.entry - currentPrice;\n    \n    const pnlPercentage = (priceDiff / signal.entry) * 100;\n    return pnlPercentage;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Signal Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <Target className=\"w-8 h-8 text-blue-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Total Signals</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{signals.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <TrendingUp className=\"w-8 h-8 text-green-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Buy Signals</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {signals.filter(s => s.type === 'BUY').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <TrendingDown className=\"w-8 h-8 text-red-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Sell Signals</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {signals.filter(s => s.type === 'SELL').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Current Symbol Signals */}\n      {filteredSignals.length > 0 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {selectedSymbol} Signals\n            </h3>\n          </div>\n          \n          <div className=\"p-6 space-y-4\">\n            {filteredSignals.map(signal => {\n              const pnl = currentQuote ? calculatePnL(signal, currentQuote.price) : null;\n              \n              return (\n                <div key={signal.id} className={`border rounded-lg p-4 ${getSignalColor(signal.type)}`}>\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      {getSignalIcon(signal.type)}\n                      <div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"font-semibold text-gray-900 dark:text-white\">\n                            {signal.type} {signal.symbol}\n                          </span>\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {signal.confidence.toFixed(1)}% confidence\n                          </span>\n                        </div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                          {formatTime(signal.timestamp)}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {pnl !== null && (\n                      <div className={`text-right ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                        <div className=\"font-semibold\">\n                          {pnl >= 0 ? '+' : ''}{pnl.toFixed(2)}%\n                        </div>\n                        <div className=\"text-xs\">P&L</div>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">Entry:</span>\n                      <div className=\"font-medium text-gray-900 dark:text-white\">\n                        {signal.entry.toFixed(5)}\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">Stop Loss:</span>\n                      <div className=\"font-medium text-red-600\">\n                        {signal.stopLoss.toFixed(5)}\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">TP1:</span>\n                      <div className=\"font-medium text-green-600\">\n                        {signal.takeProfit1.toFixed(5)}\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">TP2:</span>\n                      <div className=\"font-medium text-green-600\">\n                        {signal.takeProfit2.toFixed(5)}\n                      </div>\n                    </div>\n                    \n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">R/R:</span>\n                      <div className=\"font-medium text-gray-900 dark:text-white\">\n                        {signal.riskRewardRatio.toFixed(2)}:1\n                      </div>\n                    </div>\n                  </div>\n\n                  {signal.reasoning.length > 0 && (\n                    <div className=\"mt-4 p-3 bg-white dark:bg-gray-700 rounded border\">\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                        Analysis Reasoning:\n                      </h4>\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                        {signal.reasoning.map((reason, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <span className=\"mr-2\">•</span>\n                            <span>{reason}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n\n      {/* All Signals */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Recent Signals (All Pairs)\n          </h3>\n        </div>\n        \n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Signal\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Entry\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Stop Loss\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Take Profit\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  R/R Ratio\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Confidence\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Time\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              {allSignals.map(signal => (\n                <tr key={signal.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      {getSignalIcon(signal.type)}\n                      <div className=\"ml-3\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {signal.type} {signal.symbol}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {signal.entry.toFixed(5)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-red-600\">\n                    {signal.stopLoss.toFixed(5)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-green-600\">\n                    {signal.takeProfit1.toFixed(5)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {signal.riskRewardRatio.toFixed(2)}:1\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      signal.confidence >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                      signal.confidence >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :\n                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n                    }`}>\n                      {signal.confidence.toFixed(1)}%\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                    {formatTime(signal.timestamp)}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        {allSignals.length === 0 && (\n          <div className=\"text-center py-12\">\n            <Clock className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              No signals generated yet. The AI is analyzing market conditions...\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SignalPanel;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAcA,MAAM,cAA0C;QAAC,EAC/C,OAAO,EACP,cAAc,EACd,YAAY,EACZ,WAAW,EACZ;IACC,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;IACnE,MAAM,aAAa,QAAQ,KAAK,CAAC,GAAG,KAAK,uBAAuB;IAEhE,MAAM,gBAAgB,CAAC;QACrB,OAAO,SAAS,sBACd,6LAAC;YAAW,WAAU;;;;;qEACtB,6LAAC;YAAa,WAAU;;;;;;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,QACd,4EACA;IACJ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,cAAc;IAC3C;IAEA,MAAM,eAAe,CAAC,QAAqB;QACzC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,YAAY,OAAO,IAAI,KAAK,QAChC,eAAe,OAAO,KAAK,GAC3B,OAAO,KAAK,GAAG;QAEjB,MAAM,gBAAgB,AAAC,YAAY,OAAO,KAAK,GAAI;QACnD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;;;;;8CAClB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAoD,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAKrF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,WAAU;;;;;;8CACtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDACV,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMrD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAa,WAAU;;;;;;8CACxB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDACV,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQvD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCACX;gCAAe;;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAA;4BACnB,MAAM,MAAM,eAAe,aAAa,QAAQ,aAAa,KAAK,IAAI;4BAEtE,qBACE,6LAAC;gCAAoB,WAAW,AAAC,yBAAoD,OAA5B,eAAe,OAAO,IAAI;;kDACjF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,OAAO,IAAI;kEAC1B,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;4EACb,OAAO,IAAI;4EAAC;4EAAE,OAAO,MAAM;;;;;;;kFAE9B,6LAAC;wEAAK,WAAU;;4EACb,OAAO,UAAU,CAAC,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAGlC,6LAAC;gEAAI,WAAU;0EACZ,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;4CAKjC,QAAQ,sBACP,6LAAC;gDAAI,WAAW,AAAC,cAA0D,OAA7C,OAAO,IAAI,mBAAmB;;kEAC1D,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI,MAAM;4DAAI,IAAI,OAAO,CAAC;4DAAG;;;;;;;kEAEvC,6LAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAK/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAI1B,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAI7B,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAIhC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAIhC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,eAAe,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;oCAKxC,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DAGvE,6LAAC;gDAAG,WAAU;0DACX,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAO;;;;;;0EACvB,6LAAC;0EAAM;;;;;;;uDAFA;;;;;;;;;;;;;;;;;+BAzET,OAAO,EAAE;;;;;wBAmFvB;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,WAAW,GAAG,CAAC,CAAA,uBACd,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,cAAc,OAAO,IAAI;0EAC1B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;wEACZ,OAAO,IAAI;wEAAC;wEAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;8DAKpC,6LAAC;oDAAG,WAAU;8DACX,OAAO,KAAK,CAAC,OAAO,CAAC;;;;;;8DAExB,6LAAC;oDAAG,WAAU;8DACX,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;8DAE3B,6LAAC;oDAAG,WAAU;8DACX,OAAO,WAAW,CAAC,OAAO,CAAC;;;;;;8DAE9B,6LAAC;oDAAG,WAAU;;wDACX,OAAO,eAAe,CAAC,OAAO,CAAC;wDAAG;;;;;;;8DAErC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,AAAC,4DAIjB,OAHC,OAAO,UAAU,IAAI,KAAK,yEAC1B,OAAO,UAAU,IAAI,KAAK,6EAC1B;;4DAEC,OAAO,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;8DAGlC,6LAAC;oDAAG,WAAU;8DACX,WAAW,OAAO,SAAS;;;;;;;2CAjCvB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;oBAyCzB,WAAW,MAAM,KAAK,mBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;KA1QM;uCA4QS", "debugId": null}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/RiskPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { RiskManagementEngine, TradeSignal } from '@/lib/trading/risk-management';\nimport { RealTimeQuote } from '@/lib/data-providers/tradingview';\nimport { Shield, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react';\n\ninterface RiskPanelProps {\n  riskManager: RiskManagementEngine;\n  signals: TradeSignal[];\n  quotes: Map<string, RealTimeQuote>;\n}\n\nconst RiskPanel: React.FC<RiskPanelProps> = ({\n  riskManager,\n  signals,\n  quotes\n}) => {\n  const activeTrades = riskManager.getActiveTrades();\n  const totalRiskExposure = riskManager.getTotalRiskExposure();\n\n  // Mock account data for demo\n  const accountData = {\n    balance: 10000,\n    equity: 10000,\n    margin: 0,\n    freeMargin: 10000,\n    marginLevel: 0\n  };\n\n  const riskMetrics = {\n    dailyRisk: (totalRiskExposure / accountData.balance) * 100,\n    maxDrawdown: 5.2,\n    winRate: 68.5,\n    profitFactor: 1.85,\n    sharpeRatio: 1.42\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Risk Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <DollarSign className=\"w-8 h-8 text-green-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Account Balance</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                ${accountData.balance.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <Shield className=\"w-8 h-8 text-blue-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Daily Risk</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {riskMetrics.dailyRisk.toFixed(1)}%\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <TrendingUp className=\"w-8 h-8 text-purple-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Win Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {riskMetrics.winRate.toFixed(1)}%\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <AlertTriangle className=\"w-8 h-8 text-orange-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Max Drawdown</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {riskMetrics.maxDrawdown.toFixed(1)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Risk Settings */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Risk Management Settings\n          </h3>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Max Risk Per Trade\n                </label>\n                <div className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"range\"\n                    min=\"0.5\"\n                    max=\"5\"\n                    step=\"0.1\"\n                    defaultValue=\"2\"\n                    className=\"flex-1\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 w-12\">2.0%</span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Max Daily Risk\n                </label>\n                <div className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"range\"\n                    min=\"2\"\n                    max=\"10\"\n                    step=\"0.5\"\n                    defaultValue=\"6\"\n                    className=\"flex-1\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 w-12\">6.0%</span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Max Open Trades\n                </label>\n                <select className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2\">\n                  <option value=\"1\">1</option>\n                  <option value=\"2\">2</option>\n                  <option value=\"3\" selected>3</option>\n                  <option value=\"4\">4</option>\n                  <option value=\"5\">5</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Min Risk/Reward Ratio\n                </label>\n                <div className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"range\"\n                    min=\"1\"\n                    max=\"3\"\n                    step=\"0.1\"\n                    defaultValue=\"1.5\"\n                    className=\"flex-1\"\n                  />\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 w-12\">1.5:1</span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Stop Loss Type\n                </label>\n                <select className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2\">\n                  <option>Fixed Percentage</option>\n                  <option>ATR Based</option>\n                  <option>Support/Resistance</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"trailingStop\"\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <label htmlFor=\"trailingStop\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                  Enable trailing stop loss\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Performance Metrics\n          </h3>\n        </div>\n        \n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-600 mb-2\">\n                {riskMetrics.profitFactor.toFixed(2)}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">Profit Factor</div>\n              <div className=\"text-xs text-gray-500 mt-1\">\n                Gross Profit / Gross Loss\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                {riskMetrics.sharpeRatio.toFixed(2)}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">Sharpe Ratio</div>\n              <div className=\"text-xs text-gray-500 mt-1\">\n                Risk-adjusted return\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-600 mb-2\">\n                {signals.length}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">Total Signals</div>\n              <div className=\"text-xs text-gray-500 mt-1\">\n                Generated today\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Active Trades */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Active Trades\n          </h3>\n        </div>\n        \n        <div className=\"p-6\">\n          {activeTrades.length > 0 ? (\n            <div className=\"space-y-4\">\n              {activeTrades.map(trade => {\n                const currentQuote = quotes.get(trade.symbol);\n                const currentPrice = currentQuote?.price || trade.entry;\n                const pnl = trade.type === 'BUY' ? \n                  currentPrice - trade.entry : \n                  trade.entry - currentPrice;\n                const pnlPercentage = (pnl / trade.entry) * 100;\n\n                return (\n                  <div key={trade.id} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-3 h-3 rounded-full ${\n                          trade.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'\n                        }`}></div>\n                        <span className=\"font-medium text-gray-900 dark:text-white\">\n                          {trade.type} {trade.symbol}\n                        </span>\n                      </div>\n                      <div className={`text-right ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                        <div className=\"font-semibold\">\n                          {pnl >= 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%\n                        </div>\n                        <div className=\"text-xs\">\n                          ${Math.abs(pnl * 1000).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-4 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">Entry:</span>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {trade.entry.toFixed(5)}\n                        </div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">Current:</span>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {currentPrice.toFixed(5)}\n                        </div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">Stop Loss:</span>\n                        <div className=\"font-medium text-red-600\">\n                          {trade.stopLoss.toFixed(5)}\n                        </div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">Take Profit:</span>\n                        <div className=\"font-medium text-green-600\">\n                          {trade.takeProfit1.toFixed(5)}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"mt-3 flex justify-end space-x-2\">\n                      <button className=\"px-3 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded\">\n                        Modify\n                      </button>\n                      <button className=\"px-3 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded\">\n                        Close\n                      </button>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <Shield className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                No active trades. Risk exposure: 0%\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Risk Warnings */}\n      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <AlertTriangle className=\"w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-3 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n              Risk Management Notice\n            </h4>\n            <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n              This is a demo trading bot. All trades are simulated. Never risk more than you can afford to lose in live trading.\n              Past performance does not guarantee future results.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RiskPanel;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAaA,MAAM,YAAsC;QAAC,EAC3C,WAAW,EACX,OAAO,EACP,MAAM,EACP;IACC,MAAM,eAAe,YAAY,eAAe;IAChD,MAAM,oBAAoB,YAAY,oBAAoB;IAE1D,6BAA6B;IAC7B,MAAM,cAAc;QAClB,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,WAAW,AAAC,oBAAoB,YAAY,OAAO,GAAI;QACvD,aAAa;QACb,SAAS;QACT,cAAc;QACd,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,WAAU;;;;;;8CACtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;;gDAAmD;gDAC5D,YAAY,OAAO,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;;;;;8CAClB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;;gDACV,YAAY,SAAS,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,WAAU;;;;;;8CACtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;;gDACV,YAAY,OAAO,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAMxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAc,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;;gDACV,YAAY,WAAW,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,cAAa;4DACb,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;sDAIpE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,cAAa;4DACb,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;sDAIpE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAO,OAAM;sEAAI;;;;;;sEAClB,6LAAC;4DAAO,OAAM;sEAAI;;;;;;sEAClB,6LAAC;4DAAO,OAAM;4DAAI,QAAQ;sEAAC;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAI;;;;;;sEAClB,6LAAC;4DAAO,OAAM;sEAAI;;;;;;;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,cAAa;4DACb,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;sDAIpE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;sEAAO;;;;;;sEACR,6LAAC;sEAAO;;;;;;sEACR,6LAAC;sEAAO;;;;;;;;;;;;;;;;;;sDAIZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,YAAY,YAAY,CAAC,OAAO,CAAC;;;;;;sDAEpC,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;8CAK9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,YAAY,WAAW,CAAC,OAAO,CAAC;;;;;;sDAEnC,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;8CAK9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,MAAM;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAA;gCAChB,MAAM,eAAe,OAAO,GAAG,CAAC,MAAM,MAAM;gCAC5C,MAAM,eAAe,CAAA,yBAAA,mCAAA,aAAc,KAAK,KAAI,MAAM,KAAK;gCACvD,MAAM,MAAM,MAAM,IAAI,KAAK,QACzB,eAAe,MAAM,KAAK,GAC1B,MAAM,KAAK,GAAG;gCAChB,MAAM,gBAAgB,AAAC,MAAM,MAAM,KAAK,GAAI;gCAE5C,qBACE,6LAAC;oCAAmB,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,wBAEhB,OADC,MAAM,IAAI,KAAK,QAAQ,iBAAiB;;;;;;sEAE1C,6LAAC;4DAAK,WAAU;;gEACb,MAAM,IAAI;gEAAC;gEAAE,MAAM,MAAM;;;;;;;;;;;;;8DAG9B,6LAAC;oDAAI,WAAW,AAAC,cAA0D,OAA7C,OAAO,IAAI,mBAAmB;;sEAC1D,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,IAAI,MAAM;gEAAI,cAAc,OAAO,CAAC;gEAAG;;;;;;;sEAEjD,6LAAC;4DAAI,WAAU;;gEAAU;gEACrB,KAAK,GAAG,CAAC,MAAM,MAAM,OAAO,CAAC;;;;;;;;;;;;;;;;;;;sDAKrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ,MAAM,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;8DAGzB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ,aAAa,OAAO,CAAC;;;;;;;;;;;;8DAG1B,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ,MAAM,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;8DAG5B,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEACZ,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;sDAKjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAAqG;;;;;;8DAGvH,6LAAC;oDAAO,WAAU;8DAAyF;;;;;;;;;;;;;mCAnDrG,MAAM,EAAE;;;;;4BAyDtB;;;;;qFAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAc,WAAU;;;;;;sCACzB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;KA3UM;uCA6US", "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/AlertPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { AlertSystem } from '@/lib/notifications/alert-system';\nimport { TradeSignal } from '@/lib/trading/risk-management';\nimport { Bell, Mail, MessageSquare, Settings, Check, X, Clock } from 'lucide-react';\n\ninterface AlertPanelProps {\n  alertSystem: AlertSystem;\n  signals: TradeSignal[];\n}\n\nconst AlertPanel: React.FC<AlertPanelProps> = ({\n  alertSystem,\n  signals\n}) => {\n  const [telegramEnabled, setTelegramEnabled] = useState(false);\n  const [emailEnabled, setEmailEnabled] = useState(false);\n  const [telegramBotToken, setTelegramBotToken] = useState('');\n  const [telegramChatId, setTelegramChatId] = useState('');\n  const [emailAddress, setEmailAddress] = useState('');\n\n  // Mock alert history for demo\n  const alertHistory = [\n    {\n      id: '1',\n      type: 'TRADE_SIGNAL',\n      title: '🚨 BUY Signal - EURUSD',\n      message: 'Strong bullish signal detected with 85% confidence',\n      timestamp: Date.now() - 300000, // 5 minutes ago\n      status: 'sent',\n      channels: ['telegram']\n    },\n    {\n      id: '2',\n      type: 'MARKET_UPDATE',\n      title: '📊 Market Update - GBPUSD',\n      message: 'Market sentiment changed to bearish',\n      timestamp: Date.now() - 900000, // 15 minutes ago\n      status: 'sent',\n      channels: ['email']\n    },\n    {\n      id: '3',\n      type: 'RISK_WARNING',\n      title: '⚠️ Risk Warning',\n      message: 'Daily risk limit approaching 80%',\n      timestamp: Date.now() - 1800000, // 30 minutes ago\n      status: 'sent',\n      channels: ['telegram', 'email']\n    }\n  ];\n\n  const alertStats = alertSystem.getAlertStats();\n\n  const handleTestAlert = async (channel: 'telegram' | 'email') => {\n    try {\n      if (channel === 'telegram') {\n        await alertSystem.sendSystemStatus('ONLINE', 'Test alert from AI Trading Bot');\n      } else {\n        await alertSystem.sendSystemStatus('ONLINE', 'Test email alert from AI Trading Bot');\n      }\n      alert(`Test ${channel} alert sent successfully!`);\n    } catch (error) {\n      alert(`Failed to send test ${channel} alert: ${error}`);\n    }\n  };\n\n  const getAlertIcon = (type: string) => {\n    switch (type) {\n      case 'TRADE_SIGNAL':\n        return <Bell className=\"w-4 h-4 text-blue-500\" />;\n      case 'MARKET_UPDATE':\n        return <MessageSquare className=\"w-4 h-4 text-green-500\" />;\n      case 'RISK_WARNING':\n        return <X className=\"w-4 h-4 text-red-500\" />;\n      default:\n        return <Bell className=\"w-4 h-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'sent':\n        return <Check className=\"w-4 h-4 text-green-500\" />;\n      case 'pending':\n        return <Clock className=\"w-4 h-4 text-yellow-500\" />;\n      case 'failed':\n        return <X className=\"w-4 h-4 text-red-500\" />;\n      default:\n        return <Clock className=\"w-4 h-4 text-gray-500\" />;\n    }\n  };\n\n  const formatTime = (timestamp: number) => {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(minutes / 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m ago`;\n    }\n    return `${minutes}m ago`;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Alert Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <Bell className=\"w-8 h-8 text-blue-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Queue Length</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {alertStats.queueLength}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <Clock className=\"w-8 h-8 text-orange-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Active Countdowns</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {alertStats.activeCountdowns}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <Check className=\"w-8 h-8 text-green-500 mr-3\" />\n            <div>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Alerts Sent Today</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {alertHistory.length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Alert Configuration */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white flex items-center\">\n            <Settings className=\"w-5 h-5 mr-2\" />\n            Alert Configuration\n          </h3>\n        </div>\n        \n        <div className=\"p-6 space-y-6\">\n          {/* Telegram Configuration */}\n          <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center\">\n                <MessageSquare className=\"w-5 h-5 text-blue-500 mr-2\" />\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  Telegram Notifications\n                </h4>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={telegramEnabled}\n                  onChange={(e) => setTelegramEnabled(e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n            \n            {telegramEnabled && (\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Bot Token\n                  </label>\n                  <input\n                    type=\"password\"\n                    value={telegramBotToken}\n                    onChange={(e) => setTelegramBotToken(e.target.value)}\n                    placeholder=\"Enter your Telegram bot token\"\n                    className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Chat ID\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={telegramChatId}\n                    onChange={(e) => setTelegramChatId(e.target.value)}\n                    placeholder=\"Enter your Telegram chat ID\"\n                    className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm\"\n                  />\n                </div>\n                <button\n                  onClick={() => handleTestAlert('telegram')}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700\"\n                >\n                  Send Test Alert\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Email Configuration */}\n          <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center\">\n                <Mail className=\"w-5 h-5 text-green-500 mr-2\" />\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  Email Notifications\n                </h4>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={emailEnabled}\n                  onChange={(e) => setEmailEnabled(e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n            \n            {emailEnabled && (\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Email Address\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={emailAddress}\n                    onChange={(e) => setEmailAddress(e.target.value)}\n                    placeholder=\"Enter your email address\"\n                    className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm\"\n                  />\n                </div>\n                <button\n                  onClick={() => handleTestAlert('email')}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700\"\n                >\n                  Send Test Email\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Alert Types */}\n          <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n            <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n              Alert Types\n            </h4>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Trade Signals</span>\n                <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Market Updates</span>\n                <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Risk Warnings</span>\n                <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">System Status</span>\n                <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Countdown Alerts</span>\n                <input type=\"checkbox\" defaultChecked className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Alert History */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            Recent Alerts\n          </h3>\n        </div>\n        \n        <div className=\"p-6\">\n          {alertHistory.length > 0 ? (\n            <div className=\"space-y-4\">\n              {alertHistory.map(alert => (\n                <div key={alert.id} className=\"flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"flex-shrink-0\">\n                    {getAlertIcon(alert.type)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                        {alert.title}\n                      </h4>\n                      <div className=\"flex items-center space-x-2\">\n                        {getStatusIcon(alert.status)}\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          {formatTime(alert.timestamp)}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                      {alert.message}\n                    </p>\n                    \n                    <div className=\"flex items-center space-x-2 mt-2\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">Sent via:</span>\n                      {alert.channels.map(channel => (\n                        <span key={channel} className=\"text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-2 py-1 rounded\">\n                          {channel}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <Bell className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                No alerts sent yet. Configure your notification settings above.\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Demo Notice */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <Bell className=\"w-5 h-5 text-blue-600 dark:text-blue-400 mr-3 mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              Demo Mode Notice\n            </h4>\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              This is a demo version. To enable real notifications, you need to configure your Telegram bot token and email settings.\n              All alerts shown here are simulated for demonstration purposes.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AlertPanel;\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;AAFA;;;AAYA,MAAM,aAAwC;QAAC,EAC7C,WAAW,EACX,OAAO,EACR;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,8BAA8B;IAC9B,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;YACR,UAAU;gBAAC;aAAW;QACxB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;YACR,UAAU;gBAAC;aAAQ;QACrB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,KAAK,GAAG,KAAK;YACxB,QAAQ;YACR,UAAU;gBAAC;gBAAY;aAAQ;QACjC;KACD;IAED,MAAM,aAAa,YAAY,aAAa;IAE5C,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,IAAI,YAAY,YAAY;gBAC1B,MAAM,YAAY,gBAAgB,CAAC,UAAU;YAC/C,OAAO;gBACL,MAAM,YAAY,gBAAgB,CAAC,UAAU;YAC/C;YACA,MAAM,AAAC,QAAe,OAAR,SAAQ;QACxB,EAAE,OAAO,OAAO;YACd,MAAM,AAAC,uBAAwC,OAAlB,SAAQ,YAAgB,OAAN;QACjD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC;oBAAc,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC;oBAAE,WAAU;;;;;;YACtB;gBACE,qBAAO,6LAAC;oBAAK,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAM,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC;oBAAM,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC;oBAAE,WAAU;;;;;;YACtB;gBACE,qBAAO,6LAAC;oBAAM,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAO,MAAM;QACnB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QAEnC,IAAI,QAAQ,GAAG;YACb,OAAO,AAAC,GAAY,OAAV,OAAM,MAAiB,OAAb,UAAU,IAAG;QACnC;QACA,OAAO,AAAC,GAAU,OAAR,SAAQ;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;;;;;8CACjB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDACV,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;;;;;8CACjB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDACV,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAS,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAc,WAAU;;;;;;kEACzB,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;;;;;;;0DAIpE,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;wDACpD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;oCAIlB,iCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAQP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAG,WAAU;kEAAoD;;;;;;;;;;;;0DAIpE,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;wDACjD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;oCAIlB,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAC/C,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAQP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAM,MAAK;wDAAW,cAAc;wDAAC,WAAU;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAM,MAAK;wDAAW,cAAc;wDAAC,WAAU;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAM,MAAK;wDAAW,cAAc;wDAAC,WAAU;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAM,MAAK;wDAAW,cAAc;wDAAC,WAAU;;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;kEAC3D,6LAAC;wDAAM,MAAK;wDAAW,cAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAA,uBAChB,6LAAC;oCAAmB,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACZ,aAAa,OAAM,IAAI;;;;;;sDAG1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,OAAM,KAAK;;;;;;sEAEd,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,OAAM,MAAM;8EAC3B,6LAAC;oEAAK,WAAU;8EACb,WAAW,OAAM,SAAS;;;;;;;;;;;;;;;;;;8DAKjC,6LAAC;oDAAE,WAAU;8DACV,OAAM,OAAO;;;;;;8DAGhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;wDAC1D,OAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,wBAClB,6LAAC;gEAAmB,WAAU;0EAC3B;+DADQ;;;;;;;;;;;;;;;;;;mCAzBT,OAAM,EAAE;;;;;;;;;qFAmCtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;0BASxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GA9VM;KAAA;uCAgWS", "debugId": null}}, {"offset": {"line": 4535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/components/TradingDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { TradingViewProvider, RealTimeQuote } from '@/lib/data-providers/tradingview';\nimport { RiskManagementEngine, TradeSignal } from '@/lib/trading/risk-management';\nimport { MarketStateAnalyzer, MarketState } from '@/lib/market-analysis/market-state';\nimport { AIPatternRecognition, AIDecision } from '@/lib/ai/pattern-recognition';\nimport { AlertSystem } from '@/lib/notifications/alert-system';\nimport { TechnicalAnalysisEngine, TechnicalIndicators } from '@/lib/technical-analysis/indicators';\nimport TradingChart from './TradingChart';\nimport MarketOverview from './MarketOverview';\nimport SignalPanel from './SignalPanel';\nimport RiskPanel from './RiskPanel';\nimport AlertPanel from './AlertPanel';\nimport { Activity, TrendingUp, Shield, Bell, Settings, BarChart3 } from 'lucide-react';\n\ninterface TradingDashboardProps {\n  tradingProvider: TradingViewProvider;\n  riskManager: RiskManagementEngine;\n  marketAnalyzer: MarketStateAnalyzer;\n  aiEngine: AIPatternRecognition;\n  alertSystem: AlertSystem;\n}\n\ninterface DashboardData {\n  quotes: Map<string, RealTimeQuote>;\n  signals: TradeSignal[];\n  marketStates: Map<string, MarketState>;\n  aiDecisions: Map<string, AIDecision>;\n  indicators: Map<string, TechnicalIndicators>;\n}\n\nconst TradingDashboard: React.FC<TradingDashboardProps> = ({\n  tradingProvider,\n  riskManager,\n  marketAnalyzer,\n  aiEngine,\n  alertSystem\n}) => {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');\n  const [dashboardData, setDashboardData] = useState<DashboardData>({\n    quotes: new Map(),\n    signals: [],\n    marketStates: new Map(),\n    aiDecisions: new Map(),\n    indicators: new Map()\n  });\n  const [isLive, setIsLive] = useState(true);\n\n  const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'];\n\n  useEffect(() => {\n    setupRealTimeUpdates();\n    return () => {\n      // Cleanup\n    };\n  }, []);\n\n  const setupRealTimeUpdates = () => {\n    // Listen for real-time quotes\n    tradingProvider.on('quote', (quote: RealTimeQuote) => {\n      setDashboardData(prev => ({\n        ...prev,\n        quotes: new Map(prev.quotes.set(quote.symbol, quote))\n      }));\n    });\n\n    // Listen for candle updates and perform analysis\n    tradingProvider.on('candle', async (data: any) => {\n      await performAnalysis(data.symbol);\n    });\n\n    // Initial analysis for all symbols\n    symbols.forEach(symbol => {\n      performAnalysis(symbol);\n    });\n  };\n\n  const performAnalysis = async (symbol: string) => {\n    try {\n      const chartData = tradingProvider.getChartData(symbol);\n      if (!chartData || chartData.data.length < 50) return;\n\n      // Technical analysis\n      const engine = new TechnicalAnalysisEngine(chartData.data);\n      const indicators = engine.calculateAllIndicators();\n\n      // Market state analysis\n      const marketState = marketAnalyzer.analyzeMarketState(chartData.data, indicators, symbol);\n\n      // AI decision\n      const aiDecision = aiEngine.makeDecision(chartData.data, indicators, marketState, symbol);\n\n      // Update dashboard data\n      setDashboardData(prev => ({\n        ...prev,\n        marketStates: new Map(prev.marketStates.set(symbol, marketState)),\n        aiDecisions: new Map(prev.aiDecisions.set(symbol, aiDecision)),\n        indicators: new Map(prev.indicators.set(symbol, indicators))\n      }));\n\n      // Generate trade signal if conditions are met\n      const currentQuote = tradingProvider.getCurrentQuote(symbol);\n      if (currentQuote && aiDecision.confidence > 75) {\n        const signal = riskManager.generateTradeSignal(\n          symbol,\n          currentQuote.price,\n          indicators,\n          chartData.data\n        );\n\n        if (signal) {\n          setDashboardData(prev => ({\n            ...prev,\n            signals: [signal, ...prev.signals.slice(0, 9)] // Keep last 10 signals\n          }));\n        }\n      }\n    } catch (error) {\n      console.error(`Analysis error for ${symbol}:`, error);\n    }\n  };\n\n  const tabs = [\n    { id: 'overview', label: 'Market Overview', icon: BarChart3 },\n    { id: 'chart', label: 'Trading Chart', icon: TrendingUp },\n    { id: 'signals', label: 'Trade Signals', icon: Activity },\n    { id: 'risk', label: 'Risk Management', icon: Shield },\n    { id: 'alerts', label: 'Alerts', icon: Bell },\n    { id: 'settings', label: 'Settings', icon: Settings }\n  ];\n\n  const currentQuote = dashboardData.quotes.get(selectedSymbol);\n  const currentMarketState = dashboardData.marketStates.get(selectedSymbol);\n  const currentAIDecision = dashboardData.aiDecisions.get(selectedSymbol);\n  const currentIndicators = dashboardData.indicators.get(selectedSymbol);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  🤖 AI Trading Bot\n                </h1>\n              </div>\n              <div className=\"ml-6 flex items-center space-x-2\">\n                <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                  {isLive ? 'Live' : 'Offline'}\n                </span>\n              </div>\n            </div>\n\n            {/* Symbol Selector */}\n            <div className=\"flex items-center space-x-4\">\n              <select\n                value={selectedSymbol}\n                onChange={(e) => setSelectedSymbol(e.target.value)}\n                className=\"bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {symbols.map(symbol => (\n                  <option key={symbol} value={symbol}>{symbol}</option>\n                ))}\n              </select>\n\n              {currentQuote && (\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {currentQuote.price.toFixed(5)}\n                  </span>\n                  <span className={`text-sm ${currentQuote.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                    {currentQuote.change >= 0 ? '+' : ''}{currentQuote.changePercent.toFixed(2)}%\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8\">\n            {tabs.map(tab => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{tab.label}</span>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {activeTab === 'overview' && (\n          <MarketOverview\n            symbols={symbols}\n            quotes={dashboardData.quotes}\n            marketStates={dashboardData.marketStates}\n            aiDecisions={dashboardData.aiDecisions}\n            signals={dashboardData.signals}\n          />\n        )}\n\n        {activeTab === 'chart' && (\n          <TradingChart\n            symbol={selectedSymbol}\n            tradingProvider={tradingProvider}\n            indicators={currentIndicators}\n            marketState={currentMarketState}\n            aiDecision={currentAIDecision}\n          />\n        )}\n\n        {activeTab === 'signals' && (\n          <SignalPanel\n            signals={dashboardData.signals}\n            selectedSymbol={selectedSymbol}\n            currentQuote={currentQuote}\n            riskManager={riskManager}\n          />\n        )}\n\n        {activeTab === 'risk' && (\n          <RiskPanel\n            riskManager={riskManager}\n            signals={dashboardData.signals}\n            quotes={dashboardData.quotes}\n          />\n        )}\n\n        {activeTab === 'alerts' && (\n          <AlertPanel\n            alertSystem={alertSystem}\n            signals={dashboardData.signals}\n          />\n        )}\n\n        {activeTab === 'settings' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n              Settings\n            </h2>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Trading Mode\n                </label>\n                <select className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2\">\n                  <option>Demo Mode</option>\n                  <option>Live Trading (Disabled)</option>\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Risk Level\n                </label>\n                <select className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2\">\n                  <option>Conservative</option>\n                  <option>Moderate</option>\n                  <option>Aggressive</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  AI Confidence Threshold\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"50\"\n                  max=\"95\"\n                  defaultValue=\"75\"\n                  className=\"w-full\"\n                />\n                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                  <span>50%</span>\n                  <span>95%</span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"notifications\"\n                  defaultChecked\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <label htmlFor=\"notifications\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                  Enable notifications\n                </label>\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"autoTrade\"\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <label htmlFor=\"autoTrade\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                  Enable auto-trading (Demo only)\n                </label>\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n\n      {/* Status Bar */}\n      <footer className=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3\">\n          <div className=\"flex justify-between items-center text-sm text-gray-600 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-4\">\n              <span>Active Signals: {dashboardData.signals.length}</span>\n              <span>•</span>\n              <span>Market Session: {currentMarketState?.session.name || 'Unknown'}</span>\n              <span>•</span>\n              <span>Last Update: {new Date().toLocaleTimeString()}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span>Powered by AI</span>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default TradingDashboard;\n"], "names": [], "mappings": ";;;;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAbA;;;;;;;;;AAgCA,MAAM,mBAAoD;QAAC,EACzD,eAAe,EACf,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACZ;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,QAAQ,IAAI;QACZ,SAAS,EAAE;QACX,cAAc,IAAI;QAClB,aAAa,IAAI;QACjB,YAAY,IAAI;IAClB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,UAAU;QAAC;QAAU;QAAU;QAAU;KAAS;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;YACA;8CAAO;gBACL,UAAU;gBACZ;;QACF;qCAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,8BAA8B;QAC9B,gBAAgB,EAAE,CAAC,SAAS,CAAC;YAC3B,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,QAAQ,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,MAAM,MAAM,EAAE;gBAChD,CAAC;QACH;QAEA,iDAAiD;QACjD,gBAAgB,EAAE,CAAC,UAAU,OAAO;YAClC,MAAM,gBAAgB,KAAK,MAAM;QACnC;QAEA,mCAAmC;QACnC,QAAQ,OAAO,CAAC,CAAA;YACd,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,YAAY,gBAAgB,YAAY,CAAC;YAC/C,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,MAAM,GAAG,IAAI;YAE9C,qBAAqB;YACrB,MAAM,SAAS,IAAI,oJAAA,CAAA,0BAAuB,CAAC,UAAU,IAAI;YACzD,MAAM,aAAa,OAAO,sBAAsB;YAEhD,wBAAwB;YACxB,MAAM,cAAc,eAAe,kBAAkB,CAAC,UAAU,IAAI,EAAE,YAAY;YAElF,cAAc;YACd,MAAM,aAAa,SAAS,YAAY,CAAC,UAAU,IAAI,EAAE,YAAY,aAAa;YAElF,wBAAwB;YACxB,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,cAAc,IAAI,IAAI,KAAK,YAAY,CAAC,GAAG,CAAC,QAAQ;oBACpD,aAAa,IAAI,IAAI,KAAK,WAAW,CAAC,GAAG,CAAC,QAAQ;oBAClD,YAAY,IAAI,IAAI,KAAK,UAAU,CAAC,GAAG,CAAC,QAAQ;gBAClD,CAAC;YAED,8CAA8C;YAC9C,MAAM,eAAe,gBAAgB,eAAe,CAAC;YACrD,IAAI,gBAAgB,WAAW,UAAU,GAAG,IAAI;gBAC9C,MAAM,SAAS,YAAY,mBAAmB,CAC5C,QACA,aAAa,KAAK,EAClB,YACA,UAAU,IAAI;gBAGhB,IAAI,QAAQ;oBACV,iBAAiB,CAAA,OAAQ,CAAC;4BACxB,GAAG,IAAI;4BACP,SAAS;gCAAC;mCAAW,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG;6BAAG,CAAC,uBAAuB;wBACxE,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,sBAA4B,OAAP,QAAO,MAAI;QACjD;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAmB,MAAM;QAAU;QAC5D;YAAE,IAAI;YAAS,OAAO;YAAiB,MAAM;QAAW;QACxD;YAAE,IAAI;YAAW,OAAO;YAAiB,MAAM;QAAS;QACxD;YAAE,IAAI;YAAQ,OAAO;YAAmB,MAAM;QAAO;QACrD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM;QAAK;QAC5C;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAS;KACrD;IAED,MAAM,eAAe,cAAc,MAAM,CAAC,GAAG,CAAC;IAC9C,MAAM,qBAAqB,cAAc,YAAY,CAAC,GAAG,CAAC;IAC1D,MAAM,oBAAoB,cAAc,WAAW,CAAC,GAAG,CAAC;IACxD,MAAM,oBAAoB,cAAc,UAAU,CAAC,GAAG,CAAC;IAEvD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;;;;;;kDAInE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,wBAA8D,OAAvC,SAAS,iBAAiB;;;;;;0DAClE,6LAAC;gDAAK,WAAU;0DACb,SAAS,SAAS;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;gDAAoB,OAAO;0DAAS;+CAAxB;;;;;;;;;;oCAIhB,8BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,aAAa,KAAK,CAAC,OAAO,CAAC;;;;;;0DAE9B,6LAAC;gDAAK,WAAW,AAAC,WAAuE,OAA7D,aAAa,MAAM,IAAI,IAAI,mBAAmB;;oDACvE,aAAa,MAAM,IAAI,IAAI,MAAM;oDAAI,aAAa,aAAa,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU1F,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA;4BACR,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,AAAC,wEAIX,OAHC,cAAc,IAAI,EAAE,GAChB,qDACA;;kDAGN,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,IAAI,KAAK;;;;;;;+BATX,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAK,WAAU;;oBACb,cAAc,4BACb,6LAAC,uIAAA,CAAA,UAAc;wBACb,SAAS;wBACT,QAAQ,cAAc,MAAM;wBAC5B,cAAc,cAAc,YAAY;wBACxC,aAAa,cAAc,WAAW;wBACtC,SAAS,cAAc,OAAO;;;;;;oBAIjC,cAAc,yBACb,6LAAC,qIAAA,CAAA,UAAY;wBACX,QAAQ;wBACR,iBAAiB;wBACjB,YAAY;wBACZ,aAAa;wBACb,YAAY;;;;;;oBAIf,cAAc,2BACb,6LAAC,oIAAA,CAAA,UAAW;wBACV,SAAS,cAAc,OAAO;wBAC9B,gBAAgB;wBAChB,cAAc;wBACd,aAAa;;;;;;oBAIhB,cAAc,wBACb,6LAAC,kIAAA,CAAA,UAAS;wBACR,aAAa;wBACb,SAAS,cAAc,OAAO;wBAC9B,QAAQ,cAAc,MAAM;;;;;;oBAI/B,cAAc,0BACb,6LAAC,mIAAA,CAAA,UAAU;wBACT,aAAa;wBACb,SAAS,cAAc,OAAO;;;;;;oBAIjC,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;kEAAO;;;;;;kEACR,6LAAC;kEAAO;;;;;;;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;kEAAO;;;;;;kEACR,6LAAC;kEAAO;;;;;;kEACR,6LAAC;kEAAO;;;;;;;;;;;;;;;;;;kDAIZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,cAAa;gDACb,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAIV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,cAAc;gDACd,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAAgD;;;;;;;;;;;;kDAK3F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAY,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/F,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAiB,cAAc,OAAO,CAAC,MAAM;;;;;;;kDACnD,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAK;4CAAiB,CAAA,+BAAA,yCAAA,mBAAoB,OAAO,CAAC,IAAI,KAAI;;;;;;;kDAC3D,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAK;4CAAc,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;0CAEnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;GA3TM;KAAA;uCA6TS", "debugId": null}}, {"offset": {"line": 5251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/data-providers/tradingview.ts"], "sourcesContent": ["// TradingView Data Provider and WebSocket Connection\nimport WebSocket from 'ws';\nimport { CandleData } from '../technical-analysis/indicators';\n\nexport interface TradingViewConfig {\n  symbols: string[];\n  timeframes: string[];\n  apiKey?: string;\n  webhookUrl?: string;\n}\n\nexport interface TradingViewSymbol {\n  symbol: string;\n  exchange: string;\n  type: 'forex' | 'crypto' | 'stock' | 'commodity';\n  description: string;\n  currency: string;\n  session: string;\n  timezone: string;\n}\n\nexport interface RealTimeQuote {\n  symbol: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  timestamp: number;\n  bid: number;\n  ask: number;\n  spread: number;\n}\n\nexport interface TradingViewChart {\n  symbol: string;\n  timeframe: string;\n  data: CandleData[];\n  lastUpdate: number;\n}\n\nexport class TradingViewProvider {\n  private config: TradingViewConfig;\n  private ws: WebSocket | null = null;\n  private subscriptions: Map<string, boolean> = new Map();\n  private dataCache: Map<string, TradingViewChart> = new Map();\n  private quoteCache: Map<string, RealTimeQuote> = new Map();\n  private eventListeners: Map<string, Function[]> = new Map();\n\n  // Major currency pairs and symbols\n  private readonly MAJOR_PAIRS = [\n    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'\n  ];\n\n  private readonly MINOR_PAIRS = [\n    'EURJPY', 'GBPJPY', 'EURGBP', 'EURAUD', 'EURCHF', 'AUDCAD', 'GBPAUD'\n  ];\n\n  private readonly EXOTIC_PAIRS = [\n    'USDTRY', 'USDZAR', 'USDMXN', 'USDSEK', 'USDNOK', 'USDPLN', 'USDHUF'\n  ];\n\n  private readonly COMMODITIES = [\n    'XAUUSD', 'XAGUSD', 'USOIL', 'UKOIL', 'NATGAS'\n  ];\n\n  constructor(config: TradingViewConfig) {\n    this.config = config;\n    this.initializeEventListeners();\n  }\n\n  private initializeEventListeners(): void {\n    this.eventListeners.set('quote', []);\n    this.eventListeners.set('candle', []);\n    this.eventListeners.set('error', []);\n    this.eventListeners.set('connected', []);\n    this.eventListeners.set('disconnected', []);\n  }\n\n  // Connect to TradingView WebSocket (simulated real-time data)\n  async connect(): Promise<void> {\n    try {\n      // Since TradingView doesn't provide direct WebSocket API, we'll simulate real-time data\n      // In production, you would use a proper data provider like Alpha Vantage, IEX Cloud, etc.\n      this.simulateRealTimeConnection();\n      this.emit('connected', { timestamp: Date.now() });\n    } catch (error) {\n      this.emit('error', { error: error.message, timestamp: Date.now() });\n      throw error;\n    }\n  }\n\n  private simulateRealTimeConnection(): void {\n    // Simulate WebSocket connection with periodic updates\n    setInterval(() => {\n      this.config.symbols.forEach(symbol => {\n        if (this.subscriptions.get(symbol)) {\n          this.generateSimulatedQuote(symbol);\n        }\n      });\n    }, 1000); // Update every second\n\n    // Simulate candle updates every minute\n    setInterval(() => {\n      this.config.symbols.forEach(symbol => {\n        if (this.subscriptions.get(symbol)) {\n          this.updateCandleData(symbol);\n        }\n      });\n    }, 60000); // Update every minute\n  }\n\n  private generateSimulatedQuote(symbol: string): void {\n    const lastQuote = this.quoteCache.get(symbol);\n    const basePrice = lastQuote?.price || this.getBasePrice(symbol);\n    \n    // Generate realistic price movement\n    const volatility = this.getSymbolVolatility(symbol);\n    const randomChange = (Math.random() - 0.5) * volatility * basePrice;\n    const newPrice = Math.max(basePrice + randomChange, basePrice * 0.95); // Prevent negative prices\n    \n    const change = newPrice - basePrice;\n    const changePercent = (change / basePrice) * 100;\n    \n    const quote: RealTimeQuote = {\n      symbol,\n      price: Number(newPrice.toFixed(this.getDecimalPlaces(symbol))),\n      change: Number(change.toFixed(this.getDecimalPlaces(symbol))),\n      changePercent: Number(changePercent.toFixed(2)),\n      volume: Math.floor(Math.random() * 1000000) + 100000,\n      timestamp: Date.now(),\n      bid: Number((newPrice - this.getSpread(symbol) / 2).toFixed(this.getDecimalPlaces(symbol))),\n      ask: Number((newPrice + this.getSpread(symbol) / 2).toFixed(this.getDecimalPlaces(symbol))),\n      spread: this.getSpread(symbol)\n    };\n\n    this.quoteCache.set(symbol, quote);\n    this.emit('quote', quote);\n  }\n\n  private updateCandleData(symbol: string): void {\n    const chart = this.dataCache.get(symbol);\n    if (!chart) return;\n\n    const lastCandle = chart.data[chart.data.length - 1];\n    const currentQuote = this.quoteCache.get(symbol);\n    \n    if (!currentQuote) return;\n\n    const now = Date.now();\n    const candleTime = Math.floor(now / 60000) * 60000; // Round to minute\n\n    // Check if we need a new candle or update existing one\n    if (lastCandle && lastCandle.timestamp === candleTime) {\n      // Update existing candle\n      lastCandle.close = currentQuote.price;\n      lastCandle.high = Math.max(lastCandle.high, currentQuote.price);\n      lastCandle.low = Math.min(lastCandle.low, currentQuote.price);\n      lastCandle.volume += Math.floor(Math.random() * 10000);\n    } else {\n      // Create new candle\n      const newCandle: CandleData = {\n        open: lastCandle ? lastCandle.close : currentQuote.price,\n        high: currentQuote.price,\n        low: currentQuote.price,\n        close: currentQuote.price,\n        volume: Math.floor(Math.random() * 100000) + 10000,\n        timestamp: candleTime\n      };\n      \n      chart.data.push(newCandle);\n      \n      // Keep only last 1000 candles\n      if (chart.data.length > 1000) {\n        chart.data = chart.data.slice(-1000);\n      }\n    }\n\n    chart.lastUpdate = now;\n    this.emit('candle', { symbol, candle: chart.data[chart.data.length - 1] });\n  }\n\n  // Subscribe to symbol updates\n  async subscribe(symbol: string): Promise<void> {\n    this.subscriptions.set(symbol, true);\n    \n    // Initialize chart data if not exists\n    if (!this.dataCache.has(symbol)) {\n      const chartData = await this.getHistoricalData(symbol, '1m', 500);\n      this.dataCache.set(symbol, chartData);\n    }\n\n    // Generate initial quote\n    this.generateSimulatedQuote(symbol);\n  }\n\n  // Unsubscribe from symbol updates\n  unsubscribe(symbol: string): void {\n    this.subscriptions.set(symbol, false);\n  }\n\n  // Get historical data\n  async getHistoricalData(\n    symbol: string, \n    timeframe: string = '1m', \n    limit: number = 500\n  ): Promise<TradingViewChart> {\n    // Simulate historical data generation\n    const data: CandleData[] = [];\n    const basePrice = this.getBasePrice(symbol);\n    const volatility = this.getSymbolVolatility(symbol);\n    \n    const now = Date.now();\n    const timeframeMs = this.getTimeframeMs(timeframe);\n    \n    let currentPrice = basePrice;\n    \n    for (let i = limit; i >= 0; i--) {\n      const timestamp = now - (i * timeframeMs);\n      \n      // Generate realistic OHLCV data\n      const open = currentPrice;\n      const change = (Math.random() - 0.5) * volatility * open;\n      const close = Math.max(open + change, open * 0.95);\n      \n      const high = Math.max(open, close) + (Math.random() * volatility * open * 0.5);\n      const low = Math.min(open, close) - (Math.random() * volatility * open * 0.5);\n      \n      const volume = Math.floor(Math.random() * 1000000) + 50000;\n      \n      data.push({\n        open: Number(open.toFixed(this.getDecimalPlaces(symbol))),\n        high: Number(high.toFixed(this.getDecimalPlaces(symbol))),\n        low: Number(low.toFixed(this.getDecimalPlaces(symbol))),\n        close: Number(close.toFixed(this.getDecimalPlaces(symbol))),\n        volume,\n        timestamp\n      });\n      \n      currentPrice = close;\n    }\n\n    return {\n      symbol,\n      timeframe,\n      data,\n      lastUpdate: now\n    };\n  }\n\n  // Get current quote\n  getCurrentQuote(symbol: string): RealTimeQuote | null {\n    return this.quoteCache.get(symbol) || null;\n  }\n\n  // Get chart data\n  getChartData(symbol: string): TradingViewChart | null {\n    return this.dataCache.get(symbol) || null;\n  }\n\n  // Get all available symbols\n  getAllSymbols(): TradingViewSymbol[] {\n    const symbols: TradingViewSymbol[] = [];\n    \n    // Add major pairs\n    this.MAJOR_PAIRS.forEach(symbol => {\n      symbols.push({\n        symbol,\n        exchange: 'FX',\n        type: 'forex',\n        description: `${symbol.slice(0, 3)}/${symbol.slice(3, 6)}`,\n        currency: 'USD',\n        session: '24x7',\n        timezone: 'UTC'\n      });\n    });\n\n    // Add minor pairs\n    this.MINOR_PAIRS.forEach(symbol => {\n      symbols.push({\n        symbol,\n        exchange: 'FX',\n        type: 'forex',\n        description: `${symbol.slice(0, 3)}/${symbol.slice(3, 6)}`,\n        currency: 'USD',\n        session: '24x7',\n        timezone: 'UTC'\n      });\n    });\n\n    // Add exotic pairs\n    this.EXOTIC_PAIRS.forEach(symbol => {\n      symbols.push({\n        symbol,\n        exchange: 'FX',\n        type: 'forex',\n        description: `${symbol.slice(0, 3)}/${symbol.slice(3, 6)}`,\n        currency: 'USD',\n        session: '24x7',\n        timezone: 'UTC'\n      });\n    });\n\n    // Add commodities\n    this.COMMODITIES.forEach(symbol => {\n      symbols.push({\n        symbol,\n        exchange: 'COMEX',\n        type: 'commodity',\n        description: symbol === 'XAUUSD' ? 'Gold/USD' : \n                    symbol === 'XAGUSD' ? 'Silver/USD' : \n                    symbol === 'USOIL' ? 'Crude Oil' : \n                    symbol === 'UKOIL' ? 'Brent Oil' : 'Natural Gas',\n        currency: 'USD',\n        session: '24x5',\n        timezone: 'UTC'\n      });\n    });\n\n    return symbols;\n  }\n\n  // Event listener management\n  on(event: string, callback: Function): void {\n    if (!this.eventListeners.has(event)) {\n      this.eventListeners.set(event, []);\n    }\n    this.eventListeners.get(event)!.push(callback);\n  }\n\n  off(event: string, callback: Function): void {\n    const listeners = this.eventListeners.get(event);\n    if (listeners) {\n      const index = listeners.indexOf(callback);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    }\n  }\n\n  private emit(event: string, data: any): void {\n    const listeners = this.eventListeners.get(event);\n    if (listeners) {\n      listeners.forEach(callback => callback(data));\n    }\n  }\n\n  // Helper methods\n  private getBasePrice(symbol: string): number {\n    const basePrices: { [key: string]: number } = {\n      'EURUSD': 1.0850,\n      'GBPUSD': 1.2650,\n      'USDJPY': 149.50,\n      'USDCHF': 0.8950,\n      'AUDUSD': 0.6750,\n      'USDCAD': 1.3650,\n      'NZDUSD': 0.6150,\n      'XAUUSD': 2050.00,\n      'XAGUSD': 24.50,\n      'USOIL': 78.50,\n      'UKOIL': 82.00,\n      'NATGAS': 2.85\n    };\n    \n    return basePrices[symbol] || 1.0000;\n  }\n\n  private getSymbolVolatility(symbol: string): number {\n    const volatilities: { [key: string]: number } = {\n      'EURUSD': 0.001,\n      'GBPUSD': 0.0015,\n      'USDJPY': 0.002,\n      'USDCHF': 0.001,\n      'AUDUSD': 0.0012,\n      'USDCAD': 0.001,\n      'NZDUSD': 0.0015,\n      'XAUUSD': 0.01,\n      'XAGUSD': 0.02,\n      'USOIL': 0.02,\n      'UKOIL': 0.02,\n      'NATGAS': 0.03\n    };\n    \n    return volatilities[symbol] || 0.001;\n  }\n\n  private getDecimalPlaces(symbol: string): number {\n    if (symbol.includes('JPY')) return 3;\n    if (symbol.startsWith('XAU') || symbol.startsWith('XAG')) return 2;\n    if (symbol.includes('OIL') || symbol === 'NATGAS') return 2;\n    return 5;\n  }\n\n  private getSpread(symbol: string): number {\n    const spreads: { [key: string]: number } = {\n      'EURUSD': 0.00001,\n      'GBPUSD': 0.00002,\n      'USDJPY': 0.001,\n      'USDCHF': 0.00002,\n      'AUDUSD': 0.00002,\n      'USDCAD': 0.00002,\n      'NZDUSD': 0.00003,\n      'XAUUSD': 0.50,\n      'XAGUSD': 0.03,\n      'USOIL': 0.03,\n      'UKOIL': 0.03,\n      'NATGAS': 0.005\n    };\n    \n    return spreads[symbol] || 0.00002;\n  }\n\n  private getTimeframeMs(timeframe: string): number {\n    const timeframes: { [key: string]: number } = {\n      '1m': 60 * 1000,\n      '5m': 5 * 60 * 1000,\n      '15m': 15 * 60 * 1000,\n      '30m': 30 * 60 * 1000,\n      '1h': 60 * 60 * 1000,\n      '4h': 4 * 60 * 60 * 1000,\n      '1d': 24 * 60 * 60 * 1000\n    };\n    \n    return timeframes[timeframe] || 60 * 1000;\n  }\n\n  // Disconnect\n  disconnect(): void {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n    this.subscriptions.clear();\n    this.emit('disconnected', { timestamp: Date.now() });\n  }\n\n  // Get market status\n  getMarketStatus(symbol: string): 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'POST_MARKET' {\n    const now = new Date();\n    const utcHour = now.getUTCHours();\n    \n    // Forex market is open 24/5\n    if (symbol.length === 6 && !symbol.startsWith('XAU') && !symbol.startsWith('XAG')) {\n      const dayOfWeek = now.getUTCDay();\n      if (dayOfWeek === 0 || (dayOfWeek === 6 && utcHour >= 22)) {\n        return 'CLOSED'; // Weekend\n      }\n      if (dayOfWeek === 1 && utcHour < 1) {\n        return 'CLOSED'; // Monday before 1 AM UTC\n      }\n      return 'OPEN';\n    }\n    \n    // Commodities have different hours\n    if (utcHour >= 1 && utcHour < 23) {\n      return 'OPEN';\n    }\n    \n    return 'CLOSED';\n  }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;AAwC9C,MAAM;IA8BH,2BAAiC;QACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;QACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE;QACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;QACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE;QACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE;IAC5C;IAEA,8DAA8D;IAC9D,MAAM,UAAyB;QAC7B,IAAI;YACF,wFAAwF;YACxF,0FAA0F;YAC1F,IAAI,CAAC,0BAA0B;YAC/B,IAAI,CAAC,IAAI,CAAC,aAAa;gBAAE,WAAW,KAAK,GAAG;YAAG;QACjD,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,IAAI,CAAC,SAAS;gBAAE,OAAO,MAAM,OAAO;gBAAE,WAAW,KAAK,GAAG;YAAG;YACjE,MAAM;QACR;IACF;IAEQ,6BAAmC;QACzC,sDAAsD;QACtD,YAAY;YACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS;oBAClC,IAAI,CAAC,sBAAsB,CAAC;gBAC9B;YACF;QACF,GAAG,OAAO,sBAAsB;QAEhC,uCAAuC;QACvC,YAAY;YACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS;oBAClC,IAAI,CAAC,gBAAgB,CAAC;gBACxB;YACF;QACF,GAAG,QAAQ,sBAAsB;IACnC;IAEQ,uBAAuB,MAAc,EAAQ;QACnD,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACtC,MAAM,YAAY,CAAA,sBAAA,gCAAA,UAAW,KAAK,KAAI,IAAI,CAAC,YAAY,CAAC;QAExD,oCAAoC;QACpC,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC5C,MAAM,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;QAC1D,MAAM,WAAW,KAAK,GAAG,CAAC,YAAY,cAAc,YAAY,OAAO,0BAA0B;QAEjG,MAAM,SAAS,WAAW;QAC1B,MAAM,gBAAgB,AAAC,SAAS,YAAa;QAE7C,MAAM,QAAuB;YAC3B;YACA,OAAO,OAAO,SAAS,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACrD,QAAQ,OAAO,OAAO,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACpD,eAAe,OAAO,cAAc,OAAO,CAAC;YAC5C,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;YAC9C,WAAW,KAAK,GAAG;YACnB,KAAK,OAAO,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAClF,KAAK,OAAO,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAClF,QAAQ,IAAI,CAAC,SAAS,CAAC;QACzB;QAEA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS;IACrB;IAEQ,iBAAiB,MAAc,EAAQ;QAC7C,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACjC,IAAI,CAAC,OAAO;QAEZ,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,EAAE;QACpD,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAEzC,IAAI,CAAC,cAAc;QAEnB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,SAAS,OAAO,kBAAkB;QAEtE,uDAAuD;QACvD,IAAI,cAAc,WAAW,SAAS,KAAK,YAAY;YACrD,yBAAyB;YACzB,WAAW,KAAK,GAAG,aAAa,KAAK;YACrC,WAAW,IAAI,GAAG,KAAK,GAAG,CAAC,WAAW,IAAI,EAAE,aAAa,KAAK;YAC9D,WAAW,GAAG,GAAG,KAAK,GAAG,CAAC,WAAW,GAAG,EAAE,aAAa,KAAK;YAC5D,WAAW,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAClD,OAAO;YACL,oBAAoB;YACpB,MAAM,YAAwB;gBAC5B,MAAM,aAAa,WAAW,KAAK,GAAG,aAAa,KAAK;gBACxD,MAAM,aAAa,KAAK;gBACxB,KAAK,aAAa,KAAK;gBACvB,OAAO,aAAa,KAAK;gBACzB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU;gBAC7C,WAAW;YACb;YAEA,MAAM,IAAI,CAAC,IAAI,CAAC;YAEhB,8BAA8B;YAC9B,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM;gBAC5B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC;QACF;QAEA,MAAM,UAAU,GAAG;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE;YAAQ,QAAQ,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,EAAE;QAAC;IAC1E;IAEA,8BAA8B;IAC9B,MAAM,UAAU,MAAc,EAAiB;QAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ;QAE/B,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS;YAC/B,MAAM,YAAY,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,MAAM;YAC7D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ;QAC7B;QAEA,yBAAyB;QACzB,IAAI,CAAC,sBAAsB,CAAC;IAC9B;IAEA,kCAAkC;IAClC,YAAY,MAAc,EAAQ;QAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ;IACjC;IAEA,sBAAsB;IACtB,MAAM,kBACJ,MAAc,EAGa;YAF3B,YAAA,iEAAoB,MACpB,QAAA,iEAAgB;QAEhB,sCAAsC;QACtC,MAAM,OAAqB,EAAE;QAC7B,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;QACpC,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAE5C,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC;QAExC,IAAI,eAAe;QAEnB,IAAK,IAAI,IAAI,OAAO,KAAK,GAAG,IAAK;YAC/B,MAAM,YAAY,MAAO,IAAI;YAE7B,gCAAgC;YAChC,MAAM,OAAO;YACb,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;YACpD,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,QAAQ,OAAO;YAE7C,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAU,KAAK,MAAM,KAAK,aAAa,OAAO;YAC1E,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAU,KAAK,MAAM,KAAK,aAAa,OAAO;YAEzE,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW;YAErD,KAAK,IAAI,CAAC;gBACR,MAAM,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAChD,MAAM,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAChD,KAAK,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAC9C,OAAO,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAClD;gBACA;YACF;YAEA,eAAe;QACjB;QAEA,OAAO;YACL;YACA;YACA;YACA,YAAY;QACd;IACF;IAEA,oBAAoB;IACpB,gBAAgB,MAAc,EAAwB;QACpD,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW;IACxC;IAEA,iBAAiB;IACjB,aAAa,MAAc,EAA2B;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW;IACvC;IAEA,4BAA4B;IAC5B,gBAAqC;QACnC,MAAM,UAA+B,EAAE;QAEvC,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YACvB,QAAQ,IAAI,CAAC;gBACX;gBACA,UAAU;gBACV,MAAM;gBACN,aAAa,AAAC,GAAwB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAsB,OAAnB,OAAO,KAAK,CAAC,GAAG;gBACtD,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YACvB,QAAQ,IAAI,CAAC;gBACX;gBACA,UAAU;gBACV,MAAM;gBACN,aAAa,AAAC,GAAwB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAsB,OAAnB,OAAO,KAAK,CAAC,GAAG;gBACtD,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;YACxB,QAAQ,IAAI,CAAC;gBACX;gBACA,UAAU;gBACV,MAAM;gBACN,aAAa,AAAC,GAAwB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAsB,OAAnB,OAAO,KAAK,CAAC,GAAG;gBACtD,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,kBAAkB;QAClB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;YACvB,QAAQ,IAAI,CAAC;gBACX;gBACA,UAAU;gBACV,MAAM;gBACN,aAAa,WAAW,WAAW,aACvB,WAAW,WAAW,eACtB,WAAW,UAAU,cACrB,WAAW,UAAU,cAAc;gBAC/C,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,GAAG,KAAa,EAAE,QAAkB,EAAQ;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ;YACnC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE;QACnC;QACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAQ,IAAI,CAAC;IACvC;IAEA,IAAI,KAAa,EAAE,QAAkB,EAAQ;QAC3C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAC1C,IAAI,WAAW;YACb,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF;IAEQ,KAAK,KAAa,EAAE,IAAS,EAAQ;QAC3C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAC1C,IAAI,WAAW;YACb,UAAU,OAAO,CAAC,CAAA,WAAY,SAAS;QACzC;IACF;IAEA,iBAAiB;IACT,aAAa,MAAc,EAAU;QAC3C,MAAM,aAAwC;YAC5C,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;QACZ;QAEA,OAAO,UAAU,CAAC,OAAO,IAAI;IAC/B;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,eAA0C;YAC9C,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;QACZ;QAEA,OAAO,YAAY,CAAC,OAAO,IAAI;IACjC;IAEQ,iBAAiB,MAAc,EAAU;QAC/C,IAAI,OAAO,QAAQ,CAAC,QAAQ,OAAO;QACnC,IAAI,OAAO,UAAU,CAAC,UAAU,OAAO,UAAU,CAAC,QAAQ,OAAO;QACjE,IAAI,OAAO,QAAQ,CAAC,UAAU,WAAW,UAAU,OAAO;QAC1D,OAAO;IACT;IAEQ,UAAU,MAAc,EAAU;QACxC,MAAM,UAAqC;YACzC,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;QACZ;QAEA,OAAO,OAAO,CAAC,OAAO,IAAI;IAC5B;IAEQ,eAAe,SAAiB,EAAU;QAChD,MAAM,aAAwC;YAC5C,MAAM,KAAK;YACX,MAAM,IAAI,KAAK;YACf,OAAO,KAAK,KAAK;YACjB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,KAAK;YAChB,MAAM,IAAI,KAAK,KAAK;YACpB,MAAM,KAAK,KAAK,KAAK;QACvB;QAEA,OAAO,UAAU,CAAC,UAAU,IAAI,KAAK;IACvC;IAEA,aAAa;IACb,aAAmB;QACjB,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;QACA,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,WAAW,KAAK,GAAG;QAAG;IACpD;IAEA,oBAAoB;IACpB,gBAAgB,MAAc,EAAoD;QAChF,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,WAAW;QAE/B,4BAA4B;QAC5B,IAAI,OAAO,MAAM,KAAK,KAAK,CAAC,OAAO,UAAU,CAAC,UAAU,CAAC,OAAO,UAAU,CAAC,QAAQ;YACjF,MAAM,YAAY,IAAI,SAAS;YAC/B,IAAI,cAAc,KAAM,cAAc,KAAK,WAAW,IAAK;gBACzD,OAAO,UAAU,UAAU;YAC7B;YACA,IAAI,cAAc,KAAK,UAAU,GAAG;gBAClC,OAAO,UAAU,yBAAyB;YAC5C;YACA,OAAO;QACT;QAEA,mCAAmC;QACnC,IAAI,WAAW,KAAK,UAAU,IAAI;YAChC,OAAO;QACT;QAEA,OAAO;IACT;IAzYA,YAAY,MAAyB,CAAE;QAxBvC,+KAAQ,UAAR,KAAA;QACA,+KAAQ,MAAuB;QAC/B,+KAAQ,iBAAsC,IAAI;QAClD,+KAAQ,aAA2C,IAAI;QACvD,+KAAQ,cAAyC,IAAI;QACrD,+KAAQ,kBAA0C,IAAI;QAEtD,mCAAmC;QACnC,+KAAiB,eAAc;YAC7B;YAAU;YAAU;YAAU;YAAU;YAAU;YAAU;SAC7D;QAED,+KAAiB,eAAc;YAC7B;YAAU;YAAU;YAAU;YAAU;YAAU;YAAU;SAC7D;QAED,+KAAiB,gBAAe;YAC9B;YAAU;YAAU;YAAU;YAAU;YAAU;YAAU;SAC7D;QAED,+KAAiB,eAAc;YAC7B;YAAU;YAAU;YAAS;YAAS;SACvC;QAGC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,wBAAwB;IAC/B;AAuYF", "debugId": null}}, {"offset": {"line": 5651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/trading/risk-management.ts"], "sourcesContent": ["// Risk Management and Trade Management System\nimport { TechnicalIndicators, CandleData } from '../technical-analysis/indicators';\n\nexport interface TradeSignal {\n  id: string;\n  symbol: string;\n  type: 'BUY' | 'SELL';\n  entry: number;\n  stopLoss: number;\n  takeProfit1: number;\n  takeProfit2: number;\n  takeProfit3: number;\n  riskRewardRatio: number;\n  confidence: number;\n  timestamp: number;\n  reasoning: string[];\n  indicators: Partial<TechnicalIndicators>;\n}\n\nexport interface RiskParameters {\n  maxRiskPerTrade: number; // Percentage of account\n  maxDailyRisk: number; // Percentage of account\n  maxOpenTrades: number;\n  minRiskRewardRatio: number;\n  accountBalance: number;\n}\n\nexport interface TradeEntry {\n  price: number;\n  stopLoss: number;\n  takeProfit: number[];\n  positionSize: number;\n  riskAmount: number;\n}\n\nexport class RiskManagementEngine {\n  private riskParams: RiskParameters;\n  private activeTrades: TradeSignal[] = [];\n\n  constructor(riskParams: RiskParameters) {\n    this.riskParams = riskParams;\n  }\n\n  // Calculate optimal position size based on risk\n  calculatePositionSize(entry: number, stopLoss: number, riskPercentage: number): number {\n    const riskAmount = this.riskParams.accountBalance * (riskPercentage / 100);\n    const riskPerUnit = Math.abs(entry - stopLoss);\n    \n    if (riskPerUnit === 0) return 0;\n    \n    return riskAmount / riskPerUnit;\n  }\n\n  // Calculate risk-reward ratio\n  calculateRiskRewardRatio(entry: number, stopLoss: number, takeProfit: number): number {\n    const risk = Math.abs(entry - stopLoss);\n    const reward = Math.abs(takeProfit - entry);\n    \n    if (risk === 0) return 0;\n    \n    return reward / risk;\n  }\n\n  // Generate trade signal based on technical analysis\n  generateTradeSignal(\n    symbol: string,\n    currentPrice: number,\n    indicators: TechnicalIndicators,\n    candleData: CandleData[]\n  ): TradeSignal | null {\n    const analysis = this.analyzeMarketConditions(indicators, candleData);\n    \n    if (!analysis.shouldTrade) {\n      return null;\n    }\n\n    const signal = this.createTradeSignal(\n      symbol,\n      currentPrice,\n      analysis,\n      indicators\n    );\n\n    // Validate signal against risk parameters\n    if (this.validateSignal(signal)) {\n      return signal;\n    }\n\n    return null;\n  }\n\n  private analyzeMarketConditions(indicators: TechnicalIndicators, candleData: CandleData[]) {\n    const reasoning: string[] = [];\n    let bullishSignals = 0;\n    let bearishSignals = 0;\n    let confidence = 0;\n\n    // RSI Analysis\n    if (indicators.rsi < 30) {\n      bullishSignals++;\n      reasoning.push('RSI oversold (< 30)');\n    } else if (indicators.rsi > 70) {\n      bearishSignals++;\n      reasoning.push('RSI overbought (> 70)');\n    }\n\n    // MACD Analysis\n    if (indicators.macd.MACD > indicators.macd.signal && indicators.macd.histogram > 0) {\n      bullishSignals++;\n      reasoning.push('MACD bullish crossover');\n    } else if (indicators.macd.MACD < indicators.macd.signal && indicators.macd.histogram < 0) {\n      bearishSignals++;\n      reasoning.push('MACD bearish crossover');\n    }\n\n    // EMA Analysis\n    const currentPrice = candleData[candleData.length - 1].close;\n    const ema = indicators.ema[indicators.ema.length - 1];\n    \n    if (currentPrice > ema) {\n      bullishSignals++;\n      reasoning.push('Price above EMA');\n    } else {\n      bearishSignals++;\n      reasoning.push('Price below EMA');\n    }\n\n    // Supertrend Analysis\n    if (indicators.supertrend.trend === 'up') {\n      bullishSignals++;\n      reasoning.push('Supertrend bullish');\n    } else {\n      bearishSignals++;\n      reasoning.push('Supertrend bearish');\n    }\n\n    // VWAP Analysis\n    if (currentPrice > indicators.vwap) {\n      bullishSignals++;\n      reasoning.push('Price above VWAP');\n    } else {\n      bearishSignals++;\n      reasoning.push('Price below VWAP');\n    }\n\n    // Volume Profile Analysis\n    const poc = indicators.volumeProfile.poc;\n    if (currentPrice > poc) {\n      bullishSignals++;\n      reasoning.push('Price above POC');\n    } else {\n      bearishSignals++;\n      reasoning.push('Price below POC');\n    }\n\n    // Fair Value Gap Analysis\n    const activeFVGs = indicators.fvg.filter(gap => !gap.filled);\n    if (activeFVGs.length > 0) {\n      const nearestFVG = activeFVGs.reduce((nearest, gap) => \n        Math.abs(gap.price - currentPrice) < Math.abs(nearest.price - currentPrice) ? gap : nearest\n      );\n      \n      if (nearestFVG.type === 'bullish' && currentPrice < nearestFVG.price) {\n        bullishSignals++;\n        reasoning.push('Approaching bullish FVG');\n      } else if (nearestFVG.type === 'bearish' && currentPrice > nearestFVG.price) {\n        bearishSignals++;\n        reasoning.push('Approaching bearish FVG');\n      }\n    }\n\n    // Change of Character Analysis\n    const recentCHoCH = indicators.choch.slice(-3);\n    if (recentCHoCH.length > 0) {\n      const latestCHoCH = recentCHoCH[recentCHoCH.length - 1];\n      if (latestCHoCH.type === 'bullish') {\n        bullishSignals++;\n        reasoning.push('Recent bullish CHoCH');\n      } else {\n        bearishSignals++;\n        reasoning.push('Recent bearish CHoCH');\n      }\n    }\n\n    // Break of Structure Analysis\n    const recentBOS = indicators.bos.slice(-3);\n    if (recentBOS.length > 0) {\n      const latestBOS = recentBOS[recentBOS.length - 1];\n      if (latestBOS.type === 'bullish') {\n        bullishSignals++;\n        reasoning.push('Recent bullish BOS');\n      } else {\n        bearishSignals++;\n        reasoning.push('Recent bearish BOS');\n      }\n    }\n\n    // Support/Resistance Analysis\n    const nearestSupport = indicators.supportResistance\n      .filter(level => level.type === 'support' && level.price < currentPrice)\n      .sort((a, b) => Math.abs(a.price - currentPrice) - Math.abs(b.price - currentPrice))[0];\n    \n    const nearestResistance = indicators.supportResistance\n      .filter(level => level.type === 'resistance' && level.price > currentPrice)\n      .sort((a, b) => Math.abs(a.price - currentPrice) - Math.abs(b.price - currentPrice))[0];\n\n    // Calculate confidence based on signal strength\n    const totalSignals = bullishSignals + bearishSignals;\n    if (totalSignals > 0) {\n      if (bullishSignals > bearishSignals) {\n        confidence = (bullishSignals / totalSignals) * 100;\n      } else {\n        confidence = (bearishSignals / totalSignals) * 100;\n      }\n    }\n\n    const shouldTrade = Math.abs(bullishSignals - bearishSignals) >= 3 && confidence >= 60;\n    const direction = bullishSignals > bearishSignals ? 'BUY' : 'SELL';\n\n    return {\n      shouldTrade,\n      direction: direction as 'BUY' | 'SELL',\n      confidence,\n      reasoning,\n      nearestSupport,\n      nearestResistance,\n      bullishSignals,\n      bearishSignals\n    };\n  }\n\n  private createTradeSignal(\n    symbol: string,\n    currentPrice: number,\n    analysis: any,\n    indicators: TechnicalIndicators\n  ): TradeSignal {\n    const { direction, confidence, reasoning, nearestSupport, nearestResistance } = analysis;\n    \n    let entry = currentPrice;\n    let stopLoss: number;\n    let takeProfit1: number;\n    let takeProfit2: number;\n    let takeProfit3: number;\n\n    if (direction === 'BUY') {\n      // For buy signals\n      stopLoss = nearestSupport ? nearestSupport.price * 0.999 : currentPrice * 0.98; // 2% below or below support\n      \n      const riskDistance = entry - stopLoss;\n      takeProfit1 = entry + (riskDistance * 1.5); // 1.5:1 RR\n      takeProfit2 = entry + (riskDistance * 2.5); // 2.5:1 RR\n      takeProfit3 = entry + (riskDistance * 4.0); // 4:1 RR\n      \n      // Adjust TPs based on resistance levels\n      if (nearestResistance && takeProfit1 > nearestResistance.price) {\n        takeProfit1 = nearestResistance.price * 0.999;\n        takeProfit2 = nearestResistance.price * 1.005;\n        takeProfit3 = nearestResistance.price * 1.015;\n      }\n    } else {\n      // For sell signals\n      stopLoss = nearestResistance ? nearestResistance.price * 1.001 : currentPrice * 1.02; // 2% above or above resistance\n      \n      const riskDistance = stopLoss - entry;\n      takeProfit1 = entry - (riskDistance * 1.5); // 1.5:1 RR\n      takeProfit2 = entry - (riskDistance * 2.5); // 2.5:1 RR\n      takeProfit3 = entry - (riskDistance * 4.0); // 4:1 RR\n      \n      // Adjust TPs based on support levels\n      if (nearestSupport && takeProfit1 < nearestSupport.price) {\n        takeProfit1 = nearestSupport.price * 1.001;\n        takeProfit2 = nearestSupport.price * 0.995;\n        takeProfit3 = nearestSupport.price * 0.985;\n      }\n    }\n\n    const riskRewardRatio = this.calculateRiskRewardRatio(entry, stopLoss, takeProfit1);\n\n    return {\n      id: this.generateSignalId(),\n      symbol,\n      type: direction,\n      entry,\n      stopLoss,\n      takeProfit1,\n      takeProfit2,\n      takeProfit3,\n      riskRewardRatio,\n      confidence,\n      timestamp: Date.now(),\n      reasoning,\n      indicators\n    };\n  }\n\n  private validateSignal(signal: TradeSignal): boolean {\n    // Check minimum risk-reward ratio\n    if (signal.riskRewardRatio < this.riskParams.minRiskRewardRatio) {\n      return false;\n    }\n\n    // Check maximum open trades\n    if (this.activeTrades.length >= this.riskParams.maxOpenTrades) {\n      return false;\n    }\n\n    // Check confidence threshold\n    if (signal.confidence < 60) {\n      return false;\n    }\n\n    // Check if we already have a trade on this symbol\n    const existingTrade = this.activeTrades.find(trade => trade.symbol === signal.symbol);\n    if (existingTrade) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private generateSignalId(): string {\n    return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Calculate optimal entry points based on market structure\n  calculateOptimalEntry(\n    signal: TradeSignal,\n    indicators: TechnicalIndicators,\n    currentPrice: number\n  ): TradeEntry {\n    const positionSize = this.calculatePositionSize(\n      signal.entry,\n      signal.stopLoss,\n      this.riskParams.maxRiskPerTrade\n    );\n\n    const riskAmount = Math.abs(signal.entry - signal.stopLoss) * positionSize;\n\n    return {\n      price: signal.entry,\n      stopLoss: signal.stopLoss,\n      takeProfit: [signal.takeProfit1, signal.takeProfit2, signal.takeProfit3],\n      positionSize,\n      riskAmount\n    };\n  }\n\n  // Update risk parameters\n  updateRiskParameters(newParams: Partial<RiskParameters>): void {\n    this.riskParams = { ...this.riskParams, ...newParams };\n  }\n\n  // Add active trade\n  addActiveTrade(signal: TradeSignal): void {\n    this.activeTrades.push(signal);\n  }\n\n  // Remove active trade\n  removeActiveTrade(signalId: string): void {\n    this.activeTrades = this.activeTrades.filter(trade => trade.id !== signalId);\n  }\n\n  // Get active trades\n  getActiveTrades(): TradeSignal[] {\n    return [...this.activeTrades];\n  }\n\n  // Calculate total risk exposure\n  getTotalRiskExposure(): number {\n    return this.activeTrades.reduce((total, trade) => {\n      const riskAmount = Math.abs(trade.entry - trade.stopLoss);\n      return total + riskAmount;\n    }, 0);\n  }\n\n  // Check if new trade would exceed daily risk limit\n  canTakeNewTrade(potentialRisk: number): boolean {\n    const currentRisk = this.getTotalRiskExposure();\n    const maxDailyRiskAmount = this.riskParams.accountBalance * (this.riskParams.maxDailyRisk / 100);\n    \n    return (currentRisk + potentialRisk) <= maxDailyRiskAmount;\n  }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAmCvC,MAAM;IAQX,gDAAgD;IAChD,sBAAsB,KAAa,EAAE,QAAgB,EAAE,cAAsB,EAAU;QACrF,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,CAAC,iBAAiB,GAAG;QACzE,MAAM,cAAc,KAAK,GAAG,CAAC,QAAQ;QAErC,IAAI,gBAAgB,GAAG,OAAO;QAE9B,OAAO,aAAa;IACtB;IAEA,8BAA8B;IAC9B,yBAAyB,KAAa,EAAE,QAAgB,EAAE,UAAkB,EAAU;QACpF,MAAM,OAAO,KAAK,GAAG,CAAC,QAAQ;QAC9B,MAAM,SAAS,KAAK,GAAG,CAAC,aAAa;QAErC,IAAI,SAAS,GAAG,OAAO;QAEvB,OAAO,SAAS;IAClB;IAEA,oDAAoD;IACpD,oBACE,MAAc,EACd,YAAoB,EACpB,UAA+B,EAC/B,UAAwB,EACJ;QACpB,MAAM,WAAW,IAAI,CAAC,uBAAuB,CAAC,YAAY;QAE1D,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,OAAO;QACT;QAEA,MAAM,SAAS,IAAI,CAAC,iBAAiB,CACnC,QACA,cACA,UACA;QAGF,0CAA0C;QAC1C,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;YAC/B,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,wBAAwB,UAA+B,EAAE,UAAwB,EAAE;QACzF,MAAM,YAAsB,EAAE;QAC9B,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,aAAa;QAEjB,eAAe;QACf,IAAI,WAAW,GAAG,GAAG,IAAI;YACvB;YACA,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,WAAW,GAAG,GAAG,IAAI;YAC9B;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,gBAAgB;QAChB,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,IAAI,WAAW,IAAI,CAAC,SAAS,GAAG,GAAG;YAClF;YACA,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,IAAI,WAAW,IAAI,CAAC,SAAS,GAAG,GAAG;YACzF;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,eAAe;QACf,MAAM,eAAe,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,KAAK;QAC5D,MAAM,MAAM,WAAW,GAAG,CAAC,WAAW,GAAG,CAAC,MAAM,GAAG,EAAE;QAErD,IAAI,eAAe,KAAK;YACtB;YACA,UAAU,IAAI,CAAC;QACjB,OAAO;YACL;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,sBAAsB;QACtB,IAAI,WAAW,UAAU,CAAC,KAAK,KAAK,MAAM;YACxC;YACA,UAAU,IAAI,CAAC;QACjB,OAAO;YACL;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,gBAAgB;QAChB,IAAI,eAAe,WAAW,IAAI,EAAE;YAClC;YACA,UAAU,IAAI,CAAC;QACjB,OAAO;YACL;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,0BAA0B;QAC1B,MAAM,MAAM,WAAW,aAAa,CAAC,GAAG;QACxC,IAAI,eAAe,KAAK;YACtB;YACA,UAAU,IAAI,CAAC;QACjB,OAAO;YACL;YACA,UAAU,IAAI,CAAC;QACjB;QAEA,0BAA0B;QAC1B,MAAM,aAAa,WAAW,GAAG,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,MAAM;QAC3D,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,SAAS,MAC7C,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,gBAAgB,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,gBAAgB,MAAM;YAGtF,IAAI,WAAW,IAAI,KAAK,aAAa,eAAe,WAAW,KAAK,EAAE;gBACpE;gBACA,UAAU,IAAI,CAAC;YACjB,OAAO,IAAI,WAAW,IAAI,KAAK,aAAa,eAAe,WAAW,KAAK,EAAE;gBAC3E;gBACA,UAAU,IAAI,CAAC;YACjB;QACF;QAEA,+BAA+B;QAC/B,MAAM,cAAc,WAAW,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,MAAM,cAAc,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;YACvD,IAAI,YAAY,IAAI,KAAK,WAAW;gBAClC;gBACA,UAAU,IAAI,CAAC;YACjB,OAAO;gBACL;gBACA,UAAU,IAAI,CAAC;YACjB;QACF;QAEA,8BAA8B;QAC9B,MAAM,YAAY,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAM,YAAY,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE;YACjD,IAAI,UAAU,IAAI,KAAK,WAAW;gBAChC;gBACA,UAAU,IAAI,CAAC;YACjB,OAAO;gBACL;gBACA,UAAU,IAAI,CAAC;YACjB;QACF;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,WAAW,iBAAiB,CAChD,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,aAAa,MAAM,KAAK,GAAG,cAC1D,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,EAAE;QAEzF,MAAM,oBAAoB,WAAW,iBAAiB,CACnD,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,gBAAgB,MAAM,KAAK,GAAG,cAC7D,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,EAAE;QAEzF,gDAAgD;QAChD,MAAM,eAAe,iBAAiB;QACtC,IAAI,eAAe,GAAG;YACpB,IAAI,iBAAiB,gBAAgB;gBACnC,aAAa,AAAC,iBAAiB,eAAgB;YACjD,OAAO;gBACL,aAAa,AAAC,iBAAiB,eAAgB;YACjD;QACF;QAEA,MAAM,cAAc,KAAK,GAAG,CAAC,iBAAiB,mBAAmB,KAAK,cAAc;QACpF,MAAM,YAAY,iBAAiB,iBAAiB,QAAQ;QAE5D,OAAO;YACL;YACA,WAAW;YACX;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEQ,kBACN,MAAc,EACd,YAAoB,EACpB,QAAa,EACb,UAA+B,EAClB;QACb,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG;QAEhF,IAAI,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,cAAc,OAAO;YACvB,kBAAkB;YAClB,WAAW,iBAAiB,eAAe,KAAK,GAAG,QAAQ,eAAe,MAAM,4BAA4B;YAE5G,MAAM,eAAe,QAAQ;YAC7B,cAAc,QAAS,eAAe,KAAM,WAAW;YACvD,cAAc,QAAS,eAAe,KAAM,WAAW;YACvD,cAAc,QAAS,eAAe,KAAM,SAAS;YAErD,wCAAwC;YACxC,IAAI,qBAAqB,cAAc,kBAAkB,KAAK,EAAE;gBAC9D,cAAc,kBAAkB,KAAK,GAAG;gBACxC,cAAc,kBAAkB,KAAK,GAAG;gBACxC,cAAc,kBAAkB,KAAK,GAAG;YAC1C;QACF,OAAO;YACL,mBAAmB;YACnB,WAAW,oBAAoB,kBAAkB,KAAK,GAAG,QAAQ,eAAe,MAAM,+BAA+B;YAErH,MAAM,eAAe,WAAW;YAChC,cAAc,QAAS,eAAe,KAAM,WAAW;YACvD,cAAc,QAAS,eAAe,KAAM,WAAW;YACvD,cAAc,QAAS,eAAe,KAAM,SAAS;YAErD,qCAAqC;YACrC,IAAI,kBAAkB,cAAc,eAAe,KAAK,EAAE;gBACxD,cAAc,eAAe,KAAK,GAAG;gBACrC,cAAc,eAAe,KAAK,GAAG;gBACrC,cAAc,eAAe,KAAK,GAAG;YACvC;QACF;QAEA,MAAM,kBAAkB,IAAI,CAAC,wBAAwB,CAAC,OAAO,UAAU;QAEvE,OAAO;YACL,IAAI,IAAI,CAAC,gBAAgB;YACzB;YACA,MAAM;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA,WAAW,KAAK,GAAG;YACnB;YACA;QACF;IACF;IAEQ,eAAe,MAAmB,EAAW;QACnD,kCAAkC;QAClC,IAAI,OAAO,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE;YAC/D,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;YAC7D,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,OAAO,UAAU,GAAG,IAAI;YAC1B,OAAO;QACT;QAEA,kDAAkD;QAClD,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,OAAO,MAAM;QACpF,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,mBAA2B;QACjC,OAAO,AAAC,UAAuB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACtE;IAEA,2DAA2D;IAC3D,sBACE,MAAmB,EACnB,UAA+B,EAC/B,YAAoB,EACR;QACZ,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAC7C,OAAO,KAAK,EACZ,OAAO,QAAQ,EACf,IAAI,CAAC,UAAU,CAAC,eAAe;QAGjC,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,QAAQ,IAAI;QAE9D,OAAO;YACL,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,QAAQ;YACzB,YAAY;gBAAC,OAAO,WAAW;gBAAE,OAAO,WAAW;gBAAE,OAAO,WAAW;aAAC;YACxE;YACA;QACF;IACF;IAEA,yBAAyB;IACzB,qBAAqB,SAAkC,EAAQ;QAC7D,IAAI,CAAC,UAAU,GAAG;YAAE,GAAG,IAAI,CAAC,UAAU;YAAE,GAAG,SAAS;QAAC;IACvD;IAEA,mBAAmB;IACnB,eAAe,MAAmB,EAAQ;QACxC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IACzB;IAEA,sBAAsB;IACtB,kBAAkB,QAAgB,EAAQ;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACrE;IAEA,oBAAoB;IACpB,kBAAiC;QAC/B,OAAO;eAAI,IAAI,CAAC,YAAY;SAAC;IAC/B;IAEA,gCAAgC;IAChC,uBAA+B;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO;YACtC,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,QAAQ;YACxD,OAAO,QAAQ;QACjB,GAAG;IACL;IAEA,mDAAmD;IACnD,gBAAgB,aAAqB,EAAW;QAC9C,MAAM,cAAc,IAAI,CAAC,oBAAoB;QAC7C,MAAM,qBAAqB,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,GAAG;QAE/F,OAAO,AAAC,cAAc,iBAAkB;IAC1C;IAvVA,YAAY,UAA0B,CAAE;QAHxC,+KAAQ,cAAR,KAAA;QACA,+KAAQ,gBAA8B,EAAE;QAGtC,IAAI,CAAC,UAAU,GAAG;IACpB;AAsVF", "debugId": null}}, {"offset": {"line": 5941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/market-analysis/market-state.ts"], "sourcesContent": ["// Market State Analysis System\nimport { TechnicalIndicators, CandleData } from '../technical-analysis/indicators';\n\nexport interface MarketState {\n  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';\n  strength: number; // 0-100\n  volatility: 'LOW' | 'MEDIUM' | 'HIGH';\n  liquidity: 'LOW' | 'MEDIUM' | 'HIGH';\n  momentum: 'STRONG_BULLISH' | 'WEAK_BULLISH' | 'NEUTRAL' | 'WEAK_BEARISH' | 'STRONG_BEARISH';\n  session: TradingSession;\n  marketPhase: 'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN';\n  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';\n  confidence: number; // 0-100\n}\n\nexport interface TradingSession {\n  name: 'ASIAN' | 'LONDON' | 'NEW_YORK' | 'OVERLAP_LONDON_NY' | 'OVERLAP_ASIAN_LONDON';\n  isActive: boolean;\n  timeRemaining: number; // minutes\n  volatilityExpected: 'LOW' | 'MEDIUM' | 'HIGH';\n  liquidityExpected: 'LOW' | 'MEDIUM' | 'HIGH';\n}\n\nexport interface MarketStructure {\n  higherHighs: boolean;\n  higherLows: boolean;\n  lowerHighs: boolean;\n  lowerLows: boolean;\n  keyLevels: {\n    support: number[];\n    resistance: number[];\n  };\n  trendLines: {\n    support: TrendLine[];\n    resistance: TrendLine[];\n  };\n}\n\nexport interface TrendLine {\n  startPrice: number;\n  endPrice: number;\n  startTime: number;\n  endTime: number;\n  slope: number;\n  strength: number;\n  touches: number;\n}\n\nexport class MarketStateAnalyzer {\n  private timeZone: string;\n\n  constructor(timeZone: string = 'UTC') {\n    this.timeZone = timeZone;\n  }\n\n  // Analyze complete market state\n  analyzeMarketState(\n    candleData: CandleData[],\n    indicators: TechnicalIndicators,\n    symbol: string\n  ): MarketState {\n    const trend = this.analyzeTrend(candleData, indicators);\n    const volatility = this.analyzeVolatility(candleData);\n    const liquidity = this.analyzeLiquidity(candleData, indicators);\n    const momentum = this.analyzeMomentum(indicators, candleData);\n    const session = this.getCurrentTradingSession(symbol);\n    const marketPhase = this.analyzeMarketPhase(candleData, indicators);\n    const riskLevel = this.calculateRiskLevel(volatility, liquidity, trend);\n    const confidence = this.calculateConfidence(trend, indicators, candleData);\n\n    return {\n      trend: trend.direction,\n      strength: trend.strength,\n      volatility,\n      liquidity,\n      momentum,\n      session,\n      marketPhase,\n      riskLevel,\n      confidence\n    };\n  }\n\n  // Analyze market trend\n  private analyzeTrend(candleData: CandleData[], indicators: TechnicalIndicators) {\n    const recentCandles = candleData.slice(-50); // Last 50 candles\n    const structure = this.analyzeMarketStructure(recentCandles);\n    \n    let bullishSignals = 0;\n    let bearishSignals = 0;\n    let strength = 0;\n\n    // EMA trend analysis\n    const ema = indicators.ema;\n    if (ema.length >= 2) {\n      const emaSlope = ema[ema.length - 1] - ema[ema.length - 2];\n      if (emaSlope > 0) bullishSignals++;\n      else bearishSignals++;\n    }\n\n    // Price vs EMA\n    const currentPrice = recentCandles[recentCandles.length - 1].close;\n    const currentEMA = ema[ema.length - 1];\n    if (currentPrice > currentEMA) bullishSignals++;\n    else bearishSignals++;\n\n    // MACD trend\n    if (indicators.macd.MACD > indicators.macd.signal) bullishSignals++;\n    else bearishSignals++;\n\n    // Supertrend\n    if (indicators.supertrend.trend === 'up') bullishSignals++;\n    else bearishSignals++;\n\n    // Market structure\n    if (structure.higherHighs && structure.higherLows) bullishSignals += 2;\n    if (structure.lowerHighs && structure.lowerLows) bearishSignals += 2;\n\n    // Volume confirmation\n    if (currentPrice > indicators.vwap) bullishSignals++;\n    else bearishSignals++;\n\n    // Calculate trend direction and strength\n    const totalSignals = bullishSignals + bearishSignals;\n    let direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';\n    \n    if (Math.abs(bullishSignals - bearishSignals) <= 1) {\n      direction = 'SIDEWAYS';\n      strength = 30; // Low strength for sideways\n    } else if (bullishSignals > bearishSignals) {\n      direction = 'BULLISH';\n      strength = Math.min((bullishSignals / totalSignals) * 100, 100);\n    } else {\n      direction = 'BEARISH';\n      strength = Math.min((bearishSignals / totalSignals) * 100, 100);\n    }\n\n    return { direction, strength };\n  }\n\n  // Analyze market structure\n  private analyzeMarketStructure(candleData: CandleData[]): MarketStructure {\n    const swingHighs: number[] = [];\n    const swingLows: number[] = [];\n\n    // Find swing highs and lows\n    for (let i = 2; i < candleData.length - 2; i++) {\n      const current = candleData[i];\n      const prev2 = candleData[i - 2];\n      const prev1 = candleData[i - 1];\n      const next1 = candleData[i + 1];\n      const next2 = candleData[i + 2];\n\n      // Swing high\n      if (current.high > prev2.high && current.high > prev1.high &&\n          current.high > next1.high && current.high > next2.high) {\n        swingHighs.push(current.high);\n      }\n\n      // Swing low\n      if (current.low < prev2.low && current.low < prev1.low &&\n          current.low < next1.low && current.low < next2.low) {\n        swingLows.push(current.low);\n      }\n    }\n\n    // Analyze structure\n    const higherHighs = this.isSequenceIncreasing(swingHighs.slice(-3));\n    const higherLows = this.isSequenceIncreasing(swingLows.slice(-3));\n    const lowerHighs = this.isSequenceDecreasing(swingHighs.slice(-3));\n    const lowerLows = this.isSequenceDecreasing(swingLows.slice(-3));\n\n    return {\n      higherHighs,\n      higherLows,\n      lowerHighs,\n      lowerLows,\n      keyLevels: {\n        support: swingLows.slice(-5),\n        resistance: swingHighs.slice(-5)\n      },\n      trendLines: {\n        support: [],\n        resistance: []\n      }\n    };\n  }\n\n  private isSequenceIncreasing(sequence: number[]): boolean {\n    if (sequence.length < 2) return false;\n    for (let i = 1; i < sequence.length; i++) {\n      if (sequence[i] <= sequence[i - 1]) return false;\n    }\n    return true;\n  }\n\n  private isSequenceDecreasing(sequence: number[]): boolean {\n    if (sequence.length < 2) return false;\n    for (let i = 1; i < sequence.length; i++) {\n      if (sequence[i] >= sequence[i - 1]) return false;\n    }\n    return true;\n  }\n\n  // Analyze volatility\n  private analyzeVolatility(candleData: CandleData[]): 'LOW' | 'MEDIUM' | 'HIGH' {\n    const recentCandles = candleData.slice(-20);\n    const ranges = recentCandles.map(candle => \n      ((candle.high - candle.low) / candle.close) * 100\n    );\n    \n    const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length;\n    \n    if (avgRange < 0.5) return 'LOW';\n    if (avgRange < 1.5) return 'MEDIUM';\n    return 'HIGH';\n  }\n\n  // Analyze liquidity\n  private analyzeLiquidity(candleData: CandleData[], indicators: TechnicalIndicators): 'LOW' | 'MEDIUM' | 'HIGH' {\n    const recentCandles = candleData.slice(-20);\n    const avgVolume = recentCandles.reduce((sum, candle) => sum + candle.volume, 0) / recentCandles.length;\n    const currentVolume = recentCandles[recentCandles.length - 1].volume;\n    \n    const volumeRatio = currentVolume / avgVolume;\n    \n    // Also consider volume profile\n    const volumeProfile = indicators.volumeProfile;\n    const pocStrength = volumeProfile.levels.length > 0 ? \n      Math.max(...volumeProfile.levels.map(l => l.percentage)) : 0;\n    \n    const liquidityScore = (volumeRatio * 0.7) + (pocStrength / 100 * 0.3);\n    \n    if (liquidityScore < 0.8) return 'LOW';\n    if (liquidityScore < 1.5) return 'MEDIUM';\n    return 'HIGH';\n  }\n\n  // Analyze momentum\n  private analyzeMomentum(indicators: TechnicalIndicators, candleData: CandleData[]): \n    'STRONG_BULLISH' | 'WEAK_BULLISH' | 'NEUTRAL' | 'WEAK_BEARISH' | 'STRONG_BEARISH' {\n    \n    let momentumScore = 0;\n    \n    // RSI momentum\n    if (indicators.rsi > 60) momentumScore += 2;\n    else if (indicators.rsi > 50) momentumScore += 1;\n    else if (indicators.rsi < 40) momentumScore -= 2;\n    else if (indicators.rsi < 50) momentumScore -= 1;\n    \n    // MACD momentum\n    if (indicators.macd.histogram > 0) {\n      momentumScore += indicators.macd.histogram > 0.001 ? 2 : 1;\n    } else {\n      momentumScore += indicators.macd.histogram < -0.001 ? -2 : -1;\n    }\n    \n    // Price momentum (last 5 candles)\n    const recentCandles = candleData.slice(-5);\n    const priceChange = (recentCandles[recentCandles.length - 1].close - recentCandles[0].close) / recentCandles[0].close;\n    \n    if (priceChange > 0.01) momentumScore += 2; // 1% gain\n    else if (priceChange > 0.005) momentumScore += 1; // 0.5% gain\n    else if (priceChange < -0.01) momentumScore -= 2; // 1% loss\n    else if (priceChange < -0.005) momentumScore -= 1; // 0.5% loss\n    \n    // Volume momentum\n    const recentVolumes = candleData.slice(-5).map(c => c.volume);\n    const avgRecentVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;\n    const prevAvgVolume = candleData.slice(-10, -5).map(c => c.volume).reduce((sum, vol) => sum + vol, 0) / 5;\n    \n    if (avgRecentVolume > prevAvgVolume * 1.2) momentumScore += 1;\n    else if (avgRecentVolume < prevAvgVolume * 0.8) momentumScore -= 1;\n    \n    // Classify momentum\n    if (momentumScore >= 5) return 'STRONG_BULLISH';\n    if (momentumScore >= 2) return 'WEAK_BULLISH';\n    if (momentumScore <= -5) return 'STRONG_BEARISH';\n    if (momentumScore <= -2) return 'WEAK_BEARISH';\n    return 'NEUTRAL';\n  }\n\n  // Get current trading session\n  getCurrentTradingSession(symbol: string): TradingSession {\n    const now = new Date();\n    const utcHour = now.getUTCHours();\n    const utcMinute = now.getUTCMinutes();\n    const currentMinutes = utcHour * 60 + utcMinute;\n\n    // Trading session times in UTC\n    const sessions = {\n      ASIAN: { start: 0, end: 9 * 60 }, // 00:00 - 09:00 UTC\n      LONDON: { start: 8 * 60, end: 17 * 60 }, // 08:00 - 17:00 UTC\n      NEW_YORK: { start: 13 * 60, end: 22 * 60 }, // 13:00 - 22:00 UTC\n    };\n\n    // Determine current session\n    let currentSession: TradingSession['name'] = 'ASIAN';\n    let isActive = false;\n    let timeRemaining = 0;\n    let volatilityExpected: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';\n    let liquidityExpected: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';\n\n    // Check overlaps first\n    if (currentMinutes >= sessions.LONDON.start && currentMinutes <= sessions.NEW_YORK.end) {\n      if (currentMinutes >= sessions.NEW_YORK.start && currentMinutes <= sessions.LONDON.end) {\n        currentSession = 'OVERLAP_LONDON_NY';\n        volatilityExpected = 'HIGH';\n        liquidityExpected = 'HIGH';\n        isActive = true;\n        timeRemaining = sessions.LONDON.end - currentMinutes;\n      } else if (currentMinutes >= sessions.LONDON.start && currentMinutes < sessions.NEW_YORK.start) {\n        currentSession = 'LONDON';\n        volatilityExpected = 'MEDIUM';\n        liquidityExpected = 'HIGH';\n        isActive = true;\n        timeRemaining = sessions.NEW_YORK.start - currentMinutes;\n      } else {\n        currentSession = 'NEW_YORK';\n        volatilityExpected = 'MEDIUM';\n        liquidityExpected = 'MEDIUM';\n        isActive = true;\n        timeRemaining = sessions.NEW_YORK.end - currentMinutes;\n      }\n    } else if (currentMinutes >= sessions.ASIAN.start && currentMinutes < sessions.LONDON.start) {\n      if (currentMinutes >= (sessions.LONDON.start - 60)) {\n        currentSession = 'OVERLAP_ASIAN_LONDON';\n        volatilityExpected = 'MEDIUM';\n        liquidityExpected = 'MEDIUM';\n      } else {\n        currentSession = 'ASIAN';\n        volatilityExpected = 'LOW';\n        liquidityExpected = 'LOW';\n      }\n      isActive = true;\n      timeRemaining = sessions.LONDON.start - currentMinutes;\n    }\n\n    // Adjust for forex pairs\n    if (symbol.includes('JPY') && currentSession === 'ASIAN') {\n      volatilityExpected = 'MEDIUM';\n      liquidityExpected = 'MEDIUM';\n    }\n\n    return {\n      name: currentSession,\n      isActive,\n      timeRemaining,\n      volatilityExpected,\n      liquidityExpected\n    };\n  }\n\n  // Analyze market phase (Wyckoff method)\n  private analyzeMarketPhase(candleData: CandleData[], indicators: TechnicalIndicators): \n    'ACCUMULATION' | 'MARKUP' | 'DISTRIBUTION' | 'MARKDOWN' {\n    \n    const recentCandles = candleData.slice(-100);\n    const priceRange = Math.max(...recentCandles.map(c => c.high)) - Math.min(...recentCandles.map(c => c.low));\n    const currentPrice = recentCandles[recentCandles.length - 1].close;\n    \n    // Volume analysis\n    const avgVolume = recentCandles.reduce((sum, c) => sum + c.volume, 0) / recentCandles.length;\n    const recentVolume = recentCandles.slice(-10).reduce((sum, c) => sum + c.volume, 0) / 10;\n    \n    // Price position in range\n    const minPrice = Math.min(...recentCandles.map(c => c.low));\n    const maxPrice = Math.max(...recentCandles.map(c => c.high));\n    const pricePosition = (currentPrice - minPrice) / (maxPrice - minPrice);\n    \n    // Volatility analysis\n    const volatility = this.analyzeVolatility(recentCandles);\n    \n    // Determine phase\n    if (volatility === 'LOW' && recentVolume > avgVolume && pricePosition < 0.3) {\n      return 'ACCUMULATION';\n    } else if (indicators.supertrend.trend === 'up' && pricePosition > 0.3 && pricePosition < 0.7) {\n      return 'MARKUP';\n    } else if (volatility === 'LOW' && recentVolume > avgVolume && pricePosition > 0.7) {\n      return 'DISTRIBUTION';\n    } else {\n      return 'MARKDOWN';\n    }\n  }\n\n  // Calculate risk level\n  private calculateRiskLevel(\n    volatility: 'LOW' | 'MEDIUM' | 'HIGH',\n    liquidity: 'LOW' | 'MEDIUM' | 'HIGH',\n    trend: { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'; strength: number }\n  ): 'LOW' | 'MEDIUM' | 'HIGH' {\n    let riskScore = 0;\n    \n    // Volatility risk\n    if (volatility === 'HIGH') riskScore += 3;\n    else if (volatility === 'MEDIUM') riskScore += 2;\n    else riskScore += 1;\n    \n    // Liquidity risk (inverse)\n    if (liquidity === 'LOW') riskScore += 3;\n    else if (liquidity === 'MEDIUM') riskScore += 2;\n    else riskScore += 1;\n    \n    // Trend clarity risk\n    if (trend.direction === 'SIDEWAYS') riskScore += 2;\n    else if (trend.strength < 60) riskScore += 1;\n    \n    if (riskScore <= 3) return 'LOW';\n    if (riskScore <= 5) return 'MEDIUM';\n    return 'HIGH';\n  }\n\n  // Calculate confidence in analysis\n  private calculateConfidence(\n    trend: { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'; strength: number },\n    indicators: TechnicalIndicators,\n    candleData: CandleData[]\n  ): number {\n    let confidence = 0;\n    \n    // Trend strength contributes to confidence\n    confidence += trend.strength * 0.3;\n    \n    // Multiple timeframe confirmation (simulated)\n    confidence += 20;\n    \n    // Volume confirmation\n    const recentVolumes = candleData.slice(-5).map(c => c.volume);\n    const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;\n    const currentVolume = recentVolumes[recentVolumes.length - 1];\n    \n    if (currentVolume > avgVolume * 1.2) confidence += 15;\n    else if (currentVolume > avgVolume) confidence += 10;\n    \n    // Indicator alignment\n    let alignedIndicators = 0;\n    const currentPrice = candleData[candleData.length - 1].close;\n    \n    if ((trend.direction === 'BULLISH' && indicators.rsi > 50) || \n        (trend.direction === 'BEARISH' && indicators.rsi < 50)) alignedIndicators++;\n    \n    if ((trend.direction === 'BULLISH' && indicators.macd.MACD > indicators.macd.signal) ||\n        (trend.direction === 'BEARISH' && indicators.macd.MACD < indicators.macd.signal)) alignedIndicators++;\n    \n    if ((trend.direction === 'BULLISH' && currentPrice > indicators.vwap) ||\n        (trend.direction === 'BEARISH' && currentPrice < indicators.vwap)) alignedIndicators++;\n    \n    confidence += (alignedIndicators / 3) * 25;\n    \n    return Math.min(Math.max(confidence, 0), 100);\n  }\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;AAgDxB,MAAM;IAOX,gCAAgC;IAChC,mBACE,UAAwB,EACxB,UAA+B,EAC/B,MAAc,EACD;QACb,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5C,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC;QAC1C,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,YAAY;QACpD,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC,YAAY;QAClD,MAAM,UAAU,IAAI,CAAC,wBAAwB,CAAC;QAC9C,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,YAAY;QACxD,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC,YAAY,WAAW;QACjE,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC,OAAO,YAAY;QAE/D,OAAO;YACL,OAAO,MAAM,SAAS;YACtB,UAAU,MAAM,QAAQ;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,uBAAuB;IACf,aAAa,UAAwB,EAAE,UAA+B,EAAE;QAC9E,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC,KAAK,kBAAkB;QAC/D,MAAM,YAAY,IAAI,CAAC,sBAAsB,CAAC;QAE9C,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,WAAW;QAEf,qBAAqB;QACrB,MAAM,MAAM,WAAW,GAAG;QAC1B,IAAI,IAAI,MAAM,IAAI,GAAG;YACnB,MAAM,WAAW,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;YAC1D,IAAI,WAAW,GAAG;iBACb;QACP;QAEA,eAAe;QACf,MAAM,eAAe,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,CAAC,KAAK;QAClE,MAAM,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QACtC,IAAI,eAAe,YAAY;aAC1B;QAEL,aAAa;QACb,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE;aAC9C;QAEL,aAAa;QACb,IAAI,WAAW,UAAU,CAAC,KAAK,KAAK,MAAM;aACrC;QAEL,mBAAmB;QACnB,IAAI,UAAU,WAAW,IAAI,UAAU,UAAU,EAAE,kBAAkB;QACrE,IAAI,UAAU,UAAU,IAAI,UAAU,SAAS,EAAE,kBAAkB;QAEnE,sBAAsB;QACtB,IAAI,eAAe,WAAW,IAAI,EAAE;aAC/B;QAEL,yCAAyC;QACzC,MAAM,eAAe,iBAAiB;QACtC,IAAI;QAEJ,IAAI,KAAK,GAAG,CAAC,iBAAiB,mBAAmB,GAAG;YAClD,YAAY;YACZ,WAAW,IAAI,4BAA4B;QAC7C,OAAO,IAAI,iBAAiB,gBAAgB;YAC1C,YAAY;YACZ,WAAW,KAAK,GAAG,CAAC,AAAC,iBAAiB,eAAgB,KAAK;QAC7D,OAAO;YACL,YAAY;YACZ,WAAW,KAAK,GAAG,CAAC,AAAC,iBAAiB,eAAgB,KAAK;QAC7D;QAEA,OAAO;YAAE;YAAW;QAAS;IAC/B;IAEA,2BAA2B;IACnB,uBAAuB,UAAwB,EAAmB;QACxE,MAAM,aAAuB,EAAE;QAC/B,MAAM,YAAsB,EAAE;QAE9B,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,GAAG,GAAG,IAAK;YAC9C,MAAM,UAAU,UAAU,CAAC,EAAE;YAC7B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAC/B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAC/B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAC/B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAE/B,aAAa;YACb,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,IACtD,QAAQ,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,EAAE;gBAC1D,WAAW,IAAI,CAAC,QAAQ,IAAI;YAC9B;YAEA,YAAY;YACZ,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,IAClD,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,EAAE;gBACtD,UAAU,IAAI,CAAC,QAAQ,GAAG;YAC5B;QACF;QAEA,oBAAoB;QACpB,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC,WAAW,KAAK,CAAC,CAAC;QAChE,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,UAAU,KAAK,CAAC,CAAC;QAC9D,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,WAAW,KAAK,CAAC,CAAC;QAC/D,MAAM,YAAY,IAAI,CAAC,oBAAoB,CAAC,UAAU,KAAK,CAAC,CAAC;QAE7D,OAAO;YACL;YACA;YACA;YACA;YACA,WAAW;gBACT,SAAS,UAAU,KAAK,CAAC,CAAC;gBAC1B,YAAY,WAAW,KAAK,CAAC,CAAC;YAChC;YACA,YAAY;gBACV,SAAS,EAAE;gBACX,YAAY,EAAE;YAChB;QACF;IACF;IAEQ,qBAAqB,QAAkB,EAAW;QACxD,IAAI,SAAS,MAAM,GAAG,GAAG,OAAO;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,OAAO;QAC7C;QACA,OAAO;IACT;IAEQ,qBAAqB,QAAkB,EAAW;QACxD,IAAI,SAAS,MAAM,GAAG,GAAG,OAAO;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,IAAI,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,OAAO;QAC7C;QACA,OAAO;IACT;IAEA,qBAAqB;IACb,kBAAkB,UAAwB,EAA6B;QAC7E,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC;QACxC,MAAM,SAAS,cAAc,GAAG,CAAC,CAAA,SAC/B,AAAC,CAAC,OAAO,IAAI,GAAG,OAAO,GAAG,IAAI,OAAO,KAAK,GAAI;QAGhD,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK,OAAO,MAAM;QAE9E,IAAI,WAAW,KAAK,OAAO;QAC3B,IAAI,WAAW,KAAK,OAAO;QAC3B,OAAO;IACT;IAEA,oBAAoB;IACZ,iBAAiB,UAAwB,EAAE,UAA+B,EAA6B;QAC7G,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC;QACxC,MAAM,YAAY,cAAc,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE,KAAK,cAAc,MAAM;QACtG,MAAM,gBAAgB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,CAAC,MAAM;QAEpE,MAAM,cAAc,gBAAgB;QAEpC,+BAA+B;QAC/B,MAAM,gBAAgB,WAAW,aAAa;QAC9C,MAAM,cAAc,cAAc,MAAM,CAAC,MAAM,GAAG,IAChD,KAAK,GAAG,IAAI,cAAc,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;QAE7D,MAAM,iBAAiB,AAAC,cAAc,MAAQ,cAAc,MAAM;QAElE,IAAI,iBAAiB,KAAK,OAAO;QACjC,IAAI,iBAAiB,KAAK,OAAO;QACjC,OAAO;IACT;IAEA,mBAAmB;IACX,gBAAgB,UAA+B,EAAE,UAAwB,EACG;QAElF,IAAI,gBAAgB;QAEpB,eAAe;QACf,IAAI,WAAW,GAAG,GAAG,IAAI,iBAAiB;aACrC,IAAI,WAAW,GAAG,GAAG,IAAI,iBAAiB;aAC1C,IAAI,WAAW,GAAG,GAAG,IAAI,iBAAiB;aAC1C,IAAI,WAAW,GAAG,GAAG,IAAI,iBAAiB;QAE/C,gBAAgB;QAChB,IAAI,WAAW,IAAI,CAAC,SAAS,GAAG,GAAG;YACjC,iBAAiB,WAAW,IAAI,CAAC,SAAS,GAAG,QAAQ,IAAI;QAC3D,OAAO;YACL,iBAAiB,WAAW,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC9D;QAEA,kCAAkC;QAClC,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC;QACxC,MAAM,cAAc,CAAC,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,aAAa,CAAC,EAAE,CAAC,KAAK,IAAI,aAAa,CAAC,EAAE,CAAC,KAAK;QAErH,IAAI,cAAc,MAAM,iBAAiB,GAAG,UAAU;aACjD,IAAI,cAAc,OAAO,iBAAiB,GAAG,YAAY;aACzD,IAAI,cAAc,CAAC,MAAM,iBAAiB,GAAG,UAAU;aACvD,IAAI,cAAc,CAAC,OAAO,iBAAiB,GAAG,YAAY;QAE/D,kBAAkB;QAClB,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAC5D,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,cAAc,MAAM;QAC/F,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK;QAExG,IAAI,kBAAkB,gBAAgB,KAAK,iBAAiB;aACvD,IAAI,kBAAkB,gBAAgB,KAAK,iBAAiB;QAEjE,oBAAoB;QACpB,IAAI,iBAAiB,GAAG,OAAO;QAC/B,IAAI,iBAAiB,GAAG,OAAO;QAC/B,IAAI,iBAAiB,CAAC,GAAG,OAAO;QAChC,IAAI,iBAAiB,CAAC,GAAG,OAAO;QAChC,OAAO;IACT;IAEA,8BAA8B;IAC9B,yBAAyB,MAAc,EAAkB;QACvD,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,WAAW;QAC/B,MAAM,YAAY,IAAI,aAAa;QACnC,MAAM,iBAAiB,UAAU,KAAK;QAEtC,+BAA+B;QAC/B,MAAM,WAAW;YACf,OAAO;gBAAE,OAAO;gBAAG,KAAK,IAAI;YAAG;YAC/B,QAAQ;gBAAE,OAAO,IAAI;gBAAI,KAAK,KAAK;YAAG;YACtC,UAAU;gBAAE,OAAO,KAAK;gBAAI,KAAK,KAAK;YAAG;QAC3C;QAEA,4BAA4B;QAC5B,IAAI,iBAAyC;QAC7C,IAAI,WAAW;QACf,IAAI,gBAAgB;QACpB,IAAI,qBAAgD;QACpD,IAAI,oBAA+C;QAEnD,uBAAuB;QACvB,IAAI,kBAAkB,SAAS,MAAM,CAAC,KAAK,IAAI,kBAAkB,SAAS,QAAQ,CAAC,GAAG,EAAE;YACtF,IAAI,kBAAkB,SAAS,QAAQ,CAAC,KAAK,IAAI,kBAAkB,SAAS,MAAM,CAAC,GAAG,EAAE;gBACtF,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;gBACpB,WAAW;gBACX,gBAAgB,SAAS,MAAM,CAAC,GAAG,GAAG;YACxC,OAAO,IAAI,kBAAkB,SAAS,MAAM,CAAC,KAAK,IAAI,iBAAiB,SAAS,QAAQ,CAAC,KAAK,EAAE;gBAC9F,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;gBACpB,WAAW;gBACX,gBAAgB,SAAS,QAAQ,CAAC,KAAK,GAAG;YAC5C,OAAO;gBACL,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;gBACpB,WAAW;gBACX,gBAAgB,SAAS,QAAQ,CAAC,GAAG,GAAG;YAC1C;QACF,OAAO,IAAI,kBAAkB,SAAS,KAAK,CAAC,KAAK,IAAI,iBAAiB,SAAS,MAAM,CAAC,KAAK,EAAE;YAC3F,IAAI,kBAAmB,SAAS,MAAM,CAAC,KAAK,GAAG,IAAK;gBAClD,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;YACtB,OAAO;gBACL,iBAAiB;gBACjB,qBAAqB;gBACrB,oBAAoB;YACtB;YACA,WAAW;YACX,gBAAgB,SAAS,MAAM,CAAC,KAAK,GAAG;QAC1C;QAEA,yBAAyB;QACzB,IAAI,OAAO,QAAQ,CAAC,UAAU,mBAAmB,SAAS;YACxD,qBAAqB;YACrB,oBAAoB;QACtB;QAEA,OAAO;YACL,MAAM;YACN;YACA;YACA;YACA;QACF;IACF;IAEA,wCAAwC;IAChC,mBAAmB,UAAwB,EAAE,UAA+B,EAC1B;QAExD,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC;QACxC,MAAM,aAAa,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACzG,MAAM,eAAe,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE,CAAC,KAAK;QAElE,kBAAkB;QAClB,MAAM,YAAY,cAAc,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,cAAc,MAAM;QAC5F,MAAM,eAAe,cAAc,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK;QAEtF,0BAA0B;QAC1B,MAAM,WAAW,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACzD,MAAM,WAAW,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAC1D,MAAM,gBAAgB,CAAC,eAAe,QAAQ,IAAI,CAAC,WAAW,QAAQ;QAEtE,sBAAsB;QACtB,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC;QAE1C,kBAAkB;QAClB,IAAI,eAAe,SAAS,eAAe,aAAa,gBAAgB,KAAK;YAC3E,OAAO;QACT,OAAO,IAAI,WAAW,UAAU,CAAC,KAAK,KAAK,QAAQ,gBAAgB,OAAO,gBAAgB,KAAK;YAC7F,OAAO;QACT,OAAO,IAAI,eAAe,SAAS,eAAe,aAAa,gBAAgB,KAAK;YAClF,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,uBAAuB;IACf,mBACN,UAAqC,EACrC,SAAoC,EACpC,KAA0E,EAC/C;QAC3B,IAAI,YAAY;QAEhB,kBAAkB;QAClB,IAAI,eAAe,QAAQ,aAAa;aACnC,IAAI,eAAe,UAAU,aAAa;aAC1C,aAAa;QAElB,2BAA2B;QAC3B,IAAI,cAAc,OAAO,aAAa;aACjC,IAAI,cAAc,UAAU,aAAa;aACzC,aAAa;QAElB,qBAAqB;QACrB,IAAI,MAAM,SAAS,KAAK,YAAY,aAAa;aAC5C,IAAI,MAAM,QAAQ,GAAG,IAAI,aAAa;QAE3C,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,aAAa,GAAG,OAAO;QAC3B,OAAO;IACT;IAEA,mCAAmC;IAC3B,oBACN,KAA0E,EAC1E,UAA+B,EAC/B,UAAwB,EAChB;QACR,IAAI,aAAa;QAEjB,2CAA2C;QAC3C,cAAc,MAAM,QAAQ,GAAG;QAE/B,8CAA8C;QAC9C,cAAc;QAEd,sBAAsB;QACtB,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;QAC5D,MAAM,YAAY,cAAc,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,cAAc,MAAM;QACzF,MAAM,gBAAgB,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;QAE7D,IAAI,gBAAgB,YAAY,KAAK,cAAc;aAC9C,IAAI,gBAAgB,WAAW,cAAc;QAElD,sBAAsB;QACtB,IAAI,oBAAoB;QACxB,MAAM,eAAe,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,KAAK;QAE5D,IAAI,AAAC,MAAM,SAAS,KAAK,aAAa,WAAW,GAAG,GAAG,MAClD,MAAM,SAAS,KAAK,aAAa,WAAW,GAAG,GAAG,IAAK;QAE5D,IAAI,AAAC,MAAM,SAAS,KAAK,aAAa,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,IAC9E,MAAM,SAAS,KAAK,aAAa,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAG;QAEtF,IAAI,AAAC,MAAM,SAAS,KAAK,aAAa,eAAe,WAAW,IAAI,IAC/D,MAAM,SAAS,KAAK,aAAa,eAAe,WAAW,IAAI,EAAG;QAEvE,cAAc,AAAC,oBAAoB,IAAK;QAExC,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,IAAI;IAC3C;IA/YA,YAAY,WAAmB,KAAK,CAAE;QAFtC,+KAAQ,YAAR,KAAA;QAGE,IAAI,CAAC,QAAQ,GAAG;IAClB;AA8YF", "debugId": null}}, {"offset": {"line": 6283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/ai/pattern-recognition.ts"], "sourcesContent": ["// AI Pattern Recognition and Decision Making System\nimport { CandleData, TechnicalIndicators } from '../technical-analysis/indicators';\nimport { MarketState } from '../market-analysis/market-state';\nimport { TradeSignal } from '../trading/risk-management';\n\nexport interface PatternMatch {\n  name: string;\n  confidence: number;\n  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL';\n  timeframe: string;\n  startIndex: number;\n  endIndex: number;\n  targetPrice?: number;\n  stopLoss?: number;\n  description: string;\n}\n\nexport interface AIDecision {\n  action: 'BUY' | 'SELL' | 'HOLD' | 'CLOSE';\n  confidence: number;\n  reasoning: string[];\n  patterns: PatternMatch[];\n  riskScore: number;\n  expectedReturn: number;\n  timeHorizon: number; // in minutes\n}\n\nexport interface MarketRegime {\n  type: 'TRENDING' | 'RANGING' | 'VOLATILE' | 'QUIET';\n  strength: number;\n  duration: number; // in candles\n  characteristics: string[];\n}\n\nexport class AIPatternRecognition {\n  private learningData: Map<string, number[]> = new Map();\n  private patternDatabase: Map<string, PatternMatch[]> = new Map();\n  private successRates: Map<string, { wins: number; losses: number }> = new Map();\n\n  constructor() {\n    this.initializePatternDatabase();\n  }\n\n  // Main AI decision making function\n  makeDecision(\n    candleData: CandleData[],\n    indicators: TechnicalIndicators,\n    marketState: MarketState,\n    symbol: string\n  ): AIDecision {\n    // Detect patterns\n    const patterns = this.detectPatterns(candleData);\n    \n    // Analyze market regime\n    const regime = this.analyzeMarketRegime(candleData, indicators);\n    \n    // Calculate sentiment\n    const sentiment = this.calculateMarketSentiment(indicators, marketState);\n    \n    // Generate decision\n    const decision = this.generateDecision(patterns, regime, sentiment, marketState);\n    \n    // Learn from market behavior\n    this.updateLearningData(symbol, candleData, indicators, decision);\n    \n    return decision;\n  }\n\n  // Detect chart patterns using AI algorithms\n  private detectPatterns(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    \n    // Head and Shoulders pattern\n    patterns.push(...this.detectHeadAndShoulders(candleData));\n    \n    // Double Top/Bottom patterns\n    patterns.push(...this.detectDoubleTopBottom(candleData));\n    \n    // Triangle patterns\n    patterns.push(...this.detectTriangles(candleData));\n    \n    // Flag and Pennant patterns\n    patterns.push(...this.detectFlagsAndPennants(candleData));\n    \n    // Candlestick patterns\n    patterns.push(...this.detectCandlestickPatterns(candleData));\n    \n    // Support/Resistance breaks\n    patterns.push(...this.detectBreakoutPatterns(candleData));\n    \n    return patterns.filter(pattern => pattern.confidence > 60);\n  }\n\n  // Detect Head and Shoulders pattern\n  private detectHeadAndShoulders(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    const minPatternLength = 20;\n    \n    if (candleData.length < minPatternLength) return patterns;\n    \n    for (let i = minPatternLength; i < candleData.length - 5; i++) {\n      const window = candleData.slice(i - minPatternLength, i);\n      const peaks = this.findPeaks(window);\n      \n      if (peaks.length >= 3) {\n        const [leftShoulder, head, rightShoulder] = peaks.slice(-3);\n        \n        // Check if it forms a head and shoulders pattern\n        if (this.isHeadAndShoulders(leftShoulder, head, rightShoulder, window)) {\n          const confidence = this.calculatePatternConfidence('HEAD_AND_SHOULDERS', window);\n          \n          patterns.push({\n            name: 'Head and Shoulders',\n            confidence,\n            type: 'BEARISH',\n            timeframe: '1h',\n            startIndex: i - minPatternLength,\n            endIndex: i,\n            targetPrice: this.calculateHSTarget(leftShoulder, head, rightShoulder, window),\n            stopLoss: head.price * 1.02,\n            description: 'Bearish reversal pattern with three peaks'\n          });\n        }\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Detect Double Top/Bottom patterns\n  private detectDoubleTopBottom(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    const minPatternLength = 15;\n    \n    if (candleData.length < minPatternLength) return patterns;\n    \n    for (let i = minPatternLength; i < candleData.length - 3; i++) {\n      const window = candleData.slice(i - minPatternLength, i);\n      \n      // Double Top\n      const peaks = this.findPeaks(window);\n      if (peaks.length >= 2) {\n        const [peak1, peak2] = peaks.slice(-2);\n        if (this.isDoubleTop(peak1, peak2, window)) {\n          const confidence = this.calculatePatternConfidence('DOUBLE_TOP', window);\n          \n          patterns.push({\n            name: 'Double Top',\n            confidence,\n            type: 'BEARISH',\n            timeframe: '1h',\n            startIndex: i - minPatternLength,\n            endIndex: i,\n            targetPrice: this.calculateDoubleTopTarget(peak1, peak2, window),\n            stopLoss: Math.max(peak1.price, peak2.price) * 1.01,\n            description: 'Bearish reversal pattern with two equal peaks'\n          });\n        }\n      }\n      \n      // Double Bottom\n      const troughs = this.findTroughs(window);\n      if (troughs.length >= 2) {\n        const [trough1, trough2] = troughs.slice(-2);\n        if (this.isDoubleBottom(trough1, trough2, window)) {\n          const confidence = this.calculatePatternConfidence('DOUBLE_BOTTOM', window);\n          \n          patterns.push({\n            name: 'Double Bottom',\n            confidence,\n            type: 'BULLISH',\n            timeframe: '1h',\n            startIndex: i - minPatternLength,\n            endIndex: i,\n            targetPrice: this.calculateDoubleBottomTarget(trough1, trough2, window),\n            stopLoss: Math.min(trough1.price, trough2.price) * 0.99,\n            description: 'Bullish reversal pattern with two equal lows'\n          });\n        }\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Detect Triangle patterns\n  private detectTriangles(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    const minPatternLength = 12;\n    \n    if (candleData.length < minPatternLength) return patterns;\n    \n    for (let i = minPatternLength; i < candleData.length - 3; i++) {\n      const window = candleData.slice(i - minPatternLength, i);\n      \n      const highs = window.map((candle, idx) => ({ price: candle.high, index: idx }));\n      const lows = window.map((candle, idx) => ({ price: candle.low, index: idx }));\n      \n      // Ascending Triangle\n      if (this.isAscendingTriangle(highs, lows)) {\n        patterns.push({\n          name: 'Ascending Triangle',\n          confidence: this.calculatePatternConfidence('ASCENDING_TRIANGLE', window),\n          type: 'BULLISH',\n          timeframe: '1h',\n          startIndex: i - minPatternLength,\n          endIndex: i,\n          description: 'Bullish continuation pattern with horizontal resistance'\n        });\n      }\n      \n      // Descending Triangle\n      if (this.isDescendingTriangle(highs, lows)) {\n        patterns.push({\n          name: 'Descending Triangle',\n          confidence: this.calculatePatternConfidence('DESCENDING_TRIANGLE', window),\n          type: 'BEARISH',\n          timeframe: '1h',\n          startIndex: i - minPatternLength,\n          endIndex: i,\n          description: 'Bearish continuation pattern with horizontal support'\n        });\n      }\n      \n      // Symmetrical Triangle\n      if (this.isSymmetricalTriangle(highs, lows)) {\n        patterns.push({\n          name: 'Symmetrical Triangle',\n          confidence: this.calculatePatternConfidence('SYMMETRICAL_TRIANGLE', window),\n          type: 'NEUTRAL',\n          timeframe: '1h',\n          startIndex: i - minPatternLength,\n          endIndex: i,\n          description: 'Neutral pattern indicating potential breakout'\n        });\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Detect Flag and Pennant patterns\n  private detectFlagsAndPennants(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    const minPatternLength = 8;\n    \n    if (candleData.length < minPatternLength + 5) return patterns;\n    \n    for (let i = minPatternLength + 5; i < candleData.length - 2; i++) {\n      const trendWindow = candleData.slice(i - minPatternLength - 5, i - 5);\n      const flagWindow = candleData.slice(i - 5, i);\n      \n      // Check for strong trend before flag\n      const trendStrength = this.calculateTrendStrength(trendWindow);\n      \n      if (trendStrength > 0.7) { // Strong uptrend\n        if (this.isBullishFlag(flagWindow)) {\n          patterns.push({\n            name: 'Bullish Flag',\n            confidence: this.calculatePatternConfidence('BULLISH_FLAG', flagWindow),\n            type: 'BULLISH',\n            timeframe: '30m',\n            startIndex: i - 5,\n            endIndex: i,\n            description: 'Bullish continuation pattern after strong uptrend'\n          });\n        }\n      } else if (trendStrength < -0.7) { // Strong downtrend\n        if (this.isBearishFlag(flagWindow)) {\n          patterns.push({\n            name: 'Bearish Flag',\n            confidence: this.calculatePatternConfidence('BEARISH_FLAG', flagWindow),\n            type: 'BEARISH',\n            timeframe: '30m',\n            startIndex: i - 5,\n            endIndex: i,\n            description: 'Bearish continuation pattern after strong downtrend'\n          });\n        }\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Detect Candlestick patterns\n  private detectCandlestickPatterns(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    \n    if (candleData.length < 3) return patterns;\n    \n    for (let i = 2; i < candleData.length; i++) {\n      const current = candleData[i];\n      const prev1 = candleData[i - 1];\n      const prev2 = candleData[i - 2];\n      \n      // Doji\n      if (this.isDoji(current)) {\n        patterns.push({\n          name: 'Doji',\n          confidence: 70,\n          type: 'NEUTRAL',\n          timeframe: '15m',\n          startIndex: i,\n          endIndex: i,\n          description: 'Indecision candle indicating potential reversal'\n        });\n      }\n      \n      // Hammer\n      if (this.isHammer(current)) {\n        patterns.push({\n          name: 'Hammer',\n          confidence: 75,\n          type: 'BULLISH',\n          timeframe: '15m',\n          startIndex: i,\n          endIndex: i,\n          description: 'Bullish reversal pattern with long lower shadow'\n        });\n      }\n      \n      // Shooting Star\n      if (this.isShootingStar(current)) {\n        patterns.push({\n          name: 'Shooting Star',\n          confidence: 75,\n          type: 'BEARISH',\n          timeframe: '15m',\n          startIndex: i,\n          endIndex: i,\n          description: 'Bearish reversal pattern with long upper shadow'\n        });\n      }\n      \n      // Engulfing patterns\n      if (this.isBullishEngulfing(prev1, current)) {\n        patterns.push({\n          name: 'Bullish Engulfing',\n          confidence: 80,\n          type: 'BULLISH',\n          timeframe: '15m',\n          startIndex: i - 1,\n          endIndex: i,\n          description: 'Strong bullish reversal pattern'\n        });\n      }\n      \n      if (this.isBearishEngulfing(prev1, current)) {\n        patterns.push({\n          name: 'Bearish Engulfing',\n          confidence: 80,\n          type: 'BEARISH',\n          timeframe: '15m',\n          startIndex: i - 1,\n          endIndex: i,\n          description: 'Strong bearish reversal pattern'\n        });\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Detect Breakout patterns\n  private detectBreakoutPatterns(candleData: CandleData[]): PatternMatch[] {\n    const patterns: PatternMatch[] = [];\n    const lookback = 20;\n    \n    if (candleData.length < lookback + 1) return patterns;\n    \n    for (let i = lookback; i < candleData.length; i++) {\n      const window = candleData.slice(i - lookback, i);\n      const current = candleData[i];\n      \n      const resistance = Math.max(...window.map(c => c.high));\n      const support = Math.min(...window.map(c => c.low));\n      \n      // Resistance breakout\n      if (current.close > resistance && current.volume > this.getAverageVolume(window) * 1.5) {\n        patterns.push({\n          name: 'Resistance Breakout',\n          confidence: 85,\n          type: 'BULLISH',\n          timeframe: '1h',\n          startIndex: i - lookback,\n          endIndex: i,\n          targetPrice: resistance + (resistance - support) * 0.5,\n          stopLoss: resistance * 0.98,\n          description: 'Bullish breakout above resistance with high volume'\n        });\n      }\n      \n      // Support breakdown\n      if (current.close < support && current.volume > this.getAverageVolume(window) * 1.5) {\n        patterns.push({\n          name: 'Support Breakdown',\n          confidence: 85,\n          type: 'BEARISH',\n          timeframe: '1h',\n          startIndex: i - lookback,\n          endIndex: i,\n          targetPrice: support - (resistance - support) * 0.5,\n          stopLoss: support * 1.02,\n          description: 'Bearish breakdown below support with high volume'\n        });\n      }\n    }\n    \n    return patterns;\n  }\n\n  // Analyze market regime\n  private analyzeMarketRegime(candleData: CandleData[], indicators: TechnicalIndicators): MarketRegime {\n    const recentData = candleData.slice(-50);\n    const volatility = this.calculateVolatility(recentData);\n    const trendStrength = this.calculateTrendStrength(recentData);\n    \n    let type: MarketRegime['type'];\n    let characteristics: string[] = [];\n    \n    if (Math.abs(trendStrength) > 0.6) {\n      type = 'TRENDING';\n      characteristics.push(trendStrength > 0 ? 'Strong uptrend' : 'Strong downtrend');\n    } else if (volatility > 0.02) {\n      type = 'VOLATILE';\n      characteristics.push('High volatility', 'Choppy price action');\n    } else if (volatility < 0.005) {\n      type = 'QUIET';\n      characteristics.push('Low volatility', 'Consolidation phase');\n    } else {\n      type = 'RANGING';\n      characteristics.push('Sideways movement', 'Range-bound trading');\n    }\n    \n    // Add volume characteristics\n    const avgVolume = this.getAverageVolume(recentData);\n    const currentVolume = recentData[recentData.length - 1].volume;\n    \n    if (currentVolume > avgVolume * 1.5) {\n      characteristics.push('High volume');\n    } else if (currentVolume < avgVolume * 0.7) {\n      characteristics.push('Low volume');\n    }\n    \n    return {\n      type,\n      strength: Math.abs(trendStrength),\n      duration: this.calculateRegimeDuration(recentData, type),\n      characteristics\n    };\n  }\n\n  // Calculate market sentiment\n  private calculateMarketSentiment(indicators: TechnicalIndicators, marketState: MarketState): number {\n    let sentiment = 0;\n    let factors = 0;\n    \n    // RSI sentiment\n    if (indicators.rsi > 70) sentiment -= 2;\n    else if (indicators.rsi > 60) sentiment -= 1;\n    else if (indicators.rsi < 30) sentiment += 2;\n    else if (indicators.rsi < 40) sentiment += 1;\n    factors++;\n    \n    // MACD sentiment\n    if (indicators.macd.MACD > indicators.macd.signal) sentiment += 1;\n    else sentiment -= 1;\n    factors++;\n    \n    // Trend sentiment\n    if (marketState.trend === 'BULLISH') sentiment += marketState.strength / 50;\n    else if (marketState.trend === 'BEARISH') sentiment -= marketState.strength / 50;\n    factors++;\n    \n    // Momentum sentiment\n    const momentumScore = {\n      'STRONG_BULLISH': 2,\n      'WEAK_BULLISH': 1,\n      'NEUTRAL': 0,\n      'WEAK_BEARISH': -1,\n      'STRONG_BEARISH': -2\n    }[marketState.momentum];\n    \n    sentiment += momentumScore;\n    factors++;\n    \n    return factors > 0 ? sentiment / factors : 0;\n  }\n\n  // Generate AI decision\n  private generateDecision(\n    patterns: PatternMatch[],\n    regime: MarketRegime,\n    sentiment: number,\n    marketState: MarketState\n  ): AIDecision {\n    let action: AIDecision['action'] = 'HOLD';\n    let confidence = 0;\n    let reasoning: string[] = [];\n    let riskScore = 50; // Base risk score\n    let expectedReturn = 0;\n    \n    // Analyze patterns\n    const bullishPatterns = patterns.filter(p => p.type === 'BULLISH');\n    const bearishPatterns = patterns.filter(p => p.type === 'BEARISH');\n    \n    const bullishConfidence = bullishPatterns.reduce((sum, p) => sum + p.confidence, 0) / Math.max(bullishPatterns.length, 1);\n    const bearishConfidence = bearishPatterns.reduce((sum, p) => sum + p.confidence, 0) / Math.max(bearishPatterns.length, 1);\n    \n    // Decision logic\n    if (bullishPatterns.length > bearishPatterns.length && sentiment > 0.5) {\n      action = 'BUY';\n      confidence = Math.min((bullishConfidence + sentiment * 20 + marketState.confidence) / 3, 95);\n      reasoning.push(`${bullishPatterns.length} bullish patterns detected`);\n      reasoning.push(`Positive market sentiment (${sentiment.toFixed(2)})`);\n      expectedReturn = 2.5; // Expected 2.5% return\n    } else if (bearishPatterns.length > bullishPatterns.length && sentiment < -0.5) {\n      action = 'SELL';\n      confidence = Math.min((bearishConfidence + Math.abs(sentiment) * 20 + marketState.confidence) / 3, 95);\n      reasoning.push(`${bearishPatterns.length} bearish patterns detected`);\n      reasoning.push(`Negative market sentiment (${sentiment.toFixed(2)})`);\n      expectedReturn = 2.5; // Expected 2.5% return\n    } else {\n      reasoning.push('Mixed signals or insufficient confidence');\n      reasoning.push(`Market regime: ${regime.type}`);\n    }\n    \n    // Adjust for market regime\n    if (regime.type === 'VOLATILE') {\n      riskScore += 20;\n      reasoning.push('High volatility increases risk');\n    } else if (regime.type === 'QUIET') {\n      riskScore -= 10;\n      reasoning.push('Low volatility reduces risk');\n    }\n    \n    // Adjust for market state\n    if (marketState.riskLevel === 'HIGH') {\n      riskScore += 15;\n      confidence *= 0.9;\n    } else if (marketState.riskLevel === 'LOW') {\n      riskScore -= 10;\n      confidence *= 1.1;\n    }\n    \n    return {\n      action,\n      confidence: Math.max(Math.min(confidence, 95), 5),\n      reasoning,\n      patterns,\n      riskScore: Math.max(Math.min(riskScore, 100), 0),\n      expectedReturn,\n      timeHorizon: this.calculateTimeHorizon(patterns, regime)\n    };\n  }\n\n  // Helper methods for pattern detection\n  private findPeaks(data: CandleData[]): { price: number; index: number }[] {\n    const peaks: { price: number; index: number }[] = [];\n    \n    for (let i = 2; i < data.length - 2; i++) {\n      if (data[i].high > data[i-1].high && data[i].high > data[i-2].high &&\n          data[i].high > data[i+1].high && data[i].high > data[i+2].high) {\n        peaks.push({ price: data[i].high, index: i });\n      }\n    }\n    \n    return peaks;\n  }\n\n  private findTroughs(data: CandleData[]): { price: number; index: number }[] {\n    const troughs: { price: number; index: number }[] = [];\n    \n    for (let i = 2; i < data.length - 2; i++) {\n      if (data[i].low < data[i-1].low && data[i].low < data[i-2].low &&\n          data[i].low < data[i+1].low && data[i].low < data[i+2].low) {\n        troughs.push({ price: data[i].low, index: i });\n      }\n    }\n    \n    return troughs;\n  }\n\n  private isHeadAndShoulders(left: any, head: any, right: any, data: CandleData[]): boolean {\n    // Head should be higher than both shoulders\n    if (head.price <= left.price || head.price <= right.price) return false;\n    \n    // Shoulders should be roughly equal (within 2%)\n    const shoulderDiff = Math.abs(left.price - right.price) / Math.max(left.price, right.price);\n    return shoulderDiff < 0.02;\n  }\n\n  private isDoubleTop(peak1: any, peak2: any, data: CandleData[]): boolean {\n    const priceDiff = Math.abs(peak1.price - peak2.price) / Math.max(peak1.price, peak2.price);\n    return priceDiff < 0.015; // Within 1.5%\n  }\n\n  private isDoubleBottom(trough1: any, trough2: any, data: CandleData[]): boolean {\n    const priceDiff = Math.abs(trough1.price - trough2.price) / Math.max(trough1.price, trough2.price);\n    return priceDiff < 0.015; // Within 1.5%\n  }\n\n  private isAscendingTriangle(highs: any[], lows: any[]): boolean {\n    // Implementation for ascending triangle detection\n    return false; // Simplified for now\n  }\n\n  private isDescendingTriangle(highs: any[], lows: any[]): boolean {\n    // Implementation for descending triangle detection\n    return false; // Simplified for now\n  }\n\n  private isSymmetricalTriangle(highs: any[], lows: any[]): boolean {\n    // Implementation for symmetrical triangle detection\n    return false; // Simplified for now\n  }\n\n  private isBullishFlag(data: CandleData[]): boolean {\n    // Check for slight downward slope in consolidation\n    const firstPrice = data[0].close;\n    const lastPrice = data[data.length - 1].close;\n    const decline = (firstPrice - lastPrice) / firstPrice;\n    return decline > 0 && decline < 0.05; // 0-5% decline\n  }\n\n  private isBearishFlag(data: CandleData[]): boolean {\n    // Check for slight upward slope in consolidation\n    const firstPrice = data[0].close;\n    const lastPrice = data[data.length - 1].close;\n    const rise = (lastPrice - firstPrice) / firstPrice;\n    return rise > 0 && rise < 0.05; // 0-5% rise\n  }\n\n  private isDoji(candle: CandleData): boolean {\n    const bodySize = Math.abs(candle.close - candle.open);\n    const totalRange = candle.high - candle.low;\n    return bodySize / totalRange < 0.1; // Body is less than 10% of total range\n  }\n\n  private isHammer(candle: CandleData): boolean {\n    const bodySize = Math.abs(candle.close - candle.open);\n    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;\n    const upperShadow = candle.high - Math.max(candle.open, candle.close);\n    \n    return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;\n  }\n\n  private isShootingStar(candle: CandleData): boolean {\n    const bodySize = Math.abs(candle.close - candle.open);\n    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;\n    const upperShadow = candle.high - Math.max(candle.open, candle.close);\n    \n    return upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5;\n  }\n\n  private isBullishEngulfing(prev: CandleData, current: CandleData): boolean {\n    return prev.close < prev.open && // Previous candle is bearish\n           current.close > current.open && // Current candle is bullish\n           current.open < prev.close && // Current opens below previous close\n           current.close > prev.open; // Current closes above previous open\n  }\n\n  private isBearishEngulfing(prev: CandleData, current: CandleData): boolean {\n    return prev.close > prev.open && // Previous candle is bullish\n           current.close < current.open && // Current candle is bearish\n           current.open > prev.close && // Current opens above previous close\n           current.close < prev.open; // Current closes below previous open\n  }\n\n  private calculatePatternConfidence(patternName: string, data: CandleData[]): number {\n    // Base confidence from historical success rate\n    const successRate = this.successRates.get(patternName);\n    let baseConfidence = 70;\n    \n    if (successRate) {\n      const total = successRate.wins + successRate.losses;\n      if (total > 10) {\n        baseConfidence = (successRate.wins / total) * 100;\n      }\n    }\n    \n    // Adjust based on volume and volatility\n    const avgVolume = this.getAverageVolume(data);\n    const currentVolume = data[data.length - 1].volume;\n    const volumeMultiplier = currentVolume / avgVolume;\n    \n    let confidence = baseConfidence;\n    if (volumeMultiplier > 1.5) confidence += 10;\n    else if (volumeMultiplier < 0.7) confidence -= 10;\n    \n    return Math.max(Math.min(confidence, 95), 30);\n  }\n\n  private calculateTrendStrength(data: CandleData[]): number {\n    if (data.length < 2) return 0;\n    \n    const firstPrice = data[0].close;\n    const lastPrice = data[data.length - 1].close;\n    const change = (lastPrice - firstPrice) / firstPrice;\n    \n    // Normalize to -1 to 1 range\n    return Math.max(Math.min(change * 10, 1), -1);\n  }\n\n  private calculateVolatility(data: CandleData[]): number {\n    const returns = [];\n    for (let i = 1; i < data.length; i++) {\n      const ret = (data[i].close - data[i-1].close) / data[i-1].close;\n      returns.push(ret);\n    }\n    \n    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;\n    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;\n    \n    return Math.sqrt(variance);\n  }\n\n  private getAverageVolume(data: CandleData[]): number {\n    return data.reduce((sum, candle) => sum + candle.volume, 0) / data.length;\n  }\n\n  private calculateRegimeDuration(data: CandleData[], type: MarketRegime['type']): number {\n    // Simplified calculation - count consecutive candles of same regime\n    return Math.min(data.length, 20);\n  }\n\n  private calculateTimeHorizon(patterns: PatternMatch[], regime: MarketRegime): number {\n    // Calculate expected time horizon based on patterns and regime\n    let horizon = 60; // Default 1 hour\n    \n    if (patterns.some(p => p.name.includes('Triangle') || p.name.includes('Flag'))) {\n      horizon = 30; // Shorter for continuation patterns\n    }\n    \n    if (regime.type === 'VOLATILE') {\n      horizon *= 0.7; // Faster moves in volatile markets\n    } else if (regime.type === 'QUIET') {\n      horizon *= 1.5; // Slower moves in quiet markets\n    }\n    \n    return Math.round(horizon);\n  }\n\n  private calculateHSTarget(left: any, head: any, right: any, data: CandleData[]): number {\n    // Calculate neckline and project target\n    const neckline = (left.price + right.price) / 2;\n    const headHeight = head.price - neckline;\n    return neckline - headHeight;\n  }\n\n  private calculateDoubleTopTarget(peak1: any, peak2: any, data: CandleData[]): number {\n    const support = Math.min(...data.map(c => c.low));\n    const resistance = Math.max(peak1.price, peak2.price);\n    return support - (resistance - support) * 0.5;\n  }\n\n  private calculateDoubleBottomTarget(trough1: any, trough2: any, data: CandleData[]): number {\n    const resistance = Math.max(...data.map(c => c.high));\n    const support = Math.min(trough1.price, trough2.price);\n    return resistance + (resistance - support) * 0.5;\n  }\n\n  private initializePatternDatabase(): void {\n    // Initialize with some default success rates\n    this.successRates.set('HEAD_AND_SHOULDERS', { wins: 7, losses: 3 });\n    this.successRates.set('DOUBLE_TOP', { wins: 6, losses: 4 });\n    this.successRates.set('DOUBLE_BOTTOM', { wins: 6, losses: 4 });\n    this.successRates.set('BULLISH_FLAG', { wins: 8, losses: 2 });\n    this.successRates.set('BEARISH_FLAG', { wins: 8, losses: 2 });\n  }\n\n  private updateLearningData(\n    symbol: string,\n    candleData: CandleData[],\n    indicators: TechnicalIndicators,\n    decision: AIDecision\n  ): void {\n    // Store learning data for future improvement\n    const key = `${symbol}_${decision.action}`;\n    const features = [\n      indicators.rsi,\n      indicators.macd.MACD,\n      indicators.vwap,\n      decision.confidence,\n      decision.riskScore\n    ];\n    \n    this.learningData.set(key, features);\n  }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;AAkC7C,MAAM;IASX,mCAAmC;IACnC,aACE,UAAwB,EACxB,UAA+B,EAC/B,WAAwB,EACxB,MAAc,EACF;QACZ,kBAAkB;QAClB,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC;QAErC,wBAAwB;QACxB,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC,YAAY;QAEpD,sBAAsB;QACtB,MAAM,YAAY,IAAI,CAAC,wBAAwB,CAAC,YAAY;QAE5D,oBAAoB;QACpB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,UAAU,QAAQ,WAAW;QAEpE,6BAA6B;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,YAAY,YAAY;QAExD,OAAO;IACT;IAEA,4CAA4C;IACpC,eAAe,UAAwB,EAAkB;QAC/D,MAAM,WAA2B,EAAE;QAEnC,6BAA6B;QAC7B,SAAS,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAE7C,6BAA6B;QAC7B,SAAS,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC;QAE5C,oBAAoB;QACpB,SAAS,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC;QAEtC,4BAA4B;QAC5B,SAAS,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAE7C,uBAAuB;QACvB,SAAS,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC;QAEhD,4BAA4B;QAC5B,SAAS,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC;QAE7C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU,GAAG;IACzD;IAEA,oCAAoC;IAC5B,uBAAuB,UAAwB,EAAkB;QACvE,MAAM,WAA2B,EAAE;QACnC,MAAM,mBAAmB;QAEzB,IAAI,WAAW,MAAM,GAAG,kBAAkB,OAAO;QAEjD,IAAK,IAAI,IAAI,kBAAkB,IAAI,WAAW,MAAM,GAAG,GAAG,IAAK;YAC7D,MAAM,SAAS,WAAW,KAAK,CAAC,IAAI,kBAAkB;YACtD,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAE7B,IAAI,MAAM,MAAM,IAAI,GAAG;gBACrB,MAAM,CAAC,cAAc,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,CAAC;gBAEzD,iDAAiD;gBACjD,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,MAAM,eAAe,SAAS;oBACtE,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,sBAAsB;oBAEzE,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN;wBACA,MAAM;wBACN,WAAW;wBACX,YAAY,IAAI;wBAChB,UAAU;wBACV,aAAa,IAAI,CAAC,iBAAiB,CAAC,cAAc,MAAM,eAAe;wBACvE,UAAU,KAAK,KAAK,GAAG;wBACvB,aAAa;oBACf;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,oCAAoC;IAC5B,sBAAsB,UAAwB,EAAkB;QACtE,MAAM,WAA2B,EAAE;QACnC,MAAM,mBAAmB;QAEzB,IAAI,WAAW,MAAM,GAAG,kBAAkB,OAAO;QAEjD,IAAK,IAAI,IAAI,kBAAkB,IAAI,WAAW,MAAM,GAAG,GAAG,IAAK;YAC7D,MAAM,SAAS,WAAW,KAAK,CAAC,IAAI,kBAAkB;YAEtD,aAAa;YACb,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC7B,IAAI,MAAM,MAAM,IAAI,GAAG;gBACrB,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC;gBACpC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,OAAO,SAAS;oBAC1C,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,cAAc;oBAEjE,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN;wBACA,MAAM;wBACN,WAAW;wBACX,YAAY,IAAI;wBAChB,UAAU;wBACV,aAAa,IAAI,CAAC,wBAAwB,CAAC,OAAO,OAAO;wBACzD,UAAU,KAAK,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK,IAAI;wBAC/C,aAAa;oBACf;gBACF;YACF;YAEA,gBAAgB;YAChB,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC;YACjC,IAAI,QAAQ,MAAM,IAAI,GAAG;gBACvB,MAAM,CAAC,SAAS,QAAQ,GAAG,QAAQ,KAAK,CAAC,CAAC;gBAC1C,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,SAAS,SAAS;oBACjD,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,iBAAiB;oBAEpE,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN;wBACA,MAAM;wBACN,WAAW;wBACX,YAAY,IAAI;wBAChB,UAAU;wBACV,aAAa,IAAI,CAAC,2BAA2B,CAAC,SAAS,SAAS;wBAChE,UAAU,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,QAAQ,KAAK,IAAI;wBACnD,aAAa;oBACf;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,2BAA2B;IACnB,gBAAgB,UAAwB,EAAkB;QAChE,MAAM,WAA2B,EAAE;QACnC,MAAM,mBAAmB;QAEzB,IAAI,WAAW,MAAM,GAAG,kBAAkB,OAAO;QAEjD,IAAK,IAAI,IAAI,kBAAkB,IAAI,WAAW,MAAM,GAAG,GAAG,IAAK;YAC7D,MAAM,SAAS,WAAW,KAAK,CAAC,IAAI,kBAAkB;YAEtD,MAAM,QAAQ,OAAO,GAAG,CAAC,CAAC,QAAQ,MAAQ,CAAC;oBAAE,OAAO,OAAO,IAAI;oBAAE,OAAO;gBAAI,CAAC;YAC7E,MAAM,OAAO,OAAO,GAAG,CAAC,CAAC,QAAQ,MAAQ,CAAC;oBAAE,OAAO,OAAO,GAAG;oBAAE,OAAO;gBAAI,CAAC;YAE3E,qBAAqB;YACrB,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,OAAO;gBACzC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY,IAAI,CAAC,0BAA0B,CAAC,sBAAsB;oBAClE,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,sBAAsB;YACtB,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,OAAO;gBAC1C,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY,IAAI,CAAC,0BAA0B,CAAC,uBAAuB;oBACnE,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,uBAAuB;YACvB,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,OAAO;gBAC3C,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY,IAAI,CAAC,0BAA0B,CAAC,wBAAwB;oBACpE,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,OAAO;IACT;IAEA,mCAAmC;IAC3B,uBAAuB,UAAwB,EAAkB;QACvE,MAAM,WAA2B,EAAE;QACnC,MAAM,mBAAmB;QAEzB,IAAI,WAAW,MAAM,GAAG,mBAAmB,GAAG,OAAO;QAErD,IAAK,IAAI,IAAI,mBAAmB,GAAG,IAAI,WAAW,MAAM,GAAG,GAAG,IAAK;YACjE,MAAM,cAAc,WAAW,KAAK,CAAC,IAAI,mBAAmB,GAAG,IAAI;YACnE,MAAM,aAAa,WAAW,KAAK,CAAC,IAAI,GAAG;YAE3C,qCAAqC;YACrC,MAAM,gBAAgB,IAAI,CAAC,sBAAsB,CAAC;YAElD,IAAI,gBAAgB,KAAK;gBACvB,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa;oBAClC,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN,YAAY,IAAI,CAAC,0BAA0B,CAAC,gBAAgB;wBAC5D,MAAM;wBACN,WAAW;wBACX,YAAY,IAAI;wBAChB,UAAU;wBACV,aAAa;oBACf;gBACF;YACF,OAAO,IAAI,gBAAgB,CAAC,KAAK;gBAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa;oBAClC,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN,YAAY,IAAI,CAAC,0BAA0B,CAAC,gBAAgB;wBAC5D,MAAM;wBACN,WAAW;wBACX,YAAY,IAAI;wBAChB,UAAU;wBACV,aAAa;oBACf;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,8BAA8B;IACtB,0BAA0B,UAAwB,EAAkB;QAC1E,MAAM,WAA2B,EAAE;QAEnC,IAAI,WAAW,MAAM,GAAG,GAAG,OAAO;QAElC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,UAAU,UAAU,CAAC,EAAE;YAC7B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAC/B,MAAM,QAAQ,UAAU,CAAC,IAAI,EAAE;YAE/B,OAAO;YACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;gBACxB,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,SAAS;YACT,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU;gBAC1B,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,gBAAgB;YAChB,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU;gBAChC,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,qBAAqB;YACrB,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,UAAU;gBAC3C,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa;gBACf;YACF;YAEA,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,UAAU;gBAC3C,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,OAAO;IACT;IAEA,2BAA2B;IACnB,uBAAuB,UAAwB,EAAkB;QACvE,MAAM,WAA2B,EAAE;QACnC,MAAM,WAAW;QAEjB,IAAI,WAAW,MAAM,GAAG,WAAW,GAAG,OAAO;QAE7C,IAAK,IAAI,IAAI,UAAU,IAAI,WAAW,MAAM,EAAE,IAAK;YACjD,MAAM,SAAS,WAAW,KAAK,CAAC,IAAI,UAAU;YAC9C,MAAM,UAAU,UAAU,CAAC,EAAE;YAE7B,MAAM,aAAa,KAAK,GAAG,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YACrD,MAAM,UAAU,KAAK,GAAG,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;YAEjD,sBAAsB;YACtB,IAAI,QAAQ,KAAK,GAAG,cAAc,QAAQ,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK;gBACtF,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa,aAAa,CAAC,aAAa,OAAO,IAAI;oBACnD,UAAU,aAAa;oBACvB,aAAa;gBACf;YACF;YAEA,oBAAoB;YACpB,IAAI,QAAQ,KAAK,GAAG,WAAW,QAAQ,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK;gBACnF,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,WAAW;oBACX,YAAY,IAAI;oBAChB,UAAU;oBACV,aAAa,UAAU,CAAC,aAAa,OAAO,IAAI;oBAChD,UAAU,UAAU;oBACpB,aAAa;gBACf;YACF;QACF;QAEA,OAAO;IACT;IAEA,wBAAwB;IAChB,oBAAoB,UAAwB,EAAE,UAA+B,EAAgB;QACnG,MAAM,aAAa,WAAW,KAAK,CAAC,CAAC;QACrC,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAC5C,MAAM,gBAAgB,IAAI,CAAC,sBAAsB,CAAC;QAElD,IAAI;QACJ,IAAI,kBAA4B,EAAE;QAElC,IAAI,KAAK,GAAG,CAAC,iBAAiB,KAAK;YACjC,OAAO;YACP,gBAAgB,IAAI,CAAC,gBAAgB,IAAI,mBAAmB;QAC9D,OAAO,IAAI,aAAa,MAAM;YAC5B,OAAO;YACP,gBAAgB,IAAI,CAAC,mBAAmB;QAC1C,OAAO,IAAI,aAAa,OAAO;YAC7B,OAAO;YACP,gBAAgB,IAAI,CAAC,kBAAkB;QACzC,OAAO;YACL,OAAO;YACP,gBAAgB,IAAI,CAAC,qBAAqB;QAC5C;QAEA,6BAA6B;QAC7B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,gBAAgB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM;QAE9D,IAAI,gBAAgB,YAAY,KAAK;YACnC,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,gBAAgB,YAAY,KAAK;YAC1C,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO;YACL;YACA,UAAU,KAAK,GAAG,CAAC;YACnB,UAAU,IAAI,CAAC,uBAAuB,CAAC,YAAY;YACnD;QACF;IACF;IAEA,6BAA6B;IACrB,yBAAyB,UAA+B,EAAE,WAAwB,EAAU;QAClG,IAAI,YAAY;QAChB,IAAI,UAAU;QAEd,gBAAgB;QAChB,IAAI,WAAW,GAAG,GAAG,IAAI,aAAa;aACjC,IAAI,WAAW,GAAG,GAAG,IAAI,aAAa;aACtC,IAAI,WAAW,GAAG,GAAG,IAAI,aAAa;aACtC,IAAI,WAAW,GAAG,GAAG,IAAI,aAAa;QAC3C;QAEA,iBAAiB;QACjB,IAAI,WAAW,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE,aAAa;aAC3D,aAAa;QAClB;QAEA,kBAAkB;QAClB,IAAI,YAAY,KAAK,KAAK,WAAW,aAAa,YAAY,QAAQ,GAAG;aACpE,IAAI,YAAY,KAAK,KAAK,WAAW,aAAa,YAAY,QAAQ,GAAG;QAC9E;QAEA,qBAAqB;QACrB,MAAM,gBAAgB;YACpB,kBAAkB;YAClB,gBAAgB;YAChB,WAAW;YACX,gBAAgB,CAAC;YACjB,kBAAkB,CAAC;QACrB,CAAC,CAAC,YAAY,QAAQ,CAAC;QAEvB,aAAa;QACb;QAEA,OAAO,UAAU,IAAI,YAAY,UAAU;IAC7C;IAEA,uBAAuB;IACf,iBACN,QAAwB,EACxB,MAAoB,EACpB,SAAiB,EACjB,WAAwB,EACZ;QACZ,IAAI,SAA+B;QACnC,IAAI,aAAa;QACjB,IAAI,YAAsB,EAAE;QAC5B,IAAI,YAAY,IAAI,kBAAkB;QACtC,IAAI,iBAAiB;QAErB,mBAAmB;QACnB,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACxD,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAExD,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,KAAK,GAAG,CAAC,gBAAgB,MAAM,EAAE;QACvH,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,KAAK,GAAG,CAAC,gBAAgB,MAAM,EAAE;QAEvH,iBAAiB;QACjB,IAAI,gBAAgB,MAAM,GAAG,gBAAgB,MAAM,IAAI,YAAY,KAAK;YACtE,SAAS;YACT,aAAa,KAAK,GAAG,CAAC,CAAC,oBAAoB,YAAY,KAAK,YAAY,UAAU,IAAI,GAAG;YACzF,UAAU,IAAI,CAAC,AAAC,GAAyB,OAAvB,gBAAgB,MAAM,EAAC;YACzC,UAAU,IAAI,CAAC,AAAC,8BAAkD,OAArB,UAAU,OAAO,CAAC,IAAG;YAClE,iBAAiB,KAAK,uBAAuB;QAC/C,OAAO,IAAI,gBAAgB,MAAM,GAAG,gBAAgB,MAAM,IAAI,YAAY,CAAC,KAAK;YAC9E,SAAS;YACT,aAAa,KAAK,GAAG,CAAC,CAAC,oBAAoB,KAAK,GAAG,CAAC,aAAa,KAAK,YAAY,UAAU,IAAI,GAAG;YACnG,UAAU,IAAI,CAAC,AAAC,GAAyB,OAAvB,gBAAgB,MAAM,EAAC;YACzC,UAAU,IAAI,CAAC,AAAC,8BAAkD,OAArB,UAAU,OAAO,CAAC,IAAG;YAClE,iBAAiB,KAAK,uBAAuB;QAC/C,OAAO;YACL,UAAU,IAAI,CAAC;YACf,UAAU,IAAI,CAAC,AAAC,kBAA6B,OAAZ,OAAO,IAAI;QAC9C;QAEA,2BAA2B;QAC3B,IAAI,OAAO,IAAI,KAAK,YAAY;YAC9B,aAAa;YACb,UAAU,IAAI,CAAC;QACjB,OAAO,IAAI,OAAO,IAAI,KAAK,SAAS;YAClC,aAAa;YACb,UAAU,IAAI,CAAC;QACjB;QAEA,0BAA0B;QAC1B,IAAI,YAAY,SAAS,KAAK,QAAQ;YACpC,aAAa;YACb,cAAc;QAChB,OAAO,IAAI,YAAY,SAAS,KAAK,OAAO;YAC1C,aAAa;YACb,cAAc;QAChB;QAEA,OAAO;YACL;YACA,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,KAAK;YAC/C;YACA;YACA,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,MAAM;YAC9C;YACA,aAAa,IAAI,CAAC,oBAAoB,CAAC,UAAU;QACnD;IACF;IAEA,uCAAuC;IAC/B,UAAU,IAAkB,EAAsC;QACxE,MAAM,QAA4C,EAAE;QAEpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YACxC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,IAAI,IAC9D,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,IAAI,EAAE;gBAClE,MAAM,IAAI,CAAC;oBAAE,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI;oBAAE,OAAO;gBAAE;YAC7C;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,IAAkB,EAAsC;QAC1E,MAAM,UAA8C,EAAE;QAEtD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YACxC,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,GAAG,IAC1D,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,GAAG,EAAE;gBAC9D,QAAQ,IAAI,CAAC;oBAAE,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG;oBAAE,OAAO;gBAAE;YAC9C;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,IAAS,EAAE,IAAS,EAAE,KAAU,EAAE,IAAkB,EAAW;QACxF,4CAA4C;QAC5C,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,EAAE,OAAO;QAElE,gDAAgD;QAChD,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,MAAM,KAAK;QAC1F,OAAO,eAAe;IACxB;IAEQ,YAAY,KAAU,EAAE,KAAU,EAAE,IAAkB,EAAW;QACvE,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK;QACzF,OAAO,YAAY,OAAO,cAAc;IAC1C;IAEQ,eAAe,OAAY,EAAE,OAAY,EAAE,IAAkB,EAAW;QAC9E,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,QAAQ,KAAK,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,QAAQ,KAAK;QACjG,OAAO,YAAY,OAAO,cAAc;IAC1C;IAEQ,oBAAoB,KAAY,EAAE,IAAW,EAAW;QAC9D,kDAAkD;QAClD,OAAO,OAAO,qBAAqB;IACrC;IAEQ,qBAAqB,KAAY,EAAE,IAAW,EAAW;QAC/D,mDAAmD;QACnD,OAAO,OAAO,qBAAqB;IACrC;IAEQ,sBAAsB,KAAY,EAAE,IAAW,EAAW;QAChE,oDAAoD;QACpD,OAAO,OAAO,qBAAqB;IACrC;IAEQ,cAAc,IAAkB,EAAW;QACjD,mDAAmD;QACnD,MAAM,aAAa,IAAI,CAAC,EAAE,CAAC,KAAK;QAChC,MAAM,YAAY,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;QAC7C,MAAM,UAAU,CAAC,aAAa,SAAS,IAAI;QAC3C,OAAO,UAAU,KAAK,UAAU,MAAM,eAAe;IACvD;IAEQ,cAAc,IAAkB,EAAW;QACjD,iDAAiD;QACjD,MAAM,aAAa,IAAI,CAAC,EAAE,CAAC,KAAK;QAChC,MAAM,YAAY,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;QAC7C,MAAM,OAAO,CAAC,YAAY,UAAU,IAAI;QACxC,OAAO,OAAO,KAAK,OAAO,MAAM,YAAY;IAC9C;IAEQ,OAAO,MAAkB,EAAW;QAC1C,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;QACpD,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;QAC3C,OAAO,WAAW,aAAa,KAAK,uCAAuC;IAC7E;IAEQ,SAAS,MAAkB,EAAW;QAC5C,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;QACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;QACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;QAEpE,OAAO,cAAc,WAAW,KAAK,cAAc,WAAW;IAChE;IAEQ,eAAe,MAAkB,EAAW;QAClD,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;QACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;QACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;QAEpE,OAAO,cAAc,WAAW,KAAK,cAAc,WAAW;IAChE;IAEQ,mBAAmB,IAAgB,EAAE,OAAmB,EAAW;QACzE,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,6BAA6B;QACvD,QAAQ,KAAK,GAAG,QAAQ,IAAI,IAAI,4BAA4B;QAC5D,QAAQ,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;QAClE,QAAQ,KAAK,GAAG,KAAK,IAAI,EAAE,qCAAqC;IACzE;IAEQ,mBAAmB,IAAgB,EAAE,OAAmB,EAAW;QACzE,OAAO,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,6BAA6B;QACvD,QAAQ,KAAK,GAAG,QAAQ,IAAI,IAAI,4BAA4B;QAC5D,QAAQ,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;QAClE,QAAQ,KAAK,GAAG,KAAK,IAAI,EAAE,qCAAqC;IACzE;IAEQ,2BAA2B,WAAmB,EAAE,IAAkB,EAAU;QAClF,+CAA+C;QAC/C,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,IAAI,iBAAiB;QAErB,IAAI,aAAa;YACf,MAAM,QAAQ,YAAY,IAAI,GAAG,YAAY,MAAM;YACnD,IAAI,QAAQ,IAAI;gBACd,iBAAiB,AAAC,YAAY,IAAI,GAAG,QAAS;YAChD;QACF;QAEA,wCAAwC;QACxC,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,MAAM;QAClD,MAAM,mBAAmB,gBAAgB;QAEzC,IAAI,aAAa;QACjB,IAAI,mBAAmB,KAAK,cAAc;aACrC,IAAI,mBAAmB,KAAK,cAAc;QAE/C,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,KAAK;IAC5C;IAEQ,uBAAuB,IAAkB,EAAU;QACzD,IAAI,KAAK,MAAM,GAAG,GAAG,OAAO;QAE5B,MAAM,aAAa,IAAI,CAAC,EAAE,CAAC,KAAK;QAChC,MAAM,YAAY,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;QAC7C,MAAM,SAAS,CAAC,YAAY,UAAU,IAAI;QAE1C,6BAA6B;QAC7B,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC;IAC7C;IAEQ,oBAAoB,IAAkB,EAAU;QACtD,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,IAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,IAAE,EAAE,CAAC,KAAK;YAC/D,QAAQ,IAAI,CAAC;QACf;QAEA,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM;QACxE,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,MAAM,IAAI,KAAK,QAAQ,MAAM;QAEhG,OAAO,KAAK,IAAI,CAAC;IACnB;IAEQ,iBAAiB,IAAkB,EAAU;QACnD,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE,KAAK,KAAK,MAAM;IAC3E;IAEQ,wBAAwB,IAAkB,EAAE,IAA0B,EAAU;QACtF,oEAAoE;QACpE,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAC/B;IAEQ,qBAAqB,QAAwB,EAAE,MAAoB,EAAU;QACnF,+DAA+D;QAC/D,IAAI,UAAU,IAAI,iBAAiB;QAEnC,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;YAC9E,UAAU,IAAI,oCAAoC;QACpD;QAEA,IAAI,OAAO,IAAI,KAAK,YAAY;YAC9B,WAAW,KAAK,mCAAmC;QACrD,OAAO,IAAI,OAAO,IAAI,KAAK,SAAS;YAClC,WAAW,KAAK,gCAAgC;QAClD;QAEA,OAAO,KAAK,KAAK,CAAC;IACpB;IAEQ,kBAAkB,IAAS,EAAE,IAAS,EAAE,KAAU,EAAE,IAAkB,EAAU;QACtF,wCAAwC;QACxC,MAAM,WAAW,CAAC,KAAK,KAAK,GAAG,MAAM,KAAK,IAAI;QAC9C,MAAM,aAAa,KAAK,KAAK,GAAG;QAChC,OAAO,WAAW;IACpB;IAEQ,yBAAyB,KAAU,EAAE,KAAU,EAAE,IAAkB,EAAU;QACnF,MAAM,UAAU,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QAC/C,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK;QACpD,OAAO,UAAU,CAAC,aAAa,OAAO,IAAI;IAC5C;IAEQ,4BAA4B,OAAY,EAAE,OAAY,EAAE,IAAkB,EAAU;QAC1F,MAAM,aAAa,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QACnD,MAAM,UAAU,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,QAAQ,KAAK;QACrD,OAAO,aAAa,CAAC,aAAa,OAAO,IAAI;IAC/C;IAEQ,4BAAkC;QACxC,6CAA6C;QAC7C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,sBAAsB;YAAE,MAAM;YAAG,QAAQ;QAAE;QACjE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc;YAAE,MAAM;YAAG,QAAQ;QAAE;QACzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,iBAAiB;YAAE,MAAM;YAAG,QAAQ;QAAE;QAC5D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB;YAAE,MAAM;YAAG,QAAQ;QAAE;QAC3D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB;YAAE,MAAM;YAAG,QAAQ;QAAE;IAC7D;IAEQ,mBACN,MAAc,EACd,UAAwB,EACxB,UAA+B,EAC/B,QAAoB,EACd;QACN,6CAA6C;QAC7C,MAAM,MAAM,AAAC,GAAY,OAAV,QAAO,KAAmB,OAAhB,SAAS,MAAM;QACxC,MAAM,WAAW;YACf,WAAW,GAAG;YACd,WAAW,IAAI,CAAC,IAAI;YACpB,WAAW,IAAI;YACf,SAAS,UAAU;YACnB,SAAS,SAAS;SACnB;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK;IAC7B;IA9uBA,aAAc;QAJd,+KAAQ,gBAAsC,IAAI;QAClD,+KAAQ,mBAA+C,IAAI;QAC3D,+KAAQ,gBAA8D,IAAI;QAGxE,IAAI,CAAC,yBAAyB;IAChC;AA6uBF", "debugId": null}}, {"offset": {"line": 6940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/lib/notifications/alert-system.ts"], "sourcesContent": ["// Alert and Notification System\nimport TelegramBot from 'node-telegram-bot-api';\nimport nodemailer from 'nodemailer';\nimport { TradeSignal } from '../trading/risk-management';\nimport { MarketState } from '../market-analysis/market-state';\nimport { RealTimeQuote } from '../data-providers/tradingview';\n\nexport interface AlertConfig {\n  telegram: {\n    botToken: string;\n    chatId: string;\n    enabled: boolean;\n  };\n  email: {\n    smtp: {\n      host: string;\n      port: number;\n      secure: boolean;\n      auth: {\n        user: string;\n        pass: string;\n      };\n    };\n    from: string;\n    to: string[];\n    enabled: boolean;\n  };\n  webhook?: {\n    url: string;\n    enabled: boolean;\n  };\n}\n\nexport interface Alert {\n  id: string;\n  type: 'TRADE_SIGNAL' | 'MARKET_UPDATE' | 'RISK_WARNING' | 'SYSTEM_STATUS' | 'COUNTDOWN';\n  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  title: string;\n  message: string;\n  data?: any;\n  timestamp: number;\n  sent: boolean;\n  channels: ('telegram' | 'email' | 'webhook')[];\n}\n\nexport interface CountdownAlert {\n  id: string;\n  signalId: string;\n  symbol: string;\n  action: string;\n  targetTime: number;\n  intervalId?: NodeJS.Timeout;\n  sent: boolean;\n}\n\nexport class AlertSystem {\n  private config: AlertConfig;\n  private telegramBot: TelegramBot | null = null;\n  private emailTransporter: nodemailer.Transporter | null = null;\n  private alertQueue: Alert[] = [];\n  private countdownAlerts: Map<string, CountdownAlert> = new Map();\n  private isProcessing = false;\n\n  constructor(config: AlertConfig) {\n    this.config = config;\n    this.initializeTelegram();\n    this.initializeEmail();\n    this.startAlertProcessor();\n  }\n\n  private initializeTelegram(): void {\n    if (this.config.telegram.enabled && this.config.telegram.botToken) {\n      try {\n        this.telegramBot = new TelegramBot(this.config.telegram.botToken, { polling: false });\n      } catch (error) {\n        console.error('Failed to initialize Telegram bot:', error);\n      }\n    }\n  }\n\n  private initializeEmail(): void {\n    if (this.config.email.enabled) {\n      try {\n        this.emailTransporter = nodemailer.createTransporter(this.config.email.smtp);\n      } catch (error) {\n        console.error('Failed to initialize email transporter:', error);\n      }\n    }\n  }\n\n  private startAlertProcessor(): void {\n    setInterval(() => {\n      this.processAlertQueue();\n    }, 1000); // Process every second\n  }\n\n  // Send trade signal alert\n  async sendTradeSignal(signal: TradeSignal, marketState: MarketState): Promise<void> {\n    const alert: Alert = {\n      id: this.generateAlertId(),\n      type: 'TRADE_SIGNAL',\n      priority: signal.confidence >= 80 ? 'HIGH' : signal.confidence >= 60 ? 'MEDIUM' : 'LOW',\n      title: `🚨 ${signal.type} Signal - ${signal.symbol}`,\n      message: this.formatTradeSignalMessage(signal, marketState),\n      data: { signal, marketState },\n      timestamp: Date.now(),\n      sent: false,\n      channels: ['telegram', 'email']\n    };\n\n    this.queueAlert(alert);\n\n    // Set up countdown alert (5 minutes before signal execution)\n    this.setupCountdownAlert(signal);\n  }\n\n  // Send market update alert\n  async sendMarketUpdate(symbol: string, marketState: MarketState, quote: RealTimeQuote): Promise<void> {\n    const alert: Alert = {\n      id: this.generateAlertId(),\n      type: 'MARKET_UPDATE',\n      priority: 'MEDIUM',\n      title: `📊 Market Update - ${symbol}`,\n      message: this.formatMarketUpdateMessage(symbol, marketState, quote),\n      data: { symbol, marketState, quote },\n      timestamp: Date.now(),\n      sent: false,\n      channels: ['telegram']\n    };\n\n    this.queueAlert(alert);\n  }\n\n  // Send risk warning\n  async sendRiskWarning(message: string, data?: any): Promise<void> {\n    const alert: Alert = {\n      id: this.generateAlertId(),\n      type: 'RISK_WARNING',\n      priority: 'CRITICAL',\n      title: '⚠️ Risk Warning',\n      message,\n      data,\n      timestamp: Date.now(),\n      sent: false,\n      channels: ['telegram', 'email']\n    };\n\n    this.queueAlert(alert);\n  }\n\n  // Send system status alert\n  async sendSystemStatus(status: 'ONLINE' | 'OFFLINE' | 'ERROR', message: string): Promise<void> {\n    const priority = status === 'ERROR' ? 'CRITICAL' : status === 'OFFLINE' ? 'HIGH' : 'LOW';\n    const emoji = status === 'ONLINE' ? '✅' : status === 'OFFLINE' ? '🔴' : '❌';\n\n    const alert: Alert = {\n      id: this.generateAlertId(),\n      type: 'SYSTEM_STATUS',\n      priority,\n      title: `${emoji} System ${status}`,\n      message,\n      timestamp: Date.now(),\n      sent: false,\n      channels: ['telegram', 'email']\n    };\n\n    this.queueAlert(alert);\n  }\n\n  // Setup countdown alert\n  private setupCountdownAlert(signal: TradeSignal): void {\n    const countdownTime = 5 * 60 * 1000; // 5 minutes\n    const targetTime = Date.now() + countdownTime;\n\n    const countdownAlert: CountdownAlert = {\n      id: this.generateAlertId(),\n      signalId: signal.id,\n      symbol: signal.symbol,\n      action: `${signal.type} at ${signal.entry}`,\n      targetTime,\n      sent: false\n    };\n\n    // Set up interval for countdown updates\n    const intervalId = setInterval(() => {\n      const timeRemaining = targetTime - Date.now();\n      \n      if (timeRemaining <= 0) {\n        this.sendCountdownComplete(countdownAlert);\n        clearInterval(intervalId);\n        this.countdownAlerts.delete(countdownAlert.id);\n      } else {\n        this.sendCountdownUpdate(countdownAlert, timeRemaining);\n      }\n    }, 60000); // Update every minute\n\n    countdownAlert.intervalId = intervalId;\n    this.countdownAlerts.set(countdownAlert.id, countdownAlert);\n  }\n\n  private async sendCountdownUpdate(countdown: CountdownAlert, timeRemaining: number): Promise<void> {\n    const minutes = Math.floor(timeRemaining / 60000);\n    \n    if (minutes === 5 || minutes === 3 || minutes === 1) {\n      const alert: Alert = {\n        id: this.generateAlertId(),\n        type: 'COUNTDOWN',\n        priority: 'MEDIUM',\n        title: `⏰ Trade Alert - ${countdown.symbol}`,\n        message: `${countdown.action} in ${minutes} minute${minutes > 1 ? 's' : ''}`,\n        data: countdown,\n        timestamp: Date.now(),\n        sent: false,\n        channels: ['telegram']\n      };\n\n      this.queueAlert(alert);\n    }\n  }\n\n  private async sendCountdownComplete(countdown: CountdownAlert): Promise<void> {\n    const alert: Alert = {\n      id: this.generateAlertId(),\n      type: 'COUNTDOWN',\n      priority: 'HIGH',\n      title: `🎯 Trade Execution - ${countdown.symbol}`,\n      message: `Time to execute: ${countdown.action}`,\n      data: countdown,\n      timestamp: Date.now(),\n      sent: false,\n      channels: ['telegram', 'email']\n    };\n\n    this.queueAlert(alert);\n  }\n\n  // Queue alert for processing\n  private queueAlert(alert: Alert): void {\n    this.alertQueue.push(alert);\n  }\n\n  // Process alert queue\n  private async processAlertQueue(): Promise<void> {\n    if (this.isProcessing || this.alertQueue.length === 0) return;\n\n    this.isProcessing = true;\n\n    try {\n      const alert = this.alertQueue.shift();\n      if (alert && !alert.sent) {\n        await this.sendAlert(alert);\n        alert.sent = true;\n      }\n    } catch (error) {\n      console.error('Error processing alert:', error);\n    } finally {\n      this.isProcessing = false;\n    }\n  }\n\n  // Send alert through configured channels\n  private async sendAlert(alert: Alert): Promise<void> {\n    const promises: Promise<void>[] = [];\n\n    if (alert.channels.includes('telegram') && this.config.telegram.enabled) {\n      promises.push(this.sendTelegramAlert(alert));\n    }\n\n    if (alert.channels.includes('email') && this.config.email.enabled) {\n      promises.push(this.sendEmailAlert(alert));\n    }\n\n    if (alert.channels.includes('webhook') && this.config.webhook?.enabled) {\n      promises.push(this.sendWebhookAlert(alert));\n    }\n\n    await Promise.allSettled(promises);\n  }\n\n  // Send Telegram alert\n  private async sendTelegramAlert(alert: Alert): Promise<void> {\n    if (!this.telegramBot) return;\n\n    try {\n      const message = this.formatTelegramMessage(alert);\n      await this.telegramBot.sendMessage(this.config.telegram.chatId, message, {\n        parse_mode: 'HTML',\n        disable_web_page_preview: true\n      });\n    } catch (error) {\n      console.error('Failed to send Telegram alert:', error);\n    }\n  }\n\n  // Send Email alert\n  private async sendEmailAlert(alert: Alert): Promise<void> {\n    if (!this.emailTransporter) return;\n\n    try {\n      const mailOptions = {\n        from: this.config.email.from,\n        to: this.config.email.to.join(', '),\n        subject: alert.title,\n        html: this.formatEmailMessage(alert)\n      };\n\n      await this.emailTransporter.sendMail(mailOptions);\n    } catch (error) {\n      console.error('Failed to send email alert:', error);\n    }\n  }\n\n  // Send Webhook alert\n  private async sendWebhookAlert(alert: Alert): Promise<void> {\n    if (!this.config.webhook?.url) return;\n\n    try {\n      const response = await fetch(this.config.webhook.url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(alert)\n      });\n\n      if (!response.ok) {\n        throw new Error(`Webhook request failed: ${response.statusText}`);\n      }\n    } catch (error) {\n      console.error('Failed to send webhook alert:', error);\n    }\n  }\n\n  // Format trade signal message\n  private formatTradeSignalMessage(signal: TradeSignal, marketState: MarketState): string {\n    const rrRatio = signal.riskRewardRatio.toFixed(2);\n    const confidence = signal.confidence.toFixed(1);\n    \n    return `\n🎯 <b>${signal.type} Signal</b>\n📈 Symbol: ${signal.symbol}\n💰 Entry: ${signal.entry}\n🛑 Stop Loss: ${signal.stopLoss}\n🎯 TP1: ${signal.takeProfit1}\n🎯 TP2: ${signal.takeProfit2}\n🎯 TP3: ${signal.takeProfit3}\n📊 R/R Ratio: ${rrRatio}:1\n🎲 Confidence: ${confidence}%\n\n📊 <b>Market Analysis:</b>\n📈 Trend: ${marketState.trend} (${marketState.strength}%)\n⚡ Momentum: ${marketState.momentum}\n💧 Liquidity: ${marketState.liquidity}\n🌊 Volatility: ${marketState.volatility}\n🕐 Session: ${marketState.session.name}\n⚠️ Risk Level: ${marketState.riskLevel}\n\n🧠 <b>Reasoning:</b>\n${signal.reasoning.map(reason => `• ${reason}`).join('\\n')}\n\n⏰ <i>Signal generated at ${new Date(signal.timestamp).toLocaleString()}</i>\n    `.trim();\n  }\n\n  // Format market update message\n  private formatMarketUpdateMessage(symbol: string, marketState: MarketState, quote: RealTimeQuote): string {\n    const changeEmoji = quote.change >= 0 ? '📈' : '📉';\n    const changeColor = quote.change >= 0 ? '🟢' : '🔴';\n    \n    return `\n📊 <b>Market Update - ${symbol}</b>\n\n💰 Price: ${quote.price}\n${changeEmoji} Change: ${quote.change} (${quote.changePercent.toFixed(2)}%) ${changeColor}\n📊 Bid/Ask: ${quote.bid}/${quote.ask}\n📈 Volume: ${quote.volume.toLocaleString()}\n\n🔍 <b>Market State:</b>\n📈 Trend: ${marketState.trend} (${marketState.strength}%)\n⚡ Momentum: ${marketState.momentum}\n💧 Liquidity: ${marketState.liquidity}\n🌊 Volatility: ${marketState.volatility}\n🕐 Session: ${marketState.session.name}\n⚠️ Risk Level: ${marketState.riskLevel}\n🎯 Confidence: ${marketState.confidence}%\n\n⏰ <i>Updated at ${new Date().toLocaleString()}</i>\n    `.trim();\n  }\n\n  // Format Telegram message\n  private formatTelegramMessage(alert: Alert): string {\n    const priorityEmoji = {\n      'LOW': '🔵',\n      'MEDIUM': '🟡',\n      'HIGH': '🟠',\n      'CRITICAL': '🔴'\n    };\n\n    return `\n${priorityEmoji[alert.priority]} <b>${alert.title}</b>\n\n${alert.message}\n\n⏰ <i>${new Date(alert.timestamp).toLocaleString()}</i>\n    `.trim();\n  }\n\n  // Format Email message\n  private formatEmailMessage(alert: Alert): string {\n    const priorityColor = {\n      'LOW': '#007bff',\n      'MEDIUM': '#ffc107',\n      'HIGH': '#fd7e14',\n      'CRITICAL': '#dc3545'\n    };\n\n    return `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <div style=\"background-color: ${priorityColor[alert.priority]}; color: white; padding: 20px; text-align: center;\">\n        <h1 style=\"margin: 0;\">${alert.title}</h1>\n      </div>\n      <div style=\"padding: 20px; background-color: #f8f9fa;\">\n        <div style=\"white-space: pre-line; line-height: 1.6;\">\n          ${alert.message.replace(/\\n/g, '<br>')}\n        </div>\n        <hr style=\"margin: 20px 0;\">\n        <p style=\"color: #6c757d; font-size: 14px; margin: 0;\">\n          Generated at ${new Date(alert.timestamp).toLocaleString()}\n        </p>\n      </div>\n    </div>\n    `;\n  }\n\n  // Generate unique alert ID\n  private generateAlertId(): string {\n    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Update configuration\n  updateConfig(newConfig: Partial<AlertConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    \n    if (newConfig.telegram) {\n      this.initializeTelegram();\n    }\n    \n    if (newConfig.email) {\n      this.initializeEmail();\n    }\n  }\n\n  // Get alert statistics\n  getAlertStats(): {\n    queueLength: number;\n    activeCountdowns: number;\n    totalSent: number;\n  } {\n    return {\n      queueLength: this.alertQueue.length,\n      activeCountdowns: this.countdownAlerts.size,\n      totalSent: 0 // This would be tracked in a real implementation\n    };\n  }\n\n  // Clear all countdown alerts\n  clearCountdownAlerts(): void {\n    this.countdownAlerts.forEach(countdown => {\n      if (countdown.intervalId) {\n        clearInterval(countdown.intervalId);\n      }\n    });\n    this.countdownAlerts.clear();\n  }\n\n  // Cleanup\n  destroy(): void {\n    this.clearCountdownAlerts();\n    this.alertQueue = [];\n    \n    if (this.telegramBot) {\n      this.telegramBot.stopPolling();\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;;;;;;;;;;;;;;AAuDzB,MAAM;IAeH,qBAA2B;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACjE,IAAI;gBACF,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAE,SAAS;gBAAM;YACrF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;IACF;IAEQ,kBAAwB;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;YAC7B,IAAI;gBACF,IAAI,CAAC,gBAAgB,GAAG,WAAW,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;YAC7E,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;YAC3D;QACF;IACF;IAEQ,sBAA4B;QAClC,YAAY;YACV,IAAI,CAAC,iBAAiB;QACxB,GAAG,OAAO,uBAAuB;IACnC;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAAmB,EAAE,WAAwB,EAAiB;QAClF,MAAM,QAAe;YACnB,IAAI,IAAI,CAAC,eAAe;YACxB,MAAM;YACN,UAAU,OAAO,UAAU,IAAI,KAAK,SAAS,OAAO,UAAU,IAAI,KAAK,WAAW;YAClF,OAAO,AAAC,MAA6B,OAAxB,OAAO,IAAI,EAAC,cAA0B,OAAd,OAAO,MAAM;YAClD,SAAS,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YAC/C,MAAM;gBAAE;gBAAQ;YAAY;YAC5B,WAAW,KAAK,GAAG;YACnB,MAAM;YACN,UAAU;gBAAC;gBAAY;aAAQ;QACjC;QAEA,IAAI,CAAC,UAAU,CAAC;QAEhB,6DAA6D;QAC7D,IAAI,CAAC,mBAAmB,CAAC;IAC3B;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,MAAc,EAAE,WAAwB,EAAE,KAAoB,EAAiB;QACpG,MAAM,QAAe;YACnB,IAAI,IAAI,CAAC,eAAe;YACxB,MAAM;YACN,UAAU;YACV,OAAO,AAAC,sBAA4B,OAAP;YAC7B,SAAS,IAAI,CAAC,yBAAyB,CAAC,QAAQ,aAAa;YAC7D,MAAM;gBAAE;gBAAQ;gBAAa;YAAM;YACnC,WAAW,KAAK,GAAG;YACnB,MAAM;YACN,UAAU;gBAAC;aAAW;QACxB;QAEA,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA,oBAAoB;IACpB,MAAM,gBAAgB,OAAe,EAAE,IAAU,EAAiB;QAChE,MAAM,QAAe;YACnB,IAAI,IAAI,CAAC,eAAe;YACxB,MAAM;YACN,UAAU;YACV,OAAO;YACP;YACA;YACA,WAAW,KAAK,GAAG;YACnB,MAAM;YACN,UAAU;gBAAC;gBAAY;aAAQ;QACjC;QAEA,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,MAAsC,EAAE,OAAe,EAAiB;QAC7F,MAAM,WAAW,WAAW,UAAU,aAAa,WAAW,YAAY,SAAS;QACnF,MAAM,QAAQ,WAAW,WAAW,MAAM,WAAW,YAAY,OAAO;QAExE,MAAM,QAAe;YACnB,IAAI,IAAI,CAAC,eAAe;YACxB,MAAM;YACN;YACA,OAAO,AAAC,GAAkB,OAAhB,OAAM,YAAiB,OAAP;YAC1B;YACA,WAAW,KAAK,GAAG;YACnB,MAAM;YACN,UAAU;gBAAC;gBAAY;aAAQ;QACjC;QAEA,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA,wBAAwB;IAChB,oBAAoB,MAAmB,EAAQ;QACrD,MAAM,gBAAgB,IAAI,KAAK,MAAM,YAAY;QACjD,MAAM,aAAa,KAAK,GAAG,KAAK;QAEhC,MAAM,iBAAiC;YACrC,IAAI,IAAI,CAAC,eAAe;YACxB,UAAU,OAAO,EAAE;YACnB,QAAQ,OAAO,MAAM;YACrB,QAAQ,AAAC,GAAoB,OAAlB,OAAO,IAAI,EAAC,QAAmB,OAAb,OAAO,KAAK;YACzC;YACA,MAAM;QACR;QAEA,wCAAwC;QACxC,MAAM,aAAa,YAAY;YAC7B,MAAM,gBAAgB,aAAa,KAAK,GAAG;YAE3C,IAAI,iBAAiB,GAAG;gBACtB,IAAI,CAAC,qBAAqB,CAAC;gBAC3B,cAAc;gBACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,eAAe,EAAE;YAC/C,OAAO;gBACL,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;YAC3C;QACF,GAAG,QAAQ,sBAAsB;QAEjC,eAAe,UAAU,GAAG;QAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,EAAE,EAAE;IAC9C;IAEA,MAAc,oBAAoB,SAAyB,EAAE,aAAqB,EAAiB;QACjG,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAE3C,IAAI,YAAY,KAAK,YAAY,KAAK,YAAY,GAAG;YACnD,MAAM,QAAe;gBACnB,IAAI,IAAI,CAAC,eAAe;gBACxB,MAAM;gBACN,UAAU;gBACV,OAAO,AAAC,mBAAmC,OAAjB,UAAU,MAAM;gBAC1C,SAAS,AAAC,GAAyB,OAAvB,UAAU,MAAM,EAAC,QAAuB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM;gBACxE,MAAM;gBACN,WAAW,KAAK,GAAG;gBACnB,MAAM;gBACN,UAAU;oBAAC;iBAAW;YACxB;YAEA,IAAI,CAAC,UAAU,CAAC;QAClB;IACF;IAEA,MAAc,sBAAsB,SAAyB,EAAiB;QAC5E,MAAM,QAAe;YACnB,IAAI,IAAI,CAAC,eAAe;YACxB,MAAM;YACN,UAAU;YACV,OAAO,AAAC,wBAAwC,OAAjB,UAAU,MAAM;YAC/C,SAAS,AAAC,oBAAoC,OAAjB,UAAU,MAAM;YAC7C,MAAM;YACN,WAAW,KAAK,GAAG;YACnB,MAAM;YACN,UAAU;gBAAC;gBAAY;aAAQ;QACjC;QAEA,IAAI,CAAC,UAAU,CAAC;IAClB;IAEA,6BAA6B;IACrB,WAAW,KAAY,EAAQ;QACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IACvB;IAEA,sBAAsB;IACtB,MAAc,oBAAmC;QAC/C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;QAEvD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK;YACnC,IAAI,SAAS,CAAC,MAAM,IAAI,EAAE;gBACxB,MAAM,IAAI,CAAC,SAAS,CAAC;gBACrB,MAAM,IAAI,GAAG;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEA,yCAAyC;IACzC,MAAc,UAAU,KAAY,EAAiB;YAWT;QAV1C,MAAM,WAA4B,EAAE;QAEpC,IAAI,MAAM,QAAQ,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE;YACvE,SAAS,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACvC;QAEA,IAAI,MAAM,QAAQ,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;YACjE,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;QACpC;QAEA,IAAI,MAAM,QAAQ,CAAC,QAAQ,CAAC,gBAAc,uBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,cAAnB,2CAAA,qBAAqB,OAAO,GAAE;YACtE,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACtC;QAEA,MAAM,QAAQ,UAAU,CAAC;IAC3B;IAEA,sBAAsB;IACtB,MAAc,kBAAkB,KAAY,EAAiB;QAC3D,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QAEvB,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,qBAAqB,CAAC;YAC3C,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,SAAS;gBACvE,YAAY;gBACZ,0BAA0B;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,mBAAmB;IACnB,MAAc,eAAe,KAAY,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,IAAI;YACF,MAAM,cAAc;gBAClB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI;gBAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;gBAC9B,SAAS,MAAM,KAAK;gBACpB,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAChC;YAEA,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,MAAc,iBAAiB,KAAY,EAAiB;YACrD;QAAL,IAAI,GAAC,uBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,cAAnB,2CAAA,qBAAqB,GAAG,GAAE;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,2BAA8C,OAApB,SAAS,UAAU;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,8BAA8B;IACtB,yBAAyB,MAAmB,EAAE,WAAwB,EAAU;QACtF,MAAM,UAAU,OAAO,eAAe,CAAC,OAAO,CAAC;QAC/C,MAAM,aAAa,OAAO,UAAU,CAAC,OAAO,CAAC;QAE7C,OAAO,AAAC,WAEC,OADL,OAAO,IAAI,EAAC,4BAER,OADC,OAAO,MAAM,EAAC,gBAEX,OADJ,OAAO,KAAK,EAAC,oBAEf,OADM,OAAO,QAAQ,EAAC,cAEtB,OADA,OAAO,WAAW,EAAC,cAEnB,OADA,OAAO,WAAW,EAAC,cAEb,OADN,OAAO,WAAW,EAAC,oBAEZ,OADD,SAAQ,uBAIZ,OAHK,YAAW,+CAGM,OAAtB,YAAY,KAAK,EAAC,MAChB,OADoB,YAAY,QAAQ,EAAC,oBAEvC,OADF,YAAY,QAAQ,EAAC,oBAElB,OADD,YAAY,SAAS,EAAC,qBAExB,OADG,YAAY,UAAU,EAAC,kBAEvB,OADH,YAAY,OAAO,CAAC,IAAI,EAAC,qBAIrC,OAHe,YAAY,SAAS,EAAC,8BAKZ,OAFzB,OAAO,SAAS,CAAC,GAAG,CAAC,CAAA,SAAU,AAAC,KAAW,OAAP,SAAU,IAAI,CAAC,OAAM,iCAEY,OAA5C,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,IAAG,cACjE,IAAI;IACR;IAEA,+BAA+B;IACvB,0BAA0B,MAAc,EAAE,WAAwB,EAAE,KAAoB,EAAU;QACxG,MAAM,cAAc,MAAM,MAAM,IAAI,IAAI,OAAO;QAC/C,MAAM,cAAc,MAAM,MAAM,IAAI,IAAI,OAAO;QAE/C,OAAO,AAAC,2BAGA,OAFY,QAAO,sBAG7B,OADU,MAAM,KAAK,EAAC,MACC,OAAvB,aAAY,aAA4B,OAAjB,MAAM,MAAM,EAAC,MAAwC,OAApC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAG,OAC3D,OADgE,aAAY,kBAC/D,OAAb,MAAM,GAAG,EAAC,KACX,OADc,MAAM,GAAG,EAAC,iBAIzB,OAHC,MAAM,MAAM,CAAC,cAAc,IAAG,2CAGT,OAAtB,YAAY,KAAK,EAAC,MAChB,OADoB,YAAY,QAAQ,EAAC,oBAEvC,OADF,YAAY,QAAQ,EAAC,oBAElB,OADD,YAAY,SAAS,EAAC,qBAExB,OADG,YAAY,UAAU,EAAC,kBAEvB,OADH,YAAY,OAAO,CAAC,IAAI,EAAC,qBAEtB,OADA,YAAY,SAAS,EAAC,qBAGrB,OAFD,YAAY,UAAU,EAAC,yBAEM,OAA5B,IAAI,OAAO,cAAc,IAAG,cACxC,IAAI;IACR;IAEA,0BAA0B;IAClB,sBAAsB,KAAY,EAAU;QAClD,MAAM,gBAAgB;YACpB,OAAO;YACP,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QAEA,OAAO,AAAC,KAC0B,OAApC,aAAa,CAAC,MAAM,QAAQ,CAAC,EAAC,QAE9B,OAFoC,MAAM,KAAK,EAAC,YAI3C,OAFL,MAAM,OAAO,EAAC,aAEkC,OAA3C,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc,IAAG,cAC5C,IAAI;IACR;IAEA,uBAAuB;IACf,mBAAmB,KAAY,EAAU;QAC/C,MAAM,gBAAgB;YACpB,OAAO;YACP,UAAU;YACV,QAAQ;YACR,YAAY;QACd;QAEA,OAAO,AAAC,8HAGqB,OADK,aAAa,CAAC,MAAM,QAAQ,CAAC,EAAC,yFAKxD,OAJqB,MAAM,KAAK,EAAC,kKAQpB,OAJb,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,SAAQ,oJAImB,OAA3C,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc,IAAG;IAKlE;IAEA,2BAA2B;IACnB,kBAA0B;QAChC,OAAO,AAAC,SAAsB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACrE;IAEA,uBAAuB;IACvB,aAAa,SAA+B,EAAQ;QAClD,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QAE7C,IAAI,UAAU,QAAQ,EAAE;YACtB,IAAI,CAAC,kBAAkB;QACzB;QAEA,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI,CAAC,eAAe;QACtB;IACF;IAEA,uBAAuB;IACvB,gBAIE;QACA,OAAO;YACL,aAAa,IAAI,CAAC,UAAU,CAAC,MAAM;YACnC,kBAAkB,IAAI,CAAC,eAAe,CAAC,IAAI;YAC3C,WAAW,EAAE,iDAAiD;QAChE;IACF;IAEA,6BAA6B;IAC7B,uBAA6B;QAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC3B,IAAI,UAAU,UAAU,EAAE;gBACxB,cAAc,UAAU,UAAU;YACpC;QACF;QACA,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;IAEA,UAAU;IACV,UAAgB;QACd,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,WAAW;QAC9B;IACF;IAraA,YAAY,MAAmB,CAAE;QAPjC,+KAAQ,UAAR,KAAA;QACA,+KAAQ,eAAkC;QAC1C,+KAAQ,oBAAkD;QAC1D,+KAAQ,cAAsB,EAAE;QAChC,+KAAQ,mBAA+C,IAAI;QAC3D,+KAAQ,gBAAe;QAGrB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,mBAAmB;IAC1B;AAiaF", "debugId": null}}, {"offset": {"line": 7305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/KK/ai-trading-bot/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport TradingDashboard from '@/components/TradingDashboard';\nimport { TradingViewProvider } from '@/lib/data-providers/tradingview';\nimport { TechnicalAnalysisEngine } from '@/lib/technical-analysis/indicators';\nimport { RiskManagementEngine } from '@/lib/trading/risk-management';\nimport { MarketStateAnalyzer } from '@/lib/market-analysis/market-state';\nimport { AIPatternRecognition } from '@/lib/ai/pattern-recognition';\nimport { AlertSystem } from '@/lib/notifications/alert-system';\n\nexport default function Home() {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [tradingProvider, setTradingProvider] = useState<TradingViewProvider | null>(null);\n  const [analysisEngine, setAnalysisEngine] = useState<TechnicalAnalysisEngine | null>(null);\n  const [riskManager, setRiskManager] = useState<RiskManagementEngine | null>(null);\n  const [marketAnalyzer, setMarketAnalyzer] = useState<MarketStateAnalyzer | null>(null);\n  const [aiEngine, setAiEngine] = useState<AIPatternRecognition | null>(null);\n  const [alertSystem, setAlertSystem] = useState<AlertSystem | null>(null);\n\n  useEffect(() => {\n    initializeTradingBot();\n  }, []);\n\n  const initializeTradingBot = async () => {\n    try {\n      // Initialize TradingView provider\n      const provider = new TradingViewProvider({\n        symbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],\n        timeframes: ['1m', '5m', '15m', '1h', '4h', '1d'],\n      });\n\n      // Initialize risk management\n      const riskMgmt = new RiskManagementEngine({\n        maxRiskPerTrade: 2, // 2% per trade\n        maxDailyRisk: 6, // 6% daily\n        maxOpenTrades: 3,\n        minRiskRewardRatio: 1.5,\n        accountBalance: 10000 // $10,000 demo account\n      });\n\n      // Initialize market analyzer\n      const analyzer = new MarketStateAnalyzer('UTC');\n\n      // Initialize AI engine\n      const ai = new AIPatternRecognition();\n\n      // Initialize alert system (with demo config)\n      const alerts = new AlertSystem({\n        telegram: {\n          botToken: process.env.NEXT_PUBLIC_TELEGRAM_BOT_TOKEN || '',\n          chatId: process.env.NEXT_PUBLIC_TELEGRAM_CHAT_ID || '',\n          enabled: false // Disabled for demo\n        },\n        email: {\n          smtp: {\n            host: 'smtp.gmail.com',\n            port: 587,\n            secure: false,\n            auth: {\n              user: '',\n              pass: ''\n            }\n          },\n          from: '',\n          to: [],\n          enabled: false // Disabled for demo\n        }\n      });\n\n      // Connect to data provider\n      await provider.connect();\n\n      // Subscribe to major pairs\n      await provider.subscribe('EURUSD');\n      await provider.subscribe('GBPUSD');\n      await provider.subscribe('USDJPY');\n      await provider.subscribe('XAUUSD');\n\n      // Set up real-time analysis\n      provider.on('candle', (data) => {\n        performRealTimeAnalysis(data.symbol, provider, riskMgmt, analyzer, ai, alerts);\n      });\n\n      // Update state\n      setTradingProvider(provider);\n      setRiskManager(riskMgmt);\n      setMarketAnalyzer(analyzer);\n      setAiEngine(ai);\n      setAlertSystem(alerts);\n      setIsInitialized(true);\n\n      console.log('🚀 AI Trading Bot initialized successfully!');\n    } catch (error) {\n      console.error('❌ Failed to initialize trading bot:', error);\n    }\n  };\n\n  const performRealTimeAnalysis = async (\n    symbol: string,\n    provider: TradingViewProvider,\n    riskMgmt: RiskManagementEngine,\n    analyzer: MarketStateAnalyzer,\n    ai: AIPatternRecognition,\n    alerts: AlertSystem\n  ) => {\n    try {\n      // Get chart data\n      const chartData = provider.getChartData(symbol);\n      if (!chartData || chartData.data.length < 50) return;\n\n      // Perform technical analysis\n      const engine = new TechnicalAnalysisEngine(chartData.data);\n      const indicators = engine.calculateAllIndicators();\n\n      // Analyze market state\n      const marketState = analyzer.analyzeMarketState(chartData.data, indicators, symbol);\n\n      // Get AI decision\n      const aiDecision = ai.makeDecision(chartData.data, indicators, marketState, symbol);\n\n      // Generate trade signal if conditions are met\n      const currentQuote = provider.getCurrentQuote(symbol);\n      if (currentQuote && aiDecision.confidence > 70) {\n        const signal = riskMgmt.generateTradeSignal(\n          symbol,\n          currentQuote.price,\n          indicators,\n          chartData.data\n        );\n\n        if (signal) {\n          // Send alert\n          await alerts.sendTradeSignal(signal, marketState);\n          console.log(`🎯 Trade signal generated for ${symbol}:`, signal);\n        }\n      }\n\n      // Send market update (every 5 minutes)\n      if (Date.now() % (5 * 60 * 1000) < 1000 && currentQuote) {\n        await alerts.sendMarketUpdate(symbol, marketState, currentQuote);\n      }\n\n    } catch (error) {\n      console.error(`Error analyzing ${symbol}:`, error);\n    }\n  };\n\n  if (!isInitialized) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4\"></div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">🤖 AI Trading Bot</h1>\n          <p className=\"text-blue-200\">Initializing advanced trading systems...</p>\n          <div className=\"mt-4 space-y-2 text-sm text-blue-300\">\n            <p>✅ Loading technical indicators</p>\n            <p>✅ Connecting to market data</p>\n            <p>✅ Initializing AI pattern recognition</p>\n            <p>✅ Setting up risk management</p>\n            <p>🔄 Starting real-time analysis...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <TradingDashboard\n        tradingProvider={tradingProvider!}\n        riskManager={riskManager!}\n        marketAnalyzer={marketAnalyzer!}\n        aiEngine={aiEngine!}\n        alertSystem={alertSystem!}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAkDoB;;AAhDpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,IAAI,iJAAA,CAAA,sBAAmB,CAAC;gBACvC,SAAS;oBAAC;oBAAU;oBAAU;oBAAU;iBAAS;gBACjD,YAAY;oBAAC;oBAAM;oBAAM;oBAAO;oBAAM;oBAAM;iBAAK;YACnD;YAEA,6BAA6B;YAC7B,MAAM,WAAW,IAAI,8IAAA,CAAA,uBAAoB,CAAC;gBACxC,iBAAiB;gBACjB,cAAc;gBACd,eAAe;gBACf,oBAAoB;gBACpB,gBAAgB,MAAM,uBAAuB;YAC/C;YAEA,6BAA6B;YAC7B,MAAM,WAAW,IAAI,sJAAA,CAAA,sBAAmB,CAAC;YAEzC,uBAAuB;YACvB,MAAM,KAAK,IAAI,6IAAA,CAAA,uBAAoB;YAEnC,6CAA6C;YAC7C,MAAM,SAAS,IAAI,iJAAA,CAAA,cAAW,CAAC;gBAC7B,UAAU;oBACR,UAAU,oEAA8C;oBACxD,QAAQ,kEAA4C;oBACpD,SAAS,MAAM,oBAAoB;gBACrC;gBACA,OAAO;oBACL,MAAM;wBACJ,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,MAAM;4BACJ,MAAM;4BACN,MAAM;wBACR;oBACF;oBACA,MAAM;oBACN,IAAI,EAAE;oBACN,SAAS,MAAM,oBAAoB;gBACrC;YACF;YAEA,2BAA2B;YAC3B,MAAM,SAAS,OAAO;YAEtB,2BAA2B;YAC3B,MAAM,SAAS,SAAS,CAAC;YACzB,MAAM,SAAS,SAAS,CAAC;YACzB,MAAM,SAAS,SAAS,CAAC;YACzB,MAAM,SAAS,SAAS,CAAC;YAEzB,4BAA4B;YAC5B,SAAS,EAAE,CAAC,UAAU,CAAC;gBACrB,wBAAwB,KAAK,MAAM,EAAE,UAAU,UAAU,UAAU,IAAI;YACzE;YAEA,eAAe;YACf,mBAAmB;YACnB,eAAe;YACf,kBAAkB;YAClB,YAAY;YACZ,eAAe;YACf,iBAAiB;YAEjB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,MAAM,0BAA0B,OAC9B,QACA,UACA,UACA,UACA,IACA;QAEA,IAAI;YACF,iBAAiB;YACjB,MAAM,YAAY,SAAS,YAAY,CAAC;YACxC,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,MAAM,GAAG,IAAI;YAE9C,6BAA6B;YAC7B,MAAM,SAAS,IAAI,oJAAA,CAAA,0BAAuB,CAAC,UAAU,IAAI;YACzD,MAAM,aAAa,OAAO,sBAAsB;YAEhD,uBAAuB;YACvB,MAAM,cAAc,SAAS,kBAAkB,CAAC,UAAU,IAAI,EAAE,YAAY;YAE5E,kBAAkB;YAClB,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,IAAI,EAAE,YAAY,aAAa;YAE5E,8CAA8C;YAC9C,MAAM,eAAe,SAAS,eAAe,CAAC;YAC9C,IAAI,gBAAgB,WAAW,UAAU,GAAG,IAAI;gBAC9C,MAAM,SAAS,SAAS,mBAAmB,CACzC,QACA,aAAa,KAAK,EAClB,YACA,UAAU,IAAI;gBAGhB,IAAI,QAAQ;oBACV,aAAa;oBACb,MAAM,OAAO,eAAe,CAAC,QAAQ;oBACrC,QAAQ,GAAG,CAAC,AAAC,iCAAuC,OAAP,QAAO,MAAI;gBAC1D;YACF;YAEA,uCAAuC;YACvC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,QAAQ,cAAc;gBACvD,MAAM,OAAO,gBAAgB,CAAC,QAAQ,aAAa;YACrD;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,mBAAyB,OAAP,QAAO,MAAI;QAC9C;IACF;IAEA,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,UAAgB;YACf,iBAAiB;YACjB,aAAa;YACb,gBAAgB;YAChB,UAAU;YACV,aAAa;;;;;;;;;;;AAIrB;GAvKwB;KAAA", "debugId": null}}]}