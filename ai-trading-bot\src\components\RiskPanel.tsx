'use client';

import React from 'react';
import { RiskManagementEngine, TradeSignal } from '@/lib/trading/risk-management';
import { RealTimeQuote } from '@/lib/data-providers/tradingview';
import { Shield, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react';

interface RiskPanelProps {
  riskManager: RiskManagementEngine;
  signals: TradeSignal[];
  quotes: Map<string, RealTimeQuote>;
}

const RiskPanel: React.FC<RiskPanelProps> = ({
  riskManager,
  signals,
  quotes
}) => {
  const activeTrades = riskManager.getActiveTrades();
  const totalRiskExposure = riskManager.getTotalRiskExposure();

  // Mock account data for demo
  const accountData = {
    balance: 10000,
    equity: 10000,
    margin: 0,
    freeMargin: 10000,
    marginLevel: 0
  };

  const riskMetrics = {
    dailyRisk: (totalRiskExposure / accountData.balance) * 100,
    maxDrawdown: 5.2,
    winRate: 68.5,
    profitFactor: 1.85,
    sharpeRatio: 1.42
  };

  return (
    <div className="space-y-6">
      {/* Risk Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <DollarSign className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Account Balance</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                ${accountData.balance.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Daily Risk</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {riskMetrics.dailyRisk.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingUp className="w-8 h-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {riskMetrics.winRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <AlertTriangle className="w-8 h-8 text-orange-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Max Drawdown</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {riskMetrics.maxDrawdown.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Risk Management Settings
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Risk Per Trade
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="0.5"
                    max="5"
                    step="0.1"
                    defaultValue="2"
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-12">2.0%</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Daily Risk
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="2"
                    max="10"
                    step="0.5"
                    defaultValue="6"
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-12">6.0%</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Open Trades
                </label>
                <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3" selected>3</option>
                  <option value="4">4</option>
                  <option value="5">5</option>
                </select>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Min Risk/Reward Ratio
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="1"
                    max="3"
                    step="0.1"
                    defaultValue="1.5"
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-12">1.5:1</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Stop Loss Type
                </label>
                <select className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                  <option>Fixed Percentage</option>
                  <option>ATR Based</option>
                  <option>Support/Resistance</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="trailingStop"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="trailingStop" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Enable trailing stop loss
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Performance Metrics
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {riskMetrics.profitFactor.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Profit Factor</div>
              <div className="text-xs text-gray-500 mt-1">
                Gross Profit / Gross Loss
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {riskMetrics.sharpeRatio.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Sharpe Ratio</div>
              <div className="text-xs text-gray-500 mt-1">
                Risk-adjusted return
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {signals.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Signals</div>
              <div className="text-xs text-gray-500 mt-1">
                Generated today
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Trades */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Active Trades
          </h3>
        </div>
        
        <div className="p-6">
          {activeTrades.length > 0 ? (
            <div className="space-y-4">
              {activeTrades.map(trade => {
                const currentQuote = quotes.get(trade.symbol);
                const currentPrice = currentQuote?.price || trade.entry;
                const pnl = trade.type === 'BUY' ? 
                  currentPrice - trade.entry : 
                  trade.entry - currentPrice;
                const pnlPercentage = (pnl / trade.entry) * 100;

                return (
                  <div key={trade.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          trade.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'
                        }`}></div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {trade.type} {trade.symbol}
                        </span>
                      </div>
                      <div className={`text-right ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        <div className="font-semibold">
                          {pnl >= 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%
                        </div>
                        <div className="text-xs">
                          ${Math.abs(pnl * 1000).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Entry:</span>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {trade.entry.toFixed(5)}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Current:</span>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {currentPrice.toFixed(5)}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Stop Loss:</span>
                        <div className="font-medium text-red-600">
                          {trade.stopLoss.toFixed(5)}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Take Profit:</span>
                        <div className="font-medium text-green-600">
                          {trade.takeProfit1.toFixed(5)}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 flex justify-end space-x-2">
                      <button className="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded">
                        Modify
                      </button>
                      <button className="px-3 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded">
                        Close
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                No active trades. Risk exposure: 0%
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Risk Warnings */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-3 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
              Risk Management Notice
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              This is a demo trading bot. All trades are simulated. Never risk more than you can afford to lose in live trading.
              Past performance does not guarantee future results.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskPanel;
