'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Simulate initialization
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 3000);

    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(timeInterval);
    };
  }, []);

  // Mock market data
  const mockMarketData = [
    { symbol: 'EURUSD', price: 1.0850, change: 0.0012, changePercent: 0.11, trend: 'BULLISH' },
    { symbol: 'GBPUSD', price: 1.2650, change: -0.0025, changePercent: -0.20, trend: 'BEARISH' },
    { symbol: 'USDJPY', price: 149.50, change: 0.35, changePercent: 0.23, trend: 'BULLISH' },
    { symbol: 'XAUUSD', price: 2050.00, change: 15.50, changePercent: 0.76, trend: 'BULLISH' },
  ];

  const mockSignals = [
    {
      id: '1',
      symbol: 'EURUSD',
      type: 'BUY',
      entry: 1.0850,
      stopLoss: 1.0820,
      takeProfit: 1.0900,
      confidence: 85,
      timestamp: Date.now() - 300000,
      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']
    },
    {
      id: '2',
      symbol: 'GBPUSD',
      type: 'SELL',
      entry: 1.2650,
      stopLoss: 1.2680,
      takeProfit: 1.2600,
      confidence: 78,
      timestamp: Date.now() - 600000,
      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']
    }
  ];

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-white mb-2">🤖 AI Trading Bot</h1>
          <p className="text-blue-200">Initializing advanced trading systems...</p>
          <div className="mt-4 space-y-2 text-sm text-blue-300">
            <p>✅ Loading technical indicators</p>
            <p>✅ Connecting to market data</p>
            <p>✅ Initializing AI pattern recognition</p>
            <p>✅ Setting up risk management</p>
            <p>🔄 Starting real-time analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                🤖 AI Trading Bot
              </h1>
              <div className="ml-6 flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">Live</span>
              </div>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {currentTime.toLocaleTimeString()}
            </div>
          </div>
        </div>
      </header>

      {/* Main Dashboard */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Market Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {mockMarketData.map(market => (
            <div key={market.symbol} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {market.symbol}
                </h3>
                <div className={`w-4 h-4 ${
                  market.trend === 'BULLISH' ? 'text-green-500' : 'text-red-500'
                }`}>
                  {market.trend === 'BULLISH' ? '📈' : '📉'}
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">
                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}
                  </span>
                  <span className={`text-sm font-medium ${
                    market.change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%
                  </span>
                </div>
                <div className={`text-xs px-2 py-1 rounded-full text-center ${
                  market.trend === 'BULLISH'
                    ? 'text-green-600 bg-green-50 dark:bg-green-900/20'
                    : 'text-red-600 bg-red-50 dark:bg-red-900/20'
                }`}>
                  {market.trend}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trading Signals */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              🎯 AI Trading Signals
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {mockSignals.map(signal => (
                <div key={signal.id} className={`border rounded-lg p-4 ${
                  signal.type === 'BUY'
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                    : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                }`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-5 h-5 ${
                        signal.type === 'BUY' ? 'text-green-500' : 'text-red-500'
                      }`}>
                        {signal.type === 'BUY' ? '📈' : '📉'}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold text-gray-900 dark:text-white">
                            {signal.type} {signal.symbol}
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {signal.confidence}% confidence
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {new Date(signal.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Entry:</span>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {signal.entry.toFixed(5)}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Stop Loss:</span>
                      <div className="font-medium text-red-600">
                        {signal.stopLoss.toFixed(5)}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Take Profit:</span>
                      <div className="font-medium text-green-600">
                        {signal.takeProfit.toFixed(5)}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-white dark:bg-gray-700 rounded border">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      🧠 AI Analysis:
                    </h4>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {signal.reasoning.map((reason, index) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{reason}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Technical Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">RSI (EURUSD)</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">45.2</div>
            <div className="text-sm text-green-600">Neutral</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">MACD</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">0.0012</div>
            <div className="text-sm text-green-600">Bullish</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">VWAP</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">1.0845</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Volume Weighted</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Supertrend</h3>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">1.0820</div>
            <div className="text-sm text-green-600">Uptrend</div>
          </div>
        </div>

        {/* Risk Management */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              🛡️ Risk Management
            </h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">$10,000</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Account Balance</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">2.5%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Daily Risk</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">68.5%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Win Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">2</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Active Signals</div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>🤖 AI Trading Bot v1.0</span>
              <span>•</span>
              <span>Demo Mode Active</span>
              <span>•</span>
              <span>Last Update: {currentTime.toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Powered by AI</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
