'use client';

import { useState, useEffect } from 'react';
import AutoTradingRobot from '../components/AutoTradingRobot';
import AdvancedMarketAnalysis from '../components/AdvancedMarketAnalysis';
import TimeframeAnalysis from '../components/TimeframeAnalysis';
import ManualAIRecommendations from '../components/ManualAIRecommendations';
import FibonacciAnalysis from '../components/FibonacciAnalysis';
import ProfessionalOrderBlocks from '../components/ProfessionalOrderBlocks';
import ProfessionalRecommendationEngine from '../components/ProfessionalRecommendationEngine';
import ProfessionalTradingSystem from '../components/ProfessionalTradingSystem';

export default function Home() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Simulate initialization
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 3000);

    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(timeInterval);
    };
  }, []);

  // Enhanced real-time market data with all major currencies and commodities
  const [marketData, setMarketData] = useState([
    // Major Forex Pairs
    {
      symbol: 'EURUSD',
      name: 'يورو/دولار أمريكي',
      price: 1.0850,
      change: 0.0012,
      changePercent: 0.11,
      trend: 'BULLISH',
      volume: 1250000,
      volatility: 'MEDIUM',
      liquidity: 'HIGH',
      session: 'LONDON',
      riskLevel: 'LOW',
      orderBlocks: 3,
      fvg: 2,
      choch: 1,
      bos: 0,
      flag: '🇪🇺🇺🇸'
    },
    {
      symbol: 'GBPUSD',
      name: 'جنيه إسترليني/دولار أمريكي',
      price: 1.2650,
      change: -0.0025,
      changePercent: -0.20,
      trend: 'BEARISH',
      volume: 980000,
      volatility: 'HIGH',
      liquidity: 'MEDIUM',
      session: 'LONDON',
      riskLevel: 'MEDIUM',
      orderBlocks: 2,
      fvg: 1,
      choch: 0,
      bos: 1,
      flag: '🇬🇧🇺🇸'
    },
    {
      symbol: 'USDJPY',
      name: 'دولار أمريكي/ين ياباني',
      price: 149.50,
      change: 0.35,
      changePercent: 0.23,
      trend: 'BULLISH',
      volume: 1100000,
      volatility: 'MEDIUM',
      liquidity: 'HIGH',
      session: 'ASIAN',
      riskLevel: 'LOW',
      orderBlocks: 4,
      fvg: 3,
      choch: 1,
      bos: 1,
      flag: '🇺🇸🇯🇵'
    },
    {
      symbol: 'USDCHF',
      name: 'دولار أمريكي/فرنك سويسري',
      price: 0.8920,
      change: 0.0008,
      changePercent: 0.09,
      trend: 'BULLISH',
      volume: 650000,
      volatility: 'LOW',
      liquidity: 'HIGH',
      session: 'LONDON',
      riskLevel: 'LOW',
      orderBlocks: 2,
      fvg: 1,
      choch: 0,
      bos: 0,
      flag: '🇺🇸🇨🇭'
    },
    {
      symbol: 'AUDUSD',
      name: 'دولار أسترالي/دولار أمريكي',
      price: 0.6580,
      change: -0.0015,
      changePercent: -0.23,
      trend: 'BEARISH',
      volume: 720000,
      volatility: 'MEDIUM',
      liquidity: 'MEDIUM',
      session: 'ASIAN',
      riskLevel: 'MEDIUM',
      orderBlocks: 3,
      fvg: 2,
      choch: 1,
      bos: 0,
      flag: '🇦🇺🇺🇸'
    },
    {
      symbol: 'USDCAD',
      name: 'دولار أمريكي/دولار كندي',
      price: 1.3650,
      change: 0.0020,
      changePercent: 0.15,
      trend: 'BULLISH',
      volume: 580000,
      volatility: 'MEDIUM',
      liquidity: 'MEDIUM',
      session: 'NEW_YORK',
      riskLevel: 'LOW',
      orderBlocks: 2,
      fvg: 1,
      choch: 0,
      bos: 1,
      flag: '🇺🇸🇨🇦'
    },
    {
      symbol: 'NZDUSD',
      name: 'دولار نيوزيلندي/دولار أمريكي',
      price: 0.6120,
      change: -0.0012,
      changePercent: -0.19,
      trend: 'BEARISH',
      volume: 420000,
      volatility: 'HIGH',
      liquidity: 'LOW',
      session: 'ASIAN',
      riskLevel: 'HIGH',
      orderBlocks: 1,
      fvg: 1,
      choch: 0,
      bos: 0,
      flag: '🇳🇿🇺🇸'
    },
    {
      symbol: 'EURGBP',
      name: 'يورو/جنيه إسترليني',
      price: 0.8580,
      change: 0.0005,
      changePercent: 0.06,
      trend: 'NEUTRAL',
      volume: 480000,
      volatility: 'LOW',
      liquidity: 'MEDIUM',
      session: 'LONDON',
      riskLevel: 'LOW',
      orderBlocks: 2,
      fvg: 1,
      choch: 0,
      bos: 0,
      flag: '🇪🇺🇬🇧'
    },
    // Commodities
    {
      symbol: 'XAUUSD',
      name: 'الذهب/دولار أمريكي',
      price: 2050.00,
      change: 15.50,
      changePercent: 0.76,
      trend: 'BULLISH',
      volume: 750000,
      volatility: 'HIGH',
      liquidity: 'MEDIUM',
      session: 'NEW_YORK',
      riskLevel: 'MEDIUM',
      orderBlocks: 5,
      fvg: 4,
      choch: 2,
      bos: 1,
      flag: '🥇💰'
    },
    {
      symbol: 'XAGUSD',
      name: 'الفضة/دولار أمريكي',
      price: 24.50,
      change: 0.35,
      changePercent: 1.45,
      trend: 'BULLISH',
      volume: 320000,
      volatility: 'HIGH',
      liquidity: 'LOW',
      session: 'NEW_YORK',
      riskLevel: 'HIGH',
      orderBlocks: 3,
      fvg: 2,
      choch: 1,
      bos: 1,
      flag: '🥈💰'
    },
    {
      symbol: 'USOIL',
      name: 'النفط الخام/دولار أمريكي',
      price: 78.50,
      change: -1.20,
      changePercent: -1.51,
      trend: 'BEARISH',
      volume: 890000,
      volatility: 'HIGH',
      liquidity: 'HIGH',
      session: 'NEW_YORK',
      riskLevel: 'HIGH',
      orderBlocks: 4,
      fvg: 3,
      choch: 1,
      bos: 2,
      flag: '🛢️💰'
    },
    // Additional Major Pairs
    {
      symbol: 'EURJPY',
      name: 'يورو/ين ياباني',
      price: 162.30,
      change: 0.45,
      changePercent: 0.28,
      trend: 'BULLISH',
      volume: 680000,
      volatility: 'MEDIUM',
      liquidity: 'MEDIUM',
      session: 'ASIAN',
      riskLevel: 'MEDIUM',
      orderBlocks: 3,
      fvg: 2,
      choch: 1,
      bos: 0,
      flag: '🇪🇺🇯🇵'
    },
    {
      symbol: 'GBPJPY',
      name: 'جنيه إسترليني/ين ياباني',
      price: 189.20,
      change: -0.80,
      changePercent: -0.42,
      trend: 'BEARISH',
      volume: 520000,
      volatility: 'HIGH',
      liquidity: 'MEDIUM',
      session: 'LONDON',
      riskLevel: 'HIGH',
      orderBlocks: 2,
      fvg: 3,
      choch: 0,
      bos: 1,
      flag: '🇬🇧🇯🇵'
    },
    // Crypto (if enabled)
    {
      symbol: 'BTCUSD',
      name: 'بيتكوين/دولار أمريكي',
      price: 43250.00,
      change: 850.00,
      changePercent: 2.01,
      trend: 'BULLISH',
      volume: 1200000,
      volatility: 'HIGH',
      liquidity: 'HIGH',
      session: 'GLOBAL',
      riskLevel: 'HIGH',
      orderBlocks: 6,
      fvg: 5,
      choch: 2,
      bos: 3,
      flag: '₿💰'
    },
    {
      symbol: 'ETHUSD',
      name: 'إيثريوم/دولار أمريكي',
      price: 2650.00,
      change: 45.50,
      changePercent: 1.75,
      trend: 'BULLISH',
      volume: 980000,
      volatility: 'HIGH',
      liquidity: 'MEDIUM',
      session: 'GLOBAL',
      riskLevel: 'HIGH',
      orderBlocks: 4,
      fvg: 3,
      choch: 1,
      bos: 2,
      flag: '⟠💰'
    },
  ]);

  // Enhanced signals with ICT concepts
  const [signals, setSignals] = useState([
    {
      id: '1',
      symbol: 'EURUSD',
      type: 'BUY',
      entry: 1.0850,
      stopLoss: 1.0820,
      takeProfit1: 1.0900,
      takeProfit2: 1.0950,
      takeProfit3: 1.1000,
      riskReward: 1.67,
      confidence: 87,
      timestamp: Date.now() - 300000,
      reasoning: [
        'Bullish Order Block at 1.0845-1.0855',
        'Fair Value Gap filled and holding',
        'CHoCH confirmed bullish structure',
        'RSI oversold with divergence',
        'MACD bullish crossover',
        'Price above VWAP',
        'London session high liquidity'
      ],
      patterns: ['Bullish Order Block', 'Fair Value Gap', 'CHoCH'],
      session: 'LONDON',
      marketStructure: 'BULLISH',
      smartMoney: 'ACCUMULATION'
    },
    {
      id: '2',
      symbol: 'GBPUSD',
      type: 'SELL',
      entry: 1.2650,
      stopLoss: 1.2680,
      takeProfit1: 1.2600,
      takeProfit2: 1.2550,
      takeProfit3: 1.2500,
      riskReward: 1.67,
      confidence: 82,
      timestamp: Date.now() - 600000,
      reasoning: [
        'Bearish Breaker Block at 1.2655-1.2665',
        'Break of Structure confirmed',
        'Bearish engulfing pattern',
        'RSI overbought rejection',
        'Volume spike on breakdown',
        'Below key support level'
      ],
      patterns: ['Bearish Breaker Block', 'BOS', 'Engulfing'],
      session: 'LONDON',
      marketStructure: 'BEARISH',
      smartMoney: 'DISTRIBUTION'
    },
    {
      id: '3',
      symbol: 'XAUUSD',
      type: 'BUY',
      entry: 2050.00,
      stopLoss: 2035.00,
      takeProfit1: 2075.00,
      takeProfit2: 2100.00,
      takeProfit3: 2125.00,
      riskReward: 1.67,
      confidence: 91,
      timestamp: Date.now() - 900000,
      reasoning: [
        'Premium Discount Array (PDA) setup',
        'Institutional Order Block respected',
        'Liquidity sweep completed',
        'Fair Value Gap acting as support',
        'Smart money accumulation zone',
        'Dollar weakness confluence'
      ],
      patterns: ['Order Block', 'Liquidity Sweep', 'PDA'],
      session: 'NEW_YORK',
      marketStructure: 'BULLISH',
      smartMoney: 'ACCUMULATION'
    },
    {
      id: '4',
      symbol: 'BTCUSD',
      type: 'BUY',
      entry: 43250.00,
      stopLoss: 42500.00,
      takeProfit1: 44500.00,
      takeProfit2: 45500.00,
      takeProfit3: 46500.00,
      riskReward: 1.67,
      confidence: 89,
      timestamp: Date.now() - 1200000,
      reasoning: [
        'Crypto market showing strong bullish momentum',
        'Breaking above key resistance at 43000',
        'Volume surge indicating institutional interest',
        'Fair Value Gap acting as strong support',
        'Smart money accumulation pattern detected',
        'Global adoption news driving sentiment'
      ],
      patterns: ['Volume Surge', 'Resistance Break', 'Institutional Flow'],
      session: 'GLOBAL',
      marketStructure: 'BULLISH',
      smartMoney: 'ACCUMULATION'
    },
    {
      id: '5',
      symbol: 'USOIL',
      type: 'SELL',
      entry: 78.50,
      stopLoss: 80.00,
      takeProfit1: 76.50,
      takeProfit2: 75.00,
      takeProfit3: 73.50,
      riskReward: 1.33,
      confidence: 78,
      timestamp: Date.now() - 1500000,
      reasoning: [
        'Oil showing bearish divergence on RSI',
        'Inventory data showing oversupply',
        'Geopolitical tensions easing',
        'Dollar strength pressuring commodities',
        'Technical breakdown below support',
        'OPEC production concerns'
      ],
      patterns: ['Bearish Divergence', 'Support Break', 'Supply Pressure'],
      session: 'NEW_YORK',
      marketStructure: 'BEARISH',
      smartMoney: 'DISTRIBUTION'
    }
  ]);

  // Real-time updates simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setMarketData(prevData =>
        prevData.map(market => ({
          ...market,
          price: market.price + (Math.random() - 0.5) * 0.001 * market.price,
          change: market.change + (Math.random() - 0.5) * 0.0005,
          changePercent: market.changePercent + (Math.random() - 0.5) * 0.05,
          volume: market.volume + Math.floor((Math.random() - 0.5) * 50000)
        }))
      );
    }, 2000); // Update every 2 seconds

    return () => clearInterval(interval);
  }, []);

  const mockSignals = [
    {
      id: '1',
      symbol: 'EURUSD',
      type: 'BUY',
      entry: 1.0850,
      stopLoss: 1.0820,
      takeProfit: 1.0900,
      confidence: 85,
      timestamp: Date.now() - 300000,
      reasoning: ['RSI oversold', 'MACD bullish crossover', 'Price above VWAP']
    },
    {
      id: '2',
      symbol: 'GBPUSD',
      type: 'SELL',
      entry: 1.2650,
      stopLoss: 1.2680,
      takeProfit: 1.2600,
      confidence: 78,
      timestamp: Date.now() - 600000,
      reasoning: ['Bearish engulfing pattern', 'RSI overbought', 'Break of support']
    }
  ];

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-white mb-2">🤖 AI Trading Bot</h1>
          <p className="text-blue-200">Initializing advanced trading systems...</p>
          <div className="mt-4 space-y-2 text-sm text-blue-300">
            <p>✅ Loading technical indicators</p>
            <p>✅ Connecting to market data</p>
            <p>✅ Initializing AI pattern recognition</p>
            <p>✅ Setting up risk management</p>
            <p>🔄 Starting real-time analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900" dir="rtl">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                🤖 بوت التداول الذكي الاحترافي
              </h1>
              <div className="mr-6 flex items-center space-x-2 space-x-reverse">
                <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                <span className="text-sm text-gray-600 dark:text-gray-300">مباشر</span>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {currentTime.toLocaleTimeString('ar-SA')}
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-sm text-gray-600 dark:text-gray-400">الوضع التجريبي</span>
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Dashboard */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* نظرة عامة محسنة على السوق */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {marketData.map(market => (
            <div key={market.symbol} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4 border-blue-500">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-lg">{market.flag}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {market.symbol}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {market.name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className={`w-3 h-3 rounded-full ${
                    market.trend === 'BULLISH' ? 'bg-green-500' :
                    market.trend === 'BEARISH' ? 'bg-red-500' : 'bg-yellow-500'
                  }`}></div>
                  <span className="text-xs text-gray-500">{
                    market.session === 'LONDON' ? 'لندن' :
                    market.session === 'NEW_YORK' ? 'نيويورك' :
                    market.session === 'ASIAN' ? 'آسيا' : market.session
                  }</span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">
                    {market.price.toFixed(market.symbol.includes('JPY') ? 3 : 5)}
                  </span>
                  <span className={`text-sm font-medium ${
                    market.change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {market.change >= 0 ? '+' : ''}{market.changePercent.toFixed(2)}%
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className={`px-2 py-1 rounded text-center ${
                    market.trend === 'BULLISH'
                      ? 'text-green-600 bg-green-50 dark:bg-green-900/20'
                      : market.trend === 'BEARISH'
                      ? 'text-red-600 bg-red-50 dark:bg-red-900/20'
                      : 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'
                  }`}>
                    {market.trend === 'BULLISH' ? 'صاعد' :
                     market.trend === 'BEARISH' ? 'هابط' : 'محايد'}
                  </div>
                  <div className={`px-2 py-1 rounded text-center ${
                    market.riskLevel === 'LOW' ? 'text-green-600 bg-green-50' :
                    market.riskLevel === 'MEDIUM' ? 'text-yellow-600 bg-yellow-50' :
                    'text-red-600 bg-red-50'
                  } dark:bg-opacity-20`}>
                    {market.riskLevel === 'LOW' ? 'منخفض' :
                     market.riskLevel === 'MEDIUM' ? 'متوسط' : 'عالي'}
                  </div>
                </div>

                <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                  <div className="flex justify-between">
                    <span>الحجم:</span>
                    <span>{(market.volume / 1000000).toFixed(1)} مليون</span>
                  </div>
                  <div className="flex justify-between">
                    <span>التقلب:</span>
                    <span>{market.volatility === 'HIGH' ? 'عالي' :
                           market.volatility === 'MEDIUM' ? 'متوسط' : 'منخفض'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>السيولة:</span>
                    <span>{market.liquidity === 'HIGH' ? 'عالية' :
                           market.liquidity === 'MEDIUM' ? 'متوسطة' : 'منخفضة'}</span>
                  </div>
                </div>

                {/* ICT Concepts Summary */}
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <div className="grid grid-cols-4 gap-1 text-xs">
                    <div className="text-center">
                      <div className="font-medium text-blue-600">{market.orderBlocks}</div>
                      <div className="text-gray-500">OB</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-purple-600">{market.fvg}</div>
                      <div className="text-gray-500">FVG</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-green-600">{market.choch}</div>
                      <div className="text-gray-500">CHoCH</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-orange-600">{market.bos}</div>
                      <div className="text-gray-500">BOS</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Professional Trading System - Ultimate Analysis */}
        <ProfessionalTradingSystem />

        {/* Professional Recommendation Engine - Top Priority */}
        <ProfessionalRecommendationEngine />

        {/* Advanced Market Analysis with TradingView Integration */}
        <AdvancedMarketAnalysis />

        {/* Manual AI Recommendations */}
        <ManualAIRecommendations />

        {/* Enhanced Multi-Timeframe Analysis */}
        <TimeframeAnalysis />

        {/* Advanced Fibonacci Analysis */}
        <FibonacciAnalysis />

        {/* Professional Order Blocks Analysis */}
        <ProfessionalOrderBlocks />

        {/* Auto Trading Robot */}
        <AutoTradingRobot />

        {/* إشارات التداول المحسنة مع تحليل ICT */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                🎯 إشارات التداول الذكية
                <span className="mr-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  مفاهيم ICT
                </span>
              </h3>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {signals.length} إشارة نشطة
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              {signals.map(signal => (
                <div key={signal.id} className={`border-2 rounded-xl p-6 transition-all hover:shadow-lg ${
                  signal.type === 'BUY'
                    ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800'
                    : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200 dark:from-red-900/20 dark:to-rose-900/20 dark:border-red-800'
                }`}>
                  {/* Signal Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        signal.type === 'BUY' ? 'bg-green-500' : 'bg-red-500'
                      }`}>
                        <span className="text-white text-xl">
                          {signal.type === 'BUY' ? '📈' : '📉'}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center space-x-3">
                          <span className="text-xl font-bold text-gray-900 dark:text-white">
                            {signal.type} {signal.symbol}
                          </span>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            signal.confidence >= 85 ? 'bg-green-100 text-green-800' :
                            signal.confidence >= 75 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-orange-100 text-orange-800'
                          }`}>
                            {signal.confidence}% Confidence
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 space-x-reverse mt-1 text-sm text-gray-600 dark:text-gray-400">
                          <span>{new Date(signal.timestamp).toLocaleString('ar-SA')}</span>
                          <span>•</span>
                          <span>جلسة {signal.session === 'LONDON' ? 'لندن' :
                                      signal.session === 'NEW_YORK' ? 'نيويورك' :
                                      signal.session === 'ASIAN' ? 'آسيا' : signal.session}</span>
                          <span>•</span>
                          <span>هيكل {signal.marketStructure === 'BULLISH' ? 'صاعد' :
                                     signal.marketStructure === 'BEARISH' ? 'هابط' : 'محايد'}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">
                        R/R: {signal.riskReward}:1
                      </div>
                      <div className={`text-sm px-2 py-1 rounded ${
                        signal.smartMoney === 'ACCUMULATION' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {signal.smartMoney}
                      </div>
                    </div>
                  </div>

                  {/* مستويات الأسعار */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                    <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">الدخول</div>
                      <div className="font-bold text-gray-900 dark:text-white">
                        {signal.entry.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">وقف الخسارة</div>
                      <div className="font-bold text-red-600">
                        {signal.stopLoss.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">هدف 1</div>
                      <div className="font-bold text-green-600">
                        {signal.takeProfit1.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">هدف 2</div>
                      <div className="font-bold text-green-600">
                        {signal.takeProfit2.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">هدف 3</div>
                      <div className="font-bold text-green-600">
                        {signal.takeProfit3.toFixed(signal.symbol.includes('JPY') ? 3 : 5)}
                      </div>
                    </div>
                  </div>

                  {/* أنماط ICT */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      🔍 الأنماط المكتشفة:
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {signal.patterns.map((pattern, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium">
                          {pattern === 'Bullish Order Block' ? 'منطقة طلبات صاعدة' :
                           pattern === 'Fair Value Gap' ? 'فجوة القيمة العادلة' :
                           pattern === 'CHoCH' ? 'تغيير الطبيعة' :
                           pattern === 'Bearish Breaker Block' ? 'كتلة كسر هابطة' :
                           pattern === 'BOS' ? 'كسر الهيكل' :
                           pattern === 'Engulfing' ? 'نمط الابتلاع' :
                           pattern === 'Order Block' ? 'منطقة الطلبات' :
                           pattern === 'Liquidity Sweep' ? 'اكتساح السيولة' :
                           pattern === 'PDA' ? 'مصفوفة العرض والطلب' : pattern}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* التحليل المتقدم */}
                  <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                      🧠 تحليل ICT المتقدم:
                      <span className="mr-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                        مفاهيم الأموال الذكية
                      </span>
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                        {signal.reasoning.slice(0, Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-2 text-blue-500">▶</span>
                            <span>{reason}</span>
                          </li>
                        ))}
                      </ul>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                        {signal.reasoning.slice(Math.ceil(signal.reasoning.length / 2)).map((reason, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-2 text-purple-500">▶</span>
                            <span>{reason}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-2 space-x-reverse">
                      <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                        📊 عرض الرسم البياني
                      </button>
                      <button className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors">
                        📋 نسخ الإشارة
                      </button>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      رقم الإشارة: {signal.id}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* لوحة المؤشرات الفنية المتقدمة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              📊 التحليل الفني المتقدم
              <span className="mr-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                مباشر
              </span>
            </h3>
          </div>

          <div className="p-6">
            {/* المؤشرات التقليدية */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                🔢 المؤشرات التقليدية
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300">RSI (14)</h3>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">EURUSD</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">45.2</div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-green-600">Neutral</div>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{width: '45%'}}></div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-purple-700 dark:text-purple-300">MACD</h3>
                    <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">12,26,9</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">0.0012</div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-green-600">Bullish Cross</div>
                    <div className="text-xs text-gray-500">Signal: 0.0008</div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-green-700 dark:text-green-300">VWAP</h3>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Volume</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">1.0845</div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-blue-600">Above Price</div>
                    <div className="text-xs text-gray-500">Support</div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-orange-700 dark:text-orange-300">Supertrend</h3>
                    <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">10,3</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">1.0820</div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-green-600">Uptrend</div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* مفاهيم ICT */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                🎯 مفاهيم الأموال الذكية ICT
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-cyan-200 dark:border-cyan-800">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-cyan-700 dark:text-cyan-300 mb-2">Order Blocks</h3>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">14</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Active Zones</div>
                    <div className="mt-2 flex justify-center space-x-1">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-violet-200 dark:border-violet-800">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-violet-700 dark:text-violet-300 mb-2">Fair Value Gaps</h3>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">7</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Unfilled</div>
                    <div className="mt-2 text-xs text-violet-600">3 Bullish, 4 Bearish</div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg p-4 border border-emerald-200 dark:border-emerald-800">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-emerald-700 dark:text-emerald-300 mb-2">CHoCH</h3>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">2</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Recent</div>
                    <div className="mt-2 text-xs text-emerald-600">Bullish Structure</div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-amber-700 dark:text-amber-300 mb-2">BOS</h3>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">3</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Confirmed</div>
                    <div className="mt-2 text-xs text-amber-600">Strong Momentum</div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-rose-200 dark:border-rose-800">
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">Liquidity</h3>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-1">HIGH</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">London Session</div>
                    <div className="mt-2 text-xs text-rose-600">Optimal Trading</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Market Structure Analysis */}
            <div>
              <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                🏗️ Market Structure Analysis
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Trend Analysis</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Higher Highs:</span>
                      <span className="text-xs font-medium text-green-600">✓ Confirmed</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Higher Lows:</span>
                      <span className="text-xs font-medium text-green-600">✓ Confirmed</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Structure:</span>
                      <span className="text-xs font-medium text-blue-600">BULLISH</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Volume Profile</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">POC:</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">1.0842</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">VAH:</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">1.0865</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">VAL:</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">1.0825</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Smart Money</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Flow:</span>
                      <span className="text-xs font-medium text-green-600">ACCUMULATION</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Sentiment:</span>
                      <span className="text-xs font-medium text-blue-600">BULLISH</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Confidence:</span>
                      <span className="text-xs font-medium text-purple-600">87%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* لوحة إدارة المخاطر المتقدمة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                🛡️ إدارة المخاطر المتقدمة
                <span className="mr-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                  مراقبة مباشرة
                </span>
              </h3>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">محرك المخاطر نشط</span>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">💰</span>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Demo</span>
                </div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">$10,000</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Account Balance</div>
                <div className="flex items-center text-xs text-green-600">
                  <span className="mr-1">↗</span>
                  <span>+2.5% This Month</span>
                </div>
              </div>

              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">⚡</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Safe</span>
                </div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">2.5%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Daily Risk Exposure</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{width: '42%'}}></div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">🎯</span>
                  </div>
                  <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Excellent</span>
                </div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">72.3%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Win Rate (30 Days)</div>
                <div className="text-xs text-purple-600">23 Wins / 9 Losses</div>
              </div>

              <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">📊</span>
                  </div>
                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Active</span>
                </div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">3</div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Active Signals</div>
                <div className="text-xs text-orange-600">2 BUY / 1 SELL</div>
              </div>
            </div>

            {/* Risk Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                  📈 Performance Metrics
                </h4>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Profit Factor:</span>
                    <span className="text-sm font-bold text-green-600">1.85</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Sharpe Ratio:</span>
                    <span className="text-sm font-bold text-blue-600">1.42</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Max Drawdown:</span>
                    <span className="text-sm font-bold text-red-600">-3.2%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Avg R/R Ratio:</span>
                    <span className="text-sm font-bold text-purple-600">1.67:1</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Recovery Factor:</span>
                    <span className="text-sm font-bold text-indigo-600">2.1</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">
                  ⚙️ Risk Parameters
                </h4>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Max Risk Per Trade:</span>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">2.0%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{width: '40%'}}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Max Daily Risk:</span>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">6.0%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{width: '42%'}}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Max Open Trades:</span>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">3 / 5</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-500 h-2 rounded-full" style={{width: '60%'}}></div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Min R/R Ratio:</span>
                    <span className="text-sm font-bold text-purple-600">1.5:1</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Risk Alerts */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-white text-sm">⚠</span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                    Risk Management Active
                  </h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    All trades are automatically monitored for risk compliance. Current exposure is within safe limits.
                    Demo mode ensures no real money is at risk.
                  </p>
                  <div className="mt-2 flex items-center space-x-4 text-xs text-yellow-600 dark:text-yellow-400">
                    <span>✓ Position sizing active</span>
                    <span>✓ Stop loss enforcement</span>
                    <span>✓ Daily limit monitoring</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Enhanced Footer */}
      <footer className="bg-gradient-to-r from-gray-900 to-blue-900 text-white mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* معلومات البوت */}
            <div>
              <h4 className="text-lg font-semibold mb-4 flex items-center">
                🤖 بوت التداول الذكي
                <span className="mr-2 text-xs bg-blue-600 px-2 py-1 rounded-full">الإصدار 2.0</span>
              </h4>
              <p className="text-sm text-gray-300 mb-4">
                نظام تداول احترافي مدعوم بالذكاء الاصطناعي مع مفاهيم ICT المتقدمة وتحليل الأموال الذكية.
              </p>
              <div className="flex items-center space-x-2 space-x-reverse">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm">مباشر ونشط</span>
              </div>
            </div>

            {/* الميزات */}
            <div>
              <h4 className="text-lg font-semibold mb-4">🎯 الميزات</h4>
              <ul className="text-sm text-gray-300 space-y-2">
                <li>• مفاهيم الأموال الذكية ICT</li>
                <li>• مناطق الطلبات وفجوات القيمة العادلة</li>
                <li>• كشف CHoCH وBOS</li>
                <li>• إدارة مخاطر متقدمة</li>
                <li>• تحليل السوق المباشر</li>
                <li>• تحليل متعدد الإطارات الزمنية</li>
                <li>• روبوت التداول الآلي</li>
                <li>• دعم جميع العملات الرئيسية</li>
              </ul>
            </div>

            {/* حالة النظام */}
            <div>
              <h4 className="text-lg font-semibold mb-4">📊 حالة النظام</h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">بيانات السوق:</span>
                  <span className="text-green-400">✓ متصل</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">محرك الذكاء الاصطناعي:</span>
                  <span className="text-green-400">✓ نشط</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">مدير المخاطر:</span>
                  <span className="text-green-400">✓ يراقب</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">الروبوت الآلي:</span>
                  <span className="text-blue-400">✓ جاهز</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">الإشارات المولدة:</span>
                  <span className="text-blue-400">{signals.length} اليوم</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">آخر تحديث:</span>
                  <span className="text-yellow-400">{currentTime.toLocaleTimeString('ar-SA')}</span>
                </div>
              </div>
            </div>

            {/* إخلاء المسؤولية */}
            <div>
              <h4 className="text-lg font-semibold mb-4">⚠️ مهم</h4>
              <div className="text-xs text-gray-300 space-y-2">
                <p className="bg-red-900/30 border border-red-700 rounded p-2">
                  <strong>الوضع التجريبي:</strong> هذا عرض توضيحي. لا توجد أموال حقيقية متضمنة.
                </p>
                <p className="bg-yellow-900/30 border border-yellow-700 rounded p-2">
                  <strong>تحذير المخاطر:</strong> التداول ينطوي على مخاطر كبيرة. لا تتداول بأموال لا تستطيع تحمل خسارتها.
                </p>
                <p className="bg-blue-900/30 border border-blue-700 rounded p-2">
                  <strong>تعليمي:</strong> هذا البرنامج للأغراض التعليمية فقط.
                </p>
                <p className="bg-green-900/30 border border-green-700 rounded p-2">
                  <strong>الروبوت الآلي:</strong> ميزة تجريبية - اختبر جميع الإعدادات بعناية.
                </p>
              </div>
            </div>
          </div>

          {/* الشريط السفلي */}
          <div className="border-t border-gray-700 mt-8 pt-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-6 space-x-reverse text-sm text-gray-300 mb-4 md:mb-0">
                <span>© 2024 بوت التداول الذكي</span>
                <span>•</span>
                <span>مطور بـ Next.js و TypeScript</span>
                <span>•</span>
                <span>مدعوم بالذكاء الاصطناعي المتقدم</span>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="flex items-center space-x-2 space-x-reverse text-sm">
                  <span className="text-gray-300">الأداء:</span>
                  <span className="text-green-400 font-medium">+2.5% شهرياً</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse text-sm">
                  <span className="text-gray-300">وقت التشغيل:</span>
                  <span className="text-blue-400 font-medium">99.9%</span>
                </div>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
