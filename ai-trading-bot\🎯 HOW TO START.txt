🚀 AI Trading Bot - Quick Start Guide
========================================

📁 FOLDER NAME: ai-trading-bot

🎯 TO START THE APPLICATION:

METHOD 1 - EASY WAY (Recommended):
==================================
1. Double-click on: "🚀 Start AI Trading Bot.bat"
2. Wait for the server to start
3. Open your browser and go to: http://localhost:3000

METHOD 2 - Manual Way:
=====================
1. Right-click inside the "ai-trading-bot" folder
2. Select "Open in Terminal" or "Open PowerShell window here"
3. Type: npm run dev
4. Press Enter
5. Open browser: http://localhost:3000

🔧 TROUBLESHOOTING:
==================

❌ If "Node.js not found":
   - Download and install from: https://nodejs.org/
   - Restart your computer
   - Try again

❌ If "Port 3000 already in use":
   - Close any other applications using port 3000
   - Or use: npm run dev -- --port 3001
   - Then open: http://localhost:3001

❌ If "Module not found":
   - Delete "node_modules" folder
   - Run the .bat file again (it will reinstall)

💡 TIPS:
========
- The .bat file will automatically install dependencies if needed
- Keep the Terminal/Command window open while using the app
- To stop the server, press Ctrl+C in the Terminal
- The app works best in Chrome, Firefox, or Edge

🎉 ENJOY YOUR AI TRADING BOT!
============================

📞 Need help? Check the full README.md file for detailed instructions.

⚡ Built with passion for algorithmic trading and AI innovation
