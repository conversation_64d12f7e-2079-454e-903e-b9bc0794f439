# AI Trading Bot Environment Variables

# Telegram Bot Configuration (Optional - for notifications)
NEXT_PUBLIC_TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
NEXT_PUBLIC_TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here

# TradingView Configuration (Optional - for real data)
TRADINGVIEW_API_KEY=your_tradingview_api_key_here

# Twelve Data API (Optional - for real market data)
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here

# Demo Mode Settings
DEMO_MODE=true
DEMO_ACCOUNT_BALANCE=10000

# Risk Management Settings
MAX_RISK_PER_TRADE=2
MAX_DAILY_RISK=6
MAX_OPEN_TRADES=3
MIN_RISK_REWARD_RATIO=1.5

# AI Settings
AI_CONFIDENCE_THRESHOLD=75
PATTERN_RECOGNITION_ENABLED=true
AUTO_TRADING_ENABLED=false

# Webhook Configuration (Optional)
WEBHOOK_URL=your_webhook_url_here
WEBHOOK_SECRET=your_webhook_secret_here

# Database Configuration (Optional - for storing trade history)
DATABASE_URL=your_database_url_here

# Logging Level
LOG_LEVEL=info

# Development Settings
NODE_ENV=development
