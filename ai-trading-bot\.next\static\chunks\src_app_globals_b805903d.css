/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-duration: initial;
      --tw-content: "";
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
    }

    ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-duration: initial;
      --tw-content: "";
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: #fef2f2;
    --color-red-100: #ffe2e2;
    --color-red-200: #ffcaca;
    --color-red-300: #ffa3a3;
    --color-red-400: #ff6568;
    --color-red-500: #fb2c36;
    --color-red-600: #e40014;
    --color-red-700: #bf000f;
    --color-red-800: #9f0712;
    --color-red-900: #82181a;
    --color-orange-50: #fff7ed;
    --color-orange-100: #ffedd5;
    --color-orange-200: #ffd7a8;
    --color-orange-300: #ffb96d;
    --color-orange-500: #fe6e00;
    --color-orange-600: #f05100;
    --color-orange-700: #c53c00;
    --color-orange-800: #9f2d00;
    --color-orange-900: #7e2a0c;
    --color-amber-50: #fffbeb;
    --color-amber-200: #fee685;
    --color-amber-300: #ffd236;
    --color-amber-600: #dd7400;
    --color-amber-700: #b75000;
    --color-amber-800: #953d00;
    --color-amber-900: #7b3306;
    --color-yellow-50: #fefce8;
    --color-yellow-100: #fef9c2;
    --color-yellow-200: #fff085;
    --color-yellow-300: #ffe02a;
    --color-yellow-400: #fac800;
    --color-yellow-500: #edb200;
    --color-yellow-600: #cd8900;
    --color-yellow-700: #a36100;
    --color-yellow-800: #874b00;
    --color-yellow-900: #733e0a;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #b9f8cf;
    --color-green-300: #7bf1a8;
    --color-green-400: #05df72;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-green-700: #008138;
    --color-green-800: #016630;
    --color-green-900: #0d542b;
    --color-emerald-50: #ecfdf5;
    --color-emerald-100: #d0fae5;
    --color-emerald-200: #a4f4cf;
    --color-emerald-300: #5ee9b5;
    --color-emerald-500: #00bb7f;
    --color-emerald-600: #009767;
    --color-emerald-700: #007956;
    --color-emerald-800: #005f46;
    --color-emerald-900: #004e3b;
    --color-teal-50: #f0fdfa;
    --color-teal-900: #0b4f4a;
    --color-cyan-50: #ecfeff;
    --color-cyan-200: #a2f4fd;
    --color-cyan-300: #53eafd;
    --color-cyan-700: #007492;
    --color-cyan-800: #005f78;
    --color-cyan-900: #104e64;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bedbff;
    --color-blue-300: #90c5ff;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-blue-800: #193cb8;
    --color-blue-900: #1c398e;
    --color-indigo-50: #eef2ff;
    --color-indigo-100: #e0e7ff;
    --color-indigo-200: #c7d2ff;
    --color-indigo-500: #625fff;
    --color-indigo-600: #4f39f6;
    --color-indigo-700: #432dd7;
    --color-indigo-800: #372aac;
    --color-indigo-900: #312c85;
    --color-violet-50: #f5f3ff;
    --color-violet-200: #ddd6ff;
    --color-violet-300: #c4b4ff;
    --color-violet-600: #7f22fe;
    --color-violet-700: #7008e7;
    --color-violet-800: #5d0ec0;
    --color-violet-900: #4d179a;
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-300: #d9b3ff;
    --color-purple-500: #ac4bff;
    --color-purple-600: #9810fa;
    --color-purple-700: #8200da;
    --color-purple-800: #6e11b0;
    --color-purple-900: #59168b;
    --color-pink-50: #fdf2f8;
    --color-pink-100: #fce7f3;
    --color-pink-800: #a2004c;
    --color-pink-900: #861043;
    --color-rose-50: #fff1f2;
    --color-rose-200: #ffccd3;
    --color-rose-300: #ffa2ae;
    --color-rose-600: #e70044;
    --color-rose-700: #c20039;
    --color-rose-800: #a30037;
    --color-rose-900: #8b0836;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wider: .05em;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-50: color(display-p3 .988669 .951204 .950419);
      --color-red-100: color(display-p3 .980386 .889727 .887779);
      --color-red-200: color(display-p3 .969562 .798149 .794299);
      --color-red-300: color(display-p3 .956922 .651886 .645122);
      --color-red-400: color(display-p3 .933534 .431676 .423491);
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-red-600: color(display-p3 .830323 .140383 .133196);
      --color-red-700: color(display-p3 .692737 .116232 .104679);
      --color-red-800: color(display-p3 .569606 .121069 .108493);
      --color-red-900: color(display-p3 .466906 .133198 .12139);
      --color-orange-50: color(display-p3 .99533 .970132 .933499);
      --color-orange-100: color(display-p3 .988762 .931393 .843273);
      --color-orange-200: color(display-p3 .974647 .84705 .680111);
      --color-orange-300: color(display-p3 .96801 .734346 .464159);
      --color-orange-500: color(display-p3 .946589 .449788 .0757345);
      --color-orange-600: color(display-p3 .887467 .341665 .0219962);
      --color-orange-700: color(display-p3 .729844 .257256 .0511062);
      --color-orange-800: color(display-p3 .575591 .21198 .082665);
      --color-orange-900: color(display-p3 .457137 .188093 .0897628);
      --color-amber-50: color(display-p3 .997804 .985047 .926312);
      --color-amber-200: color(display-p3 .979824 .904554 .57325);
      --color-amber-300: color(display-p3 .974327 .83063 .33298);
      --color-amber-600: color(display-p3 .827144 .467166 .0336006);
      --color-amber-700: color(display-p3 .67989 .32771 .0520514);
      --color-amber-800: color(display-p3 .547533 .256058 .0728326);
      --color-amber-900: color(display-p3 .445958 .216422 .0823321);
      --color-yellow-50: color(display-p3 .994197 .988062 .917538);
      --color-yellow-100: color(display-p3 .993436 .977463 .782913);
      --color-yellow-200: color(display-p3 .988789 .943116 .579188);
      --color-yellow-300: color(display-p3 .982669 .880884 .32102);
      --color-yellow-400: color(display-p3 .959941 .790171 .0585198);
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-yellow-600: color(display-p3 .776342 .542492 .041709);
      --color-yellow-700: color(display-p3 .613146 .386658 .0579687);
      --color-yellow-800: color(display-p3 .503181 .30478 .075537);
      --color-yellow-900: color(display-p3 .422485 .252729 .095052);
      --color-green-50: color(display-p3 .950677 .990571 .959366);
      --color-green-100: color(display-p3 .885269 .984329 .910368);
      --color-green-200: color(display-p3 .776442 .964383 .823412);
      --color-green-300: color(display-p3 .600292 .935514 .68114);
      --color-green-400: color(display-p3 .399536 .862346 .49324);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-green-700: color(display-p3 .198355 .501799 .245335);
      --color-green-800: color(display-p3 .168568 .395123 .211217);
      --color-green-900: color(display-p3 .147288 .323577 .185694);
      --color-emerald-50: color(display-p3 .936818 .989882 .961937);
      --color-emerald-100: color(display-p3 .848335 .975974 .901691);
      --color-emerald-200: color(display-p3 .713164 .947563 .822283);
      --color-emerald-300: color(display-p3 .524941 .903425 .722352);
      --color-emerald-500: color(display-p3 .267113 .726847 .508397);
      --color-emerald-600: color(display-p3 .206557 .589057 .413962);
      --color-emerald-700: color(display-p3 .164041 .470229 .343508);
      --color-emerald-800: color(display-p3 .135396 .371401 .277561);
      --color-emerald-900: color(display-p3 .117821 .302975 .234501);
      --color-teal-50: color(display-p3 .951444 .990904 .98112);
      --color-teal-900: color(display-p3 .135344 .303314 .290629);
      --color-cyan-50: color(display-p3 .938135 .993772 .998465);
      --color-cyan-200: color(display-p3 .707418 .947027 .984826);
      --color-cyan-300: color(display-p3 .503734 .904871 .979358);
      --color-cyan-700: color(display-p3 .164124 .451431 .570574);
      --color-cyan-800: color(display-p3 .151437 .365208 .461053);
      --color-cyan-900: color(display-p3 .142586 .302008 .385094);
      --color-blue-50: color(display-p3 .941826 .963151 .995385);
      --color-blue-100: color(display-p3 .869214 .915931 .989622);
      --color-blue-200: color(display-p3 .76688 .855207 .987483);
      --color-blue-300: color(display-p3 .602559 .767214 .993938);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-blue-800: color(display-p3 .134023 .230647 .695537);
      --color-blue-900: color(display-p3 .136395 .219428 .537145);
      --color-indigo-50: color(display-p3 .936215 .948621 .995621);
      --color-indigo-100: color(display-p3 .883035 .90499 .993138);
      --color-indigo-200: color(display-p3 .786558 .821755 .988451);
      --color-indigo-500: color(display-p3 .380374 .372235 .971707);
      --color-indigo-600: color(display-p3 .297656 .227891 .929242);
      --color-indigo-700: color(display-p3 .251282 .180274 .81203);
      --color-indigo-800: color(display-p3 .207204 .165242 .647584);
      --color-indigo-900: color(display-p3 .188425 .173312 .503066);
      --color-violet-50: color(display-p3 .959212 .95304 .995713);
      --color-violet-200: color(display-p3 .861543 .838846 .988006);
      --color-violet-300: color(display-p3 .758872 .706261 .991729);
      --color-violet-600: color(display-p3 .459951 .162666 .957985);
      --color-violet-700: color(display-p3 .40161 .0841901 .871151);
      --color-violet-800: color(display-p3 .333914 .0857549 .723825);
      --color-violet-900: color(display-p3 .277841 .103712 .580169);
      --color-purple-50: color(display-p3 .977045 .961759 .996715);
      --color-purple-100: color(display-p3 .945034 .910569 .992972);
      --color-purple-200: color(display-p3 .901181 .835978 .992237);
      --color-purple-300: color(display-p3 .829394 .703737 .996084);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-purple-600: color(display-p3 .546729 .130167 .94439);
      --color-purple-700: color(display-p3 .465298 .0652579 .824397);
      --color-purple-800: color(display-p3 .393513 .10339 .664476);
      --color-purple-900: color(display-p3 .321698 .107597 .524563);
      --color-pink-50: color(display-p3 .983916 .950076 .970581);
      --color-pink-100: color(display-p3 .974549 .908208 .950227);
      --color-pink-800: color(display-p3 .584151 .105343 .297396);
      --color-pink-900: color(display-p3 .480862 .117563 .26009);
      --color-rose-50: color(display-p3 .989671 .946597 .949215);
      --color-rose-200: color(display-p3 .96875 .808776 .827317);
      --color-rose-300: color(display-p3 .96017 .647703 .683715);
      --color-rose-600: color(display-p3 .848792 .102011 .269259);
      --color-rose-700: color(display-p3 .711801 .0770816 .226777);
      --color-rose-800: color(display-p3 .591248 .0929065 .220097);
      --color-rose-900: color(display-p3 .498064 .104884 .214595);
      --color-gray-50: color(display-p3 .977213 .98084 .985102);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-50: lab(96.5005% 4.18511 1.52329);
      --color-red-100: lab(92.243% 10.2865 3.83865);
      --color-red-200: lab(86.017% 19.8815 7.75869);
      --color-red-300: lab(76.5514% 36.4219 15.5335);
      --color-red-400: lab(63.7053% 60.7449 31.3109);
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-red-600: lab(48.4493% 77.4328 61.5452);
      --color-red-700: lab(40.4273% 67.2623 53.7441);
      --color-red-800: lab(33.7174% 55.8993 41.0293);
      --color-red-900: lab(28.5139% 44.5539 29.0463);
      --color-orange-50: lab(97.7008% 1.53738 5.90646);
      --color-orange-100: lab(94.7127% 3.58391 14.3151);
      --color-orange-200: lab(88.4871% 9.94918 28.8378);
      --color-orange-300: lab(80.8059% 21.7313 50.4455);
      --color-orange-500: lab(64.272% 57.1788 90.3583);
      --color-orange-600: lab(57.1026% 64.2584 89.8886);
      --color-orange-700: lab(46.4615% 57.7275 70.8507);
      --color-orange-800: lab(37.1566% 46.6433 50.5562);
      --color-orange-900: lab(30.2951% 36.0434 37.671);
      --color-amber-50: lab(98.6252% -.635982 8.42309);
      --color-amber-200: lab(91.7203% -.505269 49.9084);
      --color-amber-300: lab(86.4156% 6.13147 78.3961);
      --color-amber-600: lab(60.3514% 40.5624 87.1228);
      --color-amber-700: lab(47.2709% 42.9082 69.2966);
      --color-amber-800: lab(37.8823% 37.1699 52.2718);
      --color-amber-900: lab(31.2288% 30.2627 40.0378);
      --color-yellow-50: lab(98.6846% -1.79058 9.77662);
      --color-yellow-100: lab(97.3564% -4.51407 27.344);
      --color-yellow-200: lab(94.3433% -5.00426 52.9663);
      --color-yellow-300: lab(89.7033% -.480324 84.4917);
      --color-yellow-400: lab(83.2664% 8.65132 106.895);
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-yellow-600: lab(62.7799% 22.4198 86.1544);
      --color-yellow-700: lab(47.8202% 25.2426 66.5015);
      --color-yellow-800: lab(38.7484% 23.5833 51.4916);
      --color-yellow-900: lab(32.3865% 21.1274 38.5958);
      --color-green-50: lab(98.1563% -5.60117 2.75913);
      --color-green-100: lab(96.186% -13.8464 6.52362);
      --color-green-200: lab(92.4222% -26.4702 12.9427);
      --color-green-300: lab(86.9953% -47.2691 25.0054);
      --color-green-400: lab(78.503% -64.9265 39.7492);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-green-700: lab(47.0329% -47.0239 31.4788);
      --color-green-800: lab(37.4616% -36.7971 22.9692);
      --color-green-900: lab(30.797% -29.6927 17.382);
      --color-emerald-50: lab(97.8462% -6.94963 1.85487);
      --color-emerald-100: lab(94.9004% -17.0769 5.63836);
      --color-emerald-200: lab(90.2247% -31.039 9.47084);
      --color-emerald-300: lab(83.9203% -48.7124 13.8849);
      --color-emerald-500: lab(66.9756% -58.27 19.5419);
      --color-emerald-600: lab(55.0481% -49.9246 15.93);
      --color-emerald-700: lab(44.4871% -41.0396 11.0361);
      --color-emerald-800: lab(35.3675% -33.1188 8.04002);
      --color-emerald-900: lab(28.8637% -26.9249 5.45986);
      --color-teal-50: lab(98.3189% -4.74921 -.111723);
      --color-teal-900: lab(29.506% -21.4706 -3.59886);
      --color-cyan-50: lab(98.3303% -5.97429 -2.62109);
      --color-cyan-200: lab(91.0821% -24.0435 -12.8306);
      --color-cyan-300: lab(85.3886% -36.7635 -21.5717);
      --color-cyan-700: lab(44.7267% -21.5987 -26.118);
      --color-cyan-800: lab(36.5114% -17.1989 -21.6292);
      --color-cyan-900: lab(30.372% -13.1853 -18.7887);
      --color-blue-50: lab(96.492% -1.14647 -5.11479);
      --color-blue-100: lab(92.0301% -2.24757 -11.6453);
      --color-blue-200: lab(86.15% -4.04379 -21.0797);
      --color-blue-300: lab(77.5052% -6.4629 -36.42);
      --color-blue-400: lab(65.0361% -1.42062 -56.9803);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-blue-800: lab(30.2514% 27.7854 -70.2699);
      --color-blue-900: lab(26.1542% 15.7545 -51.5504);
      --color-indigo-50: lab(95.4818% .411302 -6.78529);
      --color-indigo-100: lab(91.6577% 1.04591 -12.7199);
      --color-indigo-200: lab(84.4329% 3.18974 -23.9688);
      --color-indigo-500: lab(48.295% 38.3129 -81.9673);
      --color-indigo-600: lab(38.4009% 52.6132 -92.3857);
      --color-indigo-700: lab(32.4486% 49.2217 -84.6695);
      --color-indigo-800: lab(26.6645% 37.9804 -68.6402);
      --color-indigo-900: lab(23.3911% 24.6978 -50.4719);
      --color-violet-50: lab(96.2416% 2.28846 -5.51655);
      --color-violet-200: lab(87.0888% 8.53691 -19.4189);
      --color-violet-300: lab(76.7419% 18.391 -37.0706);
      --color-violet-600: lab(41.088% 68.9966 -91.995);
      --color-violet-700: lab(35.2783% 67.9912 -88.793);
      --color-violet-800: lab(29.3188% 57.7986 -76.1493);
      --color-violet-900: lab(24.3783% 45.7525 -61.4902);
      --color-purple-50: lab(97.1626% 2.99937 -4.13398);
      --color-purple-100: lab(93.3333% 6.9744 -9.83434);
      --color-purple-200: lab(87.8405% 13.4282 -18.7159);
      --color-purple-300: lab(78.3298% 26.2195 -34.9499);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-purple-600: lab(43.0295% 75.21 -86.5669);
      --color-purple-700: lab(36.1758% 69.8525 -80.0381);
      --color-purple-800: lab(30.6017% 56.7637 -64.4751);
      --color-purple-900: lab(24.9401% 45.2703 -51.2728);
      --color-pink-50: lab(96.4459% 4.53997 -1.49434);
      --color-pink-100: lab(93.5864% 9.01193 -3.15077);
      --color-pink-800: lab(34.9559% 60.2885 5.99639);
      --color-pink-900: lab(29.4367% 49.3962 3.35757);
      --color-rose-50: lab(96.2369% 4.94152 1.28012);
      --color-rose-200: lab(86.806% 19.1909 4.07754);
      --color-rose-300: lab(76.6339% 38.3549 9.68834);
      --color-rose-600: lab(49.1882% 81.577 36.0311);
      --color-rose-700: lab(41.1651% 71.6251 30.3087);
      --color-rose-800: lab(34.6481% 60.802 20.1957);
      --color-rose-900: lab(29.7104% 51.514 12.6253);
      --color-gray-50: lab(98.2596% -.247031 -.706708);
      --color-gray-100: lab(96.1596% -.082314 -1.13575);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .max-h-20 {
    max-height: calc(var(--spacing) * 20);
  }

  .max-h-24 {
    max-height: calc(var(--spacing) * 24);
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-11 {
    width: calc(var(--spacing) * 11);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-full {
    width: 100%;
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-1 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-6 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-8 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-reverse > :not(:last-child)) {
    --tw-space-x-reverse: 1;
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r-4 {
    border-right-style: var(--tw-border-style);
    border-right-width: 4px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-amber-200 {
    border-color: var(--color-amber-200);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-blue-700 {
    border-color: var(--color-blue-700);
  }

  .border-current {
    border-color: currentColor;
  }

  .border-cyan-200 {
    border-color: var(--color-cyan-200);
  }

  .border-emerald-200 {
    border-color: var(--color-emerald-200);
  }

  .border-emerald-600 {
    border-color: var(--color-emerald-600);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-400 {
    border-color: var(--color-gray-400);
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-300 {
    border-color: var(--color-green-300);
  }

  .border-green-400 {
    border-color: var(--color-green-400);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-green-600 {
    border-color: var(--color-green-600);
  }

  .border-green-700 {
    border-color: var(--color-green-700);
  }

  .border-indigo-200 {
    border-color: var(--color-indigo-200);
  }

  .border-indigo-600 {
    border-color: var(--color-indigo-600);
  }

  .border-orange-200 {
    border-color: var(--color-orange-200);
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-purple-500 {
    border-color: var(--color-purple-500);
  }

  .border-purple-600 {
    border-color: var(--color-purple-600);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-300 {
    border-color: var(--color-red-300);
  }

  .border-red-400 {
    border-color: var(--color-red-400);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-red-600 {
    border-color: var(--color-red-600);
  }

  .border-red-700 {
    border-color: var(--color-red-700);
  }

  .border-rose-200 {
    border-color: var(--color-rose-200);
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-violet-200 {
    border-color: var(--color-violet-200);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-500 {
    border-color: var(--color-yellow-500);
  }

  .border-yellow-600 {
    border-color: var(--color-yellow-600);
  }

  .border-yellow-700 {
    border-color: var(--color-yellow-700);
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-blue-900\/30 {
    background-color: rgba(28, 57, 142, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-900\/30 {
      background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
    }
  }

  .bg-emerald-50 {
    background-color: var(--color-emerald-50);
  }

  .bg-emerald-600 {
    background-color: var(--color-emerald-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-green-900\/30 {
    background-color: rgba(13, 84, 43, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-900\/30 {
      background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
    }
  }

  .bg-indigo-50 {
    background-color: var(--color-indigo-50);
  }

  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }

  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }

  .bg-pink-100 {
    background-color: var(--color-pink-100);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-red-900\/30 {
    background-color: rgba(130, 24, 26, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-red-900\/30 {
      background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
    }
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }

  .bg-yellow-900\/30 {
    background-color: rgba(115, 62, 10, .3);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-yellow-900\/30 {
      background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
    }
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-amber-50 {
    --tw-gradient-from: var(--color-amber-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-900 {
    --tw-gradient-from: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-cyan-50 {
    --tw-gradient-from: var(--color-cyan-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-emerald-50 {
    --tw-gradient-from: var(--color-emerald-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-indigo-50 {
    --tw-gradient-from: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-orange-50 {
    --tw-gradient-from: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-50 {
    --tw-gradient-from: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-50 {
    --tw-gradient-from: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-rose-50 {
    --tw-gradient-from: var(--color-rose-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-violet-50 {
    --tw-gradient-from: var(--color-violet-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-50 {
    --tw-gradient-from: var(--color-yellow-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-purple-900 {
    --tw-gradient-via: var(--color-purple-900);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-amber-50 {
    --tw-gradient-to: var(--color-amber-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-50 {
    --tw-gradient-to: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-900 {
    --tw-gradient-to: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-50 {
    --tw-gradient-to: var(--color-cyan-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-50 {
    --tw-gradient-to: var(--color-emerald-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-50 {
    --tw-gradient-to: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-50 {
    --tw-gradient-to: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-900 {
    --tw-gradient-to: var(--color-indigo-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-50 {
    --tw-gradient-to: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-pink-50 {
    --tw-gradient-to: var(--color-pink-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-50 {
    --tw-gradient-to: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-rose-50 {
    --tw-gradient-to: var(--color-rose-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-teal-50 {
    --tw-gradient-to: var(--color-teal-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-violet-50 {
    --tw-gradient-to: var(--color-violet-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-amber-600 {
    color: var(--color-amber-600);
  }

  .text-amber-700 {
    color: var(--color-amber-700);
  }

  .text-blue-200 {
    color: var(--color-blue-200);
  }

  .text-blue-300 {
    color: var(--color-blue-300);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-cyan-700 {
    color: var(--color-cyan-700);
  }

  .text-emerald-500 {
    color: var(--color-emerald-500);
  }

  .text-emerald-600 {
    color: var(--color-emerald-600);
  }

  .text-emerald-700 {
    color: var(--color-emerald-700);
  }

  .text-emerald-800 {
    color: var(--color-emerald-800);
  }

  .text-emerald-900 {
    color: var(--color-emerald-900);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-indigo-500 {
    color: var(--color-indigo-500);
  }

  .text-indigo-600 {
    color: var(--color-indigo-600);
  }

  .text-indigo-800 {
    color: var(--color-indigo-800);
  }

  .text-indigo-900 {
    color: var(--color-indigo-900);
  }

  .text-orange-500 {
    color: var(--color-orange-500);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-700 {
    color: var(--color-orange-700);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-pink-800 {
    color: var(--color-pink-800);
  }

  .text-purple-500 {
    color: var(--color-purple-500);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-purple-900 {
    color: var(--color-purple-900);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-rose-600 {
    color: var(--color-rose-600);
  }

  .text-rose-700 {
    color: var(--color-rose-700);
  }

  .text-violet-600 {
    color: var(--color-violet-600);
  }

  .text-violet-700 {
    color: var(--color-violet-700);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-400 {
    color: var(--color-yellow-400);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .text-yellow-900 {
    color: var(--color-yellow-900);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .peer-checked\:bg-blue-600:is(:where(.peer):checked ~ *) {
    background-color: var(--color-blue-600);
  }

  .peer-focus\:ring-4:is(:where(.peer):focus ~ *) {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .peer-focus\:ring-blue-300:is(:where(.peer):focus ~ *) {
    --tw-ring-color: var(--color-blue-300);
  }

  .peer-focus\:outline-none:is(:where(.peer):focus ~ *) {
    --tw-outline-style: none;
    outline-style: none;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:top-\[2px\]:after {
    content: var(--tw-content);
    top: 2px;
  }

  .after\:left-\[2px\]:after {
    content: var(--tw-content);
    left: 2px;
  }

  .after\:h-5:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 5);
  }

  .after\:w-5:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 5);
  }

  .after\:rounded-full:after {
    content: var(--tw-content);
    border-radius: 3.40282e38px;
  }

  .after\:border:after {
    content: var(--tw-content);
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .after\:border-gray-300:after {
    content: var(--tw-content);
    border-color: var(--color-gray-300);
  }

  .after\:bg-white:after {
    content: var(--tw-content);
    background-color: var(--color-white);
  }

  .after\:transition-all:after {
    content: var(--tw-content);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .peer-checked\:after\:translate-x-full:is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .peer-checked\:after\:border-white:is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    border-color: var(--color-white);
  }

  @media (hover: hover) {
    .hover\:border-gray-300:hover {
      border-color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-emerald-700:hover {
      background-color: var(--color-emerald-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-700:hover {
      background-color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-indigo-700:hover {
      background-color: var(--color-indigo-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-700:hover {
      background-color: var(--color-yellow-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-emerald-500:focus {
    --tw-ring-color: var(--color-emerald-500);
  }

  .focus\:ring-indigo-500:focus {
    --tw-ring-color: var(--color-indigo-500);
  }

  .focus\:ring-purple-500:focus {
    --tw-ring-color: var(--color-purple-500);
  }

  .focus\:ring-yellow-500:focus {
    --tw-ring-color: var(--color-yellow-500);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (min-width: 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-8 {
      grid-template-columns: repeat(8, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (prefers-color-scheme: dark) {
    :where(.dark\:divide-gray-700 > :not(:last-child)) {
      border-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-amber-800 {
      border-color: var(--color-amber-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-blue-800 {
      border-color: var(--color-blue-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-cyan-800 {
      border-color: var(--color-cyan-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-emerald-800 {
      border-color: var(--color-emerald-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-600 {
      border-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-700 {
      border-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-green-800 {
      border-color: var(--color-green-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-orange-800 {
      border-color: var(--color-orange-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-purple-800 {
      border-color: var(--color-purple-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-red-800 {
      border-color: var(--color-red-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-rose-800 {
      border-color: var(--color-rose-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-violet-800 {
      border-color: var(--color-violet-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-yellow-800 {
      border-color: var(--color-yellow-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/20 {
      background-color: rgba(28, 57, 142, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/20 {
        background-color: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/30 {
      background-color: rgba(28, 57, 142, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/30 {
        background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-emerald-900\/20 {
      background-color: rgba(0, 78, 59, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-emerald-900\/20 {
        background-color: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-600 {
      background-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700 {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-800 {
      background-color: var(--color-gray-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-900 {
      background-color: var(--color-gray-900);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/20 {
      background-color: rgba(13, 84, 43, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/20 {
        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/30 {
      background-color: rgba(13, 84, 43, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/30 {
        background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-indigo-900\/20 {
      background-color: rgba(49, 44, 133, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-indigo-900\/20 {
        background-color: color-mix(in oklab, var(--color-indigo-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-orange-900\/20 {
      background-color: rgba(126, 42, 12, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-orange-900\/20 {
        background-color: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-purple-900\/20 {
      background-color: rgba(89, 22, 139, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-purple-900\/20 {
        background-color: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900\/20 {
      background-color: rgba(130, 24, 26, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-900\/20 {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-red-900\/30 {
      background-color: rgba(130, 24, 26, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-red-900\/30 {
        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/20 {
      background-color: rgba(115, 62, 10, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/20 {
        background-color: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-yellow-900\/30 {
      background-color: rgba(115, 62, 10, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-yellow-900\/30 {
        background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-amber-900\/20 {
      --tw-gradient-from: rgba(123, 51, 6, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-amber-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-amber-900) 20%, transparent);
      }
    }

    .dark\:from-amber-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-blue-900\/20 {
      --tw-gradient-from: rgba(28, 57, 142, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-blue-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }

    .dark\:from-blue-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-cyan-900\/20 {
      --tw-gradient-from: rgba(16, 78, 100, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-cyan-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-cyan-900) 20%, transparent);
      }
    }

    .dark\:from-cyan-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-emerald-900\/20 {
      --tw-gradient-from: rgba(0, 78, 59, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-emerald-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
      }
    }

    .dark\:from-emerald-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-gray-700 {
      --tw-gradient-from: var(--color-gray-700);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-green-900\/20 {
      --tw-gradient-from: rgba(13, 84, 43, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-green-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }

    .dark\:from-green-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-indigo-900\/20 {
      --tw-gradient-from: rgba(49, 44, 133, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-indigo-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-indigo-900) 20%, transparent);
      }
    }

    .dark\:from-indigo-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-orange-900\/20 {
      --tw-gradient-from: rgba(126, 42, 12, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-orange-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }

    .dark\:from-orange-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-purple-900\/20 {
      --tw-gradient-from: rgba(89, 22, 139, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-purple-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }

    .dark\:from-purple-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-red-900\/20 {
      --tw-gradient-from: rgba(130, 24, 26, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-red-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }

    .dark\:from-red-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-rose-900\/20 {
      --tw-gradient-from: rgba(139, 8, 54, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-rose-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-rose-900) 20%, transparent);
      }
    }

    .dark\:from-rose-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-violet-900\/20 {
      --tw-gradient-from: rgba(77, 23, 154, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-violet-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-violet-900) 20%, transparent);
      }
    }

    .dark\:from-violet-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-yellow-900\/20 {
      --tw-gradient-from: rgba(115, 62, 10, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-yellow-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }

    .dark\:from-yellow-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-blue-900\/20 {
      --tw-gradient-to: rgba(28, 57, 142, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-blue-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-blue-900) 20%, transparent);
      }
    }

    .dark\:to-blue-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-cyan-900\/20 {
      --tw-gradient-to: rgba(16, 78, 100, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-cyan-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-cyan-900) 20%, transparent);
      }
    }

    .dark\:to-cyan-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-emerald-900\/20 {
      --tw-gradient-to: rgba(0, 78, 59, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-emerald-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
      }
    }

    .dark\:to-emerald-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-green-900\/20 {
      --tw-gradient-to: rgba(13, 84, 43, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-green-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }

    .dark\:to-green-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-indigo-900\/20 {
      --tw-gradient-to: rgba(49, 44, 133, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-indigo-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-indigo-900) 20%, transparent);
      }
    }

    .dark\:to-indigo-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-orange-900\/20 {
      --tw-gradient-to: rgba(126, 42, 12, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-orange-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }

    .dark\:to-orange-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-pink-900\/20 {
      --tw-gradient-to: rgba(134, 16, 67, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-pink-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-pink-900) 20%, transparent);
      }
    }

    .dark\:to-pink-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-purple-900\/20 {
      --tw-gradient-to: rgba(89, 22, 139, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-purple-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-purple-900) 20%, transparent);
      }
    }

    .dark\:to-purple-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-red-900\/20 {
      --tw-gradient-to: rgba(130, 24, 26, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-red-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }

    .dark\:to-red-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-rose-900\/20 {
      --tw-gradient-to: rgba(139, 8, 54, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-rose-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-rose-900) 20%, transparent);
      }
    }

    .dark\:to-rose-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-teal-900\/20 {
      --tw-gradient-to: rgba(11, 79, 74, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-teal-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-teal-900) 20%, transparent);
      }
    }

    .dark\:to-teal-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-violet-900\/20 {
      --tw-gradient-to: rgba(77, 23, 154, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-violet-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-violet-900) 20%, transparent);
      }
    }

    .dark\:to-violet-900\/20 {
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-amber-300 {
      color: var(--color-amber-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-100 {
      color: var(--color-blue-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-200 {
      color: var(--color-blue-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-300 {
      color: var(--color-blue-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-400 {
      color: var(--color-blue-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-cyan-300 {
      color: var(--color-cyan-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-emerald-100 {
      color: var(--color-emerald-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-emerald-200 {
      color: var(--color-emerald-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-emerald-300 {
      color: var(--color-emerald-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-300 {
      color: var(--color-gray-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400 {
      color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-300 {
      color: var(--color-green-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-400 {
      color: var(--color-green-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-indigo-100 {
      color: var(--color-indigo-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-indigo-200 {
      color: var(--color-indigo-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-orange-300 {
      color: var(--color-orange-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-100 {
      color: var(--color-purple-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-200 {
      color: var(--color-purple-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-300 {
      color: var(--color-purple-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-red-400 {
      color: var(--color-red-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-rose-300 {
      color: var(--color-rose-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-violet-300 {
      color: var(--color-violet-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-100 {
      color: var(--color-yellow-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-200 {
      color: var(--color-yellow-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-300 {
      color: var(--color-yellow-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-yellow-400 {
      color: var(--color-yellow-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:peer-focus\:ring-blue-800:is(:where(.peer):focus ~ *) {
      --tw-ring-color: var(--color-blue-800);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-700:hover {
        background-color: var(--color-gray-700);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-gray-300:hover {
        color: var(--color-gray-300);
      }
    }
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=src_app_globals_b805903d.css.map*/