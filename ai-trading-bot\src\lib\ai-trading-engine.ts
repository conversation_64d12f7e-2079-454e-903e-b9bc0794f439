// Advanced AI Trading Engine for Accurate Recommendations
import { TradingViewData, TechnicalIndicators, MarketSentiment } from './tradingview-connector';

export interface AIAnalysis {
  symbol: string;
  timeframe: string;
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
  confidence: number;
  accuracy: number;
  entry: number;
  stopLoss: number;
  takeProfit1: number;
  takeProfit2: number;
  takeProfit3: number;
  riskReward: number;
  reasoning: string[];
  technicalScore: number;
  sentimentScore: number;
  volumeScore: number;
  trendScore: number;
  overallScore: number;
}

export interface PatternRecognition {
  pattern: string;
  confidence: number;
  direction: 'BULLISH' | 'BEARISH';
  target: number;
  invalidation: number;
}

export interface MarketStructure {
  trend: 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS';
  strength: number;
  support: number[];
  resistance: number[];
  keyLevels: number[];
  breakoutProbability: number;
}

export class AITradingEngine {
  private historicalAccuracy: Map<string, number[]> = new Map();
  private patternDatabase: Map<string, PatternRecognition[]> = new Map();
  private learningData: Map<string, any[]> = new Map();

  constructor() {
    this.initializePatternDatabase();
    this.loadHistoricalAccuracy();
  }

  // Main analysis function
  async analyzeSymbol(
    data: TradingViewData,
    indicators: TechnicalIndicators,
    sentiment: MarketSentiment,
    timeframe: string
  ): Promise<AIAnalysis> {
    
    // Calculate individual scores
    const technicalScore = this.calculateTechnicalScore(data, indicators);
    const sentimentScore = this.calculateSentimentScore(sentiment);
    const volumeScore = this.calculateVolumeScore(data);
    const trendScore = this.calculateTrendScore(data, indicators);
    
    // Calculate overall score with weighted average
    const overallScore = (
      technicalScore * 0.4 +
      sentimentScore * 0.2 +
      volumeScore * 0.2 +
      trendScore * 0.2
    );

    // Determine recommendation
    const recommendation = this.determineRecommendation(overallScore, technicalScore, trendScore);
    
    // Calculate confidence based on score consistency
    const confidence = this.calculateConfidence([technicalScore, sentimentScore, volumeScore, trendScore]);
    
    // Get historical accuracy for this symbol
    const accuracy = this.getHistoricalAccuracy(data.symbol, timeframe);
    
    // Calculate entry and exit levels
    const levels = this.calculateTradingLevels(data, indicators, recommendation);
    
    // Generate AI reasoning
    const reasoning = this.generateReasoning(data, indicators, sentiment, recommendation, overallScore);

    return {
      symbol: data.symbol,
      timeframe,
      recommendation,
      confidence,
      accuracy,
      entry: levels.entry,
      stopLoss: levels.stopLoss,
      takeProfit1: levels.takeProfit1,
      takeProfit2: levels.takeProfit2,
      takeProfit3: levels.takeProfit3,
      riskReward: levels.riskReward,
      reasoning,
      technicalScore,
      sentimentScore,
      volumeScore,
      trendScore,
      overallScore
    };
  }

  // Calculate technical analysis score (0-100)
  private calculateTechnicalScore(data: TradingViewData, indicators: TechnicalIndicators): number {
    let score = 50; // Start neutral

    // RSI Analysis
    if (indicators.rsi < 30) score += 20; // Oversold - bullish
    else if (indicators.rsi > 70) score -= 20; // Overbought - bearish
    else if (indicators.rsi > 45 && indicators.rsi < 55) score += 5; // Neutral zone

    // MACD Analysis
    if (indicators.macd.macd > indicators.macd.signal) score += 15; // Bullish crossover
    else score -= 15; // Bearish crossover

    if (indicators.macd.histogram > 0) score += 10; // Positive momentum
    else score -= 10; // Negative momentum

    // EMA Analysis
    if (data.price > indicators.ema.ema20) score += 10;
    if (data.price > indicators.ema.ema50) score += 10;
    if (data.price > indicators.ema.ema200) score += 15;

    if (indicators.ema.ema20 > indicators.ema.ema50) score += 10;
    if (indicators.ema.ema50 > indicators.ema.ema200) score += 10;

    // Bollinger Bands Analysis
    if (data.price < indicators.bollinger.lower) score += 15; // Oversold
    else if (data.price > indicators.bollinger.upper) score -= 15; // Overbought

    // Stochastic Analysis
    if (indicators.stochastic.k < 20 && indicators.stochastic.d < 20) score += 10; // Oversold
    else if (indicators.stochastic.k > 80 && indicators.stochastic.d > 80) score -= 10; // Overbought

    // ADX Trend Strength
    if (indicators.adx > 25) {
      // Strong trend
      if (score > 50) score += 10; // Enhance bullish signal
      else score -= 10; // Enhance bearish signal
    }

    return Math.max(0, Math.min(100, score));
  }

  // Calculate sentiment score (0-100)
  private calculateSentimentScore(sentiment: MarketSentiment): number {
    const bullishWeight = sentiment.bullishPercent;
    const bearishWeight = sentiment.bearishPercent;
    
    // Convert to 0-100 scale
    let score = 50 + (bullishWeight - bearishWeight) / 2;
    
    // Adjust for confidence
    const sentimentConfidence = sentiment.confidence / 100;
    score = 50 + (score - 50) * sentimentConfidence;
    
    return Math.max(0, Math.min(100, score));
  }

  // Calculate volume score (0-100)
  private calculateVolumeScore(data: TradingViewData): number {
    // Compare current volume to average (simulated)
    const avgVolume = 1000000; // This would be calculated from historical data
    const volumeRatio = data.volume / avgVolume;
    
    let score = 50;
    
    if (volumeRatio > 1.5) {
      // High volume
      if (data.changePercent > 0) score += 20; // Bullish with high volume
      else score -= 20; // Bearish with high volume
    } else if (volumeRatio < 0.5) {
      // Low volume - reduce confidence
      score = 50;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  // Calculate trend score (0-100)
  private calculateTrendScore(data: TradingViewData, indicators: TechnicalIndicators): number {
    let score = 50;
    
    // Price vs EMAs
    if (data.price > indicators.ema.ema20) score += 15;
    if (data.price > indicators.ema.ema50) score += 15;
    if (data.price > indicators.ema.ema200) score += 20;
    
    // EMA alignment
    if (indicators.ema.ema20 > indicators.ema.ema50) score += 10;
    if (indicators.ema.ema50 > indicators.ema.ema200) score += 15;
    
    // Recent price action
    if (data.changePercent > 0) score += 10;
    if (data.changePercent > 1) score += 10;
    
    // ADX trend strength
    if (indicators.adx > 25) score += 15;
    
    return Math.max(0, Math.min(100, score));
  }

  // Determine recommendation based on scores
  private determineRecommendation(
    overallScore: number,
    technicalScore: number,
    trendScore: number
  ): 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL' {
    
    // Strong signals require both technical and trend alignment
    if (overallScore > 80 && technicalScore > 75 && trendScore > 75) return 'STRONG_BUY';
    if (overallScore < 20 && technicalScore < 25 && trendScore < 25) return 'STRONG_SELL';
    
    // Regular signals
    if (overallScore > 65) return 'BUY';
    if (overallScore < 35) return 'SELL';
    
    return 'NEUTRAL';
  }

  // Calculate confidence based on score consistency
  private calculateConfidence(scores: number[]): number {
    const avg = scores.reduce((a, b) => a + b, 0) / scores.length;
    const variance = scores.reduce((acc, score) => acc + Math.pow(score - avg, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Lower standard deviation = higher confidence
    const consistency = Math.max(0, 100 - standardDeviation * 2);
    
    // Combine with average score distance from neutral
    const strength = Math.abs(avg - 50) * 2;
    
    return Math.min(100, (consistency + strength) / 2);
  }

  // Calculate trading levels
  private calculateTradingLevels(
    data: TradingViewData,
    indicators: TechnicalIndicators,
    recommendation: string
  ): {
    entry: number;
    stopLoss: number;
    takeProfit1: number;
    takeProfit2: number;
    takeProfit3: number;
    riskReward: number;
  } {
    const entry = data.price;
    const atr = indicators.atr;
    const isBuy = recommendation.includes('BUY');
    
    // Calculate stop loss based on ATR and support/resistance
    let stopLoss: number;
    if (isBuy) {
      stopLoss = Math.min(
        entry - (atr * 2),
        indicators.bollinger.lower,
        entry * 0.985 // Max 1.5% risk
      );
    } else {
      stopLoss = Math.max(
        entry + (atr * 2),
        indicators.bollinger.upper,
        entry * 1.015 // Max 1.5% risk
      );
    }
    
    const riskDistance = Math.abs(entry - stopLoss);
    
    // Calculate take profit levels with increasing risk/reward ratios
    let takeProfit1: number, takeProfit2: number, takeProfit3: number;
    
    if (isBuy) {
      takeProfit1 = entry + (riskDistance * 1.5); // 1.5:1 R/R
      takeProfit2 = entry + (riskDistance * 2.5); // 2.5:1 R/R
      takeProfit3 = entry + (riskDistance * 4.0); // 4:1 R/R
    } else {
      takeProfit1 = entry - (riskDistance * 1.5);
      takeProfit2 = entry - (riskDistance * 2.5);
      takeProfit3 = entry - (riskDistance * 4.0);
    }
    
    const riskReward = Math.abs(takeProfit1 - entry) / riskDistance;
    
    return {
      entry,
      stopLoss,
      takeProfit1,
      takeProfit2,
      takeProfit3,
      riskReward
    };
  }

  // Generate AI reasoning
  private generateReasoning(
    data: TradingViewData,
    indicators: TechnicalIndicators,
    sentiment: MarketSentiment,
    recommendation: string,
    overallScore: number
  ): string[] {
    const reasoning: string[] = [];
    const isBuy = recommendation.includes('BUY');
    
    // Technical reasoning
    if (indicators.rsi < 30) {
      reasoning.push(`RSI في منطقة التشبع البيعي (${indicators.rsi.toFixed(1)}) - إشارة شراء محتملة`);
    } else if (indicators.rsi > 70) {
      reasoning.push(`RSI في منطقة التشبع الشرائي (${indicators.rsi.toFixed(1)}) - إشارة بيع محتملة`);
    }
    
    if (indicators.macd.macd > indicators.macd.signal) {
      reasoning.push(`MACD يظهر تقاطع صاعد - زخم إيجابي`);
    } else {
      reasoning.push(`MACD يظهر تقاطع هابط - زخم سلبي`);
    }
    
    // Price action reasoning
    if (data.price > indicators.ema.ema20) {
      reasoning.push(`السعر فوق المتوسط المتحرك 20 - اتجاه صاعد قصير المدى`);
    } else {
      reasoning.push(`السعر تحت المتوسط المتحرك 20 - ضغط بيع قصير المدى`);
    }
    
    // Volume reasoning
    if (data.volume > 1000000) {
      reasoning.push(`حجم تداول مرتفع (${(data.volume / 1000000).toFixed(1)}M) يؤكد الحركة`);
    }
    
    // Sentiment reasoning
    if (sentiment.bullishPercent > 60) {
      reasoning.push(`معنويات السوق إيجابية (${sentiment.bullishPercent.toFixed(1)}% متفائل)`);
    } else if (sentiment.bearishPercent > 60) {
      reasoning.push(`معنويات السوق سلبية (${sentiment.bearishPercent.toFixed(1)}% متشائم)`);
    }
    
    // Overall confidence reasoning
    reasoning.push(`النتيجة الإجمالية للذكاء الاصطناعي: ${overallScore.toFixed(1)}/100`);
    
    if (recommendation === 'STRONG_BUY' || recommendation === 'STRONG_SELL') {
      reasoning.push(`إشارة قوية مدعومة بتوافق جميع المؤشرات`);
    }
    
    return reasoning;
  }

  // Get historical accuracy for symbol
  private getHistoricalAccuracy(symbol: string, timeframe: string): number {
    const key = `${symbol}_${timeframe}`;
    const history = this.historicalAccuracy.get(key) || [];
    
    if (history.length === 0) {
      return 85; // Default accuracy for new symbols
    }
    
    // Calculate average accuracy from last 50 predictions
    const recentHistory = history.slice(-50);
    const avgAccuracy = recentHistory.reduce((a, b) => a + b, 0) / recentHistory.length;
    
    return Math.round(avgAccuracy);
  }

  // Update accuracy after trade completion
  updateAccuracy(symbol: string, timeframe: string, wasCorrect: boolean): void {
    const key = `${symbol}_${timeframe}`;
    const history = this.historicalAccuracy.get(key) || [];
    
    history.push(wasCorrect ? 100 : 0);
    
    // Keep only last 100 results
    if (history.length > 100) {
      history.shift();
    }
    
    this.historicalAccuracy.set(key, history);
  }

  // Initialize pattern database
  private initializePatternDatabase(): void {
    // This would be loaded from a comprehensive pattern database
    // For now, we'll initialize with some basic patterns
    const patterns = [
      'Double Top', 'Double Bottom', 'Head and Shoulders', 'Inverse Head and Shoulders',
      'Triangle', 'Flag', 'Pennant', 'Cup and Handle', 'Wedge', 'Channel'
    ];
    
    patterns.forEach(pattern => {
      this.patternDatabase.set(pattern, []);
    });
  }

  // Load historical accuracy data
  private loadHistoricalAccuracy(): void {
    // This would load from persistent storage
    // For now, we'll initialize with some sample data
    const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'];
    const timeframes = ['1h', '4h', '1d'];
    
    symbols.forEach(symbol => {
      timeframes.forEach(timeframe => {
        const key = `${symbol}_${timeframe}`;
        // Generate some sample historical accuracy data
        const history = Array.from({ length: 20 }, () => 
          Math.random() > 0.3 ? 100 : 0 // 70% accuracy simulation
        );
        this.historicalAccuracy.set(key, history);
      });
    });
  }

  // Recognize patterns in price data
  recognizePatterns(data: TradingViewData[], timeframe: string): PatternRecognition[] {
    const patterns: PatternRecognition[] = [];
    
    if (data.length < 20) return patterns; // Need enough data
    
    // Simple pattern recognition (would be much more sophisticated in production)
    const recent = data.slice(-20);
    const prices = recent.map(d => d.close);
    
    // Look for double top/bottom
    const highs = this.findLocalExtremes(prices, 'high');
    const lows = this.findLocalExtremes(prices, 'low');
    
    if (highs.length >= 2) {
      const lastTwo = highs.slice(-2);
      if (Math.abs(lastTwo[0].value - lastTwo[1].value) / lastTwo[0].value < 0.02) {
        patterns.push({
          pattern: 'Double Top',
          confidence: 75,
          direction: 'BEARISH',
          target: lastTwo[0].value * 0.98,
          invalidation: lastTwo[0].value * 1.005
        });
      }
    }
    
    if (lows.length >= 2) {
      const lastTwo = lows.slice(-2);
      if (Math.abs(lastTwo[0].value - lastTwo[1].value) / lastTwo[0].value < 0.02) {
        patterns.push({
          pattern: 'Double Bottom',
          confidence: 75,
          direction: 'BULLISH',
          target: lastTwo[0].value * 1.02,
          invalidation: lastTwo[0].value * 0.995
        });
      }
    }
    
    return patterns;
  }

  // Find local extremes in price data
  private findLocalExtremes(prices: number[], type: 'high' | 'low'): { index: number; value: number }[] {
    const extremes: { index: number; value: number }[] = [];
    
    for (let i = 2; i < prices.length - 2; i++) {
      const current = prices[i];
      const prev2 = prices[i - 2];
      const prev1 = prices[i - 1];
      const next1 = prices[i + 1];
      const next2 = prices[i + 2];
      
      if (type === 'high') {
        if (current > prev2 && current > prev1 && current > next1 && current > next2) {
          extremes.push({ index: i, value: current });
        }
      } else {
        if (current < prev2 && current < prev1 && current < next1 && current < next2) {
          extremes.push({ index: i, value: current });
        }
      }
    }
    
    return extremes;
  }

  // Analyze market structure
  analyzeMarketStructure(data: TradingViewData[], indicators: TechnicalIndicators): MarketStructure {
    const prices = data.map(d => d.close);
    const highs = this.findLocalExtremes(prices, 'high');
    const lows = this.findLocalExtremes(prices, 'low');
    
    // Determine trend
    let trend: 'UPTREND' | 'DOWNTREND' | 'SIDEWAYS' = 'SIDEWAYS';
    let strength = 0;
    
    if (highs.length >= 2 && lows.length >= 2) {
      const recentHighs = highs.slice(-3).map(h => h.value);
      const recentLows = lows.slice(-3).map(l => l.value);
      
      const highsIncreasing = recentHighs.every((val, i) => i === 0 || val >= recentHighs[i - 1]);
      const lowsIncreasing = recentLows.every((val, i) => i === 0 || val >= recentLows[i - 1]);
      
      const highsDecreasing = recentHighs.every((val, i) => i === 0 || val <= recentHighs[i - 1]);
      const lowsDecreasing = recentLows.every((val, i) => i === 0 || val <= recentLows[i - 1]);
      
      if (highsIncreasing && lowsIncreasing) {
        trend = 'UPTREND';
        strength = 80;
      } else if (highsDecreasing && lowsDecreasing) {
        trend = 'DOWNTREND';
        strength = 80;
      } else {
        strength = 40;
      }
    }
    
    // Calculate support and resistance levels
    const support = lows.slice(-3).map(l => l.value).sort((a, b) => b - a);
    const resistance = highs.slice(-3).map(h => h.value).sort((a, b) => a - b);
    
    // Key levels (round numbers, previous highs/lows)
    const currentPrice = data[data.length - 1].close;
    const keyLevels = this.calculateKeyLevels(currentPrice);
    
    // Breakout probability
    const breakoutProbability = this.calculateBreakoutProbability(data, indicators);
    
    return {
      trend,
      strength,
      support,
      resistance,
      keyLevels,
      breakoutProbability
    };
  }

  // Calculate key psychological levels
  private calculateKeyLevels(price: number): number[] {
    const levels: number[] = [];
    const magnitude = Math.pow(10, Math.floor(Math.log10(price)));
    
    // Round numbers
    for (let i = 0; i < 10; i++) {
      const level = Math.round(price / magnitude) * magnitude + (i * magnitude / 10);
      if (Math.abs(level - price) / price < 0.1) { // Within 10%
        levels.push(level);
      }
    }
    
    return levels.sort((a, b) => Math.abs(a - price) - Math.abs(b - price)).slice(0, 5);
  }

  // Calculate breakout probability
  private calculateBreakoutProbability(data: TradingViewData[], indicators: TechnicalIndicators): number {
    let probability = 50; // Base probability
    
    // Volume analysis
    const recentVolume = data.slice(-5).reduce((sum, d) => sum + d.volume, 0) / 5;
    const avgVolume = data.slice(-20).reduce((sum, d) => sum + d.volume, 0) / 20;
    
    if (recentVolume > avgVolume * 1.5) probability += 20;
    
    // Volatility analysis
    if (indicators.atr > 0.01) probability += 15; // High volatility
    
    // ADX trend strength
    if (indicators.adx > 25) probability += 15;
    
    return Math.min(100, probability);
  }
}
