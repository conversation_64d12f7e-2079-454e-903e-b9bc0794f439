(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ProfessionalOrderBlocks.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>ProfessionalOrderBlocks
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function ProfessionalOrderBlocks() {
    _s();
    const [selectedPair, setSelectedPair] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('EURUSD');
    const [selectedTimeframe, setSelectedTimeframe] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('1h');
    const [analysis, setAnalysis] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isAnalyzing, setIsAnalyzing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastUpdate, setLastUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date());
    const forexPairs = [
        'EURUSD',
        'GBPUSD',
        'USDJPY',
        'USDCHF',
        'AUDUSD',
        'USDCAD',
        'NZDUSD',
        'EURGBP',
        'EURJPY',
        'GBPJPY',
        'XAUUSD',
        'XAGUSD',
        'USOIL',
        'BTCUSD'
    ];
    const timeframes = [
        {
            key: '15m',
            name: '15 دقيقة',
            weight: 1
        },
        {
            key: '1h',
            name: '1 ساعة',
            weight: 2
        },
        {
            key: '4h',
            name: '4 ساعات',
            weight: 3
        },
        {
            key: '1d',
            name: '1 يوم',
            weight: 4
        }
    ];
    const runProfessionalAnalysis = async ()=>{
        setIsAnalyzing(true);
        try {
            // Simulate professional analysis delay
            await new Promise((resolve)=>setTimeout(resolve, 3000));
            const basePrice = getBasePrice(selectedPair);
            const volatility = getVolatility(selectedPair);
            // Generate professional order blocks
            const orderBlocks = generateOrderBlocks(basePrice, volatility);
            // Analyze market structure
            const marketStructure = analyzeMarketStructure(orderBlocks, basePrice);
            // Calculate institutional sentiment
            const institutionalSentiment = calculateInstitutionalSentiment(orderBlocks);
            // Generate liquidity levels
            const liquidityLevels = generateLiquidityLevels(basePrice, volatility);
            // Generate professional recommendation
            const recommendation = generateProfessionalRecommendation(orderBlocks, marketStructure, institutionalSentiment, basePrice);
            setAnalysis({
                symbol: selectedPair,
                currentPrice: basePrice + (Math.random() - 0.5) * volatility * basePrice * 0.01,
                orderBlocks,
                marketStructure,
                institutionalSentiment,
                liquidityLevels,
                recommendation
            });
            setLastUpdate(new Date());
        } catch (error) {
            console.error('Professional Analysis Error:', error);
        } finally{
            setIsAnalyzing(false);
        }
    };
    const generateOrderBlocks = (basePrice, volatility)=>{
        var _timeframes_find;
        const blocks = [];
        const timeframeWeight = ((_timeframes_find = timeframes.find((tf)=>tf.key === selectedTimeframe)) === null || _timeframes_find === void 0 ? void 0 : _timeframes_find.weight) || 2;
        // Generate 3-7 order blocks based on timeframe
        const blockCount = 3 + Math.floor(Math.random() * 5);
        for(let i = 0; i < blockCount; i++){
            const type = Math.random() > 0.5 ? 'BULLISH' : 'BEARISH';
            const distance = (Math.random() * 0.02 + 0.005) * basePrice; // 0.5% to 2.5% from current price
            const level = type === 'BULLISH' ? basePrice - distance : basePrice + distance;
            const age = Math.floor(Math.random() * 48); // 0-48 hours
            const tested = Math.floor(Math.random() * 4); // 0-3 times tested
            const volume = 500000 + Math.random() * 2000000;
            // Calculate strength based on multiple factors
            let strength = 50;
            strength += timeframeWeight * 10; // Higher timeframe = stronger
            strength += Math.max(0, (48 - age) / 48 * 20); // Fresher = stronger
            strength -= tested * 15; // More tested = weaker
            strength += volume / 1000000 * 5; // Higher volume = stronger
            strength = Math.max(20, Math.min(95, strength));
            // Determine validity
            let validity = 'FRESH';
            if (tested > 0) validity = 'TESTED';
            if (tested > 2 && strength < 40) validity = 'BROKEN';
            // Institutional flow analysis
            const institutionalFlow = type === 'BULLISH' ? Math.random() > 0.3 ? 'ACCUMULATION' : 'NEUTRAL' : Math.random() > 0.3 ? 'DISTRIBUTION' : 'NEUTRAL';
            // Generate confluence factors
            const confluence = generateConfluence(level, basePrice, type);
            // Calculate probability
            const probability = calculateBlockProbability(strength, validity, confluence.length, timeframeWeight);
            blocks.push({
                id: "OB_".concat(i, "_").concat(Date.now()),
                type,
                level,
                strength,
                volume,
                timeframe: selectedTimeframe,
                age,
                tested,
                validity,
                institutionalFlow,
                confluence,
                probability
            });
        }
        return blocks.sort((a, b)=>b.probability - a.probability);
    };
    const generateConfluence = (level, basePrice, type)=>{
        const confluence = [];
        // Random confluence factors
        const factors = [
            'Fibonacci 61.8%',
            'Previous Structure',
            'Volume Profile POC',
            'Daily Pivot',
            'Weekly Support/Resistance',
            'Psychological Level',
            'Trendline Confluence',
            'Moving Average',
            'Institutional Level'
        ];
        const confluenceCount = 1 + Math.floor(Math.random() * 4); // 1-4 confluence factors
        const selectedFactors = factors.sort(()=>0.5 - Math.random()).slice(0, confluenceCount);
        return selectedFactors;
    };
    const calculateBlockProbability = (strength, validity, confluenceCount, timeframeWeight)=>{
        let probability = strength;
        // Validity adjustment
        if (validity === 'FRESH') probability += 10;
        else if (validity === 'TESTED') probability -= 5;
        else probability -= 20; // BROKEN
        // Confluence adjustment
        probability += confluenceCount * 5;
        // Timeframe adjustment
        probability += timeframeWeight * 3;
        return Math.max(10, Math.min(95, probability));
    };
    const analyzeMarketStructure = (blocks, currentPrice)=>{
        const bullishBlocks = blocks.filter((b)=>b.type === 'BULLISH' && b.validity !== 'BROKEN');
        const bearishBlocks = blocks.filter((b)=>b.type === 'BEARISH' && b.validity !== 'BROKEN');
        const bullishStrength = bullishBlocks.reduce((sum, b)=>sum + b.strength, 0);
        const bearishStrength = bearishBlocks.reduce((sum, b)=>sum + b.strength, 0);
        const difference = Math.abs(bullishStrength - bearishStrength);
        const total = bullishStrength + bearishStrength;
        if (total === 0) return 'RANGING';
        const dominancePercentage = difference / total;
        if (dominancePercentage < 0.2) return 'RANGING';
        return bullishStrength > bearishStrength ? 'BULLISH' : 'BEARISH';
    };
    const calculateInstitutionalSentiment = (blocks)=>{
        let sentiment = 0;
        let totalWeight = 0;
        blocks.forEach((block)=>{
            const weight = block.strength * (block.validity === 'FRESH' ? 1.5 : block.validity === 'TESTED' ? 1 : 0.5);
            const blockSentiment = block.type === 'BULLISH' ? 1 : -1;
            if (block.institutionalFlow === 'ACCUMULATION') {
                sentiment += blockSentiment * weight * 1.3;
            } else if (block.institutionalFlow === 'DISTRIBUTION') {
                sentiment += blockSentiment * weight * 1.3;
            } else {
                sentiment += blockSentiment * weight;
            }
            totalWeight += weight;
        });
        return totalWeight > 0 ? sentiment / totalWeight * 100 : 0;
    };
    const generateLiquidityLevels = (basePrice, volatility)=>{
        const buyLiquidity = [];
        const sellLiquidity = [];
        // Generate 3-5 liquidity levels above and below current price
        for(let i = 0; i < 4; i++){
            const distance = (i + 1) * 0.005 * basePrice; // 0.5%, 1%, 1.5%, 2%
            buyLiquidity.push(basePrice - distance);
            sellLiquidity.push(basePrice + distance);
        }
        return {
            buyLiquidity,
            sellLiquidity
        };
    };
    const generateProfessionalRecommendation = (blocks, marketStructure, sentiment, currentPrice)=>{
        const strongBlocks = blocks.filter((b)=>b.strength > 70 && b.validity !== 'BROKEN');
        const nearestBlock = blocks.reduce((nearest, current)=>{
            const nearestDistance = Math.abs(nearest.level - currentPrice);
            const currentDistance = Math.abs(current.level - currentPrice);
            return currentDistance < nearestDistance ? current : nearest;
        });
        let action = 'HOLD';
        let confidence = 50;
        const reasoning = [];
        // Determine action based on multiple factors
        if (marketStructure === 'BULLISH' && sentiment > 30) {
            action = sentiment > 60 ? 'STRONG_BUY' : 'BUY';
            confidence = 70 + Math.min(25, sentiment / 4);
            reasoning.push("هيكل السوق صاعد مع معنويات مؤسسية إيجابية (".concat(sentiment.toFixed(1), ")"));
        } else if (marketStructure === 'BEARISH' && sentiment < -30) {
            action = sentiment < -60 ? 'STRONG_SELL' : 'SELL';
            confidence = 70 + Math.min(25, Math.abs(sentiment) / 4);
            reasoning.push("هيكل السوق هابط مع معنويات مؤسسية سلبية (".concat(sentiment.toFixed(1), ")"));
        } else {
            reasoning.push("السوق في حالة توازن - انتظار إشارة واضحة");
        }
        // Add order block analysis
        if (nearestBlock.strength > 80) {
            reasoning.push("منطقة طلبات قوية عند ".concat(nearestBlock.level.toFixed(5), " (قوة: ").concat(nearestBlock.strength.toFixed(0), "%)"));
        }
        if (strongBlocks.length > 0) {
            reasoning.push("".concat(strongBlocks.length, " منطقة طلبات قوية تدعم التحليل"));
        }
        // Calculate entry zone and levels
        const entryZone = {
            min: nearestBlock.level - Math.abs(nearestBlock.level * 0.001),
            max: nearestBlock.level + Math.abs(nearestBlock.level * 0.001)
        };
        const stopLoss = action.includes('BUY') ? nearestBlock.level - Math.abs(nearestBlock.level * 0.015) : nearestBlock.level + Math.abs(nearestBlock.level * 0.015);
        const targets = action.includes('BUY') ? [
            nearestBlock.level + Math.abs(nearestBlock.level * 0.02),
            nearestBlock.level + Math.abs(nearestBlock.level * 0.035),
            nearestBlock.level + Math.abs(nearestBlock.level * 0.05)
        ] : [
            nearestBlock.level - Math.abs(nearestBlock.level * 0.02),
            nearestBlock.level - Math.abs(nearestBlock.level * 0.035),
            nearestBlock.level - Math.abs(nearestBlock.level * 0.05)
        ];
        const riskReward = Math.abs(targets[0] - nearestBlock.level) / Math.abs(nearestBlock.level - stopLoss);
        return {
            action,
            confidence,
            reasoning,
            entryZone,
            stopLoss,
            targets,
            riskReward
        };
    };
    const getBasePrice = (symbol)=>{
        const prices = {
            'EURUSD': 1.0850,
            'GBPUSD': 1.2650,
            'USDJPY': 149.50,
            'USDCHF': 0.8920,
            'AUDUSD': 0.6580,
            'USDCAD': 1.3650,
            'NZDUSD': 0.6120,
            'EURGBP': 0.8580,
            'EURJPY': 162.30,
            'GBPJPY': 189.20,
            'XAUUSD': 2050.00,
            'XAGUSD': 24.50,
            'USOIL': 78.50,
            'BTCUSD': 43250.00
        };
        return prices[symbol] || 1.0000;
    };
    const getVolatility = (symbol)=>{
        const volatilities = {
            'EURUSD': 0.8,
            'GBPUSD': 1.2,
            'USDJPY': 1.0,
            'USDCHF': 0.7,
            'AUDUSD': 1.5,
            'USDCAD': 1.0,
            'NZDUSD': 1.8,
            'EURGBP': 0.6,
            'EURJPY': 1.3,
            'GBPJPY': 1.8,
            'XAUUSD': 2.0,
            'XAGUSD': 2.5,
            'USOIL': 3.0,
            'BTCUSD': 4.0
        };
        return volatilities[symbol] || 1.0;
    };
    const getPairFlag = (symbol)=>{
        const flags = {
            'EURUSD': '🇪🇺🇺🇸',
            'GBPUSD': '🇬🇧🇺🇸',
            'USDJPY': '🇺🇸🇯🇵',
            'USDCHF': '🇺🇸🇨🇭',
            'AUDUSD': '🇦🇺🇺🇸',
            'USDCAD': '🇺🇸🇨🇦',
            'NZDUSD': '🇳🇿🇺🇸',
            'EURGBP': '🇪🇺🇬🇧',
            'EURJPY': '🇪🇺🇯🇵',
            'GBPJPY': '🇬🇧🇯🇵',
            'XAUUSD': '🥇💰',
            'XAGUSD': '🥈💰',
            'USOIL': '🛢️💰',
            'BTCUSD': '₿💰'
        };
        return flags[symbol] || '💱';
    };
    const getBlockColor = (block)=>{
        if (block.validity === 'BROKEN') return 'bg-gray-100 border-gray-400 text-gray-600';
        if (block.type === 'BULLISH') {
            if (block.strength > 80) return 'bg-green-100 border-green-600 text-green-800';
            if (block.strength > 60) return 'bg-green-50 border-green-400 text-green-700';
            return 'bg-green-25 border-green-300 text-green-600';
        } else {
            if (block.strength > 80) return 'bg-red-100 border-red-600 text-red-800';
            if (block.strength > 60) return 'bg-red-50 border-red-400 text-red-700';
            return 'bg-red-25 border-red-300 text-red-600';
        }
    };
    const getActionColor = (action)=>{
        switch(action){
            case 'STRONG_BUY':
                return 'bg-green-600 text-white';
            case 'BUY':
                return 'bg-green-500 text-white';
            case 'HOLD':
                return 'bg-yellow-500 text-white';
            case 'SELL':
                return 'bg-red-500 text-white';
            case 'STRONG_SELL':
                return 'bg-red-600 text-white';
            default:
                return 'bg-gray-500 text-white';
        }
    };
    const getActionText = (action)=>{
        switch(action){
            case 'STRONG_BUY':
                return 'شراء قوي';
            case 'BUY':
                return 'شراء';
            case 'HOLD':
                return 'انتظار';
            case 'SELL':
                return 'بيع';
            case 'STRONG_SELL':
                return 'بيع قوي';
            default:
                return 'غير محدد';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white dark:bg-gray-800 rounded-lg shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-bold text-gray-900 dark:text-white flex items-center",
                                children: [
                                    "🏛️ تحليل Order Blocks الاحترافي",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "mr-3 px-2 py-1 bg-indigo-600 text-white rounded text-sm",
                                        children: "ICT Pro"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 407,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 405,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3 space-x-reverse",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                        children: [
                                            "آخر تحديث: ",
                                            lastUpdate.toLocaleTimeString('ar-SA')
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 412,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: runProfessionalAnalysis,
                                        disabled: isAnalyzing,
                                        className: "px-6 py-2 rounded-lg font-medium transition-colors ".concat(isAnalyzing ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-indigo-600 text-white hover:bg-indigo-700'),
                                        children: isAnalyzing ? '🔍 جاري التحليل...' : '🚀 تحليل احترافي'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 415,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 411,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                        lineNumber: 404,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "زوج العملة:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 432,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: selectedPair,
                                        onChange: (e)=>setSelectedPair(e.target.value),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",
                                        children: forexPairs.map((pair)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: pair,
                                                children: [
                                                    getPairFlag(pair),
                                                    " ",
                                                    pair
                                                ]
                                            }, pair, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 441,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 435,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 431,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "الإطار الزمني:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 449,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: selectedTimeframe,
                                        onChange: (e)=>setSelectedTimeframe(e.target.value),
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",
                                        children: timeframes.map((tf)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: tf.key,
                                                children: [
                                                    tf.name,
                                                    " (وزن: ",
                                                    tf.weight,
                                                    ")"
                                                ]
                                            }, tf.key, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 458,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 452,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 448,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                        lineNumber: 430,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                lineNumber: 403,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6",
                children: [
                    isAnalyzing && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inline-flex items-center space-x-3 space-x-reverse",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 472,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-lg text-gray-600 dark:text-gray-400",
                                        children: "🔍 تحليل مناطق الطلبات المؤسسية..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 473,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 471,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-4 text-sm text-gray-500 space-y-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• فحص هيكل السوق"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 478,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• تحليل تدفق الأموال الذكية"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 479,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• حساب مستويات السيولة"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 480,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: "• توليد توصيات احترافية"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 481,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 477,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                        lineNumber: 470,
                        columnNumber: 11
                    }, this),
                    !isAnalyzing && !analysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-6xl mb-4",
                                children: "🏛️"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 489,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold text-gray-900 dark:text-white mb-2",
                                children: "تحليل Order Blocks الاحترافي"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 490,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",
                                children: "اكتشف مناطق الطلبات المؤسسية وتحليل تدفق الأموال الذكية باستخدام مفاهيم ICT المتقدمة"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 493,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 max-w-lg mx-auto",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-indigo-900 dark:text-indigo-100 mb-2",
                                        children: "💡 ما هي Order Blocks؟"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 497,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "text-sm text-indigo-800 dark:text-indigo-200 space-y-1 text-right",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• مناطق تراكم الطلبات المؤسسية الكبيرة"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 501,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• مستويات دعم ومقاومة قوية جداً"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 502,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• تحليل تدفق الأموال الذكية"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 503,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "• دقة عالية في تحديد نقاط الانعكاس"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 504,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 500,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 496,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                        lineNumber: 488,
                        columnNumber: 11
                    }, this),
                    !isAnalyzing && analysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-3 space-x-reverse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-2xl",
                                                        children: getPairFlag(analysis.symbol)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 517,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "text-lg font-bold text-gray-900 dark:text-white",
                                                                children: [
                                                                    analysis.symbol,
                                                                    " - ",
                                                                    selectedTimeframe
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 519,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                children: [
                                                                    "هيكل السوق: ",
                                                                    analysis.marketStructure === 'BULLISH' ? '📈 صاعد' : analysis.marketStructure === 'BEARISH' ? '📉 هابط' : '➡️ متذبذب'
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 522,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 518,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 516,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-right",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900 dark:text-white",
                                                        children: analysis.currentPrice.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 528,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: "السعر الحالي"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 531,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 527,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 515,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-bold ".concat(analysis.institutionalSentiment > 0 ? 'text-green-600' : analysis.institutionalSentiment < 0 ? 'text-red-600' : 'text-yellow-600'),
                                                        children: analysis.institutionalSentiment.toFixed(1)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 537,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: "معنويات مؤسسية"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 540,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 536,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-bold text-blue-600",
                                                        children: analysis.orderBlocks.length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 543,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: "مناطق طلبات"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 546,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 542,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-bold text-purple-600",
                                                        children: analysis.orderBlocks.filter((b)=>b.validity === 'FRESH').length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 549,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 dark:text-gray-400",
                                                        children: "مناطق جديدة"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 552,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 548,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 535,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 514,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white dark:bg-gray-700 rounded-lg p-6 border-2 border-indigo-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",
                                        children: [
                                            "🎯 التوصية الاحترافية",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "mr-3 px-3 py-1 rounded-full text-sm font-medium ".concat(getActionColor(analysis.recommendation.action)),
                                                children: getActionText(analysis.recommendation.action)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 561,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 559,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-gray-900 dark:text-white mb-3",
                                                        children: "📋 تحليل الأسباب:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 568,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "space-y-2",
                                                        children: analysis.recommendation.reasoning.map((reason, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                className: "flex items-start text-sm",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "mr-2 text-indigo-500",
                                                                        children: "▶"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 572,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-700 dark:text-gray-300",
                                                                        children: reason
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 573,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, "reasoning-".concat(i), true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 571,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 569,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 567,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-gray-900 dark:text-white mb-3",
                                                        children: "💰 مستويات التداول:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 580,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-2 text-sm",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600 dark:text-gray-400",
                                                                        children: "منطقة الدخول:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 583,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: [
                                                                            analysis.recommendation.entryZone.min.toFixed(5),
                                                                            " - ",
                                                                            analysis.recommendation.entryZone.max.toFixed(5)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 584,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 582,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600 dark:text-gray-400",
                                                                        children: "وقف الخسارة:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 589,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium text-red-600",
                                                                        children: analysis.recommendation.stopLoss.toFixed(5)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 590,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 588,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600 dark:text-gray-400",
                                                                        children: "الهدف الأول:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 595,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium text-green-600",
                                                                        children: analysis.recommendation.targets[0].toFixed(5)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 596,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 594,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600 dark:text-gray-400",
                                                                        children: "نسبة R/R:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 601,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium text-blue-600",
                                                                        children: [
                                                                            analysis.recommendation.riskReward.toFixed(2),
                                                                            ":1"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 602,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 600,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600 dark:text-gray-400",
                                                                        children: "مستوى الثقة:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 607,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium text-purple-600",
                                                                        children: [
                                                                            analysis.recommendation.confidence.toFixed(0),
                                                                            "%"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 608,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 606,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 581,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 579,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 566,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 558,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "rounded-xl p-6 border-4 ".concat(analysis.recommendation.action.includes('BUY') ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20' : analysis.recommendation.action.includes('SELL') ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500 dark:from-red-900/20 dark:to-rose-900/20' : 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-500 dark:from-yellow-900/20 dark:to-amber-900/20'),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-3xl font-bold text-gray-900 dark:text-white mb-2",
                                                children: "🏛️ خلاصة تحليل ICT"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 626,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "inline-flex items-center px-8 py-4 rounded-full text-2xl font-bold ".concat(getActionColor(analysis.recommendation.action)),
                                                children: [
                                                    analysis.recommendation.action.includes('BUY') && '📈 ',
                                                    analysis.recommendation.action.includes('SELL') && '📉 ',
                                                    analysis.recommendation.action === 'انتظار' && '➡️ ',
                                                    getActionText(analysis.recommendation.action)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 629,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-lg text-gray-600 dark:text-gray-400 mt-2",
                                                children: "بناءً على تحليل Order Blocks المؤسسية ومفاهيم ICT المتقدمة"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 635,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 625,
                                        columnNumber: 15
                                    }, this),
                                    analysis.recommendation.action !== 'انتظار' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center",
                                                        children: "🎯 إعداد ICT المقترح"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 644,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "grid grid-cols-2 gap-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-blue-50 dark:bg-blue-900/20 rounded p-3 text-center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                children: "منطقة الدخول"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 651,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-lg font-bold text-blue-600",
                                                                                children: ((analysis.recommendation.entryZone.min + analysis.recommendation.entryZone.max) / 2).toFixed(5)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 652,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 650,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-red-50 dark:bg-red-900/20 rounded p-3 text-center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm text-gray-600 dark:text-gray-400",
                                                                                children: "وقف الخسارة"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 657,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-lg font-bold text-red-600",
                                                                                children: analysis.recommendation.stopLoss.toFixed(5)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 658,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 656,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 649,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "grid grid-cols-3 gap-2",
                                                                children: analysis.recommendation.targets.map((target, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "bg-green-50 dark:bg-green-900/20 rounded p-2 text-center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-xs text-gray-600 dark:text-gray-400",
                                                                                children: [
                                                                                    "هدف ",
                                                                                    i + 1
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 667,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm font-bold text-green-600",
                                                                                children: target.toFixed(5)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 668,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, "target-".concat(i), true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 666,
                                                                        columnNumber: 27
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 664,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-indigo-50 dark:bg-indigo-900/20 rounded p-3",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-sm text-gray-600 dark:text-gray-400",
                                                                            children: "نسبة المخاطرة/العائد"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                            lineNumber: 677,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-xl font-bold text-indigo-600",
                                                                            children: [
                                                                                analysis.recommendation.riskReward.toFixed(2),
                                                                                ":1"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                            lineNumber: 678,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                    lineNumber: 676,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 675,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 648,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 643,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded-lg p-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center",
                                                        children: "🏛️ تحليل ICT المتقدم"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 688,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-purple-50 dark:bg-purple-900/20 rounded p-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                        className: "font-medium text-purple-800 dark:text-purple-200 mb-2",
                                                                        children: "📊 Order Blocks النشطة:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 694,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-sm space-y-1",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "مناطق جديدة: ",
                                                                                    analysis.orderBlocks.filter((b)=>b.validity === 'FRESH').length
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 698,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "مناطق مختبرة: ",
                                                                                    analysis.orderBlocks.filter((b)=>b.validity === 'TESTED').length
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 699,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                children: [
                                                                                    "قوة متوسطة: ",
                                                                                    (analysis.orderBlocks.reduce((sum, b)=>sum + b.strength, 0) / analysis.orderBlocks.length).toFixed(0),
                                                                                    "%"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 700,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 697,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 693,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-orange-50 dark:bg-orange-900/20 rounded p-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                        className: "font-medium text-orange-800 dark:text-orange-200 mb-2",
                                                                        children: "💰 تدفق الأموال الذكية:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 705,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-sm",
                                                                        children: [
                                                                            "معنويات مؤسسية: ",
                                                                            analysis.institutionalSentiment > 0 ? 'إيجابية' : analysis.institutionalSentiment < 0 ? 'سلبية' : 'محايدة',
                                                                            "(",
                                                                            analysis.institutionalSentiment.toFixed(1),
                                                                            ")"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 708,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 704,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-blue-50 dark:bg-blue-900/20 rounded p-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                        className: "font-medium text-blue-800 dark:text-blue-200 mb-2",
                                                                        children: "🎯 مستويات السيولة:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 715,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-sm",
                                                                        children: [
                                                                            "سيولة شراء: ",
                                                                            analysis.liquidityLevels.buyLiquidity.length,
                                                                            " مستوى",
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 719,
                                                                                columnNumber: 91
                                                                            }, this),
                                                                            "سيولة بيع: ",
                                                                            analysis.liquidityLevels.sellLiquidity.length,
                                                                            " مستوى"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 718,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 714,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 692,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 687,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 641,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-6 grid grid-cols-1 lg:grid-cols-2 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded-lg p-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between mb-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                                                children: "مستوى الثقة ICT:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 732,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm font-bold text-gray-900 dark:text-white",
                                                                children: [
                                                                    analysis.recommendation.confidence.toFixed(0),
                                                                    "%"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 733,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 731,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-full bg-gray-200 rounded-full h-3",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-3 rounded-full transition-all duration-500 ".concat(analysis.recommendation.confidence > 80 ? 'bg-green-500' : analysis.recommendation.confidence > 60 ? 'bg-yellow-500' : 'bg-red-500'),
                                                            style: {
                                                                width: "".concat(analysis.recommendation.confidence, "%")
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                            lineNumber: 736,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 735,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 730,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-indigo-900 dark:text-indigo-100 mb-2",
                                                        children: [
                                                            "🏆 أقوى Order Block: ",
                                                            analysis.nearestLevel.percentage
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 747,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-indigo-800 dark:text-indigo-200",
                                                        children: [
                                                            "قوة: ",
                                                            analysis.nearestLevel.strength.toFixed(0),
                                                            "% | نوع: ",
                                                            analysis.nearestLevel.type === 'support' ? 'دعم' : 'مقاومة',
                                                            " | حالة: ",
                                                            analysis.nearestLevel.validity
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 750,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 746,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 729,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 618,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white dark:bg-gray-700 rounded-lg p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-lg font-semibold text-gray-900 dark:text-white mb-4",
                                        children: "🏛️ مناطق الطلبات المؤسسية"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 761,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: analysis.orderBlocks.map((block, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "border-2 rounded-lg p-4 transition-all duration-200 ".concat(getBlockColor(block)),
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between mb-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center space-x-3 space-x-reverse",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-lg font-bold",
                                                                        children: [
                                                                            block.type === 'BULLISH' ? '🟢' : '🔴',
                                                                            " ",
                                                                            block.type
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 773,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "font-medium",
                                                                                children: block.level.toFixed(analysis.symbol.includes('JPY') ? 3 : 5)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 777,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-xs",
                                                                                children: [
                                                                                    block.validity,
                                                                                    " | ",
                                                                                    block.timeframe
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                                lineNumber: 780,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 776,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 772,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-right",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-sm font-medium",
                                                                        children: [
                                                                            "احتمالية: ",
                                                                            block.probability.toFixed(0),
                                                                            "%"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 787,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-xs",
                                                                        children: [
                                                                            "قوة: ",
                                                                            block.strength.toFixed(0),
                                                                            "%"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 790,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 786,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 771,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "grid grid-cols-2 md:grid-cols-4 gap-2 text-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600",
                                                                        children: "العمر:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 798,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium mr-1",
                                                                        children: [
                                                                            block.age,
                                                                            "س"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 799,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 797,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600",
                                                                        children: "اختبار:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 802,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium mr-1",
                                                                        children: [
                                                                            block.tested,
                                                                            "x"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 803,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 801,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600",
                                                                        children: "الحجم:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 806,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium mr-1",
                                                                        children: [
                                                                            (block.volume / 1000000).toFixed(1),
                                                                            "M"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 807,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 805,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-gray-600",
                                                                        children: "التدفق:"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 810,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium mr-1",
                                                                        children: block.institutionalFlow
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 811,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 809,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 796,
                                                        columnNumber: 21
                                                    }, this),
                                                    block.confluence.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-3 pt-3 border-t border-current",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "font-medium",
                                                                    children: "التقارب: "
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                    lineNumber: 818,
                                                                    columnNumber: 27
                                                                }, this),
                                                                block.confluence.join(', ')
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                            lineNumber: 817,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 816,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, block.id, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 767,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 765,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 760,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-lg font-semibold text-gray-900 dark:text-white mb-4",
                                        children: "💧 مستويات السيولة"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 830,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded p-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-green-600 mb-2",
                                                        children: "🟢 سيولة الشراء:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 836,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-1 text-sm",
                                                        children: analysis.liquidityLevels.buyLiquidity.map((level, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        children: [
                                                                            "مستوى ",
                                                                            i + 1,
                                                                            ":"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 840,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: level.toFixed(5)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 841,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, "buy-liquidity-".concat(i), true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 839,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 837,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 835,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white dark:bg-gray-700 rounded p-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                        className: "font-medium text-red-600 mb-2",
                                                        children: "🔴 سيولة البيع:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 848,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-1 text-sm",
                                                        children: analysis.liquidityLevels.sellLiquidity.map((level, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        children: [
                                                                            "مستوى ",
                                                                            i + 1,
                                                                            ":"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 852,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium",
                                                                        children: level.toFixed(5)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                        lineNumber: 853,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, "sell-liquidity-".concat(i), true, {
                                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                                lineNumber: 851,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                        lineNumber: 849,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                                lineNumber: 847,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                        lineNumber: 834,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                                lineNumber: 829,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                        lineNumber: 512,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
                lineNumber: 467,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ProfessionalOrderBlocks.tsx",
        lineNumber: 401,
        columnNumber: 5
    }, this);
}
_s(ProfessionalOrderBlocks, "z+4zHuFv6KChp6JH45WBXqCJJaQ=");
_c = ProfessionalOrderBlocks;
var _c;
__turbopack_context__.k.register(_c, "ProfessionalOrderBlocks");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ProfessionalOrderBlocks_tsx_c69fb262._.js.map