// Forex Factory News Integration
import * as cheerio from 'cheerio';

export interface NewsEvent {
  id: string;
  time: string;
  currency: string;
  impact: 'LOW' | 'MEDIUM' | 'HIGH';
  event: string;
  actual?: string;
  forecast?: string;
  previous?: string;
  timestamp: number;
  affectedPairs: string[];
}

export interface EconomicCalendar {
  date: string;
  events: NewsEvent[];
  highImpactCount: number;
  mediumImpactCount: number;
  lowImpactCount: number;
}

export class ForexFactoryService {
  private baseUrl = 'https://www.forexfactory.com';
  private userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

  // Get today's economic calendar
  async getTodaysCalendar(): Promise<EconomicCalendar> {
    try {
      const today = new Date().toISOString().split('T')[0];
      return await this.getCalendarForDate(today);
    } catch (error) {
      console.error('❌ Error fetching today\'s calendar:', error);
      return this.getEmptyCalendar();
    }
  }

  // Get economic calendar for specific date
  async getCalendarForDate(date: string): Promise<EconomicCalendar> {
    try {
      const url = `${this.baseUrl}/calendar?day=${date}`;
      const response = await fetch(url, {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      return this.parseCalendarHTML(html, date);

    } catch (error) {
      console.error(`❌ Error fetching calendar for ${date}:`, error);
      return this.getEmptyCalendar();
    }
  }

  // Parse HTML and extract news events
  private parseCalendarHTML(html: string, date: string): EconomicCalendar {
    const $ = cheerio.load(html);
    const events: NewsEvent[] = [];
    let highImpactCount = 0;
    let mediumImpactCount = 0;
    let lowImpactCount = 0;

    // Parse calendar table rows
    $('.calendar__row').each((index, element) => {
      try {
        const $row = $(element);
        
        // Extract time
        const timeText = $row.find('.calendar__time').text().trim();
        if (!timeText || timeText === 'All Day') return;

        // Extract currency
        const currency = $row.find('.calendar__currency').text().trim();
        if (!currency) return;

        // Extract impact level
        const impactElement = $row.find('.calendar__impact');
        let impact: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';
        
        if (impactElement.find('.icon--ff-impact-red').length > 0) {
          impact = 'HIGH';
          highImpactCount++;
        } else if (impactElement.find('.icon--ff-impact-ora').length > 0) {
          impact = 'MEDIUM';
          mediumImpactCount++;
        } else {
          lowImpactCount++;
        }

        // Extract event name
        const event = $row.find('.calendar__event').text().trim();
        if (!event) return;

        // Extract actual, forecast, previous values
        const actual = $row.find('.calendar__actual').text().trim() || undefined;
        const forecast = $row.find('.calendar__forecast').text().trim() || undefined;
        const previous = $row.find('.calendar__previous').text().trim() || undefined;

        // Create timestamp
        const timestamp = this.parseEventTime(date, timeText);

        // Determine affected currency pairs
        const affectedPairs = this.getAffectedPairs(currency);

        const newsEvent: NewsEvent = {
          id: `ff_${timestamp}_${currency}_${index}`,
          time: timeText,
          currency,
          impact,
          event,
          actual,
          forecast,
          previous,
          timestamp,
          affectedPairs
        };

        events.push(newsEvent);

      } catch (error) {
        console.error('❌ Error parsing calendar row:', error);
      }
    });

    return {
      date,
      events: events.sort((a, b) => a.timestamp - b.timestamp),
      highImpactCount,
      mediumImpactCount,
      lowImpactCount
    };
  }

  // Parse event time and create timestamp
  private parseEventTime(date: string, timeText: string): number {
    try {
      // Handle different time formats
      let hour = 0;
      let minute = 0;

      if (timeText.includes(':')) {
        const [hourStr, minuteStr] = timeText.split(':');
        hour = parseInt(hourStr);
        minute = parseInt(minuteStr.replace(/[^\d]/g, ''));

        // Handle AM/PM
        if (timeText.toLowerCase().includes('pm') && hour !== 12) {
          hour += 12;
        } else if (timeText.toLowerCase().includes('am') && hour === 12) {
          hour = 0;
        }
      }

      const eventDate = new Date(date);
      eventDate.setHours(hour, minute, 0, 0);
      
      return eventDate.getTime();
    } catch (error) {
      console.error('❌ Error parsing time:', timeText, error);
      return new Date(date).getTime();
    }
  }

  // Get currency pairs affected by news
  private getAffectedPairs(currency: string): string[] {
    const majorPairs: { [key: string]: string[] } = {
      'USD': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'],
      'EUR': ['EURUSD', 'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD'],
      'GBP': ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPCHF', 'GBPAUD', 'GBPCAD'],
      'JPY': ['USDJPY', 'EURJPY', 'GBPJPY', 'AUDJPY', 'CADJPY', 'CHFJPY'],
      'CHF': ['USDCHF', 'EURCHF', 'GBPCHF', 'AUDCHF', 'CADCHF', 'CHFJPY'],
      'AUD': ['AUDUSD', 'EURAUD', 'GBPAUD', 'AUDJPY', 'AUDCHF', 'AUDCAD'],
      'CAD': ['USDCAD', 'EURCAD', 'GBPCAD', 'CADJPY', 'CADCHF', 'AUDCAD'],
      'NZD': ['NZDUSD', 'EURNZD', 'GBPNZD', 'NZDJPY', 'NZDCHF', 'NZDCAD']
    };

    return majorPairs[currency] || [];
  }

  // Get high impact events for today
  async getHighImpactEvents(): Promise<NewsEvent[]> {
    const calendar = await this.getTodaysCalendar();
    return calendar.events.filter(event => event.impact === 'HIGH');
  }

  // Get upcoming events (next 2 hours)
  async getUpcomingEvents(): Promise<NewsEvent[]> {
    const calendar = await this.getTodaysCalendar();
    const now = Date.now();
    const twoHoursFromNow = now + (2 * 60 * 60 * 1000);

    return calendar.events.filter(event => 
      event.timestamp >= now && event.timestamp <= twoHoursFromNow
    );
  }

  // Check if there are high impact events affecting a specific pair
  async isHighImpactEventAffecting(pair: string): Promise<boolean> {
    const highImpactEvents = await this.getHighImpactEvents();
    return highImpactEvents.some(event => 
      event.affectedPairs.includes(pair.toUpperCase())
    );
  }

  // Get market sentiment based on news
  async getNewsSentiment(pair: string): Promise<{
    sentiment: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    confidence: number;
    events: NewsEvent[];
  }> {
    const calendar = await this.getTodaysCalendar();
    const relevantEvents = calendar.events.filter(event => 
      event.affectedPairs.includes(pair.toUpperCase()) && 
      event.impact !== 'LOW'
    );

    if (relevantEvents.length === 0) {
      return {
        sentiment: 'NEUTRAL',
        confidence: 0,
        events: []
      };
    }

    // Analyze sentiment based on actual vs forecast
    let bullishScore = 0;
    let bearishScore = 0;
    let totalEvents = 0;

    relevantEvents.forEach(event => {
      if (event.actual && event.forecast) {
        const actual = parseFloat(event.actual.replace(/[^\d.-]/g, ''));
        const forecast = parseFloat(event.forecast.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(actual) && !isNaN(forecast)) {
          const weight = event.impact === 'HIGH' ? 3 : 2;
          
          if (actual > forecast) {
            bullishScore += weight;
          } else if (actual < forecast) {
            bearishScore += weight;
          }
          
          totalEvents += weight;
        }
      }
    });

    if (totalEvents === 0) {
      return {
        sentiment: 'NEUTRAL',
        confidence: 0,
        events: relevantEvents
      };
    }

    const bullishRatio = bullishScore / totalEvents;
    const bearishRatio = bearishScore / totalEvents;
    const confidence = Math.abs(bullishRatio - bearishRatio) * 100;

    let sentiment: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    if (bullishRatio > bearishRatio && confidence > 20) {
      sentiment = 'BULLISH';
    } else if (bearishRatio > bullishRatio && confidence > 20) {
      sentiment = 'BEARISH';
    }

    return {
      sentiment,
      confidence: Math.min(confidence, 100),
      events: relevantEvents
    };
  }

  // Get empty calendar fallback
  private getEmptyCalendar(): EconomicCalendar {
    return {
      date: new Date().toISOString().split('T')[0],
      events: [],
      highImpactCount: 0,
      mediumImpactCount: 0,
      lowImpactCount: 0
    };
  }

  // Format news summary for display
  formatNewsSummary(calendar: EconomicCalendar): string {
    const { events, highImpactCount, mediumImpactCount } = calendar;
    
    let summary = `📰 Economic Calendar - ${calendar.date}\n\n`;
    summary += `📊 Events: ${events.length} total\n`;
    summary += `🔴 High Impact: ${highImpactCount}\n`;
    summary += `🟡 Medium Impact: ${mediumImpactCount}\n\n`;

    if (highImpactCount > 0) {
      summary += `🚨 High Impact Events:\n`;
      events
        .filter(e => e.impact === 'HIGH')
        .slice(0, 5)
        .forEach(event => {
          summary += `• ${event.time} ${event.currency} - ${event.event}\n`;
        });
    }

    return summary;
  }
}
